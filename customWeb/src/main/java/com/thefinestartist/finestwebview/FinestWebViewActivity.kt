package com.thefinestartist.finestwebview
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Intent
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.PorterDuff
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.StateListDrawable
import android.net.MailTo
import android.net.Uri
import android.os.Build.*
import android.os.Bundle
import android.util.TypedValue
import android.view.*
import android.view.animation.Animation
import android.view.animation.Animation.*
import android.view.animation.AnimationUtils
import android.webkit.DownloadListener
import android.webkit.WebChromeClient
import android.webkit.WebSettings.*
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.*
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatImageButton
import androidx.appcompat.widget.Toolbar
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.snackbar.Snackbar
import com.thefinestartist.finestwebview.enums.ProgressBarPosition
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onDownloadStart
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onLoadResource
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onPageCommitVisible
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onPageFinished
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onPageStarted
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onProgressChanged
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onReceivedTitle
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.onReceivedTouchIconUrl
import com.thefinestartist.finestwebview.listeners.BroadCastManager.Companion.unregister
import com.thefinestartist.finestwebview.utils.BitmapUtil.getColoredBitmap
import com.thefinestartist.finestwebview.utils.BitmapUtil.getGradientBitmap
import com.thefinestartist.finestwebview.utils.ColorUtil.disableColor

import com.thefinestartist.finestwebview.utils.DisplayUtil.getHeight
import com.thefinestartist.finestwebview.utils.DisplayUtil.getStatusBarHeight
import com.thefinestartist.finestwebview.utils.DisplayUtil.getWidth
import com.thefinestartist.finestwebview.utils.UnitConverter.dpToPx
import com.thefinestartist.finestwebview.utils.UrlParser.getHost
import com.thefinestartist.finestwebview.views.ShadowLayout

class FinestWebViewActivity : AppCompatActivity(), AppBarLayout.OnOffsetChangedListener,
    View.OnClickListener {

    private var key = 0

    private var rtl = false

    private var statusBarColor = 0

    private var toolbarColor = 0
    private var toolbarScrollFlags = 0

    private var iconDefaultColor = 0
    private var iconDisabledColor = 0
    private var iconPressedColor = 0
    private var iconSelector = 0

    private var showIconClose = false
    private var disableIconClose = false
    private var showIconBack = false
    private var disableIconBack = false
    private var showIconForward = false
    private var disableIconForward = false
    private var showIconMenu = false
    private var disableIconMenu = false

    private var showSwipeRefreshLayout = false
    private var swipeRefreshColor = 0
    private var swipeRefreshColors: IntArray? = null

    private var showDivider = false
    private var gradientDivider = false
    private var dividerColor = 0
    private var dividerHeight = 0f

    private var showProgressBar = false
    private var progressBarColor = 0
    private var progressBarHeight = 0f
    private var progressBarPosition: ProgressBarPosition? = null

    private var titleDefault: String? = null
    private var updateTitleFromHtml = false
    private var titleSize = 0f
    private var titleFont: Int? = null
    private var finestWebViewTitleColor = 0

    private var showUrl = false
    private var urlSize = 0f
    private var urlFont: Int? = null
    private var urlColor = 0

    private var menuColor = 0
    private var menuDropShadowColor = 0
    private var menuDropShadowSize = 0f
    private var menuSelector = 0

    private var menuTextSize = 0f
    private var menuTextFont: Int? = null
    private var menuTextColor = 0

    private var menuTextGravity = 0
    private var menuTextPaddingLeft = 0f
    private var menuTextPaddingRight = 0f

    private var showMenuRefresh = false
    private var stringResRefresh = 0
    private var showMenuFind = false
    private var stringResFind = 0
    private var showMenuShareVia = false
    private var stringResShareVia = 0
    private var showMenuCopyLink = false
    private var stringResCopyLink = 0
    private var showMenuOpenWith = false
    private var stringResOpenWith = 0

    private var animationCloseEnter = 0
    private var animationCloseExit = 0

    private var backPressToClose = false
    private var stringResCopiedToClipboard = 0

    private var webViewSupportZoom: Boolean? = null
    private var webViewMediaPlaybackRequiresUserGesture: Boolean? = null
    private var webViewBuiltInZoomControls: Boolean? = null
    private var webViewDisplayZoomControls: Boolean? = null
    private var webViewAllowFileAccess: Boolean? = null
    private var webViewAllowContentAccess: Boolean? = null
    private var webViewLoadWithOverviewMode: Boolean? = null
    private var webViewSaveFormData: Boolean? = null
    private var webViewTextZoom: Int? = null
    private var webViewUseWideViewPort: Boolean? = null
    private var webViewSupportMultipleWindows: Boolean? = null
    private var webViewLayoutAlgorithm: LayoutAlgorithm? = null
    private var webViewStandardFontFamily: String? = null
    private var webViewFixedFontFamily: String? = null
    private var webViewSansSerifFontFamily: String? = null
    private var webViewSerifFontFamily: String? = null
    private var webViewCursiveFontFamily: String? = null
    private var webViewFantasyFontFamily: String? = null
    private var webViewMinimumFontSize: Int? = null
    private var webViewMinimumLogicalFontSize: Int? = null
    private var webViewDefaultFontSize: Int? = null
    private var webViewDefaultFixedFontSize: Int? = null
    private var webViewLoadsImagesAutomatically: Boolean? = null
    private var webViewBlockNetworkImage: Boolean? = null
    private var webViewBlockNetworkLoads: Boolean? = null
    private var webViewJavaScriptEnabled: Boolean? = null
    private var webViewAllowUniversalAccessFromFileURLs: Boolean? = null
    private var webViewAllowFileAccessFromFileURLs: Boolean? = null
    private var webViewGeolocationDatabasePath: String? = null
    private var webViewAppCacheEnabled: Boolean? = null
    private var webViewAppCachePath: String? = null
    private var webViewDatabaseEnabled: Boolean? = null
    private var webViewDomStorageEnabled: Boolean? = null
    private var webViewGeolocationEnabled: Boolean? = null
    private var webViewJavaScriptCanOpenWindowsAutomatically: Boolean? = null
    private var webViewDefaultTextEncodingName: String? = null
    private var webViewUserAgentString: String? = null
    private var webViewNeedInitialFocus: Boolean? = null
    private var webViewCacheMode: Int? = null
    private var webViewMixedContentMode: Int? = null
    private var webViewOffscreenPreRaster: Boolean? = null

    private var injectJavaScript: String? = null

    private var mimeType: String? = null
    private var encoding: String? = null
    private var data: String? = null
    private var url: String? = null
    private var coordinatorLayout: CoordinatorLayout? = null
    private var appBar: AppBarLayout? = null
    private var toolbar: Toolbar? = null
    private var toolbarLayout: RelativeLayout? = null
    private var title: TextView? = null
    private var urlTv: TextView? = null
    private var close: AppCompatImageButton? = null
    private var back: AppCompatImageButton? = null
    private var forward: AppCompatImageButton? = null
    private var more: AppCompatImageButton? = null
    private var swipeRefreshLayout: SwipeRefreshLayout? = null
    private var webView: WebView? = null
    private var gradient: View? = null
    private var divider: View? = null
    private var progressBar: ProgressBar? = null
    private var menuLayout: RelativeLayout? = null
    private var shadowLayout: ShadowLayout? = null
    private var menuBackground: LinearLayout? = null
    private var menuRefresh: LinearLayout? = null
    private var menuRefreshTv: TextView? = null
    private var menuFind: LinearLayout? = null
    private var menuFindTv: TextView? = null
    private var menuShareVia: LinearLayout? = null
    private var menuShareViaTv: TextView? = null
    private var menuCopyLink: LinearLayout? = null
    private var menuCopyLinkTv: TextView? = null
    private var menuOpenWith: LinearLayout? = null
    private var menuOpenWithTv: TextView? = null
    private var webLayout: FrameLayout? = null

    var downloadListener =
        DownloadListener { url, userAgent, contentDisposition, mimetype, contentLength ->
            onDownloadStart(
                this@FinestWebViewActivity,
                key,
                url,
                userAgent,
                contentDisposition,
                mimetype,
                contentLength
            )
        }

    private fun initializeOptions() {
        val intent = intent ?: return
        val finestWebView = if (VERSION.SDK_INT >= VERSION_CODES.TIRAMISU) {
            intent.getSerializableExtra(
                "FinestWebView",
                FinestWebView::class.java
            ) as FinestWebView?
        } else {
            intent.getSerializableExtra("FinestWebView") as FinestWebView?
        }

        // set theme before resolving attributes depending on those
        setTheme(((if (finestWebView!!.theme != null) finestWebView.theme else 0)!!))

        // resolve themed attributes
        val typedValue = TypedValue()
        val typedArray = obtainStyledAttributes(
            typedValue.data,
            intArrayOf(
                android.R.attr.colorPrimaryDark,
                android.R.attr.colorPrimary,
                android.R.attr.colorAccent,
                android.R.attr.textColorPrimary,
                android.R.attr.textColorSecondary,
                android.R.attr.selectableItemBackground,
                android.R.attr.selectableItemBackgroundBorderless
            )
        )
        val colorPrimaryDark =
            typedArray.getColor(0, ContextCompat.getColor(this, R.color.finestGray))
        val colorPrimary = typedArray.getColor(1, ContextCompat.getColor(this, R.color.finestWhite))
        val colorAccent = typedArray.getColor(2, ContextCompat.getColor(this, R.color.finestBlack))
        val textColorPrimary = typedArray.getColor(3, ContextCompat.getColor(this, R.color.finestBlack))
        val textColorSecondary = typedArray.getColor(4, ContextCompat.getColor(this, R.color.finestSilver))
        val selectableItemBackground = typedArray.getResourceId(5, 0)
        val selectableItemBackgroundBorderless = typedArray.getResourceId(6, 0)
        typedArray.recycle()
        key = finestWebView.key!!

        rtl = finestWebView.rtl?.let {
            finestWebView.rtl
        } ?: run {
            resources.getBoolean(R.bool.is_right_to_left)
        }

        statusBarColor = finestWebView.statusBarColor?.let { finestWebView.statusBarColor }
            ?: run { colorPrimaryDark }

        toolbarColor =
            finestWebView.toolbarColor?.let { finestWebView.toolbarColor } ?: run { colorPrimary }

        toolbarScrollFlags =
            finestWebView.toolbarScrollFlags?.let { finestWebView.toolbarScrollFlags }
                ?: run { AppBarLayout.LayoutParams.SCROLL_FLAG_SCROLL or AppBarLayout.LayoutParams.SCROLL_FLAG_ENTER_ALWAYS }

        iconDefaultColor = finestWebView.iconDefaultColor?.let { finestWebView.iconDefaultColor }
            ?: run { colorAccent }

        iconDisabledColor = finestWebView.iconDisabledColor?.let { finestWebView.iconDisabledColor }
            ?: run { disableColor(iconDefaultColor) }

        iconPressedColor = finestWebView.iconPressedColor?.let { finestWebView.iconPressedColor }
            ?: run { iconDefaultColor }

        iconSelector = finestWebView.iconSelector?.let { finestWebView.iconSelector }
            ?: run { selectableItemBackgroundBorderless }

        showIconClose = finestWebView.showIconClose?.let { finestWebView.showIconClose } ?: run { true }

        disableIconClose =
            finestWebView.disableIconClose?.let { finestWebView.disableIconClose } ?: run { false }

        showIconBack = finestWebView.showIconBack?.let { finestWebView.showIconBack } ?: run { true }

        disableIconBack =
            finestWebView.disableIconBack?.let { finestWebView.disableIconBack } ?: run { false }

        showIconForward =
            finestWebView.showIconForward?.let { finestWebView.showIconForward } ?: run { true }

        disableIconForward =
            finestWebView.disableIconForward?.let { finestWebView.disableIconForward } ?: run { false }

        showIconMenu = finestWebView.showIconMenu?.let { finestWebView.showIconMenu } ?: run { true }

        disableIconMenu =
            finestWebView.disableIconMenu?.let { finestWebView.disableIconMenu } ?: run { false }

        showSwipeRefreshLayout =
            finestWebView.showSwipeRefreshLayout?.let { finestWebView.showSwipeRefreshLayout }
                ?: run { true }

        swipeRefreshColor = finestWebView.swipeRefreshColor?.let { finestWebView.swipeRefreshColor }
            ?: run { colorAccent }


        if (finestWebView.swipeRefreshColors != null) {
            val colors = IntArray(finestWebView.swipeRefreshColors!!.size)
            for (i in finestWebView.swipeRefreshColors!!.indices) {
                colors[i] = finestWebView.swipeRefreshColors!![i]
            }
            swipeRefreshColors = colors
        }
        showDivider = finestWebView.showDivider?.let { finestWebView.showDivider } ?: run { true }
        gradientDivider =
            finestWebView.gradientDivider?.let { finestWebView.gradientDivider } ?: run { true }

        dividerColor = finestWebView.dividerColor?.let { finestWebView.dividerColor } ?: run {
            ContextCompat.getColor(this@FinestWebViewActivity, R.color.finestBlack10)
        }

        dividerHeight = finestWebView.dividerHeight?.let { finestWebView.dividerHeight } ?: run {
            resources.getDimension(R.dimen.defaultDividerHeight)
        }

        showProgressBar =
            finestWebView.showProgressBar?.let { finestWebView.showProgressBar } ?: run { true }

        progressBarColor = finestWebView.progressBarColor?.let { finestWebView.progressBarColor }
            ?: run { colorAccent }

        progressBarHeight =
            finestWebView.progressBarHeight?.let { finestWebView.progressBarHeight } ?: run {
                resources.getDimension(R.dimen.defaultProgressBarHeight)
            }

        progressBarPosition =
            finestWebView.progressBarPosition?.let { finestWebView.progressBarPosition }
                ?: run { ProgressBarPosition.BOTTOM_OF_TOOLBAR }

        titleDefault = finestWebView.titleDefault
        updateTitleFromHtml =
            finestWebView.updateTitleFromHtml?.let { finestWebView.updateTitleFromHtml } ?: run { true }

        titleSize = finestWebView.titleSize?.let { finestWebView.titleSize } ?: run {
            resources.getDimension(R.dimen.defaultTitleSize)
        }

        titleFont =
            finestWebView.titleFont?.let { finestWebView.titleFont } ?: run { R.font.roboto_medium }

        finestWebViewTitleColor =
            finestWebView.titleColor?.let { finestWebView.titleColor } ?: run { textColorPrimary }

        showUrl = finestWebView.showUrl?.let { finestWebView.showUrl } ?: run { true }

        urlSize = finestWebView.urlSize?.let { finestWebView.urlSize }
            ?: run { resources.getDimension(R.dimen.defaultUrlSize) }

        urlFont = finestWebView.urlFont?.let { finestWebView.urlFont } ?: run { R.font.roboto_regular }

        urlColor = finestWebView.urlColor?.let { finestWebView.urlColor } ?: run { textColorSecondary }

        menuColor = finestWebView.menuColor?.let { finestWebView.menuColor } ?: run {
            ContextCompat.getColor(this@FinestWebViewActivity, R.color.finestWhite)
        }

        menuDropShadowColor =
            finestWebView.menuDropShadowColor?.let { finestWebView.menuDropShadowColor } ?: run {
                ContextCompat.getColor(this@FinestWebViewActivity, R.color.finestBlack10)
            }

        menuDropShadowSize =
            finestWebView.menuDropShadowSize?.let { finestWebView.menuDropShadowSize } ?: run {
                resources.getDimension(R.dimen.defaultMenuDropShadowSize)
            }

        menuSelector = finestWebView.menuSelector?.let { finestWebView.menuSelector }
            ?: run { selectableItemBackground }

        menuTextSize = finestWebView.menuTextSize?.let { finestWebView.menuTextSize } ?: run {
            resources.getDimension(R.dimen.defaultMenuTextSize)
        }
        menuTextFont = finestWebView.menuTextFont?.let { finestWebView.menuTextFont }
            ?: run { R.font.roboto_regular }

        menuTextColor = finestWebView.menuTextColor?.let { finestWebView.menuTextColor } ?: run {
            ContextCompat.getColor(this@FinestWebViewActivity, R.color.finestBlack)
        }

        menuTextGravity = finestWebView.menuTextGravity?.let { finestWebView.menuTextGravity }
            ?: run { Gravity.CENTER_VERTICAL or Gravity.START }

        menuTextPaddingLeft =
            finestWebView.menuTextPaddingLeft?.let { finestWebView.menuTextPaddingLeft } ?: run {
                if (rtl) resources.getDimension(
                    R.dimen.defaultMenuTextPaddingRight
                ) else resources.getDimension(R.dimen.defaultMenuTextPaddingLeft)
            }

        menuTextPaddingRight =
            finestWebView.menuTextPaddingRight?.let { finestWebView.menuTextPaddingRight } ?: run {
                if (rtl) resources.getDimension(
                    R.dimen.defaultMenuTextPaddingLeft
                ) else resources.getDimension(R.dimen.defaultMenuTextPaddingRight)
            }

        showMenuRefresh =
            finestWebView.showMenuRefresh?.let { finestWebView.showMenuRefresh } ?: run { true }

        stringResRefresh = finestWebView.stringResRefresh?.let { finestWebView.stringResRefresh }
            ?: run { R.string.refresh }

        showMenuFind = finestWebView.showMenuFind?.let { finestWebView.showMenuFind } ?: run { false }

        stringResFind =
            finestWebView.stringResFind?.let { finestWebView.stringResFind } ?: run { R.string.find }

        showMenuShareVia =
            finestWebView.showMenuShareVia?.let { finestWebView.showMenuShareVia } ?: run { true }

        stringResShareVia = finestWebView.stringResShareVia?.let { finestWebView.stringResShareVia }
            ?: run { R.string.share_via }

        showMenuCopyLink =
            finestWebView.showMenuCopyLink?.let { finestWebView.showMenuCopyLink } ?: run { true }

        stringResCopyLink = finestWebView.stringResCopyLink?.let { finestWebView.stringResCopyLink }
            ?: run { R.string.copy_link }

        showMenuOpenWith =
            finestWebView.showMenuOpenWith.let { finestWebView.showMenuOpenWith } ?: run { true }

        stringResOpenWith = finestWebView.stringResOpenWith.let { finestWebView.stringResOpenWith }
            ?: run { R.string.open_with }

        animationCloseEnter =
            finestWebView.animationCloseEnter?.let { finestWebView.animationCloseEnter }
                ?: run { R.anim.modal_activity_close_enter }

        animationCloseExit =
            finestWebView.animationCloseExit?.let { finestWebView.animationCloseExit }
                ?: run { R.anim.modal_activity_close_exit }

        backPressToClose =
            finestWebView.backPressToClose?.let { finestWebView.backPressToClose } ?: run { false }

        stringResCopiedToClipboard =
            finestWebView.stringResCopiedToClipboard?.let { finestWebView.stringResCopiedToClipboard }
                ?: run { R.string.copied_to_clipboard }

        webViewSupportZoom = finestWebView.webViewSupportZoom
        webViewMediaPlaybackRequiresUserGesture =
            finestWebView.webViewMediaPlaybackRequiresUserGesture
        webViewBuiltInZoomControls =
            if (finestWebView.webViewBuiltInZoomControls != null) finestWebView.webViewBuiltInZoomControls else false
        webViewDisplayZoomControls =
            if (finestWebView.webViewDisplayZoomControls != null) finestWebView.webViewDisplayZoomControls else false
        webViewAllowFileAccess =
            if (finestWebView.webViewAllowFileAccess != null) finestWebView.webViewAllowFileAccess else true
        webViewAllowContentAccess = finestWebView.webViewAllowContentAccess
        webViewLoadWithOverviewMode =
            if (finestWebView.webViewLoadWithOverviewMode != null) finestWebView.webViewLoadWithOverviewMode else true
        webViewSaveFormData = finestWebView.webViewSaveFormData
        webViewTextZoom = finestWebView.webViewTextZoom
        webViewUseWideViewPort = finestWebView.webViewUseWideViewPort
        webViewSupportMultipleWindows = finestWebView.webViewSupportMultipleWindows
        webViewLayoutAlgorithm = finestWebView.webViewLayoutAlgorithm
        webViewStandardFontFamily = finestWebView.webViewStandardFontFamily
        webViewFixedFontFamily = finestWebView.webViewFixedFontFamily
        webViewSansSerifFontFamily = finestWebView.webViewSansSerifFontFamily
        webViewSerifFontFamily = finestWebView.webViewSerifFontFamily
        webViewCursiveFontFamily = finestWebView.webViewCursiveFontFamily
        webViewFantasyFontFamily = finestWebView.webViewFantasyFontFamily
        webViewMinimumFontSize = finestWebView.webViewMinimumFontSize
        webViewMinimumLogicalFontSize = finestWebView.webViewMinimumLogicalFontSize
        webViewDefaultFontSize = finestWebView.webViewDefaultFontSize
        webViewDefaultFixedFontSize = finestWebView.webViewDefaultFixedFontSize
        webViewLoadsImagesAutomatically = finestWebView.webViewLoadsImagesAutomatically
        webViewBlockNetworkImage = finestWebView.webViewBlockNetworkImage
        webViewBlockNetworkLoads = finestWebView.webViewBlockNetworkLoads
        webViewJavaScriptEnabled =
            if (finestWebView.webViewJavaScriptEnabled != null) finestWebView.webViewJavaScriptEnabled else true
        webViewAllowUniversalAccessFromFileURLs =
            finestWebView.webViewAllowUniversalAccessFromFileURLs
        webViewAllowFileAccessFromFileURLs = finestWebView.webViewAllowFileAccessFromFileURLs
        webViewGeolocationDatabasePath = finestWebView.webViewGeolocationDatabasePath
        webViewAppCacheEnabled =
            if (finestWebView.webViewAppCacheEnabled != null) finestWebView.webViewAppCacheEnabled else true
        webViewAppCachePath = finestWebView.webViewAppCachePath
        webViewDatabaseEnabled = finestWebView.webViewDatabaseEnabled
        webViewDomStorageEnabled =
            if (finestWebView.webViewDomStorageEnabled != null) finestWebView.webViewDomStorageEnabled else true
        webViewGeolocationEnabled = finestWebView.webViewGeolocationEnabled
        webViewJavaScriptCanOpenWindowsAutomatically =
            finestWebView.webViewJavaScriptCanOpenWindowsAutomatically
        webViewDefaultTextEncodingName = finestWebView.webViewDefaultTextEncodingName
        webViewUserAgentString = finestWebView.webViewUserAgentString
        webViewNeedInitialFocus = finestWebView.webViewNeedInitialFocus
        webViewCacheMode = finestWebView.webViewCacheMode
        webViewMixedContentMode = finestWebView.webViewMixedContentMode
        webViewOffscreenPreRaster = finestWebView.webViewOffscreenPreRaster
        injectJavaScript = finestWebView.injectJavaScript
        mimeType = finestWebView.mimeType
        encoding = finestWebView.encoding
        data = finestWebView.data
        url = finestWebView.url
    }

    private fun bindViews() {
        coordinatorLayout = findViewById<View>(R.id.coordinatorLayout) as CoordinatorLayout
        appBar = findViewById<View>(R.id.appBar) as AppBarLayout
        appBar?.visibility = View.VISIBLE
        toolbar = findViewById<View>(R.id.toolbar) as Toolbar
        toolbarLayout = findViewById<View>(R.id.toolbarLayout) as RelativeLayout
        title = findViewById<View>(R.id.title) as TextView
        urlTv = findViewById<View>(R.id.url) as TextView
        close = findViewById<View>(R.id.close) as AppCompatImageButton
        back = findViewById<View>(R.id.back) as AppCompatImageButton
        forward = findViewById<View>(R.id.forward) as AppCompatImageButton
        more = findViewById<View>(R.id.more) as AppCompatImageButton
        close?.setOnClickListener(this)
        back?.setOnClickListener(this)
        forward?.setOnClickListener(this)
        more?.setOnClickListener(this)
        swipeRefreshLayout = findViewById<View>(R.id.swipeRefreshLayout) as SwipeRefreshLayout
        gradient = findViewById(R.id.gradient)
        divider = findViewById(R.id.divider)
        progressBar = findViewById<View>(R.id.progressBar) as ProgressBar
        menuLayout = findViewById<View>(R.id.menuLayout) as RelativeLayout
        shadowLayout = findViewById<View>(R.id.shadowLayout) as ShadowLayout
        menuBackground = findViewById<View>(R.id.menuBackground) as LinearLayout
        menuRefresh = findViewById<View>(R.id.menuRefresh) as LinearLayout
        menuRefreshTv = findViewById<View>(R.id.menuRefreshTv) as TextView
        menuFind = findViewById<View>(R.id.menuFind) as LinearLayout
        menuFindTv = findViewById<View>(R.id.menuFindTv) as TextView
        menuShareVia = findViewById<View>(R.id.menuShareVia) as LinearLayout
        menuShareViaTv = findViewById<View>(R.id.menuShareViaTv) as TextView
        menuCopyLink = findViewById<View>(R.id.menuCopyLink) as LinearLayout
        menuCopyLinkTv = findViewById<View>(R.id.menuCopyLinkTv) as TextView
        menuOpenWith = findViewById<View>(R.id.menuOpenWith) as LinearLayout
        menuOpenWithTv = findViewById<View>(R.id.menuOpenWithTv) as TextView
        webLayout = findViewById<View>(R.id.webLayout) as FrameLayout
        webView = WebView(this)
        webLayout?.addView(webView)
    }

    protected fun layoutViews() {
        setSupportActionBar(toolbar)
        run { // AppBar
            var toolbarHeight = resources.getDimension(R.dimen.toolbarHeight)
            if (!gradientDivider) {
                toolbarHeight += dividerHeight
            }
            val params = CoordinatorLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                toolbarHeight.toInt()
            )
            appBar?.layoutParams = params
            coordinatorLayout?.requestLayout()
        }
        run { // Toolbar
            val toolbarHeight = resources.getDimension(R.dimen.toolbarHeight)
            val params = LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                toolbarHeight.toInt()
            )
            toolbarLayout?.minimumHeight = toolbarHeight.toInt()
            toolbarLayout?.layoutParams = params
            coordinatorLayout?.requestLayout()
        }
        run { // TextViews
            val maxWidth = maxWidth
            title?.maxWidth = maxWidth
            urlTv?.maxWidth = maxWidth
            requestCenterLayout()
        }
        run { // Divider
            if (gradientDivider) {
                val toolbarHeight = resources.getDimension(R.dimen.toolbarHeight)
                val params = gradient?.layoutParams as CoordinatorLayout.LayoutParams
                params.setMargins(0, toolbarHeight.toInt(), 0, 0)
                gradient?.layoutParams = params
            }
        }
        run { // ProgressBar
            progressBar?.minimumHeight = progressBarHeight.toInt()
            val params = CoordinatorLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                progressBarHeight.toInt()
            )
            val toolbarHeight = resources.getDimension(R.dimen.toolbarHeight)
            when (progressBarPosition) {
                ProgressBarPosition.TOP_OF_TOOLBAR -> params.setMargins(0, 0, 0, 0)
                ProgressBarPosition.BOTTOM_OF_TOOLBAR -> params.setMargins(
                    0,
                    toolbarHeight.toInt() - progressBarHeight.toInt(),
                    0,
                    0
                )
                ProgressBarPosition.TOP_OF_WEBVIEW -> params.setMargins(
                    0,
                    toolbarHeight.toInt(),
                    0,
                    0
                )
                ProgressBarPosition.BOTTOM_OF_WEBVIEW -> params.setMargins(
                    0,
                    getHeight(this) - progressBarHeight.toInt(),
                    0,
                    0
                )
                else -> {
                    params.setMargins(0, 0, 0, 0)
                }
            }
            progressBar?.layoutParams = params
        }
        run { // WebLayout
            val toolbarHeight = resources.getDimension(R.dimen.toolbarHeight)
            val statusBarHeight = getStatusBarHeight(this)
            val screenHeight = getHeight(this)
            var webLayoutMinimumHeight = screenHeight - toolbarHeight - statusBarHeight
            if (showDivider && !gradientDivider) {
                webLayoutMinimumHeight -= dividerHeight
            }
            webLayout?.minimumHeight = webLayoutMinimumHeight.toInt()
        }
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initializeViews() {
        setSupportActionBar(toolbar)
        run { // StatusBar
            val window = window
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.statusBarColor = statusBarColor

        }
        run { // AppBar
            appBar?.addOnOffsetChangedListener(this)
        }
        run { // Toolbar
            toolbar?.setBackgroundColor(toolbarColor)
            val params = toolbar?.layoutParams as AppBarLayout.LayoutParams
            params.scrollFlags = toolbarScrollFlags
            toolbar?.layoutParams = params
        }
        run { // TextViews
            title?.text = titleDefault
            title?.setTextSize(TypedValue.COMPLEX_UNIT_PX, titleSize)
            title?.setTypeface(titleFont?.let { titleFont ->
                ResourcesCompat.getFont(
                    this@FinestWebViewActivity,
                    titleFont
                )
            })
            title?.setTextColor(titleColor)
            urlTv?.visibility = if (showUrl) View.VISIBLE else View.GONE
            urlTv?.text = url?.let { getHost(it) }
            urlTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, urlSize)
            urlTv?.setTypeface(urlFont?.let { urlFont ->
                ResourcesCompat.getFont(
                    this@FinestWebViewActivity,
                    urlFont
                )
            })
            urlTv?.setTextColor(urlColor)
            requestCenterLayout()
        }
        run { // Icons
            close?.setBackgroundResource(iconSelector)
            back?.setBackgroundResource(iconSelector)
            forward?.setBackgroundResource(iconSelector)
            more?.setBackgroundResource(iconSelector)
            close?.visibility = if (showIconClose) View.VISIBLE else View.GONE
            close?.isEnabled = !disableIconClose
            if ((showMenuRefresh || showMenuFind || showMenuShareVia || showMenuCopyLink || showMenuOpenWith) && showIconMenu) {
                more?.visibility = View.GONE
            } else {
                more?.visibility = View.GONE
            }
            more?.isEnabled = !disableIconMenu
        }
        run { // WebView
            webView?.webChromeClient = MyWebChromeClient()
            webView?.webViewClient = MyWebViewClient()
            webView?.setDownloadListener(downloadListener)
            val settings = webView!!.settings
            webViewSupportZoom?.let {
                settings.setSupportZoom(it)
            }
            settings.mediaPlaybackRequiresUserGesture.let { webViewMediaPlaybackRequiresUserGesture }

            settings.builtInZoomControls.let { webViewBuiltInZoomControls }
            if (webViewBuiltInZoomControls as Boolean) { // Remove NestedScrollView to enable BuiltInZoomControls
                (webView?.parent as ViewGroup).removeAllViews()
                swipeRefreshLayout?.addView(webView)
                swipeRefreshLayout?.removeViewAt(1)
            }

            webViewDisplayZoomControls?.let { settings.displayZoomControls = it }
            webViewAllowFileAccess?.let { settings.allowFileAccess = it }
            webViewAllowContentAccess?.let { settings.allowContentAccess = it }
            webViewLoadWithOverviewMode?.let { settings.loadWithOverviewMode = it }
            webViewSaveFormData?.let { settings.saveFormData = it }
            webViewTextZoom?.let { settings.textZoom = it }
            webViewUseWideViewPort?.let { settings.useWideViewPort = it }
            webViewSupportMultipleWindows?.let { settings.setSupportMultipleWindows(it) }
            webViewLayoutAlgorithm?.let { settings.layoutAlgorithm = it }
            webViewStandardFontFamily?.let { settings.standardFontFamily = it }
            webViewFixedFontFamily?.let { settings.fixedFontFamily = it }
            webViewSansSerifFontFamily?.let { settings.sansSerifFontFamily = it }
            webViewSerifFontFamily?.let { settings.serifFontFamily = it }
            webViewCursiveFontFamily?.let { settings.cursiveFontFamily = it }
            webViewFantasyFontFamily?.let { settings.fantasyFontFamily = it }
            webViewMinimumFontSize?.let { settings.minimumFontSize = it }
            webViewMinimumLogicalFontSize?.let { settings.minimumLogicalFontSize = it }
            webViewDefaultFontSize?.let { settings.defaultFontSize = it }
            webViewDefaultFixedFontSize?.let { settings.defaultFixedFontSize = it }
            webViewLoadsImagesAutomatically?.let { settings.loadsImagesAutomatically = it }
            webViewBlockNetworkImage?.let { settings.blockNetworkImage = it }
            webViewBlockNetworkLoads?.let { settings.blockNetworkLoads = it }
            webViewJavaScriptEnabled?.let { settings.javaScriptEnabled = it }
            webViewAllowUniversalAccessFromFileURLs?.let { settings.allowUniversalAccessFromFileURLs = it }
            webViewAllowFileAccessFromFileURLs?.let { settings.allowFileAccessFromFileURLs = it }
            webViewGeolocationDatabasePath?.let { settings.setGeolocationDatabasePath(it) }

            /*if (webViewAppCacheEnabled != null) {
              settings.setAppCacheEnabled(webViewAppCacheEnabled!!)
            }
            if (webViewAppCachePath != null) {
              settings.setAppCachePath(webViewAppCachePath)
            }*/
            webViewDatabaseEnabled?.let { settings.databaseEnabled = it }
            webViewDomStorageEnabled?.let { settings.domStorageEnabled = it }
            webViewGeolocationEnabled?.let { settings.setGeolocationEnabled(it) }
            webViewJavaScriptCanOpenWindowsAutomatically?.let {
                settings.javaScriptCanOpenWindowsAutomatically = it
            }
            webViewDefaultTextEncodingName?.let { settings.defaultTextEncodingName = it }
            webViewUserAgentString?.let { settings.setUserAgentString(it) }

            webViewNeedInitialFocus?.let { settings.setNeedInitialFocus(it) }
            webViewCacheMode?.let { settings.cacheMode = it }

            data?.let {
                webView?.loadData(it, mimeType, encoding)
            } ?: run {
                url?.let { url ->
                    webView?.loadUrl(url)
                }
            }
        }
        run { // SwipeRefreshLayout
            swipeRefreshLayout?.isEnabled = showSwipeRefreshLayout
            if (showSwipeRefreshLayout) {
                swipeRefreshLayout?.post { swipeRefreshLayout?.isRefreshing = true }
            }
            if (swipeRefreshColors == null) {
                swipeRefreshLayout?.setColorSchemeColors(swipeRefreshColor)
            } else {
                swipeRefreshLayout?.setColorSchemeColors(swipeRefreshColor)
            }
            swipeRefreshLayout?.setOnRefreshListener { webView?.reload() }
        }
        run { // Divider
            gradient?.visibility = if (showDivider && gradientDivider) View.VISIBLE else View.GONE
            divider?.visibility = if (showDivider && !gradientDivider) View.VISIBLE else View.GONE
            if (gradientDivider) {
                val dividerWidth = getWidth(this)
                val bitmap = getGradientBitmap(dividerWidth, dividerHeight.toInt(), dividerColor)
                val drawable = BitmapDrawable(resources, bitmap)
                gradient?.background = drawable
                val params = gradient?.layoutParams as CoordinatorLayout.LayoutParams
                params.height = dividerHeight.toInt()
                gradient?.layoutParams = params
            } else {
                divider!!.setBackgroundColor(dividerColor)
                val params = divider!!.layoutParams as LinearLayout.LayoutParams
                params.height = dividerHeight.toInt()
                divider!!.layoutParams = params
            }
        }
        run { // ProgressBar
            progressBar?.visibility = if (showProgressBar) View.VISIBLE else View.GONE
            progressBar?.progressDrawable?.setColorFilter(progressBarColor, PorterDuff.Mode.SRC_IN)
            progressBar?.minimumHeight = progressBarHeight.toInt()
            val params = CoordinatorLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                progressBarHeight.toInt()
            )
            val toolbarHeight = resources.getDimension(R.dimen.toolbarHeight)
            when (progressBarPosition) {
                ProgressBarPosition.TOP_OF_TOOLBAR -> params.setMargins(0, 0, 0, 0)
                ProgressBarPosition.BOTTOM_OF_TOOLBAR -> params.setMargins(0, toolbarHeight.toInt() - progressBarHeight.toInt(), 0, 0)
                ProgressBarPosition.TOP_OF_WEBVIEW -> params.setMargins(0, toolbarHeight.toInt(), 0, 0)
                ProgressBarPosition.BOTTOM_OF_WEBVIEW -> params.setMargins(
                    0, getHeight(this) - progressBarHeight.toInt(), 0, 0
                )
                else -> {
                    params.setMargins(0, 0, 0, 0)
                }
            }
            progressBar?.layoutParams = params
        }
        run { // Menu
            val drawable = GradientDrawable()
            drawable.cornerRadius = resources.getDimension(R.dimen.defaultMenuCornerRadius)
            drawable.setColor(menuColor)
            menuBackground?.background = drawable
            shadowLayout?.setShadowColor(menuDropShadowColor)
            shadowLayout?.setShadowSize(menuDropShadowSize)
            val params = RelativeLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            val margin =
                (resources.getDimension(R.dimen.defaultMenuLayoutMargin) - menuDropShadowSize).toInt()
            params.setMargins(0, margin, margin, 0)
            params.addRule(RelativeLayout.ALIGN_PARENT_TOP)
            params.addRule(if (rtl) RelativeLayout.ALIGN_PARENT_LEFT else RelativeLayout.ALIGN_PARENT_RIGHT)
            shadowLayout?.layoutParams = params
            menuRefresh?.visibility = if (showMenuRefresh) View.VISIBLE else View.GONE
            menuRefresh?.setBackgroundResource(menuSelector)
            menuRefresh?.gravity = menuTextGravity
            menuRefreshTv?.setText(stringResRefresh)
            menuRefreshTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, menuTextSize)
            menuRefreshTv?.setTypeface(menuTextFont?.let { menuTextFont ->
                ResourcesCompat.getFont(this@FinestWebViewActivity, menuTextFont)
            })
            menuRefreshTv?.setTextColor(menuTextColor)
            menuRefreshTv?.setPadding(menuTextPaddingLeft.toInt(), 0, menuTextPaddingRight.toInt(), 0)
            menuFind?.visibility = if (showMenuFind) View.VISIBLE else View.GONE
            menuFind?.setBackgroundResource(menuSelector)
            menuFind?.gravity = menuTextGravity
            menuFindTv?.setText(stringResFind)
            menuFindTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, menuTextSize)
            menuFindTv?.setTypeface(menuTextFont?.let { menuTextFont ->
                ResourcesCompat.getFont(this@FinestWebViewActivity, menuTextFont)
            })
            menuFindTv?.setTextColor(menuTextColor)
            menuFindTv?.setPadding(menuTextPaddingLeft.toInt(), 0, menuTextPaddingRight.toInt(), 0)
            menuShareVia?.visibility = if (showMenuShareVia) View.VISIBLE else View.GONE
            menuShareVia?.setBackgroundResource(menuSelector)
            menuShareVia?.gravity = menuTextGravity
            menuShareViaTv?.setText(stringResShareVia)
            menuShareViaTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, menuTextSize)
            menuShareViaTv?.setTypeface(menuTextFont?.let { menuTextFont ->
                ResourcesCompat.getFont(
                    this@FinestWebViewActivity,
                    menuTextFont
                )
            })
            menuShareViaTv?.setTextColor(menuTextColor)
            menuShareViaTv?.setPadding(
                menuTextPaddingLeft.toInt(), 0, menuTextPaddingRight.toInt(), 0
            )
            menuCopyLink?.visibility = if (showMenuCopyLink) View.VISIBLE else View.GONE
            menuCopyLink?.setBackgroundResource(menuSelector)
            menuCopyLink?.gravity = menuTextGravity
            menuCopyLinkTv?.setText(stringResCopyLink)
            menuCopyLinkTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, menuTextSize)
            menuCopyLinkTv?.setTypeface(menuTextFont?.let { menuTextFont ->
                ResourcesCompat.getFont(this@FinestWebViewActivity, menuTextFont)
            })
            menuCopyLinkTv?.setTextColor(menuTextColor)
            menuCopyLinkTv?.setPadding(
                menuTextPaddingLeft.toInt(), 0, menuTextPaddingRight.toInt(), 0
            )
            menuOpenWith?.visibility = if (showMenuOpenWith) View.VISIBLE else View.GONE
            menuOpenWith?.setBackgroundResource(menuSelector)
            menuOpenWith?.gravity = menuTextGravity
            menuOpenWithTv?.setText(stringResOpenWith)
            menuOpenWithTv?.setTextSize(TypedValue.COMPLEX_UNIT_PX, menuTextSize)
            menuOpenWithTv?.setTypeface(menuTextFont?.let { menuTextFont ->
                ResourcesCompat.getFont(this@FinestWebViewActivity, menuTextFont)
            })
            menuOpenWithTv?.setTextColor(menuTextColor)
            menuOpenWithTv?.setPadding(menuTextPaddingLeft.toInt(), 0, menuTextPaddingRight.toInt(), 0)
        }
    }

    private val maxWidth: Int
        get() = if (forward?.visibility == View.VISIBLE) {
            getWidth(this) - dpToPx(this, 100)
        } else {
            getWidth(this) - dpToPx(this, 52)
        }

    open fun updateIcon(icon: ImageButton?, @DrawableRes drawableRes: Int) {
        val states = StateListDrawable()
        run {
            val bitmap = getColoredBitmap(this, drawableRes, iconPressedColor)
            val drawable = BitmapDrawable(resources, bitmap)
            states.addState(intArrayOf(android.R.attr.state_pressed), drawable)
        }
        run {
            val bitmap = getColoredBitmap(this, drawableRes, iconDisabledColor)
            val drawable = BitmapDrawable(resources, bitmap)
            states.addState(intArrayOf(-android.R.attr.state_enabled), drawable)
        }
        run {
            val bitmap = getColoredBitmap(this, drawableRes, iconDefaultColor)
            val drawable = BitmapDrawable(resources, bitmap)
            states.addState(intArrayOf(), drawable)
        }
        icon?.setImageDrawable(states)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initializeOptions()
        setContentView(R.layout.finest_web_view)
        bindViews()
        layoutViews()
        initializeViews()
    }

    override fun onBackPressed() {
        if (menuLayout?.visibility == View.VISIBLE) {
            hideMenu()
        } else if (backPressToClose || !webView?.canGoBack()!!) {
            exitActivity()
        } else {
            webView?.goBack()
        }
    }

    override fun onClick(v: View) {
        val viewId = v.id
        if (viewId == R.id.close) {
            if (rtl) {
                showMenu()
            } else {
                exitActivity()
            }
        } else if (viewId == R.id.back) {
            if (rtl) {
                webView?.goForward()
            } else {
                webView?.goBack()
            }
        } else if (viewId == R.id.forward) {
            if (rtl) {
                webView?.goBack()
            } else {
                webView?.goForward()
            }
        } else if (viewId == R.id.more) {
            if (rtl) {
                exitActivity()
            } else {
                showMenu()
            }
        } else if (viewId == R.id.menuLayout) {
            hideMenu()
        } else if (viewId == R.id.menuRefresh) {
            webView?.reload()
            hideMenu()
        } else if (viewId == R.id.menuFind) {
            webView?.showFindDialog("", true)
            hideMenu()
        } else if (viewId == R.id.menuShareVia) {
            val sendIntent = Intent()
            sendIntent.action = Intent.ACTION_SEND
            sendIntent.putExtra(Intent.EXTRA_TEXT, webView?.url)
            sendIntent.type = "text/plain"
            startActivity(Intent.createChooser(sendIntent, resources.getString(stringResShareVia)))
            hideMenu()
        } else if (viewId == R.id.menuCopyLink) {
            val clipboardManager = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
            val clip = ClipData.newPlainText("ClipboardManagerUtil", webView?.url)
            clipboardManager.setPrimaryClip(clip)
            val snackbar = Snackbar.make(
                coordinatorLayout!!,
                getString(stringResCopiedToClipboard),
                Snackbar.LENGTH_LONG
            )
            val snackbarView = snackbar.view
            snackbarView.setBackgroundColor(toolbarColor)
            if (snackbarView is ViewGroup) {
                updateChildTextView(snackbarView)
            }
            snackbar.show()
            hideMenu()
        } else if (viewId == R.id.menuOpenWith) {
            val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(webView?.url))
            startActivity(browserIntent)
            hideMenu()
        }
    }

    protected fun updateChildTextView(viewGroup: ViewGroup?) {
        if (viewGroup == null || viewGroup.childCount == 0) {
            return
        }
        for (i in 0 until viewGroup.childCount) {
            val view = viewGroup.getChildAt(i)
            if (view is TextView) {
                view.setTextColor(titleColor)
                view.typeface = titleFont?.let { titleFont ->
                    ResourcesCompat.getFont(
                        this@FinestWebViewActivity,
                        titleFont
                    )
                }
                view.setLineSpacing(0f, 1.1f)
                view.includeFontPadding = false
            }
            if (view is ViewGroup) {
                updateChildTextView(view)
            }
        }
    }

    private fun showMenu() {
        menuLayout?.visibility = View.VISIBLE
        val popupAnim = AnimationUtils.loadAnimation(this, R.anim.popup_flyout_show)
        shadowLayout?.startAnimation(popupAnim)
    }

    private fun hideMenu() {
        val popupAnim = AnimationUtils.loadAnimation(this, R.anim.popup_flyout_hide)
        shadowLayout?.startAnimation(popupAnim)
        popupAnim.setAnimationListener(object : AnimationListener {
            override fun onAnimationStart(animation: Animation) {}
            override fun onAnimationEnd(animation: Animation) {
                menuLayout?.visibility = View.GONE
            }

            override fun onAnimationRepeat(animation: Animation) {}
        })
    }

    private fun exitActivity() {
        super.onBackPressed()
        overridePendingTransition(animationCloseEnter, animationCloseExit)
    }

    override fun onOffsetChanged(appBarLayout: AppBarLayout, verticalOffset: Int) {
        if (toolbarScrollFlags == 0) {
            return
        }
        gradient?.translationY = verticalOffset.toFloat()
        gradient?.alpha =
            1 - Math.abs(verticalOffset).toFloat() / appBarLayout.totalScrollRange.toFloat()
        when (progressBarPosition) {
            ProgressBarPosition.BOTTOM_OF_TOOLBAR -> progressBar?.translationY =
                verticalOffset.toFloat()
                    .coerceAtLeast(progressBarHeight - appBarLayout.totalScrollRange)
            ProgressBarPosition.TOP_OF_WEBVIEW -> progressBar?.translationY =
                verticalOffset.toFloat()
            ProgressBarPosition.TOP_OF_TOOLBAR, ProgressBarPosition.BOTTOM_OF_WEBVIEW -> {
            }
            else -> {
            }
        }
        menuLayout?.translationY = verticalOffset.toFloat()
            .coerceAtLeast(-resources.getDimension(R.dimen.defaultMenuLayoutMargin))
    }

    protected fun requestCenterLayout() {
        val maxWidth: Int = if (webView!!.canGoBack() || webView!!.canGoForward()) {
            getWidth(this) - dpToPx(this, 48) * 4
        } else {
            getWidth(this) - dpToPx(this, 48) * 2
        }
        title?.maxWidth = maxWidth
        urlTv?.maxWidth = maxWidth
        title?.requestLayout()
        urlTv?.requestLayout()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            layoutViews()
        } else if (newConfig.orientation == Configuration.ORIENTATION_PORTRAIT) {
            layoutViews()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        unregister(this@FinestWebViewActivity, key)
        if (webView == null) {
            return
        }
        webView?.onPause()
    }

    inner class MyWebChromeClient : WebChromeClient() {
        override fun onProgressChanged(view: WebView, progress: Int) {
            var progress = progress
            onProgressChanged(this@FinestWebViewActivity, key, progress)
            if (showSwipeRefreshLayout) {
                if (progress == 100) {
                    swipeRefreshLayout?.post { swipeRefreshLayout?.isRefreshing = false }
                }
                if (!swipeRefreshLayout?.isRefreshing!! && progress != 100) {
                    swipeRefreshLayout?.post { swipeRefreshLayout?.isRefreshing = true }
                }
            }
            if (progress == 100) {
                progress = 0
            }
            progressBar?.progress = progress
        }

        override fun onReceivedTitle(view: WebView, title: String) {
            onReceivedTitle(this@FinestWebViewActivity, key, title)
        }

        override fun onReceivedTouchIconUrl(view: WebView, url: String, precomposed: Boolean) {
            onReceivedTouchIconUrl(this@FinestWebViewActivity, key, url, precomposed)
        }
    }

    inner class MyWebViewClient : WebViewClient() {
        override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
            onPageStarted(this@FinestWebViewActivity, key, url)
        }

        override fun onPageFinished(view: WebView, url: String) {
            onPageFinished(this@FinestWebViewActivity, key, url)
            if (updateTitleFromHtml) {
                title?.text = view.title
            }
            urlTv?.text = getHost(url)
            requestCenterLayout()
            if (view.canGoBack() || view.canGoForward()) {
                if(view.canGoBack()) {
                    back?.visibility = if (showIconBack) View.VISIBLE else View.GONE
                }else{
                    back?.visibility = View.GONE
                }
                if(view.canGoForward()) {
                    forward?.visibility = if (showIconForward) View.VISIBLE else View.GONE
                }else{
                    forward?.visibility = View.GONE
                }
                back?.isEnabled =
                    !disableIconBack && if (rtl) view.canGoForward() else view.canGoBack()
                forward?.isEnabled =
                    !disableIconForward && if (rtl) view.canGoBack() else view.canGoForward()
            } else {
                back?.visibility = View.GONE
                forward?.visibility = View.GONE
            }
            injectJavaScript?.let {
                webView?.evaluateJavascript(it, null)
            }
        }

        @Deprecated("Deprecated in Java")
        override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
            return if (url.endsWith(".mp4")) {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.setDataAndType(Uri.parse(url), "video/*")
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                view.context.startActivity(intent) // If we return true, onPageStarted, onPageFinished won't be called.
                true
            } else if (url.startsWith("tel:") || url.startsWith("sms:") || url.startsWith("smsto:") || url.startsWith(
                    "mms:"
                ) || url.startsWith("mmsto:")
            ) {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                view.context.startActivity(intent)
                true // If we return true, onPageStarted, onPageFinished won't be called.
            } else if (url.startsWith("mailto:")) {
                val mt = MailTo.parse(url)
                val emailIntent = Intent(Intent.ACTION_SEND)
                emailIntent.type = "text/html"
                emailIntent.putExtra(Intent.EXTRA_EMAIL, arrayOf(mt.to))
                emailIntent.putExtra(Intent.EXTRA_SUBJECT, mt.subject)
                emailIntent.putExtra(Intent.EXTRA_CC, mt.cc)
                emailIntent.putExtra(Intent.EXTRA_TEXT, mt.body)
                startActivity(emailIntent)
                true
            } else {
                super.shouldOverrideUrlLoading(view, url)
            }
        }

        override fun onLoadResource(view: WebView, url: String) {
            onLoadResource(this@FinestWebViewActivity, key, url)
        }

        override fun onPageCommitVisible(view: WebView, url: String) {
            onPageCommitVisible(this@FinestWebViewActivity, key, url)
        }
    }
}