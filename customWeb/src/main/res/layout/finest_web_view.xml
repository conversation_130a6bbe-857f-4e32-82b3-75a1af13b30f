<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/coordinatorLayout"
  android:layout_width="match_parent"
  android:layout_height="match_parent"
  android:background="@color/finestWhite">

  <ProgressBar
    android:id="@+id/progressBar"
    style="?android:attr/progressBarStyleHorizontal"
    android:layout_width="match_parent"
    android:layout_height="@dimen/defaultProgressBarHeight"
    android:layout_marginTop="@dimen/toolbarHeight"
    android:indeterminate="false"
    android:progressDrawable="@drawable/progress_drawable"
      android:visibility="visible"/>

  <View
    android:id="@+id/gradient"
    android:layout_width="match_parent"
    android:layout_height="@dimen/defaultDividerHeight"
    android:layout_marginTop="@dimen/toolbarHeight"
      android:visibility="visible"
    android:background="@color/finestBlack10"/>

  <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
    android:id="@+id/swipeRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="SpeakableTextPresentCheck">

      <FrameLayout
          android:id="@+id/webLayout"
          android:layout_width="wrap_content"
          android:layout_height="wrap_content" />

    </androidx.core.widget.NestedScrollView>
  </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

  <com.google.android.material.appbar.AppBarLayout
    android:id="@+id/appBar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:elevation="0dp"
      android:visibility="visible">

    <androidx.appcompat.widget.Toolbar
      android:id="@+id/toolbar"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:padding="0dp"
      android:gravity="center_vertical"
      app:contentInsetEnd="0dp"
      app:contentInsetLeft="0dp"
      app:contentInsetRight="0dp"
      app:contentInsetStart="0dp"
      app:layout_scrollFlags="scroll|enterAlways">

      <include layout="@layout/toolbar_content"/>
    </androidx.appcompat.widget.Toolbar>
  </com.google.android.material.appbar.AppBarLayout>

  <include layout="@layout/menus"
      android:visibility="gone"/>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
