<resources>

  <declare-styleable name="ShadowLayout">
    <attr format="dimension" name="slCornerRadius"/>
    <attr format="dimension" name="slShadowSize"/>
    <attr format="color" name="slShadowColor"/>
    <attr format="dimension" name="slDx"/>
    <attr format="dimension" name="slDy"/>
  </declare-styleable>

  <style name="FinestWebViewTheme.Fullscreen" parent="FinestWebViewTheme">
    <item name="android:windowContentOverlay">@null</item>
    <item name="android:windowFullscreen">true</item>
  </style>

  <style name="FinestWebViewTheme.Light" parent="Theme.AppCompat.DayNight.NoActionBar">
    <item name="colorPrimary">@color/finestWhite</item>
    <item name="colorPrimaryDark">@color/finestGray</item>
    <item name="colorAccent">@color/finestBlack</item>
    <item name="android:textColorPrimary">@color/finestBlack</item>
    <item name="android:textColorSecondary">@color/finestSilver</item>
    <item name="windowActionModeOverlay">true</item>
  </style>

  <style name="FinestWebViewTheme.Light.Fullscreen" parent="FinestWebViewTheme.Light">
    <item name="android:windowContentOverlay">@null</item>
    <item name="android:windowFullscreen">true</item>
  </style>

  <style name="FinestWebViewTheme" parent="Theme.AppCompat.NoActionBar">
    <item name="colorPrimary">@color/finestWhite</item>
    <item name="colorPrimaryDark">@color/finestGray</item>
    <item name="colorAccent">@color/finestBlack</item>
    <item name="android:textColorPrimary">@color/finestBlack</item>
    <item name="android:textColorSecondary">@color/finestSilver</item>
    <item name="windowActionModeOverlay">true</item>
  </style>

</resources>
