apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'

android {
    compileSdkVersion 35
    namespace 'com.thefinestartist.finestwebview'
    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 35
        versionCode 1
        versionName "1.0.0"
        vectorDrawables.useSupportLibrary = true
    }

    lintOptions {
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation "androidx.coordinatorlayout:coordinatorlayout:1.3.0"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.1.0"
    implementation "com.google.android.material:material:1.12.0"
}

repositories {
    mavenCentral()
}

//$ ./gradlew clean build bintrayUpload -PbintrayUser=thefinestartist -PbintrayKey=BINTRAY_KEY -PdryRun=false
