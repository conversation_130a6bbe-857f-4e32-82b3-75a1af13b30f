<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <uses-feature android:name="android.hardware.location.gps" />

    <uses-feature
        android:name="android.hardware.bluetooth_le"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.camera.flash"
        android:required="false" />

    <!--TODO Disable   android:networkSecurityConfig="@xml/network_security_config"  in release build. enable only if you want to intercept response -->
    <application
        android:name=".BaseApplication"
        android:allowBackup="false"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:preserveLegacyExternalStorage="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        tools:replace="android:allowBackup"
        tools:targetApi="r">

        <!--Added singleTask to prevent multiple launch instance. We also added this to solve login and database issue when login with link and pressed back button and again try to launch the app.-->
        <activity
            android:name=".SplashActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <!-- https://www.sitefotos.com/mobile?accesscode=compnayId&email=<EMAIL> -->
                <data android:host="www.sitefotos.com" />
                <data android:pathPrefix="/mobile" />
                <data android:scheme="https" />


                <data android:host="sitefotos.com" />
                <data android:pathPrefix="/mobile" />
                <data android:scheme="https" />


                <data android:host="***********" />
                <data android:pathPrefix="/mobile" />
                <data android:scheme="http" />
            </intent-filter>

        </activity>
        <activity
            android:name=".login.LoginActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensor" />
        <activity
            android:name=".language.AppLanguageActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".main.MainActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="sensor"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.SENDTO" />
                <data android:scheme="mailto" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name=".map.PropertyMapActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensor" />
        <activity
            android:name=".camera.CameraActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensor"
            android:theme="@style/AppBaseThemeNoActionBarFullScreen"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.SENDTO" />

                <data android:scheme="mailto" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>

        <service
            android:name=".service.FileUploaderService"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name=".service.LocationService"
            android:enabled="true"
            android:exported="true" /> <!-- Removed this from app version 2.2.9 -->
        <activity
            android:name=".map.NewPropertyActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"

            android:screenOrientation="sensor" />
        <activity
            android:name=".uploadImage.UploadImagesActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".webview.WebViewActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensor" />

        <activity
            android:name=".webview.URLWebViewActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="sensor" />
        <activity
            android:name=".form.FormDetailActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".form.TMFormDetailActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />

        <activity
            android:name=".form.SubFormDetailActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".form.SignatureActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".form.SketchActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"

            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".webview.AppWebViewActivity"
            android:alwaysRetainTaskState="true"
            android:exported="false"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name="com.thefinestartist.finestwebview.FinestWebViewActivity"
            android:alwaysRetainTaskState="true"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.NoActionBar"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".measurement.AppMeasurementActivity"
            android:alwaysRetainTaskState="true"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:screenOrientation="portrait" />
        <!--
 Service to asynchronously fetch a location address using a Geocoder. Setting the
        android:exported attribute to "false" stops other apps from starting this
        service, even when using an explicit intent.
        -->
        <service
            android:name=".service.FetchAddressIntentService"
            android:exported="false" />

        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="${googleMapApiKey}" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="firebase_crashlytics_collection_enabled"
            android:value="${enableCrashReporting}" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/external_files" />

        </provider>

        <activity
            android:name=".gallery.GalleryActivity"
            android:alwaysRetainTaskState="true"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppBaseThemeNoActionBar" />
        <activity
            android:name=".PermissionOverlayActivity"
            android:alwaysRetainTaskState="true"
            android:exported="false"
            android:screenOrientation="sensor" />

        <receiver
            android:name=".notification.LocalNotificationReceiver"
            android:enabled="true"
            android:exported="false" />

        <service
            android:name=".notification.AppFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Set custom default icon. This is used when no icon is set for incoming notification messages.
     See README(https://goo.gl/l4GJaQ) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@mipmap/ic_launcher" />
        <!-- Set color used with incoming notification messages. This is used when no color is set for the incoming
             notification message. See README(https://goo.gl/6BKBk7) for more. -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="@string/default_notification_channel_id" />

    </application>

</manifest>