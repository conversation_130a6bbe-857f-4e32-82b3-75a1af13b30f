package com.sitefotos.language;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemLanguageBinding;
import com.sitefotos.models.LangData;

import java.util.List;


public class LangAdapter extends RecyclerView.Adapter<LangAdapter.ViewHolder> {

    private List<LangData> lstLang;
    private final ViewClicked viewClicked;
    private  ItemLanguageBinding binding;
    public interface ViewClicked {
        void onItemClicked(int position);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvLang;
        AppCompatCheckBox cbLang;
        public ViewHolder(ItemLanguageBinding binding) {
            super(binding.getRoot());
            tvTitle = binding.tvTitle;
            tvLang = binding.tvLang;
            cbLang = binding.cbLang;
        }
    }

    public LangAdapter(List<LangData> lstLang, ViewClicked viewClicked) {
        this.lstLang = lstLang;
        this.viewClicked = viewClicked;

    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemLanguageBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<LangData> lstLang) {
        this.lstLang = lstLang;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        LangData langData = lstLang.get(position);
        holder.tvTitle.setText(langData.getTitle());
        holder.tvLang.setText(langData.getLang());
        holder.cbLang.setChecked(langData.getSelected());

        holder.cbLang.setOnCheckedChangeListener(null);
        holder.itemView.setOnClickListener(view -> {
            if (!langData.getSelected()) {
                viewClicked.onItemClicked(position);
            }
        });

        holder.cbLang.setClickable(false);

    }

    @Override
    public int getItemCount() {
        return lstLang.size();
    }

}
