package com.sitefotos.language;

import static com.sitefotos.Constants.PARAM_STAG;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.DividerItemDecoration;

import com.sitefotos.BaseActivity;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.SplashActivity;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityLanguageBinding;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.models.LangData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class AppLanguageActivity extends BaseActivity implements View.OnClickListener {

    List<LangData> lstLang = new ArrayList<>();
    private LangAdapter langAdapter;
    private ActivityLanguageBinding binding;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        setOnClickListener();
        initView();
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(newBase);
    }

    private void initBinding() {
        binding = ActivityLanguageBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void initView() {
        lstLang = StaticUtils.getLanguageData(this);
        setActionBarVisibility(false);
        setActionBarData();
        setAdapter();
    }

    private void setOnClickListener() {
        binding.tlOther.imgBtnBack.setOnClickListener(this);
    }

    private void setAdapter() {
        langAdapter = new LangAdapter(lstLang, position -> {
            PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name),
                    getString(R.string.change_lang_conform_message), getString(R.string.yes), getString(R.string.no),
                    true, (dialog, which) -> setSelectedLangAndRestartApp(position), (dialog, which) -> {
                        lstLang.clear();
                        lstLang = StaticUtils.getLanguageData(AppLanguageActivity.this);
                        langAdapter.updateList(lstLang);
                    });
        });
        binding.rvLanguage.setHasFixedSize(true);
        binding.rvLanguage.setAdapter(langAdapter);
        binding.rvLanguage.setNestedScrollingEnabled(false);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.rvLanguage.getContext(), 1);
        binding.rvLanguage.addItemDecoration(dividerItemDecoration);

    }

    private void setSelectedLangAndRestartApp(int position) {

        TblForms tblForms = new TblForms(this);
        tblForms.removeNotSubmittedDataFromTable();

        TblTMForms tblTMForms = new TblTMForms(this);
        tblTMForms.removeNotSubmittedDataFromTable();

        TblSites tblSites = new TblSites(this);
        tblSites.deleteDataFromTable();
        //Reset md5 of bellow key because we delete its data from DB
        md5Keys.setFormKey("");
        md5Keys.setSiteKey("");
        md5Keys.setSiteListConfigKey("");
        AppPrefShared.putValue(PARAM_STAG, "");
        StaticUtils.setMd5Data(md5Keys);
        LangData selectedData = lstLang.get(position);
        AppPrefShared.putValue(Constants.USER_CURRENT_LANGUAGE, selectedData.getLangCode());
        setLocale(this);
        restartApp();
    }

    private void setActionBarData() {
        binding.tlOther.tvTitle.setText(getString(R.string.language));
        setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(this, binding.tlOther.llTitle);

    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
    }


    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            onBackPressed();
        }
    }

    private void restartApp() {
        Intent intent = new Intent(this, SplashActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finishAffinity();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

}
