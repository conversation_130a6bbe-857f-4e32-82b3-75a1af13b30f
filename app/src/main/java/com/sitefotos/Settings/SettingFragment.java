package com.sitefotos.Settings;

import static com.sitefotos.Constants.SITE_DETAIL_REQUEST_CODE;
import static com.sitefotos.util.StaticUtils.getParamsForStandAlonBreadCrumbRequest;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.databinding.FragmentSettingBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.language.AppLanguageActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.measurement.AppMeasurementActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.models.SimpleResponse;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.NotificationUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.webview.WebViewActivity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;

import retrofit2.Call;
import retrofit2.Response;

public class SettingFragment extends BaseFragment implements View.OnClickListener {
    private Context context;


    private FragmentSettingBinding binding;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentSettingBinding.inflate(inflater, container, false);
        // imgBtnBack.setVisibility(View.GONE);
        binding.tlOther.ivRight.setVisibility(View.GONE);
        binding.tlOther.tvTitle.setText(R.string.settings);
        init();
        return binding.getRoot();
    }

    public void init() {
        context = getContext();
        setOnClickListener();
        setupActionBar();
        binding.tvMailAddress.setText(AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, "LOGGED_IN_USER_EMAIL_ADDRESS"));
        binding.tvUserName.setText(AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_NAME, "LOGGED_IN_USER_COMPANY_NAME"));

        binding.swSaveToDevice.setChecked(AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_SAVE_COPY_TO_DEVICE, false));
        binding.swTargetLocator.setChecked(AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_TARGET_LOCATOR, false));
        binding.swUsingCellularData.setChecked(AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_USING_CELLULAR_DATA, true));
        //   swDeveloper.setChecked(AppPrefShared.getBoolean(Constants.IS_DEVELOPER_OPTION_ON, false));

        try {
            updateSaveOriginalImageData();
            binding.swTargetLocator.setOnCheckedChangeListener((compoundButton, state) ->
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_TARGET_LOCATOR, state));

            binding.swSaveToDevice.setOnCheckedChangeListener((compoundButton, state) ->
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_SAVE_COPY_TO_DEVICE, state));

            binding.swUsingCellularData.setOnCheckedChangeListener((compoundButton, state) ->
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_UPLOAD_USING_CELLULAR_DATA, state));

            binding.swUploadOriginalSize.setOnCheckedChangeListener((compoundButton, state) ->
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, state));

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        binding.txtAppVersion.setText(getString(R.string.app_version).concat(" ").concat(BuildConfig.VERSION_NAME));


    }

    private void updateSaveOriginalImageData() {
        try {
            // Get hiRes variable value from app settings
            int hiRes = Integer.parseInt(AppPrefShared.getString(Constants.LOGGED_IN_USER_APP_SETTINGS_HI_RES, "0"));
            if (hiRes == 1) { // hiRes = 1 ==> turn hiRes setting inside app ON and disabled
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, true);
                binding.swUploadOriginalSize.setChecked(true);
                binding.swUploadOriginalSize.setClickable(false);
            } else if (hiRes == 2) { // hiRes = 2 ==> turn hiRes setting inside app OFF and disabled
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false);
                binding.swUploadOriginalSize.setChecked(false);
                binding.swUploadOriginalSize.setClickable(false);
            } else { // hiRes = 0 or any other value
                binding.swUploadOriginalSize.setChecked(AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false));
                binding.swUploadOriginalSize.setClickable(true);
            }
        } catch (NumberFormatException e) {
            FirebaseEventUtils.logException(e);
        }
    }



    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            onSuccessResponse(event.response);
        }
        if (event.throwable != null) {
            onFailureResponse(event.throwable);
        }
        if (event.isNoInternetConnection) {
            onNoInternetConnection();
        }
    }


    private void setupActionBar() {
        binding.tlOther.imgBtnBack.setVisibility(View.INVISIBLE);
        binding.tlOther.imgBtnBack.setEnabled(false);
        binding.tlOther.tvTitle.setText(R.string.settings);
        binding.tlOther.ivRight.setVisibility(View.GONE);
        if (getActivity() != null)
            ((BaseActivity) getActivity()).setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
    }

    public void showDialogLogout(String strTitle, String strMessage) {
        PopUtils.showCustomTwoButtonAlertDialog(context, strTitle, strMessage, getString(R.string.ok)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    dialog.dismiss();
                    if (getActivity() != null) {
                        requestForLogoutAndClearPref();
                    }

                }, (dialog, which) -> dialog.dismiss());
    }

    private void requestForLogoutAndClearPref() {
        if (getActivity() != null) {
            ((MainActivity) getActivity()).showProgress(context.getResources().getString(R.string.logging_Out));
            requestForLogout();
        }

    }

    private void requestForLogout() {
        HashMap<String, Object> params = getParamsForStandAlonBreadCrumbRequest(18);
        ApiInterface apiService = RetrofitProvider.createService(ApiInterface.class);
        Call<SimpleResponse> call = apiService.requestToPostBreadCrumbsData(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(@NonNull Call<SimpleResponse> call, @NonNull retrofit2.Response<SimpleResponse> response) {
                new ClearPrefAndDB().execute();
            }

            @Override
            public void onFailure(@NonNull Call<SimpleResponse> call, @NonNull Throwable throwable) {
                clearUserPreferences();
            }
        });
    }

    private Void clearUserPreferences() {
        if (getActivity() != null) {
            NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(context);
            BaseApplication.getInstance().clearSharedPreferencesLogOut();
            ((BaseActivity) getActivity()).logoutUserAndNavigateUserToLoginScreen();
        }
        return null;
    }


    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getActivity() == null)
            return;
        ((BaseActivity) getActivity()).visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView((BaseActivity) getActivity(),binding.tlOther.llTitle);
    }

    private void setOnClickListener() {
        binding.tlOther.ivSecondRight.setOnClickListener(this);
        binding.txtTermsOfService.setOnClickListener(this);
        binding.txtPrivacyPolicy.setOnClickListener(this);
        binding.txtLogOut.setOnClickListener(this);
        binding.ivLanguage.setOnClickListener(this);
        binding.ivMeasurementUnit.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.txtTermsOfService) {
            Intent webViewIntent = new Intent(context, WebViewActivity.class);
            webViewIntent.putExtra("link", Constants.URL_TERMS_OF_SERVICE);
            startActivity(webViewIntent);
        } else if (viewId == R.id.txtPrivacyPolicy) {
            Intent intent = new Intent(context, WebViewActivity.class);
            intent.putExtra("link", Constants.URL_PRIVACY_POLICY);
            startActivity(intent);
        } else if (viewId == R.id.txtLogOut) {
            CheckClockedInUsersBeforeLogOut();
        } else if (viewId == R.id.ivSecondRight) {
            ((BaseActivity) requireActivity()).navigateToUploadActivityScreen(binding.tlOther.ivSecondRight);
        } else if (viewId == R.id.ivLanguage) {
            CheckCheckInCrewBeforeChangeLanguage();
        } else if (viewId == R.id.ivMeasurementUnit) {
            navigateToSelectMeasurementUnitScreen();
        }
    }


    /**
     * Method to check if still crews are clocked in.
     * If yes then prevent user to log out self and show appropriate message
     * else allow him to log out and clear sqlite database.
     */
    private void CheckClockedInUsersBeforeLogOut() {
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        if (tblClockCrew.getClockedInDataCount() > 0) {
            showDialogToClockOutCrew(getString(R.string.app_name), getString(R.string.msg_clockout_user_before_logout));
        } else {
            if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 0) {
                CheckCheckedInUsersBeforeLogOut();
                return;
            }
            showDialogLogout(getString(R.string.app_name), getString(R.string.are_you_sure_you_want_to_logout));
        }
    }

    private void showDialogToClockOutCrew(String strTitle, String strMessage) {
        PopUtils.showCustomTwoButtonAlertDialog(context, strTitle, strMessage, getString(R.string.open)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    dialog.dismiss();
                    if (getActivity() != null) {
                        ((MainActivity) getActivity()).showClockScreen();
                    }
                }, (dialog, which) -> dialog.dismiss());
    }


    /**
     * Method to check if still crews are checked in in any of form.
     * If yes then prevent user to log out self and show appropriate message
     * else allow him to log out and clear sqlite database.
     */
    private void CheckCheckedInUsersBeforeLogOut() {
        CheckInMap checkInMap = StaticUtils.getCheckInMapData(getActivity());
        if (checkInMap != null) {
            showDialogToCheckInForm(checkInMap, getString(R.string.app_name), getString(R.string.msg_checkout_user_before_logout, checkInMap.getFormName(), checkInMap.getSiteName()));
        } else {
            showDialogLogout(getString(R.string.app_name), getString(R.string.are_you_sure_you_want_to_logout));
        }
    }


    private void showDialogToCheckInForm(CheckInMap checkInMap, String strTitle, String strMessage) {
        PopUtils.showCustomTwoButtonAlertDialog(context, strTitle, strMessage, getString(R.string.open)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    dialog.dismiss();
                    if (getActivity() != null) {
                        openCheckedInForm(checkInMap);
                    }
                }, (dialog, which) -> dialog.dismiss());
    }

    private void openCheckedInForm(CheckInMap checkInMap) {
        if (getActivity() != null)
            ((MainActivity) getActivity()).navigateToFormDetailScreenFromClockScreen(checkInMap, SITE_DETAIL_REQUEST_CODE);
    }

    /**
     * Method to check if still crews are clocked in.
     * If yes then prevent user to change language and show appropriate message
     * else allow him to navigate to language selection screen.
     */
    private void CheckCheckInCrewBeforeChangeLanguage() {
        CheckInMap checkInMap = StaticUtils.getCheckInMapData(getActivity());
        if (checkInMap != null) {
            showDialogToCheckInForm(checkInMap, getString(R.string.app_name), getString(R.string.msg_check_out_user_change_language, checkInMap.getFormName(), checkInMap.getSiteName()));
        } else {
            navigateToSelectLanguageScreen();
        }

    }

    private void navigateToSelectLanguageScreen() {
        Intent intent = new Intent(getActivity(), AppLanguageActivity.class);
        startActivity(intent);

    }

    private void navigateToSelectMeasurementUnitScreen() {
        Intent intent = new Intent(getActivity(), AppMeasurementActivity.class);
        startActivity(intent);
    }

    private void onSuccessResponse(Response<AppDataResponse> response) {
        updateSaveOriginalImageData();
    }

    public void onFailureResponse(Throwable throwable) {
    }

    public void onNoInternetConnection() {
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }


    /**
     * Async task to clear preference and truncate DB in separate thread
     */
    protected class ClearPrefAndDB extends AsyncTask<Void, Void, Void> {

        @Override
        protected Void doInBackground(Void... params) {
            return clearUserPreferences();
        }

        @Override
        protected void onPostExecute(Void unused) {
            if (getActivity() != null) {
                ((MainActivity) getActivity()).stopProgress();
            }
            super.onPostExecute(unused);
        }
    }
}
