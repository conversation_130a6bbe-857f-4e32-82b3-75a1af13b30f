package com.sitefotos.site.detail.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemServiceMapBinding;
import com.sitefotos.models.MapDetail;

import java.util.List;

public class ServiceMapAdapter extends RecyclerView.Adapter<ServiceMapAdapter.ViewHolder> {

    private List<MapDetail> lstMapList;

    private final ServiceMapAdapter.ViewClicked viewClicked;
    private ItemServiceMapBinding binding;

    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvMapItemName;
        public ViewHolder(ItemServiceMapBinding binding) {
            super(binding.getRoot());
            tvMapItemName = binding.tvMapItemName;
        }
    }

    public ServiceMapAdapter(List<MapDetail> lstProperty, ServiceMapAdapter.ViewClicked viewClicked) {
        this.lstMapList = lstProperty;
        this.viewClicked = viewClicked;
    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemServiceMapBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<MapDetail> lstProperty) {
        this.lstMapList = lstProperty;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        binding.tvMapItemName.setText(lstMapList.get(position).getMapName());
        holder.itemView.setOnClickListener(v -> {
            viewClicked.onItemClicked(position);
        });
    }

    @Override
    public int getItemCount() {
        return lstMapList.size();
    }

    public interface ViewClicked {
        void onItemClicked(int position);
    }
}
