package com.sitefotos.site.detail.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemServiceFormBinding;
import com.sitefotos.models.FormData;

import java.util.List;


public class ServiceFormAdapter extends RecyclerView.Adapter<ServiceFormAdapter.ViewHolder> {
    private List<FormData> lstFormList;
    private final ViewClicked viewClicked;
    private ItemServiceFormBinding binding;


    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvFormName;
        public ViewHolder(ItemServiceFormBinding binding) {
            super(binding.getRoot());
            tvFormName = binding.tvFormName;
        }
    }

    public ServiceFormAdapter(List<FormData> lstFormList, ViewClicked viewClicked) {
        this.lstFormList = lstFormList;
        this.viewClicked = viewClicked;

    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemServiceFormBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<FormData> lstFormList) {
        this.lstFormList = lstFormList;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        holder.tvFormName.setText(lstFormList.get(position).getFormName());
        holder.itemView.setOnClickListener(v -> {
            viewClicked.onItemClicked(position);
        });
    }

    @Override
    public int getItemCount() {
        return lstFormList.size();
    }

    public interface ViewClicked {
        void onItemClicked(int position);
    }

}
