package com.sitefotos.site.detail.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.CheckBox;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemMultiSelectionCrewBinding;
import com.sitefotos.models.Employees;
import com.sitefotos.util.StaticUtils;

import java.util.List;


public abstract class MultiSelectCrewAdapter extends RecyclerView.Adapter<MultiSelectCrewAdapter.ViewHolder> {
    private Context context;
    private List<Employees> lstItems;
    private ItemMultiSelectionCrewBinding binding;

    public abstract void selectItem(int position, boolean isSelected);

    protected MultiSelectCrewAdapter(Context context, List<Employees> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
    }

    public void updateList(List<Employees> lstData) {
        lstItems = lstData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemMultiSelectionCrewBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {


        int userId = StaticUtils.getEmployeeIdInInt();
        if (userId == lstItems.get(position).getEmployeeID()) {
            holder.cbSelection.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        } else {
            holder.cbSelection.setTextColor(ContextCompat.getColor(context, R.color.black));
        }

        holder.cbSelection.setText(StaticUtils.getSelectedCrewMemberName(lstItems.get(position)));


        holder.cbSelection.setOnCheckedChangeListener(null);
        holder.cbSelection.setChecked(lstItems.get(position).isSelected());
        holder.cbSelection.setOnCheckedChangeListener((compoundButton, b) -> selectItem(position, b));
    }

    @Override
    public int getItemCount() {
        return lstItems.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox cbSelection;

        public ViewHolder(ItemMultiSelectionCrewBinding binding) {
            super(binding.getRoot());
            cbSelection = binding.cbSelection;
        }
    }
}
