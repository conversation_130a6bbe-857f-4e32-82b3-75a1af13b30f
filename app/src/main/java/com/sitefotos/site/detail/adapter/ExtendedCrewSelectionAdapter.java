package com.sitefotos.site.detail.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.ItemAddCrewStickyHeaderBinding;
import com.sitefotos.databinding.ItemExtendedCrewSelectionBinding;
import com.sitefotos.models.CrewSelectionData;
import com.sitefotos.models.CrewSelectionHeader;
import com.sitefotos.util.views.StickHeaderAddCrewItemDecoration;

import java.util.ArrayList;
import java.util.List;


public abstract class ExtendedCrewSelectionAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements StickHeaderAddCrewItemDecoration.StickyHeaderInterface {
    private Context context;
    private List<Object> lstItems;
    private List<Object> lstSearchItems;
    private ItemExtendedCrewSelectionBinding binding;
    private ItemAddCrewStickyHeaderBinding headerBinding;
    private boolean showSelectedCrews;

    public abstract void onItemClicked(List<Object> lstAllData, int position, View view, CrewSelectionData crewSelectionData, boolean isSelected);

    public abstract void onHeaderImageClicked(int position, View view);


    public ExtendedCrewSelectionAdapter(Context context) {
        this.context = context;
    }

    public void updateList(List<Object> lstData,boolean shouldNotify) {
        lstItems = lstData;
        lstSearchItems = lstData;
        if (shouldNotify)
        notifyDataSetChanged();
    }

    public void showSelectedCrews(boolean showSelectedCrews) {
        this.showSelectedCrews = showSelectedCrews;
        notifyDataSetChanged();
    }
    public boolean isVisibleSelectedCrews() {
        return showSelectedCrews;
    }
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case Constants.ADAPTER_VIEW_TYPE_HEADER:
                headerBinding = ItemAddCrewStickyHeaderBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
                return new ExtendedCrewSelectionAdapter.HeaderViewHolder(headerBinding.getRoot());
            default:
                binding = ItemExtendedCrewSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
                return new ExtendedCrewSelectionAdapter.ViewHolder(binding);
        }
    }


    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof ExtendedCrewSelectionAdapter.ViewHolder) {
            ((ExtendedCrewSelectionAdapter.ViewHolder) holder).bindData(position);
        } else if (holder instanceof ExtendedCrewSelectionAdapter.HeaderViewHolder) {
            ((ExtendedCrewSelectionAdapter.HeaderViewHolder) holder).bindData(position);
        }
    }

    @Override
    public int getItemCount() {
        return lstSearchItems.size();
    }

    @Override
    public int getItemViewType(int position) {
        if (lstSearchItems.get(position) instanceof CrewSelectionHeader) {
            return Constants.ADAPTER_VIEW_TYPE_HEADER;
        }
        return Constants.ADAPTER_VIEW_TYPE_DEFAULT;
    }


    public void filterData(CharSequence charSequence) {
        String charString = charSequence.toString();
        if (charString.isEmpty()) {
            List<Object> lstFilteredData = new ArrayList<>();
            for (Object data : lstItems) {
                if (data instanceof CrewSelectionData && (((CrewSelectionData) data).isCrew() || ((CrewSelectionData) data).isSelected())) {
                    CrewSelectionData selectionData = (CrewSelectionData) data;
                    String name = selectionData.getCrewName();
                    if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(charString)) {
                        lstFilteredData.add(selectionData);
                    }
                }else if(data instanceof CrewSelectionHeader ){
                    lstFilteredData.add(data);
                }
            }
            lstSearchItems = lstFilteredData;
        } else {
            List<Object> filteredList = new ArrayList<>();
            for (Object data : lstItems) {
                if (data instanceof CrewSelectionData) {
                    CrewSelectionData selectionData = (CrewSelectionData) data;
                    String typedString = charString.trim().toLowerCase();
                    int searchId = 0;
                    boolean isDigit = false;
                    if (!TextUtils.isEmpty(typedString)) {
                        try {
                            searchId = Integer.parseInt(typedString);
                            isDigit = true;
                        } catch (NumberFormatException ignored) {
                        }
                    }
                    String name = selectionData.getCrewName();
                    if (isDigit && searchId > 0) {
                        if (!TextUtils.isEmpty(selectionData.getEmployeeSearchID())) {
                            if (selectionData.getEmployeeSearchID().equals(typedString)) {
                                filteredList.add(data);
                                break;
                            } else {
                                //To add selected data in filter list or matching data
                                if (!TextUtils.isEmpty(name) && (name.toLowerCase().contains(typedString)||selectionData.isSelected())) {
                                    filteredList.add(data);
                                }
                            }
                        }
                    } else {
                        if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString) || selectionData.isSelected()) {
                            filteredList.add(data);
                        }
                    }
                }else if(data instanceof CrewSelectionHeader){
                    filteredList.add(data);
                }

            }
            lstSearchItems = filteredList;

        }
        notifyDataSetChanged();
    }

    public List<CrewSelectionData> getData() {
        return getDataFromObject(lstItems);
    }

    public List<CrewSelectionData> getCrewData() {
        return getDataFromObject(lstSearchItems);
    }

    public void resetSearchQuery() {

    }

    private List<CrewSelectionData> getDataFromObject(List<Object> lstData) {
        List<CrewSelectionData> lstCrewData = new ArrayList<>();
        for (Object data : lstData) {
            if (data instanceof CrewSelectionData) {
                if (!((CrewSelectionData) data).isSelected()) {
                    lstCrewData.add((CrewSelectionData) data);
                }
            }
        }
        return lstCrewData;
    }


    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvExtendedCrew;
        AppCompatImageView ivCrewSelected;

        public ViewHolder(ItemExtendedCrewSelectionBinding binding) {
            super(binding.getRoot());
            tvExtendedCrew = binding.tvExtendedCrew;
            ivCrewSelected = binding.ivCrewSelected;
        }

        void bindData(int position) {
            CrewSelectionData data = (CrewSelectionData) lstSearchItems.get(position);
            tvExtendedCrew.setText(data.getCrewName());
            if (data.isSelected()) {
                ivCrewSelected.setImageResource(R.drawable.ic_checkbox_selected_blue);
            } else {
                ivCrewSelected.setImageResource(R.drawable.ic_checkbox_unselected_blue);
            }

            if (showSelectedCrews && data.getUsertype().equalsIgnoreCase(Constants.CrewSelectionStatus.SelectedCrew.toString())){
                itemView.setVisibility(View.VISIBLE);
                itemView.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
            }else if(!showSelectedCrews && data.getUsertype().equalsIgnoreCase(Constants.CrewSelectionStatus.SelectedCrew.toString())){
                itemView.setVisibility(View.GONE);
                itemView.setLayoutParams(new RecyclerView.LayoutParams(0, 0));
            }else{
                itemView.setLayoutParams(new RecyclerView.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
                itemView.setVisibility(View.VISIBLE);
            }
            tvExtendedCrew.setOnClickListener(v -> onItemClicked(lstItems, position, tvExtendedCrew, data, !data.isSelected()));
            itemView.setOnClickListener(v -> {
                tvExtendedCrew.performClick();

            });
        }
    }

    class HeaderViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvHeader;
        ImageView ivArrow;
        RelativeLayout rlMainLayout;

        HeaderViewHolder(View itemView) {
            super(itemView);
            rlMainLayout = itemView.findViewById(R.id.rlMainLayout);
            tvHeader = itemView.findViewById(R.id.tvHeaderTitle);
            ivArrow = itemView.findViewById(R.id.ivArrow);
        }

        void bindData(int position) {
            CrewSelectionHeader headerData = ((CrewSelectionHeader) lstSearchItems.get(position));
            tvHeader.setText(headerData.getTitle());
            rlMainLayout.setTag(headerData);
            switch (headerData.getHeaderId()) {
                case 1:
                    ivArrow.setVisibility(View.VISIBLE);
                    manageSelectedViewVisibility(ivArrow, showSelectedCrews);
                    break;
                case 2:
                    ivArrow.setVisibility(View.GONE);
                    break;
            }

            ivArrow.setOnClickListener(v -> {
                onHeaderImageClicked(position, v);
            });

        }
    }

    private static void manageSelectedViewVisibility(ImageView ivArrow, boolean isSelected) {
        if (!isSelected) {
            ivArrow.setImageResource(R.drawable.icn_arrow_forward_orrange);
        } else {
            ivArrow.setImageResource(R.drawable.icn_arrow_down);
        }
    }

    @Override
    public int getHeaderPositionForItem(int itemPosition) {
        int headerPosition = -1;
        do {
            if (this.isHeader(itemPosition)) {
                headerPosition = itemPosition;
                break;
            }
            itemPosition -= 1;
        } while (itemPosition >= 0);
        return headerPosition;
    }

    @Override
    public int getHeaderLayout(int headerPosition) {
        return R.layout.item_add_crew_sticky_header;
    }

    @Override
    public void bindHeaderData(View header, int headerPosition) {
        new HeaderViewHolder(header).bindData(headerPosition);
    }

    @Override
    public boolean isHeader(int itemPosition) {
        return getItemViewType(itemPosition) == Constants.ADAPTER_VIEW_TYPE_HEADER;
    }
}
