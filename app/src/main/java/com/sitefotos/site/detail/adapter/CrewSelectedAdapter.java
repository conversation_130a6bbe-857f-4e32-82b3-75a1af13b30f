package com.sitefotos.site.detail.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemSelectedCrewBinding;
import com.sitefotos.models.Employees;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;


public abstract class CrewSelectedAdapter extends RecyclerView.Adapter<CrewSelectedAdapter.ViewHolder> {
    private Context context;
    private List<Employees> lstItems;
    private List<Employees> lstSearchItems;
    private ItemSelectedCrewBinding binding;

    public abstract void selectItem(int position, Employees employees, boolean isSelected);

    protected CrewSelectedAdapter(Context context, List<Employees> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
        lstSearchItems = lstItems;
    }

    public void updateList(List<Employees> lstData) {
        lstItems = lstData;
        lstSearchItems = lstData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemSelectedCrewBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        holder.tvExtendedCrew.setText(StaticUtils.getSelectedCrewMemberName(lstSearchItems.get(position)));
        if (lstSearchItems.get(position).getEmployeeID() == StaticUtils.getEmployeeIdInInt()) {
            holder.tvExtendedCrew.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        } else {
            holder.tvExtendedCrew.setTextColor(ContextCompat.getColor(context, R.color.black));
        }
        holder.llMain.setOnClickListener(v -> {
            if (position < lstSearchItems.size())
                selectItem(position, lstSearchItems.get(position), !lstSearchItems.get(position).isSelected());
        });
    }

    @Override
    public int getItemCount() {
        return lstSearchItems.size();
    }


    public void filterData(CharSequence charSequence) {
        String charString = charSequence.toString();
        if (charString.isEmpty()) {
            List<Employees> lstFilteredData = new ArrayList<>();
            for (Employees data : lstItems) {
                String name = StaticUtils.getSingleStringFromTwoInput(data.getEmployeeFirstName(), data.getEmployeeLastName());
                if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(charString)) {
                    lstFilteredData.add(data);
                }
            }
            lstSearchItems = lstFilteredData;
        } else {
            List<Employees> filteredList = new ArrayList<>();
            for (Employees data : lstItems) {
                String typedString = charString.trim().toLowerCase();
                int searchId = 0;
                boolean isDigit = false;
                if (!TextUtils.isEmpty(typedString)) {
                    try {
                        searchId = Integer.parseInt(typedString);
                        isDigit = true;
                    } catch (NumberFormatException ignored) {
                    }
                }
                String name = StaticUtils.getSingleStringFromTwoInput(data.getEmployeeFirstName(), data.getEmployeeLastName());
                if (isDigit && searchId > 0) {
                    if (!TextUtils.isEmpty(data.getEmployeeSearchID())) {
                        int empSearchId = 0;
                        try {
                            empSearchId = Integer.parseInt(data.getEmployeeSearchID());
                        } catch (NumberFormatException ignored) {
                        }
                        if (empSearchId == searchId) {
                            filteredList.add(data);
                            break;
                        } else {
                            if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString)) {
                                filteredList.add(data);
                            }
                        }
                    }
                } else {
                    if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString)) {
                        filteredList.add(data);
                    }
                }
            }
            lstSearchItems = filteredList;

        }
        notifyDataSetChanged();
    }

    public List<Employees> getData() {
        return lstItems;
    }

    public List<Employees> getSearchData() {
        return lstSearchItems;
    }

    public List<Employees> getCrewData() {
        return lstSearchItems;
    }

    public void resetSearchQuery() {

    }


    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvExtendedCrew;
        LinearLayout llMain;

        public ViewHolder(ItemSelectedCrewBinding binding) {
            super(binding.getRoot());
            tvExtendedCrew = binding.tvExtendedCrew;
            llMain = binding.llMain;
        }
    }
}
