package com.sitefotos.site.detail.adapter;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemServiceInfoBinding;

import java.util.List;

public class ServiceInfoAdapter extends RecyclerView.Adapter<ServiceInfoAdapter.ViewHolder> {


    private List<String> lstInfoList;
    private ItemServiceInfoBinding binding;

    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvInfoName;

        public ViewHolder(ItemServiceInfoBinding binding) {
            super(binding.getRoot());
            tvInfoName = binding.tvInfoName;
        }
    }

    ServiceInfoAdapter(List<String> lstInfoList) {
        this.lstInfoList = lstInfoList;

    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        binding = ItemServiceInfoBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);

        return new ViewHolder(binding);
    }

    public void updateList(List<String> lstInfoList) {
        this.lstInfoList = lstInfoList;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        holder.tvInfoName.setText(lstInfoList.get(position));
    }

    @Override
    public int getItemCount() {
        return lstInfoList.size();
    }

}
