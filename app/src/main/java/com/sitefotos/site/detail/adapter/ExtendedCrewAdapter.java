package com.sitefotos.site.detail.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.CheckBox;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemMultiSelectionCrewBinding;
import com.sitefotos.models.Employees;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;


public abstract class ExtendedCrewAdapter extends RecyclerView.Adapter<ExtendedCrewAdapter.ViewHolder> {
    private Context context;
    private List<Employees> lstItems;
    private List<Employees> lstSearchItems;
    private ItemMultiSelectionCrewBinding binding;

    public abstract void selectItem(int position, Employees employees, boolean isSelected);

    protected ExtendedCrewAdapter(Context context, List<Employees> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
        lstSearchItems = lstItems;
    }

    public void updateList(List<Employees> lstData) {
        lstItems = lstData;
        lstSearchItems = lstData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
       binding = ItemMultiSelectionCrewBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        int userId = StaticUtils.getEmployeeIdInInt();
        if (userId == lstSearchItems.get(position).getEmployeeID()) {
            holder.cbSelection.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        } else {
            holder.cbSelection.setTextColor(ContextCompat.getColor(context, R.color.black));
        }
        holder.cbSelection.setText(StaticUtils.getSelectedCrewMemberName(lstSearchItems.get(position)));

        holder.cbSelection.setOnCheckedChangeListener(null);
        holder.cbSelection.setChecked(lstSearchItems.get(position).isSelected());
        holder.cbSelection.setOnCheckedChangeListener((compoundButton, b) -> {
            selectItem(position, lstSearchItems.get(position), b);

        });
    }

    @Override
    public int getItemCount() {
        return lstSearchItems.size();
    }


    public void filterData(CharSequence charSequence) {
        String charString = charSequence.toString();
        if (charString.isEmpty()) {
            List<Employees> lstFilteredData = new ArrayList<>();
            for (Employees data : lstItems) {
                if (data.isCrew() /*|| data.isSelected()*/) {
                    String name = StaticUtils.getSingleStringFromTwoInput(data.getEmployeeFirstName(), data.getEmployeeLastName());
                    if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(charString)) {
                        lstFilteredData.add(data);
                    }
                }
            }
            lstSearchItems = lstFilteredData;
        } else {
            List<Employees> filteredList = new ArrayList<>();
            for (Employees data : lstItems) {
                String typedString = charString.trim().toLowerCase();
                int searchId = 0;
                boolean isDigit = false;
                if (!TextUtils.isEmpty(typedString)) {
                    try {
                        searchId = Integer.parseInt(typedString);
                        isDigit = true;
                    } catch (NumberFormatException ignored) {
                    }
                }
                String name = StaticUtils.getSingleStringFromTwoInput(data.getEmployeeFirstName(), data.getEmployeeLastName());
                if (isDigit && searchId > 0) {
                    if (!TextUtils.isEmpty(data.getEmployeeSearchID())) {
                        int empSearchId = 0;
                        try {
                            empSearchId = Integer.parseInt(data.getEmployeeSearchID());
                        } catch (NumberFormatException ignored) {
                        }
                        if (empSearchId == searchId) {
                            filteredList.add(data);
                            break;
                        } else {
                            if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString)) {
                                filteredList.add(data);
                            }
                        }
                    }
                } else {
                    if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString)) {
                        filteredList.add(data);
                    }
                }
            }
            lstSearchItems = filteredList;

        }
        notifyDataSetChanged();
    }

    public List<Employees> getData() {
        return lstItems;
    }

    public List<Employees> getCrewData() {
        return lstSearchItems;
    }

    public void resetSearchQuery() {

    }


    public class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox cbSelection;

        public ViewHolder(ItemMultiSelectionCrewBinding binding) {
            super(binding.getRoot());
            cbSelection = binding.cbSelection;
        }
    }
}
