package com.sitefotos.site.detail;

import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.recyclerview.widget.DividerItemDecoration;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.FormAdapter;
import com.sitefotos.databinding.FragmentSiteDetailBinding;
import com.sitefotos.event.TMFormCheckInOutEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.form.TMFormDetailActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.FormData;
import com.sitefotos.models.SiteData;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class SiteFormFragment extends BaseFragment implements View.OnClickListener {

    private FormAdapter formAdapter;
    public SiteData siteData;
    List<FormData> lstForms;
    private List<Long> lstCheckedForms = new ArrayList<>();
    private FragmentSiteDetailBinding binding;


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentSiteDetailBinding.inflate(inflater, container, false);
        initView();
        return binding.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    /*@Override
    public void onResume() {
        super.onResume();
        if (formAdapter != null) {
            getDataFromDatabase();
            formAdapter.notifyDataSetChanged();
        }
    }*/


    private void initView() {
        getBundleData();
        setupAdapter();
        setOnClickListener();
        setupActionBar();
        getDataFromDatabase(true);
    }

    private void getBundleData() {
        if (getArguments() != null)
            siteData = getArguments().getParcelable("siteData");
    }

    private void setupAdapter() {
        formAdapter = new FormAdapter(new ArrayList<>(), lstCheckedForms, getActivity(), true, new FormAdapter.ViewClicked() {
            @Override
            public void onItemClicked(int position, View view, FormData formData) {
                checkAndNavigateToDetailScreen(position);

            }

            @Override
            public void onItemLongClicked(int position, View view) {
                if (getActivity() == null)
                    return;
                FormData formData = lstForms.get(position);
                PopUtils.showDialogForClearFormData(getActivity(), StaticUtils.getClearFormMessage(getActivity(), formData), view1 -> clearFormData(formData.getIsCheckInOut(), position, formData));
            }
        });
        binding.rvSiteForms.setHasFixedSize(true);
        binding.rvSiteForms.setAdapter(formAdapter);
        binding.rvSiteForms.setEmptyView(binding.emptyView.llEmptyViewMain);
        binding.rvSiteForms.setNestedScrollingEnabled(false);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.rvSiteForms.getContext(), 1);
        binding.rvSiteForms.addItemDecoration(dividerItemDecoration);

    }

    private void checkAndNavigateToDetailScreen(int position) {
        navigateToTNMFormDetailScreen(position);
    }


    private void setupActionBar() {
        binding.tlOther.imgBtnBack.setVisibility(View.VISIBLE);
        binding.tlOther.tvTitle.setText(siteData.getSiteName());
        if (getActivity() != null) {
            ((BaseActivity) getActivity()).setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
            updateMarginOfTitleView((BaseActivity) getActivity(), binding.tlOther.llTitle);


        }
    }


    private void navigateToTNMFormDetailScreen(int position) {
        FormData data = lstForms.get(position);
        Intent intent = new Intent(getActivity(), TMFormDetailActivity.class);
        intent.putExtra("formPkId", data.getFormPKId());
        intent.putExtra("siteData", siteData);
        if (getActivity() != null) {
            getActivity().startActivityForResult(intent, Constants.SITE_DETAIL_REQUEST_CODE);
            getActivity().overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
        }
    }


    private void getDataFromDatabase(boolean shouldNavigate) {
        if (getActivity() != null) {
            binding.progressBar.setVisibility(View.VISIBLE);
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                TblTMForms tblForms = new TblTMForms(getContext());
                lstForms = tblForms.getAllFormsBySiteId(siteData.getSiteId());
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    binding.progressBar.setVisibility(View.GONE);
                    TblCheckInMap tblCheckInMap = new TblCheckInMap(getContext());
                    formAdapter.updateList(lstForms, tblCheckInMap.getAllFormsBySiteIds(siteData.getSiteId()));
                    if (shouldNavigate){
                        if (lstForms.size() == 1 && !lstForms.get(0).isCheckInOutFormComplete()) {
                            navigateToTNMFormDetailScreen(0);
                        }
                    }
                });
            });
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getActivity() == null)
            return;
        ((BaseActivity) getActivity()).visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(getActivity(), binding.tlOther.llTitle);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTMFormCheckInOutEvent(TMFormCheckInOutEvent event) {
        getDataFromDatabase(false);
    }

    public void updateData() {
        if (lstForms != null && !lstForms.isEmpty()) {
            lstForms.clear();
            getDataFromDatabase(false);
        }
    }

    private void deleteFormData(final int position) {
        PopUtils.showDialogForDeleteView(getActivity(), getString(R.string.are_you_sure_you_want_to_delete_selected_form), v -> {
            deleteDataFromDatabase(position);
        });

    }

    private void deleteDataFromDatabase(int position) {
        FormData formData = lstForms.get(position);
        TblTMForms tblTMForms = new TblTMForms(getContext());
        tblTMForms.deleteDataByPKId(formData.getFormPKId());
        lstForms.remove(position);
        formAdapter.notifyDataSetChanged();
    }

    private void clearFormData(boolean isCheckInForm, int position, FormData formData) {
        if (getActivity() == null)
            return;
        ((MainActivity) getActivity()).clearDataFromDatabase(formData);
        lstForms.set(position, formData);
        if (isCheckInForm) {
            //If form is already checked in then only send checkout breadcrumb
            if (formData.getCheckin_time() > 0) {
                ((MainActivity) getActivity()).checkAndDeleteCheckInMapData(formData);
                // send checkout with type 3 with one additional parameter (cancelCheckIn)
                if (getActivity() != null) {
                    checkAndSendBreadcrumbsForSubForms(formData);
                    //And at last add main form cancel check in breadcrumb
                    ((BaseActivity) getActivity()).prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude,
                            siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), true, false, 0);
                }
                EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
            }
        } else {
            checkAndSendBreadcrumbsForSubForms(formData);
        }
        ((MainActivity) getActivity()).clearDataFromDatabase(formData);
        lstForms.set(position, formData);
        showForeGroundToast(getString(R.string.msg_form_reset));
    }

    private void checkAndSendBreadcrumbsForSubForms(FormData formData) {
        if (formData.hasSubForm()) {
            List<FormData> lstSubForms = new TblTMForms(getContext()).getAllOnlyCheckedInSubForms(formData.getMainFormPkId());
            for (FormData subFormData : lstSubForms) {
                if (getActivity() != null) {
                    //send cancel breadcrumbs from all sub forms that are not yet completed
                    if (subFormData.getSubFormOtherData() != null && subFormData.getSubFormOtherData().getCrewId() > 0) {
                        ((BaseActivity) getActivity()).prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude, siteData.getSiteId(),
                                subFormData.getFormId(), subFormData.getFormName(), subFormData.getFormSubmissionId(), true, true, subFormData.getSubFormOtherData().getCrewId());
                    }
                }
            }
        }
    }

    private void setOnClickListener() {
        binding.tlOther.imgBtnBack.setOnClickListener(this);
        binding.tlOther.ivSecondRight.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            if (getActivity() != null)
                getActivity().onBackPressed();
        } else if (viewId == R.id.ivSecondRight) {
            ((BaseActivity) requireActivity()).navigateToUploadActivityScreen(binding.tlOther.ivSecondRight);
        }


    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
