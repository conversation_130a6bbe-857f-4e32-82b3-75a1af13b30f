package com.sitefotos.site.screens;

import static com.sitefotos.Constants.SITE_DETAIL_REQUEST_CODE;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentRouteBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.FormData;
import com.sitefotos.models.Routes;
import com.sitefotos.models.SiteData;
import com.sitefotos.site.list.RouteAdapter;
import com.sitefotos.site.list.CheckInSitesAdapter;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblRoutes;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;


public class RoutesFragment extends BaseFragment implements SwipeRefreshLayout.OnRefreshListener {

    private Context context;
    private Activity activity;
    private List<Routes> lstRoute = new ArrayList<>();
    private List<Routes> lstSearchRoute = new ArrayList<>();
    private RouteAdapter adapter;
    private FragmentRouteBinding binding;
    private CheckInSitesAdapter checkInSitesAdapter;
    private List<SiteData> lstCheckedInSites = new ArrayList<>();
    private final List<SiteData> lstSearchCheckedInSites = new ArrayList<>();


    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentRouteBinding.inflate(inflater, container, false);
        activity = getActivity();
        initView();
        return binding.getRoot();
    }

    private void initView() {
        context = getActivity();
        setupToolBar();
        binding.srlRefresh.setOnRefreshListener(this);
        setEditTextChangeListener();
        manageSearchViewCloseTouchEvent(binding.edtSearch);
        binding.edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        setAdapter();
        getDataFromDatabase();

    }

    private void setupToolBar() {
        if (getParentFragment() != null) {
            ((RoutesAndAllSitesFragment) getParentFragment()).visibleUploadImageViewInScreen();
        }
    }


    private void getDataFromDatabase() {
        TblRoutes tblRoutes = new TblRoutes(getContext());
        lstRoute = tblRoutes.getAllData();
        if (adapter != null) {
            adapter.updateList(lstRoute);
        }
        if (!lstRoute.isEmpty()) {
            showSearchView(true);
        } else {
            showEmptyView(true);
        }
        if (binding != null) {
            binding.srlRefresh.setRefreshing(false);
        }

    }

    public void updateData() {
        getDataFromDatabase();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
        getProgressData();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getParentFragment() != null) {
            ((RoutesAndAllSitesFragment) getParentFragment()).visibleUploadImageViewInScreen();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            if (event.response.code() == 304) {
                binding.srlRefresh.setRefreshing(false);
                return;
            }
            if (MainActivity.currentLatitude == 0.0) {
                try {
                    new Handler().postDelayed(this::getDataFromDatabase, 2000);
                    showDataUpdateMessage();
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            } else {
                getDataFromDatabase();
                onSuccessResponse();
                showDataUpdateMessage();
            }
        } else if (event.throwable != null) {
            onFailureResponse();
        } else if (event.isNoInternetConnection) {
            onNoInternetConnection();
        }
    }


    private void showDataUpdateMessage() {
        if (getActivity() != null && ((MainActivity) getActivity()).activeFragment instanceof RoutesFragment) {
            showForeGroundToast(getString(R.string.msg_route_data_updated));
        }
    }

    public void setAdapter() {

        lstRoute = new ArrayList<>();

        adapter = new RouteAdapter(context, lstRoute, (position, view) -> {
            if (getActivity() != null) {
                StaticUtils.hideSoftKeyboard(getActivity());
                showSiteData(position);
            }
        });
        binding.rvRoute.setHasFixedSize(true);
        binding.rvRoute.setAdapter(adapter);
        binding.rvRoute.setEmptyView(binding.emptyView.llEmptyViewMain);
        binding.rvRoute.setNestedScrollingEnabled(true);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.rvRoute.getContext(), 1);
        binding.rvRoute.addItemDecoration(dividerItemDecoration);
        adapter.updateList(lstRoute);

    }

    private void showSiteData(int position) {
        Routes routes;
        if (!lstSearchRoute.isEmpty()) {
            routes = lstSearchRoute.get(position);
            position = lstRoute.indexOf(routes);
            if (position < 0) {
                position = 0;
            }
        }
        routes = lstRoute.get(position);
        if (getActivity() != null)
            ((MainActivity) getActivity()).replaceSiteMapListFragment(true, routes.getRouteId(), routes.getRouteName(), routes.getSiteList());
    }

    private void setEditTextChangeListener() {
        binding.edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                searchText(charSequence.toString());
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }


    void searchText(String text) {
        lstSearchRoute.clear();
        lstSearchCheckedInSites.clear();
        if (!text.isEmpty()) {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
        } else {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        if (adapter == null || checkInSitesAdapter == null)
            return;
        if (!text.isEmpty()) {
            // routes list
            for (Routes routes : lstRoute) {
                String typedString = text.trim().toLowerCase();
                if (routes.getRouteName().toLowerCase().contains(typedString) || routes.getRouteCategory().toLowerCase().contains(typedString)) {
                    lstSearchRoute.add(routes);
                }
            }
            adapter.updateList(lstSearchRoute);

            // site list
            for (SiteData siteData : lstCheckedInSites) {
                String typedString = text.trim().toLowerCase();
                if (siteData.getSiteName().toLowerCase().contains(typedString) || siteData.getPropertyAddress().toLowerCase().contains(typedString)) {
                    lstSearchCheckedInSites.add(siteData);
                }
            }
            checkInSitesAdapter.updateList(lstSearchCheckedInSites);
            manageSiteView(lstSearchCheckedInSites);

            if (lstSearchRoute.isEmpty() && lstSearchCheckedInSites.isEmpty()) {
                showEmptyView(false);
            }
        } else {
            adapter.updateList(lstRoute);
            checkInSitesAdapter.updateList(lstCheckedInSites);
            manageSiteView(lstCheckedInSites);
        }

    }


    public List<Long> getCheckInSitesData() {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
        return tblCheckInMap.getAllSiteIds();
    }

    void getProgressData() {

        List<Long> lstSites = getCheckInSitesData();
        List<SiteData> lstProperty = new ArrayList<>();
        if (!lstSites.isEmpty()) {
            TblSites tblSites = new TblSites(context);
            lstProperty = tblSites.getAllDataByIds(lstSites);
        }
        setProgressAdapter(lstProperty);
    }

    public void setProgressAdapter(List<SiteData> lstProperty) {
        this.lstCheckedInSites = lstProperty;

        checkInSitesAdapter = new CheckInSitesAdapter(lstCheckedInSites, new CheckInSitesAdapter.ViewClicked() {
            @Override
            public void onItemClicked(int position, View view) {
                try {
                    int pos = getPosition(position);
                    if (activity != null && getParentFragment() != null) {
                        StaticUtils.hideSoftKeyboard(activity);
                        navigateToFormListingScreen(lstCheckedInSites.get(pos), false);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onMarkerClicked(int position, View view) {
                try {
                    int pos = getPosition(position);
                    if (activity != null && getParentFragment() != null) {
                        StaticUtils.hideSoftKeyboard(activity);
                        navigateToFormListingScreen(lstCheckedInSites.get(pos), true);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onItemLongClicked(int position, View view) {
                doOnItemLongPressed(position, view);
            }
        });
        initRecyclerView();

        checkInSitesAdapter.updateList(lstCheckedInSites);
        manageSiteView(lstCheckedInSites);

    }

    private void manageSiteView(List<SiteData> lstSites) {
        if (binding != null) {
            if (lstSites != null && !lstSites.isEmpty()) {
                binding.llWorkProgressView.setVisibility(View.VISIBLE);
            } else {
                binding.llWorkProgressView.setVisibility(View.GONE);
            }
        }
    }

    private void initRecyclerView() {
        if (binding != null) {
            binding.rvWorkingSites.setHasFixedSize(true);
            binding.rvWorkingSites.setAdapter(checkInSitesAdapter);
            binding.rvWorkingSites.setTag(true);
            binding.rvWorkingSites.setNestedScrollingEnabled(true);
        }
    }

    private int getPosition(int position) {
        if (!lstSearchCheckedInSites.isEmpty()) {
            SiteData siteData = lstSearchCheckedInSites.get(position);
            position = lstCheckedInSites.indexOf(siteData);
            if (position < 0) {
                position = 0;
            }
        }
        return position;
    }



    private void doOnItemLongPressed(int position, View view) {
        if (getActivity() != null) {
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                List<FormData> lstFormData = ((MainActivity) activity).getSiteFormList(lstCheckedInSites.get(position));
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    if (lstFormData.size() == 1) {
                        FormData formData = lstFormData.get(0);
                        PopUtils.showDialogForClearFormData(activity, StaticUtils.getClearFormMessage(activity,formData), view1 -> ((MainActivity) activity).clearFormData(formData.getIsCheckInOut(), lstCheckedInSites.get(position), formData));
                    }
                });
            });
        }
    }

    void navigateToFormListingScreen(SiteData siteData, boolean ShouldNavigateToMapApp) {
        try {
            if (getActivity() == null || getParentFragment() == null) {
                return;
            }
            StaticUtils.hideSoftKeyboard(getActivity());

            if (ShouldNavigateToMapApp) {
                navigateToGoogleMap(siteData.getPropertyLatitude(), siteData.getPropertyLongitude());
            } else {
                try {
                    ((MainActivity) getActivity()).navigateToFormDetailScreen(siteData, null, SITE_DETAIL_REQUEST_CODE, false, true);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } catch (Exception e) {
           FirebaseEventUtils.logException(e);
        }
    }

    void navigateToGoogleMap(double latitude, double longitude) {
        if (getActivity() == null) {
            return;
        }
        try {
            ((MainActivity) getActivity()).navigateUserToDefaultMapApp(latitude, longitude);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private void showEmptyView(boolean hideView) {
        binding.rvRoute.setEmptyView(binding.emptyView.llEmptyViewMain);
        binding.rvRoute.setEmptyData(getString(R.string.no_route_found));
        showSearchView(!hideView);
    }

    private void setEmptyView() {
        if (context == null)
            return;
        binding.rvRoute.setEmptyView(binding.emptyView.llEmptyViewMain);
        binding.rvRoute.setEmptyData("");
    }


    private void showSearchView(boolean enable) {

        if (binding != null) {
            if (enable) {
                binding.edtSearch.setVisibility(View.VISIBLE);
                binding.edtSearch.setEnabled(true);
            } else {
                binding.edtSearch.setVisibility(View.GONE);
                binding.edtSearch.setEnabled(false);
            }
        }

    }

    @Override
    public void onRefresh() {
        //edtSearch.setEnabled(false);
        if (getActivity() == null)
            return;
        if (BaseApplication.getInstance().isOnline(getActivity())) {
            ((MainActivity) getActivity()).callApiForAppData(false, true);
        } else {
            disablePullRefresh();
        }
        setEmptyView();
    }

    void disablePullRefresh() {
        binding.srlRefresh.setRefreshing(false);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void onSuccessResponse() {
        setText();
    }

    public void onFailureResponse() {
        if (getActivity() == null)
            return;
        disablePullRefresh();
    }

    public void onNoInternetConnection() {
        if (getActivity() == null)
            return;
        if (lstRoute != null && lstRoute.isEmpty()) {
            showEmptyView(true);
        }
        disablePullRefresh();

    }

    private void setText() {
        if (binding.edtSearch.getText() != null && !TextUtils.isEmpty(binding.edtSearch.getText())) {
            searchText(binding.edtSearch.getText().toString());
            binding.edtSearch.setSelection(binding.edtSearch.getText().length());
            if (getActivity() != null) {
                ((BaseActivity) getActivity()).showKeyboard(binding.edtSearch);
            }
        }
    }
}
