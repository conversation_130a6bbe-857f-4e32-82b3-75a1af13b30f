package com.sitefotos.site.screens;

import android.content.Context;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentWorkBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblRoutes;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import retrofit2.Response;

public class WorkFragment extends BaseFragment {

    private Context context;
    RoutesAndAllSitesFragment routesAndAllSitesFragment;
    SitesAndMapListFragment sitesAndMapListFragment;
    boolean isRoute;

    private FragmentWorkBinding binding;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentWorkBinding.inflate(inflater, container, false);
        init();
        return binding.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            onSuccessResponse(event.response);
        }
        if (event.throwable != null) {
            onFailureResponse(event.throwable);
        }
        if (event.isNoInternetConnection) {
            onNoInternetConnection();
        }
    }

    private void init() {
        context = getContext();
        getDataFromDatabase();
    }

    private void getDataFromDatabase() {
        TblRoutes tblRoutes = new TblRoutes(context);
        addChildFragment(AppPrefShared.getBoolean(Constants.PARAM_ROUTES_ACTIVE, false) && tblRoutes.getDataCount() > 1);
    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        if (!hidden) {
            if (getActivity() != null) {
                Fragment fragment = getActivity().getSupportFragmentManager().findFragmentById(R.id.flFragment);
                if (fragment instanceof SitesAndMapListFragment) {
                    ((SitesAndMapListFragment) fragment).checkLocationAndUpdateData();
                }
            }
        }
    }

    public boolean isRoute() {
        return isRoute;
    }

    public void setRoute(boolean route) {
        isRoute = route;
    }

    private void addChildFragment(boolean isRoute) {
        if (getActivity() != null) {
            this.isRoute = isRoute;
//            getActivity().getSupportFragmentManager().executePendingTransactions();
            FragmentTransaction transaction = getActivity().getSupportFragmentManager().beginTransaction();
            if (isRoute) {
                if (getActivity().getSupportFragmentManager().getBackStackEntryCount() > 0) {
                    if (getActivity().getSupportFragmentManager().findFragmentById(R.id.flFragment) != null) {
                        Fragment fragment = getActivity().getSupportFragmentManager().findFragmentById(R.id.flFragment);

                        if (fragment instanceof RoutesAndAllSitesFragment) {
                            RoutesAndAllSitesFragment routesFragment = (RoutesAndAllSitesFragment) fragment;
                            routesFragment.updateData();
                        } else {
                            if (routesAndAllSitesFragment != null) {
                                transaction.replace(R.id.flFragment, routesAndAllSitesFragment).commit();
                                routesAndAllSitesFragment.updateData();
                            } else {
                                routesAndAllSitesFragment = new RoutesAndAllSitesFragment();
                                transaction.add(R.id.flFragment, routesAndAllSitesFragment).addToBackStack(RoutesAndAllSitesFragment.class.getName()).commitAllowingStateLoss();
                            }
                        }
                    }
                } else {
                    if (routesAndAllSitesFragment == null)
                        routesAndAllSitesFragment = new RoutesAndAllSitesFragment();
                    transaction.add(R.id.flFragment, routesAndAllSitesFragment).addToBackStack(RoutesAndAllSitesFragment.class.getName()).commitAllowingStateLoss();
                }
            } else {
                if (getActivity().getSupportFragmentManager().getBackStackEntryCount() > 0) {
                    if (getActivity().getSupportFragmentManager().findFragmentById(R.id.flFragment) != null) {
                        Fragment fragment = getActivity().getSupportFragmentManager().findFragmentById(R.id.flFragment);

                        if (fragment instanceof SitesAndMapListFragment) {
                            ((SitesAndMapListFragment) fragment).updateData();
                        } else {
                            if (sitesAndMapListFragment != null) {
                                transaction.replace(R.id.flFragment, sitesAndMapListFragment).commit();
                                new CountDownTimer(1000, 1000) {
                                    @Override
                                    public void onTick(long millisUntilFinished) {

                                    }

                                    @Override
                                    public void onFinish() {
                                        sitesAndMapListFragment.updateData();

                                    }
                                }.start();
                            } else {
                                sitesAndMapListFragment = new SitesAndMapListFragment();
                                transaction.add(R.id.flFragment, sitesAndMapListFragment)
                                        .addToBackStack(SitesAndMapListFragment.class.getName())
                                        .commitAllowingStateLoss();

                            }
                        }
                    }
                } else {
                    if (sitesAndMapListFragment == null)
                        sitesAndMapListFragment = new SitesAndMapListFragment();

                    transaction.add(R.id.flFragment, sitesAndMapListFragment)
                            .addToBackStack(SitesAndMapListFragment.class.getName())
                            .commitAllowingStateLoss();
                }

            }
        }
    }


    void setProgressBarVisibility(boolean shouldShow) {
        if (shouldShow) {
            binding.progressBar.setVisibility(View.VISIBLE);
        } else {
            binding.progressBar.setVisibility(View.GONE);
        }
    }

    public void onPullToRefresh() {
        if (getActivity() != null) {
            ((MainActivity) getActivity()).callApiForAppData(false, true);
        }
    }


    public void onSuccessResponse(Response<AppDataResponse> response) {
        if (getActivity() == null)
            return;
        if (getActivity().isFinishing())
            return;
        binding.progressBar.setVisibility(View.GONE);

        if (response != null && response.code() == 304)
            return;
        getDataFromDatabase();
    }

    public void onFailureResponse(Throwable t) {
        if (getActivity() == null)
            return;
        if (getActivity().isFinishing())
            return;
        binding.progressBar.setVisibility(View.GONE);
    }

    public void setCurrentItem(int position) {
        if (sitesAndMapListFragment != null)
            sitesAndMapListFragment.setItemFirst(position);
    }

    public void onNoInternetConnection() {
        binding.progressBar.setVisibility(View.GONE);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    public boolean isAllSiteSelected() {
        if (routesAndAllSitesFragment != null) {
            return routesAndAllSitesFragment.isAllSiteSelected();
        }
        return false;
    }


}
