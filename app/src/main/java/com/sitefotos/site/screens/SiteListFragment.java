package com.sitefotos.site.screens;

import static com.sitefotos.Constants.PARAM_SITE_FILTER_DISTANCE;

import android.app.Activity;
import android.content.Context;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentSiteListBinding;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.FormData;
import com.sitefotos.models.SiteData;
import com.sitefotos.site.list.SiteListingAdapter;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class SiteListFragment extends BaseFragment implements SwipeRefreshLayout.OnRefreshListener {

    private Activity activity;
    private List<SiteData> lstSites = new ArrayList<>();
    private List<SiteData> lstSearchSites = new ArrayList<>();
    private SiteListingAdapter siteListingAdapter;
    private List<Long> lstCheckedSite = new ArrayList<>();
    public boolean errorFound;
    private FragmentSiteListBinding binding;
    private boolean fromRoute;


    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentSiteListBinding.inflate(inflater, container, false);
        activity = getActivity();
        binding.srlRefresh.setOnRefreshListener(this);
        if (activity != null) {
            if (getParentFragment() != null && ((SitesAndMapListFragment) getParentFragment()).isDataFetched) {
                ((SitesAndMapListFragment) getParentFragment()).updateData();
                setAdapter(new ArrayList<>(), false, false);
            } else {
                setAdapter(new ArrayList<>(), false, BaseApplication.getInstance().isOnline(activity));
            }
        }
        return binding.getRoot();
    }

    @Override
    public void onResume() {
        super.onResume();
        if (siteListingAdapter != null) {
            siteListingAdapter.notifyDataSetChanged();
        }
        checkEmptyView();
    }

    void checkEmptyView() {
        if (getParentFragment() != null) {
            Boolean isEmptyViewVisible = binding.rvSites.isEmptyViewVisible();
            boolean isLoading = ((SitesAndMapListFragment) getParentFragment()).binding.pwLoader.getVisibility() == View.VISIBLE;
            if (isEmptyViewVisible != null && isEmptyViewVisible && !isLoading) {
                showEmptyView(false, true);
            }
        }
    }

    void searchText(Activity activity, String text) {
        this.activity = activity;
        if (activity != null) {
            lstSearchSites.clear();
            if (siteListingAdapter == null)
                return;

            lstCheckedSite = getCheckInSitesData();
            if (!text.isEmpty()) {
                for (SiteData siteData : lstSites) {
                    String typedString = text.trim().toLowerCase();
                    if (siteData.getSiteName().toLowerCase().contains(typedString) || siteData.getPropertyAddress().toLowerCase().contains(typedString)) {
                        lstSearchSites.add(siteData);
                    }
                }
                siteListingAdapter.updateList(lstSearchSites, lstCheckedSite);
                if (lstSearchSites.isEmpty()) {
                    showEmptyView(false, false);
                }
            } else {
                siteListingAdapter.updateList(lstSites, lstCheckedSite);
                // showEmptyView(false,false);
            }
        }
    }

    void updateSiteList(Activity activity, List<SiteData> lstData, boolean showProgress) {
        this.lstSites = lstData;
        this.activity = activity;
        setAdapter(lstData, true, showProgress);
        if (binding != null)
            binding.srlRefresh.setRefreshing(false);
    }

    public void setAdapter(List<SiteData> lstProperty, boolean shouldUpdateList, boolean showProgress) {
        if (shouldUpdateList) {
            this.lstSites = lstProperty;
        } else {
            if (lstSites == null) {
                this.lstSites = new ArrayList<>();
            }
        }

        if (siteListingAdapter == null) {
            siteListingAdapter = new SiteListingAdapter(lstSites, getCheckInSitesData(),fromRoute, new SiteListingAdapter.ViewClicked() {
                @Override
                public void onItemClicked(int position, View view) {
                    try {
                        int pos = getPosition(position);
                        if (activity != null && getParentFragment() != null) {
                            StaticUtils.hideSoftKeyboard(activity);
                            ((SitesAndMapListFragment) getParentFragment()).navigateToFormListingScreen(lstSites.get(pos), false);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }

                @Override
                public void onMarkerClicked(int position, View view) {
                    try {
                        int pos = getPosition(position);
                        if (activity != null && getParentFragment() != null) {
                            StaticUtils.hideSoftKeyboard(activity);
                            ((SitesAndMapListFragment) getParentFragment()).navigateToFormListingScreen(lstSites.get(pos), true);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }

                @Override
                public void onItemLongClicked(int position, View view) {
                    doOnItemLongPressed(position,view);
                }
            });
            initRecyclerView();
        }
        if (binding != null && binding.rvSites.getTag() == null) {
            initRecyclerView();
        }
        if (lstSites != null && !lstSites.isEmpty()) {
            siteListingAdapter.updateList(lstSites, getCheckInSitesData());
            if (getParentFragment() != null) {
                ((SitesAndMapListFragment) getParentFragment()).setText();
            }
        } else if (lstSites != null) {
            siteListingAdapter.updateList(lstSites, getCheckInSitesData());
            showEmptyView(showProgress, true);
        } else {
            showEmptyView(showProgress, true);
        }
    }

    private void doOnItemLongPressed(int position, View view) {
        if (getActivity() != null) {
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                List<FormData> lstFormData = ((MainActivity) activity).getSiteFormList(lstSites.get(position));
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    if (lstFormData.size() == 1) {
                        FormData formData = lstFormData.get(0);
                        PopUtils.showDialogForClearFormData(activity, StaticUtils.getClearFormMessage(activity,formData), view1 -> ((MainActivity) activity).clearFormData(formData.getIsCheckInOut(), lstSites.get(position), formData));
                    }
                });
            });
        }
    }

    private void initRecyclerView() {
        if (binding != null) {
            binding.rvSites.setHasFixedSize(true);
            binding.rvSites.setAdapter(siteListingAdapter);
            binding.rvSites.setEmptyView(binding.emptyView.llEmptyViewMain);
            binding.rvSites.setTag(true);
            binding.rvSites.setNestedScrollingEnabled(true);
        }
    }

    private int getPosition(int position) {
        if (!lstSearchSites.isEmpty()) {
            SiteData siteData = lstSearchSites.get(position);
            position = lstSites.indexOf(siteData);
            if (position < 0) {
                position = 0;
            }
        }
        return position;
    }


    void showEmptyView(boolean skipCondition, boolean hideSearch) {
        try {
            String message = "";
            if (skipCondition) {
                message = "";
            } else {
                if (getContext() != null && !fromRoute &&  canGetLocation(activity) && !AppPrefShared.getBoolean(Constants.PARAM_ALL_SITES, false)) {
                    if (AppPrefShared.getFloat(PARAM_SITE_FILTER_DISTANCE, -1.0f) > -1.0f) {
                        message = getString(R.string.no_list_data_found_in_30_miles_radius, StaticUtils.getDistanceByUnit(activity,AppPrefShared.getFloat(PARAM_SITE_FILTER_DISTANCE, -1.0f)));
                    } else {
                        message = getContext().getString(R.string.no_list_data_found_in_30_miles_radius, StaticUtils.getDistanceByUnit(activity,30.00));
                    }
                } else {
                    if (getContext() == null || !isAdded()) {
                        errorFound = true;
                    }
                    if (activity != null && getContext() != null) {
                        if (fromRoute) {
                            message = getContext().getString(R.string.no_route_site_data_found);
                        } else {
                            message = getContext().getString(R.string.no_list_data_found);
                        }
                    }
                }
            }
            if (binding != null) {
                binding.rvSites.setEmptyView(binding.emptyView.llEmptyViewMain);
                binding.rvSites.setEmptyData(message);
                if (getParentFragment() != null && hideSearch) {
                    ((SitesAndMapListFragment) getParentFragment()).binding.edtSearch.setVisibility(View.GONE);
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    @Override
    public void onRefresh() {
        if (getParentFragment() != null) {
            //showEmptyView(true, true);
            ((SitesAndMapListFragment) getParentFragment()).onPullToRefreshRefresh();
        }

    }

    void stopPullToRefresh() {
        if (activity != null && binding != null) {
            binding.srlRefresh.setRefreshing(false);
            if (getParentFragment() != null) {
                ((SitesAndMapListFragment) getParentFragment()).binding.edtSearch.setVisibility(View.VISIBLE);
            }
        }
    }

    public void onFailureResponse() {
        if (activity != null) {
            binding.srlRefresh.setRefreshing(false);
        }
    }

    public void onNoInternetConnection() {
        try {
            if (activity != null) {
                showEmptyView(false, false);
                binding.srlRefresh.setRefreshing(false);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public List<Long> getCheckInSitesData() {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(activity);
        lstCheckedSite = tblCheckInMap.getAllSiteIds();
        return lstCheckedSite;
    }

    public void isFromRoute(boolean fromRoute) {
        this.fromRoute = fromRoute;
    }

    public boolean canGetLocation(Context context) {
        LocationManager locationManager = null;
        boolean isLocationEnabled = false;
        if (PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
            locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        }

        if (locationManager != null) {
            // getting GPS status
            boolean isGPSEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            boolean isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            isLocationEnabled = !(!isGPSEnabled && !isNetworkEnabled);
        }
        return isLocationEnabled;
    }

}
