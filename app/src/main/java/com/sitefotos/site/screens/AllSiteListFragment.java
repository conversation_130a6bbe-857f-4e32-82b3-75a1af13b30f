package com.sitefotos.site.screens;

import static com.sitefotos.Constants.PARAM_SITE_FILTER_DISTANCE;
import static com.sitefotos.Constants.SITE_DETAIL_REQUEST_CODE;

import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentAllSiteListBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.SiteDataUpdateEvent;
import com.sitefotos.event.TMFormCheckInOutEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.FormData;
import com.sitefotos.models.SiteData;
import com.sitefotos.site.list.SiteListingAdapter;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.PropertyUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;


public class AllSiteListFragment extends BaseFragment implements SwipeRefreshLayout.OnRefreshListener {

    private Context context;
    private List<SiteData> lstSites;
    private List<SiteData> lstSearchSites = new ArrayList<>();
    private SiteListingAdapter siteListingAdapter;
    private List<Long> lstCheckedSite = new ArrayList<>();
    double dataFetchedLatitude = 0.0;
    double dataFetchedLongitude = 0.0;
    private FragmentAllSiteListBinding binding;

    @Override
    public void onCreate(@Nullable @org.jetbrains.annotations.Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentAllSiteListBinding.inflate(inflater, container, false);
        context = getActivity();
        initData();
        return binding.getRoot();
    }

    private void initData() {
        binding.srlRefresh.setOnRefreshListener(this);
        setEditTextChangeListener();
        manageSearchViewCloseTouchEvent(binding.edtSearch);
        binding.edtSearch.setEnabled(false);
        binding.edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        getDataFromDatabase(true);
    }

    private void getDataFromDatabase( final boolean showProgress) {
        if (getActivity() != null) {
            hideOrShowProgressBar(showProgress);
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                Pair<List<SiteData>, Boolean> pairData = getSortedList();
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    lstSites = pairData.first;
                    if (!lstSites.isEmpty()) {
                        showSearchView(true);
                        setAdapter(lstSites, true, false);
                        hideOrShowProgressBar(false);
                    } else {
                        //siteListFragment.disablePullRefresh();
                        if (!BaseApplication.getInstance().isOnline(context)) {
                            setAdapter(lstSites, true, false);
                        } else {
                            if(showProgress) {
                                setAdapter(lstSites, true, pairData.second);
                            }else{
                                setAdapter(lstSites, true, showProgress);
                            }
                        }
                    }
                    dataFetchedLatitude = MainActivity.currentLatitude;
                    dataFetchedLongitude = MainActivity.currentLongitude;
                });
            });
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getParentFragment() != null) {
            ((RoutesAndAllSitesFragment) getParentFragment()).visibleUploadImageViewInScreen();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSiteDataUpdateEvent(SiteDataUpdateEvent event) {
        getDataFromDatabase(false);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTMFormCheckInOutEvent(TMFormCheckInOutEvent event) {
        getDataFromDatabase(false);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            if (event.response.code() == 304) {
                checkAndRefreshData();
                if (binding.srlRefresh != null) {
                    binding.srlRefresh.setRefreshing(false);
                }
                return;
            }
            if (MainActivity.currentLatitude == 0.0) {
                try {
                    new Handler().postDelayed(() -> {
                        getDataFromDatabase(false);
                        showDataUpdateMessage();
                    }, 2000);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            } else {
                getDataFromDatabase(false);
                showDataUpdateMessage();
            }
        } else if (event.throwable != null) {
            checkAndRefreshData();
            onFailureResponse();
        } else if (event.isNoInternetConnection) {
            checkAndRefreshData();
            onNoInternetConnection();
        }
    }

    private void showDataUpdateMessage() {
        if (getActivity() != null && ((MainActivity) getActivity()).activeFragment instanceof RoutesAndAllSitesFragment) {
            showForeGroundToast(getString(R.string.msg_site_data_updated));
        }
    }

    /**
     * Check for 16 meters location distance from last fetched data from DB. if true,
     */
    private void checkAndRefreshData() {
        if (PropertyUtils.distanceToCurrentLocation(dataFetchedLatitude, dataFetchedLongitude) > 16) {
            getDataFromDatabase(false);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (siteListingAdapter != null) {
            siteListingAdapter.notifyDataSetChanged();
        }
    }

    private void hideOrShowProgressBar(boolean shouldShow) {
        WorkFragment workFragment = getInstanceOfWorkFragment();
        if (workFragment != null) {
            workFragment.setProgressBarVisibility(shouldShow);
        }
    }

    void searchText(String text) {
        lstSearchSites.clear();
        if (text.length() > 0) {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
        } else {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        if (siteListingAdapter == null)
            return;

        lstCheckedSite = getCheckInSitesData();
        if (text.length() > 0) {
            for (SiteData siteData : lstSites) {
                String typedString = text.trim().toLowerCase();
                if (siteData.getSiteName().toLowerCase().contains(typedString) || siteData.getPropertyAddress().toLowerCase().contains(typedString)) {
                    lstSearchSites.add(siteData);
                }
            }
            siteListingAdapter.updateList(lstSearchSites, lstCheckedSite);
            if (lstSearchSites.size() == 0) {
                showEmptyView(false, false);
            }
        } else {
            siteListingAdapter.updateList(lstSites, lstCheckedSite);
            // showEmptyView(false,false);
        }
    }

    void updateSiteList(List<SiteData> lstData, boolean showProgress) {
        this.lstSites = lstData;
        setAdapter(lstData, true, showProgress);
        binding.srlRefresh.setRefreshing(false);
    }

    public void setAdapter(List<SiteData> lstProperty, boolean shouldUpdateList, boolean showProgress) {
        if (shouldUpdateList) {
            this.lstSites = lstProperty;
        } else {
            if (lstSites == null) {
                this.lstSites = new ArrayList<>();
                CustomLogKt.errorLog("setAdapter SiteList", "lstSites is null");
            }
        }
        if (siteListingAdapter == null) {
            siteListingAdapter = new SiteListingAdapter(lstSites, getCheckInSitesData(),false, new SiteListingAdapter.ViewClicked() {
                @Override
                public void onItemClicked(int position, View view) {
                    try {
                        int pos = getPosition(position);
                        if (getActivity() != null) {
                            StaticUtils.hideSoftKeyboard(getActivity());
                            navigateToFormListingScreen(lstSites.get(pos), false);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }

                @Override
                public void onMarkerClicked(int position, View view) {
                    try {
                        int pos = getPosition(position);
                        if (getActivity() != null) {
                            StaticUtils.hideSoftKeyboard(getActivity());
                            navigateToFormListingScreen(lstSites.get(pos), true);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }

                @Override
                public void onItemLongClicked(int position, View view) {
                    if (getActivity() == null)
                        return;
                    doOnItemLongPressed(position, view);
                }
            });

            binding.rvSites.setHasFixedSize(true);
            binding.rvSites.setAdapter(siteListingAdapter);
            binding.rvSites.setEmptyView(binding.emptyView.llEmptyViewMain);
            binding.rvSites.setNestedScrollingEnabled(true);
            DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.rvSites.getContext(), 1);
            binding.rvSites.addItemDecoration(dividerItemDecoration);

        }
        if (lstSites != null && !lstSites.isEmpty()) {
            siteListingAdapter.updateList(lstSites, getCheckInSitesData());
            showSearchView(true);
        } else if (lstSites != null) {
            siteListingAdapter.updateList(lstSites, getCheckInSitesData());
            showEmptyView(showProgress, true);
        } else {
            showEmptyView(showProgress, true);
        }
    }

    private void doOnItemLongPressed(int position, View view) {
        if (getActivity() != null) {
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                List<FormData> lstFormData = ((MainActivity) getActivity()).getSiteFormList(lstSites.get(position));
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    if (lstFormData.size() == 1) {
                        FormData formData = lstFormData.get(0);
                        PopUtils.showDialogForClearFormData(getActivity(), StaticUtils.getClearFormMessage(getActivity(), formData), view1 -> ((MainActivity) getActivity()).clearFormData(formData.getIsCheckInOut(), lstSites.get(position), formData));
                    }
                });
            });
        }
    }
        private void navigateToFormListingScreen (SiteData siteData,boolean ShouldNavigateToMapApp){
            if (getActivity() == null) {
                return;
            }
            if (ShouldNavigateToMapApp) {
                navigateToGoogleMap(siteData.getPropertyLatitude(), siteData.getPropertyLongitude());
            } else {
                try {
                    ((MainActivity) getActivity()).navigateToFormDetailScreen(siteData, null, SITE_DETAIL_REQUEST_CODE, true);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }

        void navigateToGoogleMap ( double latitude, double longitude){
            if (getActivity() == null) {
                return;
            }
            try {
                ((MainActivity) getActivity()).navigateUserToDefaultMapApp(latitude, longitude);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }

    private void setEditTextChangeListener() {
        binding.edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                searchText(charSequence.toString());
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }

    private int getPosition(int position) {
        if (lstSearchSites.size() > 0) {
            SiteData siteData = lstSearchSites.get(position);
            position = lstSites.indexOf(siteData);
            if (position < 0) {
                position = 0;
            }
        }
        return position;
    }


    private Pair<List<SiteData>, Boolean> getSortedList() {
        boolean showProgress = true;
        TblSites tblSites = new TblSites(context);
        lstSites = tblSites.getAllData();
        if (!lstSites.isEmpty()) {
            showProgress = false;
        }
        if (getActivity() != null && ((MainActivity) getActivity()).canGetLocation(context)) {
            //if (!AppPrefShared.getBoolean(Constants.PARAM_ROUTES_ACTIVE, false)) {
            lstSites = PropertyUtils.getClosestDistanceList(context,lstSites);
            //} else {
            //    lstSites = setZeroDistanceInList();
            // }
        } else {
            lstSites = setZeroDistanceInList();
        }
        return new Pair<>(lstSites, showProgress);
    }

    private List<SiteData> setZeroDistanceInList() {
        for (int i = 0; i < lstSites.size(); i++) {
            SiteData siteData = lstSites.get(i);
            siteData.setDistance(0.0);
            lstSites.set(i, siteData);
        }
        return lstSites;
    }

    void showEmptyView(boolean skipCondition, boolean hideSearch) {
        String message;
        if (skipCondition) {
            message = "";
        } else {
            if (getActivity() != null && ((MainActivity) getActivity()).canGetLocation(context) &&
                    !AppPrefShared.getBoolean(Constants.PARAM_ALL_SITES, false)) {
                if (AppPrefShared.getFloat(PARAM_SITE_FILTER_DISTANCE, -1.0f) > -1.0f) {
                    message = getString(R.string.no_list_data_found_in_30_miles_radius, StaticUtils.getDistanceByUnit(context,AppPrefShared.getFloat(PARAM_SITE_FILTER_DISTANCE, -1.0f)));
                } else {
                    message = getString(R.string.no_list_data_found_in_30_miles_radius, StaticUtils.getDistanceByUnit(context,30.00));
                    }
                } else {
                    message = getString(R.string.no_list_data_found);
                }
            }
            binding.rvSites.setEmptyView(binding.emptyView.llEmptyViewMain);
            binding.rvSites.setEmptyData(message);
            showSearchView(!hideSearch);
        }

        private void showSearchView ( boolean enable){
            if (enable) {
                binding.edtSearch.setVisibility(View.VISIBLE);
                binding.edtSearch.setEnabled(true);
            } else {
                binding.edtSearch.setVisibility(View.GONE);
                binding.edtSearch.setEnabled(false);
            }

        }


        public void updateData () {
            getDataFromDatabase(false);
        }

        @Override
        public void onRefresh () {
            if (getActivity() == null)
                return;
            if (BaseApplication.getInstance().isOnline(getActivity())) {
                ((MainActivity) getActivity()).callApiForAppData(false, true);
            } else {
                disablePullRefresh();
            }

        }

        void disablePullRefresh () {
            binding.srlRefresh.setRefreshing(false);
        }

        public void onFailureResponse () {
            if (getActivity() == null)
                return;
            disablePullRefresh();
        }

        public void onNoInternetConnection () {
            if (getActivity() == null)
                return;
            disablePullRefresh();
        }

        public List<Long> getCheckInSitesData () {
            TblCheckInMap tblCheckInMap = new TblCheckInMap(getActivity());
            lstCheckedSite = tblCheckInMap.getAllSiteIds();
            return lstCheckedSite;
        }

        public void setText () {
            if (binding.edtSearch != null) {
                binding.edtSearch.setVisibility(View.VISIBLE);
                if (binding.edtSearch.getText() != null && !TextUtils.isEmpty(binding.edtSearch.getText())) {
                    searchText(binding.edtSearch.getText().toString());
                    binding.edtSearch.setSelection(binding.edtSearch.getText().length());
                    if (getActivity() != null) {
                        ((BaseActivity) getActivity()).showKeyboard(binding.edtSearch);
                    }
                }
            }
        }

        @Override
        public void onDestroyView () {
            super.onDestroyView();
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }
