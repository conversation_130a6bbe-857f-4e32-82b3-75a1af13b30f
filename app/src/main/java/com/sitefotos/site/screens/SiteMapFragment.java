package com.sitefotos.site.screens;

import android.Manifest;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.app.ActivityCompat;

import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.maps.android.clustering.ClusterManager;
import com.sitefotos.BaseFragment;
import com.sitefotos.R;
import com.sitefotos.models.MapMarkerItem;
import com.sitefotos.models.SiteData;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.MapClusterRenderer;
import com.sitefotos.util.logger.CustomLogKt;

import java.util.ArrayList;
import java.util.List;

public class SiteMapFragment extends BaseFragment implements OnMapReadyCallback,
        ClusterManager.OnClusterItemInfoWindowClickListener<MapMarkerItem>, GoogleMap.OnMarkerClickListener {

    private GoogleMap googleMap;
    private List<SiteData> lstSiteData;
    private ClusterManager<MapMarkerItem> mClusterManager;
    private List<SiteData> lstSearchProperty = new ArrayList<>();
    boolean isDestroyed=  false;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_site_map, container, false);
        init(view);
        return view;
    }


    public void updateList(List<SiteData> lstProperty) {
        this.lstSiteData = lstProperty;
        if (!lstSiteData.isEmpty() && googleMap != null) {
            addMarkers(lstSiteData);
            setDataBoundAndMoveCamera();
        }

    }

    private void init(View view) {
        SupportMapFragment mapFragment = (SupportMapFragment) getChildFragmentManager().findFragmentById(R.id.map);
        if (mapFragment != null) {
            mapFragment.getMapAsync(this);
        }
    }

    @Override
    public void onMapReady(GoogleMap gMap) {
        this.googleMap = gMap;
        if (ActivityCompat.checkSelfPermission(requireActivity(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(requireActivity(), Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            googleMap.setMyLocationEnabled(true);
        }
        googleMap.setMapType(GoogleMap.MAP_TYPE_NORMAL);
        googleMap.setOnMarkerClickListener(this);
        if (lstSiteData != null && !lstSiteData.isEmpty()) {
            addMarkers(lstSiteData);
            setDataBoundAndMoveCamera();
        }
    }

    private void setDataBoundAndMoveCamera() {
        LatLngBounds.Builder builder = new LatLngBounds.Builder();
        for (SiteData siteData : lstSiteData) {
            LatLng latLng = new LatLng(siteData.getPropertyLatitude(), siteData.getPropertyLongitude());
            builder.include(latLng);
        }
        int width = getResources().getDisplayMetrics().widthPixels;
        int height = getResources().getDisplayMetrics().heightPixels;
        int padding = (int) (width * 0.10); // offset from edges of the map 10% of screen
        googleMap.animateCamera(CameraUpdateFactory.newLatLngBounds(builder.build(), width, height, padding));
    }

    /*
    Method to filter search site data and redraw map and ploy-gone lines
     */
    void search(String text) {
        if (lstSiteData == null)
            return;
        if (googleMap != null) {
            lstSearchProperty.clear();
            if (text.length() > 0) {
                for (SiteData siteData : lstSiteData) {
                    String typedString = text.trim().toLowerCase();
                    if (siteData.getSiteName().toLowerCase().contains(typedString) || siteData.getPropertyAddress().toLowerCase().contains(typedString)) {
                        lstSearchProperty.add(siteData);
                    }
                }
                addMarkers(lstSearchProperty);

            } else {
                addMarkers(lstSiteData);
            }
        }
    }

    private void addMarkers(List<SiteData> lstSites) {
        if (getActivity() == null || googleMap == null)
            return;
        googleMap.clear();
        if (getParentFragment() != null && ((SitesAndMapListFragment) getParentFragment()).getRouteId() > 0) {
            new AddOrUpdateMarkerAsync(lstSites).execute();
            //addMarkerOnMap(lstSites);
        } else {
            //if (getActivity() != null && AppPref.getBoolean(Constants.PARAM_ROUTES_ACTIVE, false)) {
            mClusterManager = new ClusterManager<>(getActivity(), googleMap);
            // mClusterManager.getAlgorithm().setMaxDistanceBetweenClusteredItems(50);
            mClusterManager.setOnClusterItemInfoWindowClickListener(this);
            mClusterManager.setRenderer(new MapClusterRenderer(getActivity(), googleMap, mClusterManager));
            googleMap.setOnCameraIdleListener(mClusterManager);
            addDataInMarkerItem(lstSites);
        }
    }

    private Bitmap getBitmapWithCount(String title) {
        if (!isDestroyed) {
            LinearLayout inflateVIew = (LinearLayout) this.getLayoutInflater().inflate(R.layout.layout_marker, null, false);
            AppCompatImageView ivMarker = inflateVIew.findViewById(R.id.ivMarker);
            AppCompatTextView tvCount = inflateVIew.findViewById(R.id.tvCount);

            tvCount.setText(title);
            inflateVIew.measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                    View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));
            inflateVIew.layout(0, 0, inflateVIew.getMeasuredWidth(), inflateVIew.getMeasuredHeight());

            inflateVIew.setDrawingCacheEnabled(true);
            inflateVIew.buildDrawingCache();
            return inflateVIew.getDrawingCache();
        }else{
            return null;
        }
    }


    private void addDataInMarkerItem(List<SiteData> lstSites) {
        mClusterManager.clearItems();
        List<MapMarkerItem> lstItems = new ArrayList<>();
        for (SiteData siteData : lstSites) {
            MapMarkerItem markerItem = null;
            try {
                markerItem = new MapMarkerItem(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(), siteData.getSiteName(), siteData.getPropertyAddress());

            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
            if (markerItem != null)
                lstItems.add(markerItem);
        }
        mClusterManager.addItems(lstItems);
    }

    @Override
    public void onDestroy() {
        isDestroyed = true;
        super.onDestroy();

    }

    @Override
    public void onClusterItemInfoWindowClick(MapMarkerItem item) {
        if (getParentFragment() != null) {
            LatLng latLng = item.getPosition();
            ((SitesAndMapListFragment) getParentFragment()).navigateToGoogleMap(latLng.latitude, latLng.longitude);
        }
    }

    @Override
    public boolean onMarkerClick(Marker marker) {
        if (getParentFragment() != null) {
            LatLng latLng = marker.getPosition();
            ((SitesAndMapListFragment) getParentFragment()).navigateToGoogleMap(latLng.latitude, latLng.longitude);
        }
        return false;
    }

    /**
     * Async task to generate and add points in marker option
     */
    protected class AddOrUpdateMarkerAsync extends AsyncTask<Void, Void, List<MarkerOptions>> {
        List<SiteData> lstSitesData;
        AddOrUpdateMarkerAsync(List<SiteData> lstSites){
            this.lstSitesData = lstSites;
        }
        @Override
        protected List<MarkerOptions> doInBackground(Void... params) {
            return  addMarkerOnMap(lstSitesData);

        }

        @Override
        protected void onPostExecute(List<MarkerOptions> result) {
            if (isDetached() || isRemoving()){
                onCancelled();
                return;
            }
            for (int i = 0; i < result.size(); i++) {
                MarkerOptions markerItem = result.get(i);
                try {
                    googleMap.addMarker(markerItem);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
    }

    private List<MarkerOptions> addMarkerOnMap(List<SiteData> lstSites) {
        List<MarkerOptions> lstMarkerOption = new ArrayList<>();
        for (int i = 0; i < lstSites.size(); i++) {
            if (isDestroyed){
                break;
            }
            SiteData siteData = lstSites.get(i);
            MapMarkerItem markerItem;
            try {
                markerItem = new MapMarkerItem(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(), siteData.getSiteName(), siteData.getPropertyAddress());
                MarkerOptions markerOptions = new MarkerOptions();
                markerOptions.position(markerItem.getPosition());
                try {
                    Bitmap bitmap = getBitmapWithCount(String.valueOf(i + 1));
                    if (bitmap != null) {
                        markerOptions.icon(BitmapDescriptorFactory.fromBitmap(bitmap));
                    }
                } catch (RuntimeException e) {
                    FirebaseEventUtils.logException(e);
                }finally {
                    lstMarkerOption.add(markerOptions);
                }

            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return lstMarkerOption;
    }
}