package com.sitefotos.site.screens;

import android.content.Context;
import android.os.Bundle;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentRouteNAllSiteBinding;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.site.adapter.RouteAndAllSiteFragmentViewPagerAdapter;
import com.sitefotos.storage.AppPrefShared;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class RoutesAndAllSitesFragment extends BaseFragment implements View.OnClickListener {

    private Context context;
    boolean isAllSiteSelected;

    RoutesFragment routesFragment;
    private AllSiteListFragment allSiteListFragment;
    private FragmentRouteNAllSiteBinding binding;


    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentRouteNAllSiteBinding.inflate(inflater, container, false);
        init();
        return binding.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    private void init() {
        context = getContext();
        setOnClickListener();
        binding.tlWorkProfile.imgBtnBack.setVisibility(View.GONE);
        setViewPagerAdapter();
        visibleUploadImageViewInScreen();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        visibleUploadImageViewInScreen();
    }


    private void setViewPagerAdapter() {
        routesFragment = new RoutesFragment();
        if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_FORCE_SITES, false)) {
            allSiteListFragment = new AllSiteListFragment();
            RouteAndAllSiteFragmentViewPagerAdapter viewPagerAdapter = new RouteAndAllSiteFragmentViewPagerAdapter(this, routesFragment, allSiteListFragment);
            binding.vpFragment.setAdapter(viewPagerAdapter);
            binding.vpFragment.setOffscreenPageLimit(2);
            binding.vpFragment.setUserInputEnabled(false);
            binding.tlWorkProfile.tvMapView.setVisibility(View.VISIBLE);
            onClickOfList();
        } else {
            RouteAndAllSiteFragmentViewPagerAdapter viewPagerAdapter = new RouteAndAllSiteFragmentViewPagerAdapter(this, routesFragment);
            binding.vpFragment.setAdapter(viewPagerAdapter);
            binding.vpFragment.setOffscreenPageLimit(1);
            binding.vpFragment.setUserInputEnabled(false);
            showOnlyRoute();
        }
    }

    private void showOnlyRoute() {
        binding.vpFragment.setCurrentItem(0);
        binding.tlWorkProfile.tvListView.setTextColor(ContextCompat.getColor(context, R.color.white));
        binding.tlWorkProfile.tvListView.setText(getString(R.string.routes));
        binding.tlWorkProfile.tvListView.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.text_size_large));
        binding.tlWorkProfile.tvListView.setTypeface(ResourcesCompat.getFont(context, R.font.roboto_medium));
        binding.tlWorkProfile.tvListView.setBackgroundResource(R.drawable.transparent_background);
        binding.tlWorkProfile.tvMapView.setVisibility(View.GONE);
        isAllSiteSelected = false;
    }

    private void onClickOfList() {
        if (!AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_FORCE_SITES, false)) {
            return;
        }
        binding.vpFragment.setCurrentItem(0);
        binding.tlWorkProfile.tvListView.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        binding.tlWorkProfile.tvListView.setText(getString(R.string.routes));
        binding.tlWorkProfile.tvListView.setBackgroundResource(R.drawable.maplist_left_selected);
        binding.tlWorkProfile.tvMapView.setTextColor(ContextCompat.getColor(context, R.color.white));
        binding.tlWorkProfile.tvMapView.setBackgroundResource(R.drawable.maplist_right_unselected);
        binding.tlWorkProfile.tvMapView.setText(getString(R.string.sites));
        isAllSiteSelected = false;
    }

    private void onClickOfMap() {
        binding.vpFragment.setCurrentItem(1);
        binding.tlWorkProfile.tvMapView.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        binding.tlWorkProfile.tvListView.setText(getString(R.string.sites));
        binding.tlWorkProfile.tvMapView.setBackgroundResource(R.drawable.maplist_right_selected);
        binding.tlWorkProfile.tvListView.setTextColor(ContextCompat.getColor(context, R.color.white));
        binding.tlWorkProfile.tvListView.setBackgroundResource(R.drawable.maplist_left_unselected);
        binding.tlWorkProfile.tvListView.setText(getString(R.string.routes));
        isAllSiteSelected = true;

    }

    public void updateData() {
        //Call init to set route and site tab if there is any changes from server
        init();
      /*  if (routesFragment != null)
            routesFragment.updateData();
        if (allSiteListFragment != null)
            allSiteListFragment.updateData();*/
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }


    private void setOnClickListener() {
        binding.tlWorkProfile.tvMapView.setOnClickListener(this);
        binding.tlWorkProfile.tvListView.setOnClickListener(this);
        binding.tlWorkProfile.ivUpload.setOnClickListener(this);
        binding.tlWorkProfile.imgBtnBack.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.tvListView) {
            onClickOfList();
        } else if (viewId == R.id.tvMapView) {
            onClickOfMap();
        } else if (viewId == R.id.ivUpload) {
            if (getActivity() != null)
                ((BaseActivity) getActivity()).navigateToUploadActivityScreen(binding.tlWorkProfile.ivUpload);
        }
    }

    public void onFailureResponse() {
        if (getActivity() == null)
            return;
        if (allSiteListFragment != null)
            allSiteListFragment.onFailureResponse();
    }

    public void onNoInternetConnection() {
        if (getActivity() == null)
            return;
        if (allSiteListFragment != null) {
            allSiteListFragment.onNoInternetConnection();
        }
    }

    public void visibleUploadImageViewInScreen() {
        if (getActivity() != null)
            ((BaseActivity) getActivity()).setInVisibilityOfUploadView(binding.tlWorkProfile.ivUpload);
    }

    public boolean isAllSiteSelected() {
        return isAllSiteSelected;
    }
}
