package com.sitefotos.site.screens;

import static com.sitefotos.Constants.SITE_DETAIL_REQUEST_CODE;
import static com.sitefotos.Constants.shouldUpdateSiteDataInList;

import android.content.Context;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentSiteNMapBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.SiteDataUpdateEvent;
import com.sitefotos.event.TMFormCheckInOutEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.Routes;
import com.sitefotos.models.SiteData;
import com.sitefotos.site.adapter.SiteFragmentViewPagerAdapter;
import com.sitefotos.storage.tables.TblRoutes;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PropertyUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class SitesAndMapListFragment extends BaseFragment implements View.OnClickListener {

    private Context context;
    private List<SiteData> lstSites = new ArrayList<>();
    private SiteMapFragment siteMapFragment = new SiteMapFragment();
    private SiteListFragment siteListFragment = new SiteListFragment();
    private String siteIds;
    private String routeName;
    private int routeId;
    public boolean isDataFetched;
    public int selectedNumber;
    double dataFetchedLatitude = 0.0;
    double dataFetchedLongitude = 0.0;
    public FragmentSiteNMapBinding binding;
    private Routes routes;
    private boolean isRouteUnAssigned;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentSiteNMapBinding.inflate(inflater, container, false);
        init();
        return binding.getRoot();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        checkLocationAndUpdateData();
    }

    public void checkLocationAndUpdateData() {
        if (getActivity() != null) {
            if (shouldUpdateSiteDataInList) {
                shouldUpdateSiteDataInList = false;
                getDataFromDatabase(true, false, true);
            }
        }
    }

    private void init() {
        context = getContext();
        getBundleData();
        setOnClickListener();
        setEditTextChangeListener();
        manageSearchViewCloseTouchEvent(binding.edtSearch);
        binding.edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
            /*if (edtSearch.getText() != null && edtSearch.getText().toString().length() > 0) {
                edtSearch.setText("");
            }*/
        if (getActivity() != null)
            ((BaseActivity) getActivity()).setInVisibilityOfUploadView(binding.tlWorkProfile.ivUpload);
        enableOrDisablePullToRefresh();
        getDataFromDatabase(true, false, true);
        shouldUpdateSiteDataInList = false;
    }

    private void setPager() {
        if (getActivity() != null && isAdded()) {
            setViewPagerAdapter();
        }
    }

    private void enableOrDisablePullToRefresh() {
        if (!TextUtils.isEmpty(siteIds) && siteListFragment != null) {
            siteListFragment.stopPullToRefresh();

        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onSiteDataUpdateEvent(SiteDataUpdateEvent event) {
        getDataFromDatabase(false, false, false);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onTMFormCheckInOutEvent(TMFormCheckInOutEvent event) {
        getDataFromDatabase(false, false, false);
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            if (event.response.code() == 304 && siteListFragment != null && !siteListFragment.errorFound) {
                checkAndRefreshData();
                setAdapter(false);
                stopPullToRefresh();
                return;
            }
            if (MainActivity.currentLatitude == 0.0) {
                try {
                    new Handler().postDelayed(() -> {
                        getDataFromDatabase(false, true, false);
                    }, 2000);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            } else {
                getDataFromDatabase(false, true, false);
            }
        } else if (event.throwable != null) {
            checkAndRefreshData();
            onFailureResponse();
        } else if (event.isNoInternetConnection) {
            checkAndRefreshData();
            onNoInternetConnection();
        }
    }

    private void showDataUpdateMessage() {
        if (getActivity() != null && ((MainActivity) getActivity()).activeFragment instanceof SitesAndMapListFragment) {
            showForeGroundToast(getString(R.string.msg_site_data_updated));
        }
    }

    /**
     * Check for 16 meters location distance from last fetched data from DB. if true,
     */
    private void checkAndRefreshData() {
        if (PropertyUtils.distanceToCurrentLocation(dataFetchedLatitude, dataFetchedLongitude) > 16.0f) {
            getDataFromDatabase(false, false, false);
        }
    }

    private void getBundleData() {
        if (getArguments() != null) {
            siteIds = getArguments().getString("siteIds");
            routeName = getArguments().getString("routeName");
            routeId = getArguments().getInt("routeId");
        }
        if (routeId > 0) {
            binding.tlWorkProfile.imgBtnBack.setVisibility(View.VISIBLE);
            TblRoutes tblRoutes = new TblRoutes(context);
            routes = tblRoutes.getDataById(routeId);
            //siteIds = StaticUtils.getStringFromList(routes.getSiteList());
        } else {

            binding.tlWorkProfile.imgBtnBack.setVisibility(View.GONE);
        }
    }

    private void getDataFromDatabase(boolean showProgress, boolean showToast, boolean setPager) {
        if (TextUtils.isEmpty(siteIds)) {
            TblRoutes tblRoutes = new TblRoutes(context);
            if (tblRoutes.getDataCount() == 1) {
                // Show route name is the is single route is assigned
                routeName = tblRoutes.getAllData().get(0).getRouteName();
                setRouteName();
            } else {
                binding.tvRouteName.setVisibility(View.GONE);
            }
        } else {
            setRouteName();

        }
        if (siteListFragment != null) {
            siteListFragment.errorFound = false;
            siteListFragment.isFromRoute(routeId > 0);
        }
        hideOrShowProgressBar(showProgress);
        //check if route is still exist in detail screen
        if (context != null && routeId > 0 && TextUtils.isEmpty(new TblRoutes(context).getDataById(routeId).getRouteId())) {
            // Route is unassigned, show empty view
            isRouteUnAssigned = true;
        }
        new GetDataFromDB(showProgress, showToast, setPager).execute();

    }

    private void setRouteName() {
        binding.tvRouteName.setVisibility(View.VISIBLE);
        binding.tvRouteName.setText(routeName);
    }


    private void hideOrShowProgressBar(boolean shouldShow) {
        try {
            if (shouldShow) {
                binding.pwLoader.setVisibility(View.VISIBLE);
            } else {
                binding.pwLoader.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private void setAdapter(boolean showProgress) {
        try {
            if (lstSites.isEmpty()) {
                if (!showProgress) {
                    showEmptyView();
                }
                binding.edtSearch.setVisibility(View.GONE);
                siteListFragment.updateSiteList(getActivity(), lstSites, showProgress);
            } else {
                binding.edtSearch.setVisibility(View.VISIBLE);
                binding.vpFragment.setVisibility(View.VISIBLE);
                hideOrShowProgressBar(false);
                siteListFragment.updateSiteList(getActivity(), lstSites, false);
            }
            siteMapFragment.updateList(lstSites);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            //this flag will help us to manage ui in SiteListFragment/
            isDataFetched = true;
            siteListFragment.stopPullToRefresh();
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {

        if (getActivity() == null)
            return;
        ((BaseActivity) getActivity()).visibleUploadImageViewInScreen(binding.tlWorkProfile.ivUpload);
    }

    private boolean updateLocationOfSites() {
        return true;
    }

    private Pair<List<SiteData>, Boolean> getSortedList() {
        boolean showProgress = true;
        TblSites tblSites = new TblSites(context);
        if (isRouteUnAssigned) {
            //Route is unassigned now. so show empty view
            lstSites = new ArrayList<>();
        } else {
            if (TextUtils.isEmpty(siteIds)) {
                lstSites = tblSites.getAllData();
            } else {
                lstSites = tblSites.getAllDataByIds(StaticUtils.getListFromString(siteIds));
            }
        }
        if (!lstSites.isEmpty()) {
            showProgress = false;
        }
        if (getActivity() != null && ((MainActivity) getActivity()).canGetLocation(context)) {
            if (routeId > 0) {
                lstSites = PropertyUtils.getClosestDistanceListFromRoute(context, lstSites, routes);
            } else {
                lstSites = PropertyUtils.getClosestDistanceList(context, lstSites);
            }

        } else {
            lstSites = setZeroDistanceInList();
        }
        return new Pair<>(lstSites, showProgress);
    }

    private List<SiteData> setZeroDistanceInList() {
        for (int i = 0; i < lstSites.size(); i++) {
            SiteData siteData = lstSites.get(i);
            siteData.setDistance(0.0);
            lstSites.set(i, siteData);
        }
        return lstSites;
    }

    private void setEditTextChangeListener() {
        binding.edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                setSearchedText(charSequence.toString());
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }

    private void setSearchedText(String searchedText) {
        if (!searchedText.isEmpty()) {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
        } else {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        siteListFragment.searchText(getActivity(), searchedText);
        siteMapFragment.search(searchedText);
    }

    private void setViewPagerAdapter() {
        SiteFragmentViewPagerAdapter siteFragmentviewPagerAdapter = new SiteFragmentViewPagerAdapter(this, siteListFragment, siteMapFragment);
        binding.vpFragment.setAdapter(siteFragmentviewPagerAdapter);
        binding.vpFragment.setOffscreenPageLimit(2);
        binding.vpFragment.setUserInputEnabled(false);
        //This code is added to set screen animation from route fragment
        if (routeId > 0) {
            new Handler().postDelayed(this::onClickOfList, 300);
        } else {
            onClickOfList();
        }

    }

    private void onClickOfList() {
        binding.vpFragment.setCurrentItem(0);
        binding.tlWorkProfile.tvListView.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        binding.tlWorkProfile.tvListView.setBackgroundResource(R.drawable.maplist_left_selected);
        binding.tlWorkProfile.tvMapView.setTextColor(ContextCompat.getColor(context, R.color.white));
        binding.tlWorkProfile.tvMapView.setBackgroundResource(R.drawable.maplist_right_unselected);
        binding.tlWorkProfile.tvListView.setText(getString(R.string.sites));
        binding.tlWorkProfile.tvMapView.setText(getString(R.string.map));
    }

    private void onClickOfMap() {
        binding.vpFragment.setCurrentItem(1);
        binding.tlWorkProfile.tvMapView.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
        binding.tlWorkProfile.tvMapView.setBackgroundResource(R.drawable.maplist_right_selected);
        binding.tlWorkProfile.tvListView.setTextColor(ContextCompat.getColor(context, R.color.white));
        binding.tlWorkProfile.tvListView.setBackgroundResource(R.drawable.maplist_left_unselected);
        binding.tlWorkProfile.tvListView.setText(getString(R.string.sites));
        binding.tlWorkProfile.tvMapView.setText(getString(R.string.map));

    }

    public void updateData() {
        getDataFromDatabase(false, false, false);
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    private void setOnClickListener() {
        binding.tlWorkProfile.tvMapView.setOnClickListener(this);
        binding.tlWorkProfile.tvListView.setOnClickListener(this);
        binding.tlWorkProfile.ivUpload.setOnClickListener(this);
        binding.tlWorkProfile.imgBtnBack.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.tvListView) {
            onClickOfList();
        } else if (view.getId() == R.id.tvMapView) {
            onClickOfMap();
        } else if (view.getId() == R.id.ivUpload) {
            if (getActivity() != null)
                ((BaseActivity) getActivity()).navigateToUploadActivityScreen(binding.tlWorkProfile.ivUpload);
        } else if (view.getId() == R.id.imgBtnBack) {
            if (getActivity() != null) {
                StaticUtils.hideSoftKeyboard(getActivity());
                getActivity().onBackPressed();
            }
        }
    }


    void onPullToRefreshRefresh() {
        WorkFragment workFragment = getInstanceOfWorkFragment();
        if (workFragment != null) {
            workFragment.onPullToRefresh();
        }
        //edtSearch.setText("");
    }

    void stopPullToRefresh() {
        if (siteListFragment != null) {
            siteListFragment.stopPullToRefresh();
        }
    }

    private void showEmptyView() {
        hideOrShowProgressBar(false);
        binding.vpFragment.setVisibility(View.VISIBLE);

        siteListFragment.showEmptyView(false, true);

    }

    void navigateToFormListingScreen(SiteData siteData, boolean ShouldNavigateToMapApp) {
        if (getActivity() == null) {
            return;
        }

        if (ShouldNavigateToMapApp) {
            navigateToGoogleMap(siteData.getPropertyLatitude(), siteData.getPropertyLongitude());
        } else {
            try {
                ((MainActivity) getActivity()).navigateToFormDetailScreen(siteData, null, SITE_DETAIL_REQUEST_CODE, false);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    void navigateToGoogleMap(double latitude, double longitude) {
        if (getActivity() == null) {
            return;
        }
        try {
            ((MainActivity) getActivity()).navigateUserToDefaultMapApp(latitude, longitude);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void onFailureResponse() {
        if (getActivity() == null)
            return;
        binding.edtSearch.setVisibility(View.VISIBLE);
        if (siteListFragment != null)
            siteListFragment.onFailureResponse();
    }

    public void onNoInternetConnection() {
        if (getActivity() == null)
            return;
        binding.edtSearch.setVisibility(View.VISIBLE);
        if (siteListFragment != null) {
            siteListFragment.onNoInternetConnection();
        }
    }

    public void setItemFirst(int position) {
        if (position == -1) {
            binding.vpFragment.setCurrentItem(selectedNumber, false);
        } else {
            selectedNumber = binding.vpFragment.getCurrentItem();
            binding.vpFragment.setCurrentItem(position, false);
        }
    }

    public int getRouteId() {
        return routeId;
    }

    public void setText() {
        binding.edtSearch.setVisibility(View.VISIBLE);
        if (!TextUtils.isEmpty(binding.edtSearch.getText())) {
            setSearchedText(binding.edtSearch.getText().toString());
            binding.edtSearch.setSelection(binding.edtSearch.getText().length());
            if (getActivity() != null) {
                ((BaseActivity) getActivity()).showKeyboard(binding.edtSearch);
            }
        }
    }


    /**
     * Async task to clear preference and truncate DB in separate thread
     */
    protected class GetDataFromDB extends AsyncTask<Void, Void, Pair<List<SiteData>, Boolean>> {

        private boolean showProgress;
        private boolean showToast;
        private boolean setPager;

        public GetDataFromDB(boolean showProgress, boolean showToast, boolean setPager) {
            this.showProgress = showProgress;
            this.showToast = showToast;
            this.setPager = setPager;

        }

        @Override
        protected Pair<List<SiteData>, Boolean> doInBackground(Void... params) {
            return getSortedList();
        }

        @Override
        protected void onPostExecute(Pair<List<SiteData>, Boolean> pairData) {
            if (setPager) {
                setPager();
            }
            lstSites = pairData.first;
            showProgress = pairData.second;
            if (!lstSites.isEmpty()) {
                binding.edtSearch.setEnabled(true);
                setAdapter(false);
                hideOrShowProgressBar(false);
            } else {
                //siteListFragment.disablePullRefresh();
                if (!BaseApplication.getInstance().isOnline(context) || (routeId > 0 && isRouteUnAssigned)) {
                    setAdapter(false);
                } else {
                    setAdapter(showProgress);
                }
            }
            dataFetchedLatitude = MainActivity.currentLatitude;
            dataFetchedLongitude = MainActivity.currentLongitude;

            if (showToast) {
                showDataUpdateMessage();
            }
        }
    }


    /**
     * Async task to clear preference and truncate DB in separate thread
     */
    protected class updateLocationOfSites extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected Boolean doInBackground(Void... params) {
            return updateLocationOfSites();
        }

        @Override
        protected void onPostExecute(Boolean result) {

        }
    }
}
