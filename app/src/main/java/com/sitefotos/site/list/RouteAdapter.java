package com.sitefotos.site.list;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemRoutesBinding;
import com.sitefotos.models.Routes;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.List;

public class RouteAdapter extends RecyclerView.Adapter<RouteAdapter.ViewHolder> {


    private List<Routes> lstRoute;
    private final ViewClicked viewClicked;
    private final Context context;
    private ItemRoutesBinding binding;


    public interface ViewClicked {
        void onItemClicked(int position, View view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvTitle;
        AppCompatTextView tvCategory;
        public ViewHolder(ItemRoutesBinding binding) {
            super(binding.getRoot());
            tvTitle = binding.tvTitle;
            tvCategory = binding.tvCategory;
        }
    }

    public RouteAdapter(Context context, List<Routes> lstRouteData, ViewClicked viewClicked) {
        this.context = context;
        this.lstRoute = lstRouteData;
        this.viewClicked = viewClicked;

    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemRoutesBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<Routes> lstSiteData) {
        this.lstRoute = lstSiteData;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        try {
            if (lstRoute == null)
                return;
            Routes data = lstRoute.get(position);
            holder.tvTitle.setText(context.getString(R.string.routes_name, "  ".concat(data.getRouteName())));
            holder.tvCategory.setText(context.getString(R.string.route_category, "  ".concat(data.getRouteCategory())));
            holder.itemView.setOnClickListener(view -> viewClicked.onItemClicked(position, view));
        } catch (IndexOutOfBoundsException e) {
            FirebaseEventUtils.logException(e);
        }

    }

    @Override
    public int getItemCount() {
        return lstRoute.size();
    }

}
