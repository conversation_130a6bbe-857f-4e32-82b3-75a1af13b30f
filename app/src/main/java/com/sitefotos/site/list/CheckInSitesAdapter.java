package com.sitefotos.site.list;

import android.annotation.SuppressLint;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.divider.MaterialDivider;
import com.sitefotos.R;
import com.sitefotos.databinding.ItemPropertyBinding;
import com.sitefotos.models.SiteData;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.List;
import java.util.Locale;

public class CheckInSitesAdapter extends RecyclerView.Adapter<CheckInSitesAdapter.ViewHolder> {
    private List<SiteData> lstSiteData;
    private final ViewClicked viewClicked;
    private ItemPropertyBinding binding;

    public interface ViewClicked {
        void onItemClicked(int position, View view);
        void onMarkerClicked(int position, View view);
        void onItemLongClicked(int position, View view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvTitle;
        AppCompatTextView tvAddress;
        AppCompatTextView tvAddress2;
        AppCompatTextView tvMiles;
        LinearLayout llProgressTitle;
        MaterialDivider bottomDivider;
        AppCompatImageView ivFormCompleteStatus;
        LinearLayout llLeft;
        LinearLayout llRight;
        public ViewHolder(ItemPropertyBinding binding) {
            super(binding.getRoot());
            tvTitle = binding.tvTitle;
            tvAddress = binding.tvAddress;
            tvAddress2 = binding.tvAddress2;
            tvMiles = binding.tvMiles;
            llProgressTitle = binding.llProgressTitle;
            bottomDivider = binding.bottomDivider;
            ivFormCompleteStatus = binding.ivFormCompleteStatus;
            llLeft = binding.llLeft;
            llRight = binding.llRight;
        }
    }

    public CheckInSitesAdapter(List<SiteData> lstProperty, ViewClicked viewClicked) {
        this.lstSiteData = lstProperty;
        this.viewClicked = viewClicked;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding  = ItemPropertyBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    @SuppressLint("NotifyDataSetChanged")
    public void updateList(List<SiteData> lstSiteData) {
        this.lstSiteData = lstSiteData;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        try {
            if (lstSiteData == null)
                return;
            SiteData siteData = lstSiteData.get(position);
            holder.tvTitle.setText(siteData.getSiteName());
            holder.tvAddress.setText(siteData.getPropertyAddress());
            if (siteData.getPropertiesVo() != null)
                holder.tvAddress2.setText(StaticUtils.concatString(siteData.getPropertiesVo().getPropertyCity(), siteData.getPropertiesVo().getPropertyState(), siteData.getPropertiesVo().getPropertyZip()));

            if (siteData.isAllFormCompleted()) {
                holder.ivFormCompleteStatus.setVisibility(View.VISIBLE);
            } else {
                holder.ivFormCompleteStatus.setVisibility(View.INVISIBLE);
            }

            if (siteData.getDistance() == 0.0) {
                holder.tvMiles.setVisibility(View.GONE);
            } else {
                holder.tvMiles.setVisibility(View.VISIBLE);
                String distance = StaticUtils.getDistanceByUnit(holder.itemView.getContext(),siteData.getDistance());
                holder.tvMiles.setText(distance);
            }

            holder.tvTitle.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
            holder.tvAddress.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
            holder.tvAddress2.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
            holder.tvMiles.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
            holder.llProgressTitle.setVisibility(View.GONE);
            holder.bottomDivider.setVisibility(View.GONE);


            //here adding more long listener because itemView's long click listener not working of child having its own click listener event
            holder.llLeft.setOnLongClickListener(view -> {
                viewClicked.onItemLongClicked(position, view);
                return false;
            });
            holder.llRight.setOnLongClickListener(view -> {
                viewClicked.onItemLongClicked(position, view);
                return false;
            });
            holder.itemView.setOnLongClickListener(view -> {
                viewClicked.onItemLongClicked(position, view);
                return false;
            });
            holder.llLeft.setOnClickListener(view -> viewClicked.onItemClicked(position, view));
            holder.llRight.setOnClickListener(view -> viewClicked.onMarkerClicked(position, view));
        } catch (IndexOutOfBoundsException e) {
            FirebaseEventUtils.logException(e);
        }

    }

    @Override
    public int getItemCount() {
        return lstSiteData.size();
    }

}
