package com.sitefotos.site.list;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.divider.MaterialDivider;
import com.sitefotos.R;
import com.sitefotos.databinding.ItemPropertyBinding;
import com.sitefotos.models.SiteData;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class SiteListingAdapter extends RecyclerView.Adapter<SiteListingAdapter.ViewHolder> {
    private List<SiteData> lstSiteData;
    private final ViewClicked viewClicked;
    private List<Long> lstCheckedSite;
    private ItemPropertyBinding binding;
    private long lastInProgressItemId;
    private final boolean fromRoute;

    public interface ViewClicked {
        void onItemClicked(int position, View view);
        void onMarkerClicked(int position, View view);
        void onItemLongClicked(int position, View view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvTitle;
        AppCompatTextView tvAddress;
        AppCompatTextView tvAddress2;
        AppCompatTextView tvMiles;
        LinearLayout llProgressTitle;
        MaterialDivider bottomDivider;
        AppCompatImageView ivFormCompleteStatus;
        LinearLayout llLeft;
        LinearLayout llRight;
        public ViewHolder(ItemPropertyBinding binding) {
            super(binding.getRoot());
            tvTitle = binding.tvTitle;
            tvAddress = binding.tvAddress;
            tvAddress2 = binding.tvAddress2;
            tvMiles = binding.tvMiles;
            llProgressTitle = binding.llProgressTitle;
            bottomDivider = binding.bottomDivider;
            ivFormCompleteStatus = binding.ivFormCompleteStatus;
            llLeft = binding.llLeft;
            llRight = binding.llRight;
        }
    }

    public SiteListingAdapter(List<SiteData> lstProperty, List<Long> lstCheckedSite,boolean fromRoute, ViewClicked viewClicked) {
        if (!fromRoute) {
            filterList(lstProperty,lstCheckedSite);
        }
        this.fromRoute = fromRoute;
        this.lstSiteData = lstProperty;
        this.lstCheckedSite = lstCheckedSite;
        this.viewClicked = viewClicked;

    }

    private void filterList(List<SiteData> lstProperty, List<Long> lstCheckedSite) {

        List<SiteData> checkedSites = new ArrayList<>();

        for (SiteData site : new ArrayList<>(lstProperty)) {
            if (lstCheckedSite.contains(site.getSiteId())) {
                lstProperty.remove(site);
                checkedSites.add(site);
                lastInProgressItemId = site.getSiteId();
            }
        }

        lstProperty.addAll(0, checkedSites);
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding  = ItemPropertyBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<SiteData> lstSiteData, List<Long> lstCheckedSite) {
        if (!fromRoute) {
            filterList(lstSiteData,lstCheckedSite);
        }
        this.lstSiteData = lstSiteData;
        this.lstCheckedSite = lstCheckedSite;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        try {
            if (lstSiteData == null)
                return;
            SiteData siteData = lstSiteData.get(position);
            holder.tvTitle.setText(siteData.getSiteName());
            holder.tvAddress.setText(siteData.getPropertyAddress());
            if (siteData.getPropertiesVo() != null)
                holder.tvAddress2.setText(StaticUtils.concatString(siteData.getPropertiesVo().getPropertyCity(), siteData.getPropertiesVo().getPropertyState(), siteData.getPropertiesVo().getPropertyZip()));

            if (siteData.isAllFormCompleted()) {
                holder.ivFormCompleteStatus.setVisibility(View.VISIBLE);
            } else {
                holder.ivFormCompleteStatus.setVisibility(View.INVISIBLE);
            }

            if (siteData.getDistance() == 0.0) {
                holder.tvMiles.setVisibility(View.GONE);
            } else {
                holder.tvMiles.setVisibility(View.VISIBLE);
                String distance = StaticUtils.getDistanceByUnit(holder.itemView.getContext(),siteData.getDistance());
                holder.tvMiles.setText(distance);
            }

            if (lstCheckedSite.contains(siteData.getSiteId())) {
                holder.tvTitle.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
                holder.tvAddress.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
                holder.tvAddress2.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
                holder.tvMiles.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
                if (position == 0 && !fromRoute) {
                    holder.llProgressTitle.setVisibility(View.VISIBLE);
                } else {
                    holder.llProgressTitle.setVisibility(View.GONE);
                }

                if (siteData.getSiteId() == lastInProgressItemId && !fromRoute) {
                    holder.bottomDivider.setVisibility(View.VISIBLE);
                } else {
                    holder.bottomDivider.setVisibility(View.GONE);
                }

            } else {
                holder.tvTitle.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.black));
                holder.tvAddress.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.gray));
                holder.tvAddress2.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.gray));
                holder.tvMiles.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.gray));
                holder.llProgressTitle.setVisibility(View.GONE);
                holder.bottomDivider.setVisibility(View.GONE);
            }
            //here adding more long listener because itemView's long click listener not working of child having its own click listener event
            holder.llLeft.setOnLongClickListener(view -> {
                viewClicked.onItemLongClicked(position, view);
                return false;
            });
            holder.llRight.setOnLongClickListener(view -> {
                viewClicked.onItemLongClicked(position, view);
                return false;
            });
            holder.itemView.setOnLongClickListener(view -> {
                viewClicked.onItemLongClicked(position, view);
                return false;
            });
            holder.llLeft.setOnClickListener(view -> viewClicked.onItemClicked(position, view));
            holder.llRight.setOnClickListener(view -> viewClicked.onMarkerClicked(position, view));
        } catch (IndexOutOfBoundsException e) {
            FirebaseEventUtils.logException(e);
        }

    }

    @Override
    public int getItemCount() {
        return lstSiteData.size();
    }

}
