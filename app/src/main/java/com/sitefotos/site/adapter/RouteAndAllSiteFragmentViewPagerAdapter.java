package com.sitefotos.site.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.sitefotos.site.screens.AllSiteListFragment;
import com.sitefotos.site.screens.RoutesFragment;


public class RouteAndAllSiteFragmentViewPagerAdapter extends FragmentStateAdapter {
    private RoutesFragment routesFragment;
    private AllSiteListFragment allSiteListFragment;
    private int dataSize =0;

    public RouteAndAllSiteFragmentViewPagerAdapter(Fragment fragment, RoutesFragment routesFragment, AllSiteListFragment allSiteListFragment) {
        super(fragment);
        this.routesFragment = routesFragment;
        this.allSiteListFragment = allSiteListFragment;
        dataSize = 2;
    }

    public RouteAndAllSiteFragmentViewPagerAdapter(Fragment fragment, RoutesFragment routesFragment) {
        super(fragment);
        this.routesFragment = routesFragment;
        dataSize = 1;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return routesFragment;
            case 1:
                return allSiteListFragment;
        }
        return routesFragment;
    }

    @Override
    public int getItemCount() {
        return dataSize;
    }
}
