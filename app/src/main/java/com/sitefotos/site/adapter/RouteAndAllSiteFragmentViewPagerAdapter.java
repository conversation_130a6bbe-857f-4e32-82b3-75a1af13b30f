package com.sitefotos.site.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.sitefotos.site.screens.AllSiteListFragment;
import com.sitefotos.site.screens.RoutesFragment;


public class RouteAndAllSiteFragmentViewPagerAdapter extends FragmentStateAdapter {
    private int dataSize = 0;
    private boolean hasAllSiteListFragment;

    public RouteAndAllSiteFragmentViewPagerAdapter(Fragment fragment, boolean hasAllSiteListFragment) {
        super(fragment);
        this.hasAllSiteListFragment = hasAllSiteListFragment;
        dataSize = hasAllSiteListFragment ? 2 : 1;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new RoutesFragment();
            case 1:
                return new AllSiteListFragment();
        }
        return new RoutesFragment();
    }

    @Override
    public int getItemCount() {
        return dataSize;
    }
}
