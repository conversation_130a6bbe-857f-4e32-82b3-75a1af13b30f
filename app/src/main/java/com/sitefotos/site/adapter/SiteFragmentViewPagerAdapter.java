package com.sitefotos.site.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.sitefotos.site.screens.SiteListFragment;
import com.sitefotos.site.screens.SiteMapFragment;


public class SiteFragmentViewPagerAdapter extends FragmentStateAdapter {
    private final int dataSize;

    public SiteFragmentViewPagerAdapter(Fragment fragment) {
        super(fragment);
        dataSize = 2;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new SiteListFragment();
            case 1:
                return new SiteMapFragment();
        }
        return new SiteListFragment();
    }

    @Override
    public int getItemCount() {
        return dataSize;
    }
}
