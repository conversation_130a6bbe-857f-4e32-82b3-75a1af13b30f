package com.sitefotos.site.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.sitefotos.site.screens.SiteListFragment;
import com.sitefotos.site.screens.SiteMapFragment;


public class SiteFragmentViewPagerAdapter extends FragmentStateAdapter {
    private final SiteListFragment siteListFragment;
    private final SiteMapFragment siteMapFragment;
    private final int dataSize;

    public SiteFragmentViewPagerAdapter(Fragment fragment, SiteListFragment siteListFragment, SiteMapFragment siteMapFragment) {
        super(fragment);
        this.siteListFragment = siteListFragment;
        this.siteMapFragment = siteMapFragment;
        dataSize = 2;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return siteListFragment;
            case 1:
                return siteMapFragment;
        }
        return siteListFragment;
    }

    @Override
    public int getItemCount() {
        return dataSize;
    }
}
