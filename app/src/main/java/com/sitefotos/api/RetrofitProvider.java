package com.sitefotos.api;


import static com.sitefotos.Constants.BASE_URL;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.sitefotos.BuildConfig;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.logger.CustomLogKt;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;
import retrofit2.converter.scalars.ScalarsConverterFactory;

public class RetrofitProvider {
    private static Retrofit retrofit;
    private static Retrofit retrofitString;
    private static Retrofit retrofitDynamic;
    private static OkHttpClient okHttpClientDynamic;
    private static OkHttpClient okHttpClient;


    private static HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(message -> {
        CustomLogKt.errorLog("response", message);
    }).setLevel(HttpLoggingInterceptor.Level.BODY);


    private static OkHttpClient getHttpClient() {

        if (okHttpClient == null) {
            okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(1, TimeUnit.MINUTES)
                    .readTimeout(1, TimeUnit.MINUTES)
                    .addInterceptor(loggingInterceptor)
                    .build();
        }
        return okHttpClient;

    }

    private static OkHttpClient getHttpClientDynamic() {

        if (okHttpClientDynamic == null) {
            okHttpClientDynamic = new OkHttpClient.Builder()
                    .connectTimeout(3, TimeUnit.MINUTES)
                    .readTimeout(3, TimeUnit.MINUTES)
                    .retryOnConnectionFailure(false)
                    .build();
        }
        return okHttpClientDynamic;

    }


    /**
     * Method to return retrofit instance. This method will retrofit retrofit instance with app api Base url.
     *
     * @return instance of ad retrofit.
     */

    private static Retrofit getRetrofit() {
        if (retrofit == null || !retrofit.baseUrl().url().toString().equals(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))) {
            Gson gson = new GsonBuilder()
                    .setLenient()
                    .create();

            try {
                retrofit = new Retrofit.Builder()
                        .baseUrl(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))
                        .client(getHttpClient())
                        .addConverterFactory(GsonConverterFactory.create(gson))
                        .build();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
                if (e.getMessage() != null)
                    CustomLogKt.errorLog("Retrofit error", "Error:" + e.getMessage());
            }
        }
        return retrofit;

    }


    /**
     * Method to return retrofit instance. This method will retrofit retrofit instance with app api Base url.
     *
     * @return instance of ad retrofit.
     */

    private static Retrofit getRetrofitString() {
        if (retrofitString == null || !retrofitString.baseUrl().url().toString().equals(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))) {
            try {
                retrofitString = new Retrofit.Builder()
                        .baseUrl(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))
                        .client(getHttpClient())
                        .addConverterFactory(ScalarsConverterFactory.create())
                        .build();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
                if (e.getMessage() != null)
                    CustomLogKt.errorLog("Retrofit error", "Error:" + e.getMessage());
            }
        }
        return retrofitString;
    }


    /**
     * Method to return retrofit instance. This method will retrofit retrofit instance with app api Base url.
     *
     * @return instance of ad retrofit.
     */

    private static Retrofit getRetrofitDynamicUrl() {
        if (retrofitDynamic == null || !retrofitDynamic.baseUrl().url().toString().equals(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))) {
            retrofitDynamic = new Retrofit.Builder()
                    .baseUrl(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))
                    .client(getHttpClientDynamic())
                    .build();
        }
        return retrofitDynamic;
    }


    public static <S> S createService(Class<S> serviceClass) {
        return getRetrofit().create(serviceClass);
    }

    public static <S> S createServiceString(Class<S> serviceClass) {
        return getRetrofitString().create(serviceClass);
    }

    public static <S> S createServiceDynamic(Class<S> serviceClass) {
        return getRetrofitDynamicUrl().create(serviceClass);
    }

    public static void resetInstance() {
        Gson gson = new GsonBuilder()
                .setLenient()
                .create();
        retrofit = new Retrofit.Builder()
                .baseUrl(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))
                .client(getHttpClient())
                .addConverterFactory(GsonConverterFactory.create(gson))
                .build();
        retrofitString = new Retrofit.Builder()
                .baseUrl(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))
                .client(getHttpClient())
                .addConverterFactory(ScalarsConverterFactory.create())
                .build();
        retrofitDynamic = new Retrofit.Builder()
                .baseUrl(AppPrefShared.getString(BASE_URL, BuildConfig.BASE_URL))
                .client(getHttpClientDynamic())
                .build();
    }

}
