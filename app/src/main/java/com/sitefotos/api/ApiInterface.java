package com.sitefotos.api;


import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.SimpleResponse;
import com.sitefotos.models.UploadImageUrlResponse;

import java.util.HashMap;
import java.util.Map;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import retrofit2.http.QueryMap;
import retrofit2.http.Url;

public interface ApiInterface {

    @GET("vpics/getuploadurl/vid/{vid}")
    Call<UploadImageUrlResponse> requestServerForGetUploadURL(@Path("vid") String vid);

    @FormUrlEncoded
    @POST("/node/app/upload-site")
    Call<String> requestToSaveNewProperty(@FieldMap Map<String, Object> body);


    @FormUrlEncoded
    @POST("/vpics/uploadformv4")
    Call<SimpleResponse> requestToSaveForm(@FieldMap Map<String, Object> body);


    @POST("/vpics/getweatherdata")
    Call<String> getWeatherApi(@QueryMap Map<String, Object> body);

    @GET("/mobile/app-data") // made changes from app version 2.4.7 (/mobile/app-data)
    Call<AppDataResponse> getAppData(@Header ("stag") String stag, @QueryMap Map<String, Object> queryMap);

    @FormUrlEncoded
    @POST("vpics/uploadimage")
    Call<String> requestToPostImageUploadData(@FieldMap Map<String, Object> body);

    @FormUrlEncoded
    @POST("/vpics/uploadbreadcrumb")
    Call<SimpleResponse> requestToPostBreadCrumbsData(@FieldMap Map<String, Object> body);

    @FormUrlEncoded
    @POST("/vpics/uploadtmdatav3")
    Call<SimpleResponse> requestToSubmitWorkLogProfileData(@FieldMap Map<String, Object> body);

    @FormUrlEncoded
    @POST("/vpics/uploadcrew")
    Call<SimpleResponse> requestToPostCrewData(@FieldMap HashMap<String, Object> map);

    @FormUrlEncoded
    @POST("/vpics/uploadissuev3")
    Call<SimpleResponse> requestToSubmitIssueData(@FieldMap Map<String, Object> body);

    @PUT
    Call<ResponseBody> uploadImage(@Url String url, @Body RequestBody body);

    @POST("/mobile/register-push-token")
    //Added this api in app version: 2.4.6
    Call<SimpleResponse> updateFcmToken(@Body Map<String, Object> body);
}


