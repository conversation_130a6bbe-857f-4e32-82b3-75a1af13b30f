package com.sitefotos.camera;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.sitefotos.R;

import java.util.List;

public class PropertyListAdapter extends BaseAdapter {

    public Context activity;
    private Context context;
    private List<PropertiesVo> list;
    private LayoutInflater inflater;
    private OnItemClickedListener onItemClickedListener;

    public PropertyListAdapter(Context context, List<PropertiesVo> list, OnItemClickedListener onItemClickedListener) {
        this.context = context;
        this.list = list;
        this.onItemClickedListener = onItemClickedListener;
        this.inflater = LayoutInflater.from(context);
        this.activity = context;
    }

    @Override
    public int getCount() {
        return list.size();
    }

    @Override
    public Object getItem(int position) {
        return list.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {

        viewHolder holder = new viewHolder();
        if (convertView == null) {
            inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            if (inflater != null) {
                convertView = inflater.inflate(R.layout.layout_list_item, parent, false);
                holder.textView = convertView.findViewById(R.id.txtListItem);

                convertView.setTag(holder);
            }

        } else {
            holder = (viewHolder) convertView.getTag();
        }
        holder.textView.setText(list.get(position).getPropertyName());
        holder.textView.setOnClickListener(v -> onItemClickedListener.setOnItemClicked(position));
        return convertView;
    }

    public interface OnItemClickedListener {
        void setOnItemClicked(int position);
    }

    public class viewHolder {
        TextView textView;
    }
}
