package com.sitefotos.camera;

public class PropertiesVo {

    private String mb_nickname = "";
    private String mb_address1 = "";
    private String cityname = "";
    private String statename = "";
    private String mb_zip_code = "";
    private String propertyEmails = "";
    private String mb_geo = "";
    private long mb_id = 0;
    private String propertyLat = "";
    private String propertyLng = "";
    private boolean isAutoshare;
    private boolean isPolygoneContainsLatLong;
    private double lat;
    private double lon;

    public double getLat() {
        return lat;
    }

    public double getLon() {
        return lon;
    }

    public void setLat(double lat) {
        this.lat = lat;
    }

    public void setLon(double lon) {
        this.lon = lon;
    }

    public double distanceInMeters = 0;


    public double getDistanceInMeters() {
        return distanceInMeters;
    }

    public void setDistanceInMeters(double distanceInMeters) {
        this.distanceInMeters = distanceInMeters;
    }

    public String getPropertyAddress() {
        return mb_address1;
    }

    public void setPropertyAddress(String propertyAddress) {
        this.mb_address1 = propertyAddress;
    }

    public String getPropertyName() {
        return mb_nickname;
    }

    public void setPropertyName(String propertyName) {
        this.mb_nickname = propertyName;
    }

    public String getPropertyCity() {
        return cityname;
    }

    public void setPropertyCity(String propertyCity) {
        this.cityname = propertyCity;
    }

    public String getPropertyState() {
        return statename;
    }

    public void setPropertyState(String propertyState) {
        this.statename = propertyState;
    }

    public String getPropertyZip() {
        return mb_zip_code;
    }

    public void setPropertyZip(String propertyZip) {
        this.mb_zip_code = propertyZip;
    }

    public String getPropertyEmails() {
        return propertyEmails;
    }

    public void setPropertyEmails(String propertyEmails) {
        this.propertyEmails = propertyEmails;
    }

    public String getPropertyGeo() {
        return mb_geo;
    }

    public void setPropertyGeo(String propertyGeo) {
        this.mb_geo = propertyGeo;
    }

    public long getPropertyId() {
        return mb_id;
    }

    public void setPropertyId(long propertyId) {
        this.mb_id = propertyId;
    }

    public String getPropertyLat() {
        return propertyLat;
    }

    public void setPropertyLat(String propertyLat) {
        this.propertyLat = propertyLat;
    }

    public String getPropertyLng() {
        return propertyLng;
    }

    public void setPropertyLng(String propertyLng) {
        this.propertyLng = propertyLng;
    }

    public boolean getIsAutoshare() {
        return isAutoshare;
    }

    public void setIsAutoshare(boolean isAutoshare) {
        this.isAutoshare = isAutoshare;
    }

    public boolean isPolygoneContainsLatLong() {
        return isPolygoneContainsLatLong;
    }

    public void setPolygoneContainsLatLong(boolean polygoneContainsLatLong) {
        isPolygoneContainsLatLong = polygoneContainsLatLong;
    }
}
