package com.sitefotos.camera;

import static com.sitefotos.Constants.CAMERA_PERMISSIONS_REQUEST;
import static com.sitefotos.Constants.IMAGEPATHHIGH;
import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.LOCATION_PERMISSION_REQUEST;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.LOGGED_IN_USER_PARAM_SKIP_GEO;
import static com.sitefotos.Constants.LOGGED_IN_USER_TAGS;
import static com.sitefotos.util.MyCameraUtils.getTagData;
import static com.sitefotos.util.MyCameraUtils.resetTagData;
import static com.sitefotos.util.MyCameraUtils.updateTagCount;
import static com.sitefotos.util.PermissionUtils.CAMERA_PERMISSION;
import static com.sitefotos.util.PermissionUtils.hasStoragePermissions;
import static com.sitefotos.util.PermissionUtils.navigateUserToPermissionScreen;

import android.animation.LayoutTransition;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.location.Location;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;

import com.otaliastudios.cameraview.CameraListener;
import com.otaliastudios.cameraview.CameraOptions;
import com.otaliastudios.cameraview.PictureResult;
import com.otaliastudios.cameraview.controls.Facing;
import com.otaliastudios.cameraview.controls.Flash;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.NetWorkChangeReceiver;
import com.sitefotos.R;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.asynk.UploadMultipleImagesTask;
import com.sitefotos.databinding.ActivityCameraBinding;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.gallery.GalleryActivity;
import com.sitefotos.gallery.MimeType;
import com.sitefotos.gallery.SelectionSpec;
import com.sitefotos.interfaces.OnTagSelected;
import com.sitefotos.main.MainActivity;
import com.sitefotos.map.MapViewFragment;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.FormData;
import com.sitefotos.models.Tags;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.MyCameraUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PolygonCalculation;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.PropertyUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;
import com.sitefotos.util.views.CameraDrawingView;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CameraActivity extends BaseActivity implements View.OnClickListener {

    public CameraDrawingView myViewDrawing;
    public int colorSelected, position = 0;
    public Bitmap bmpresizedImageTakenFromCamera;
    public Bitmap bitmapCapturedImageWithDrawing, bitmapCapturedImageWithDrawingLow;
    public boolean isGalleryMode = false;
    public boolean isEditModeDrawing = false, isEditModeCaption = false, isPreviewMode = false, isDialogueToShow = false;
    public List<PropertiesVo> lstProperty = new ArrayList<>();
    public String mBuildingId = "0", buildingName = "";
    public String dateExifImageCaptured = "";
    public Boolean isGpsEnableCalled = false;
    public boolean isPermissionCameraCalled = false, isPermissionLocationCalled = false;
    public String strWeather = "", strCaption = "";
    // Get exif data from ExifInterface
    public Location mLocationExif = null;
    List<String> lstIgnoreList = new ArrayList<>();
    MapViewFragment mapFragment;
    String strMediaPath = "";
    private CameraActivity context;
    public ActivityCameraBinding binding;
    private boolean isPlotOnMap;
    private boolean isFromMap;

    private NetWorkChangeReceiver netWorkChangeReceiver;
    private FormData formData;
    private boolean isTMForm;
    private int tagId;
    private boolean isIssue;
    private boolean isGalleryClicked;
    private long siteId;
    private CamListener camListener;
    private List<Tags> lstTags = new ArrayList<>();
    public ExecutorService dbExecutorService = Executors.newSingleThreadExecutor();
    private BroadcastReceiver formUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (context != null) {
                if (intent.hasExtra(Constants.DELETED_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_FORM_ALL_DATA, false)) {
                        setResultForDeletedForm();
                    }
                }
                if (intent.hasExtra(Constants.DELETED_FORM_LIST)) {
                    if (formData != null) {
                        ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_FORM_LIST);
                        for (Integer formId : lstDeletedFormId != null ? lstDeletedFormId : new ArrayList<Integer>()) {
                            if (formId == formData.getFormId()) {
                                setResultForDeletedForm();
                            }
                        }
                    }
                }


                if (intent.hasExtra(Constants.DELETED_WP_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_WP_FORM_ALL_DATA, false)) {
                        setResultForDeletedForm();
                    }
                }
                if (intent.hasExtra(Constants.DELETED_WP_LIST)) {

                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_WP_LIST);
                    for (Integer formId : lstDeletedFormId) {
                        if (formId == formData.getFormId()) {
                            setResultForDeletedForm();
                        }
                    }
                }
            }
        }
    };

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.black));
        context = this;
        initBinding();
        registerAppReceiver(formUpdateReceiver, new IntentFilter(Constants.INTERNAL_FORM_BROADCAST));
        registerBroadCastReceiver();
        getBundleData();
        enableLayoutTransition();

        getPermissionToFineLocation(false);
        binding.imgBtnFormCancel.setVisibility(View.VISIBLE);
        binding.imgBtnImageUpload.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnShare.setVisibility(View.GONE);
        init();
        setOnClickListener();
        AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());
    }


    private void initBinding() {
        binding = ActivityCameraBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void registerBroadCastReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.UPLOAD_IMAGE_RESULT_PROGRESS);
    }

    private void getBundleData() {
        if (getIntent() != null) {
            if (getIntent().getExtras() != null && getIntent().getExtras().containsKey("tag")) {
                tagId = getIntent().getExtras().getInt("tag", -1);
                isIssue = getIntent().getBooleanExtra("isIssue", false);
                int formId = getIntent().getExtras().getInt("mFormPkId", 0);
                isTMForm = getIntent().getBooleanExtra("isTnm", false);
                if (formId > 0) {
                    if (isTMForm) {
                        TblTMForms tblForms = new TblTMForms(CameraActivity.this);
                        formData = tblForms.getFormDataByPKId(formId);
                    } else {
                        TblForms tblForms = new TblForms(CameraActivity.this);
                        formData = tblForms.getFormDataByPKId(formId);
                    }
                }
                siteId = getIntent().getLongExtra("siteId", 0L);
                isPlotOnMap = getIntent().getBooleanExtra("isPlotOnMap", false);
                isFromMap = getIntent().getBooleanExtra("isFromMap", false);
            }
        }
    }

    public void init() {
        initCameraPermissionGranted();
        setUpCamera();
    }

    /**
     * Function to enable layout change animation to show or hide target locator view.
     */
    private void enableLayoutTransition() {
        LayoutTransition layoutTransition = new LayoutTransition();
        layoutTransition.enableTransitionType(LayoutTransition.CHANGING);
        layoutTransition.setAnimateParentHierarchy(true);
        binding.mapLayout.rlCameraMapLocatorView.setLayoutTransition(layoutTransition);
    }

    private void setUpCamera() {
        if (camListener != null) {
            binding.cameraView.camera.removeCameraListener(camListener);
        }
        camListener = new CamListener();
        binding.cameraView.camera.setLifecycleOwner(this);
        binding.cameraView.camera.addCameraListener(camListener);
        checkAndUpdateCameraFace();
    }

    /**
     * Remove camera view and add again at position 0 to manage camera orientation
     */
    private void resetCameraView() {
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            binding.cameraView.camera.close();
            binding.cameraView.camera.open();
        }
    }

    private void initCameraPermissionGranted() {
        // camera preview surface variables
        progressBarWebView.setVisibility(View.GONE);
        handleVisibilityOFElements(true);
    }

    private void checkAndUpdateCameraFace() {

        Collection<Facing> facings;
        try {
            if (binding.cameraView.camera.getCameraOptions() == null)
                return;
            facings = binding.cameraView.camera.getCameraOptions().getSupportedFacing();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            return;
        }

        if (facings.size() == 1 && facings.contains(Facing.BACK)) {
            binding.cameraView.camera.setFacing(Facing.BACK);
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
        } else if (facings.size() == 1 && facings.contains(Facing.FRONT)) {
            binding.cameraView.camera.setFacing(Facing.FRONT);
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
        } else {
            if (AppPrefShared.getBoolean(Constants.CAMERA_FLIP_MODE, false)) {
                binding.cameraView.camera.setFacing(Facing.FRONT);
            } else {
                binding.cameraView.camera.setFacing(Facing.BACK);
            }
            binding.imgBtnFlipCamera.setVisibility(View.VISIBLE);
        }
        hasFlash();
    }


    @Override
    protected void onStart() {
        super.onStart();
        startLocationServiceIfNotStarted();
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (isGalleryClicked) {
            isGalleryClicked = false;
            return;
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            if (netWorkChangeReceiver == null)
                netWorkChangeReceiver = new NetWorkChangeReceiver();
            registerAppReceiver(netWorkChangeReceiver, new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION));
        }

        binding.cameraPreview.imgBtnWeather.setSelected(!canGetLocation(context));
        binding.cameraPreview.imgBtnWeather.setVisibility(View.VISIBLE);
        if (binding.cameraView.camera.getCameraOptions() == null) {
            init();
        }
    }

    @Override
    protected void onStop() {
        isPermissionCameraCalled = false;
        super.onStop();
    }

    private void checkAndStartUploadProcess() {
        if (checkSDCardPermission()) {
            if (checkFoPropertyTag()) {
                askUserToRefreshLocation();
            } else {
                // Here we can check if plot on map is enable and updatedExtra lat lang value is 0.0 it means user is
                // not added marker on map, so we display a dialog to notify user to add at least one marker on map
                // if plotOnMap is disabled old flow is working as it is.
                if (isPlotOnMap) {
                    if (mapFragment != null && mapFragment.updatedExtraLatitude != 0.0 && mapFragment.updatedExtraLongitude != 0.0) {
                        saveCapturedImageAndStartUploadProcess();
                    } else {
                        PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name), getString(R.string.plot_on_map_location_not_detected),
                                getString(R.string.ok), false, (dialog, which) -> dialog.dismiss());
                    }
                } else {
                    saveCapturedImageAndStartUploadProcess();
                }
            }
        }
    }

    private void askUserToRefreshLocation() {

        PopUtils.showCustomTwoButtonAlertDialog(CameraActivity.this, getString(R.string.app_name), getString(R.string.site_untag_message),
                getString(R.string.refresh), getString(R.string.ignore), false,
                (dialog, which) -> refreshLocation(), (dialog, which) -> saveCapturedImageAndStartUploadProcess());
    }

    private void refreshLocation() {
        // requestAPIToUpdateLocation();
        binding.rlProgress.setVisibility(View.VISIBLE);
        new Handler().postDelayed(() -> {
            binding.rlProgress.setVisibility(View.GONE);
            showPropertyDialog(isGalleryMode, false);
        }, 500);

    }


    private boolean checkFoPropertyTag() {
        if (AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 1) {
            return false;
        }
        return mBuildingId.equalsIgnoreCase("0");

    }

    private void refreshPropertyData() {

        if (PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
            if (!TextUtils.isEmpty(binding.cameraPreview.tvPropertyName.getText().toString().trim())) {
                lstIgnoreList.add(mBuildingId);
                PopUtils.showConformAlertDialogForProperty(CameraActivity.this, binding.cameraPreview.tvPropertyName.getText().toString(),
                        (dialogInterface, i) -> showPropertyDialog(isGalleryMode, true), (dialogInterface, i) -> {
                        });
            } else {
                showPropertyDialog(isGalleryMode, true);
            }
        } else {
            getPermissionToFineLocation(true);
        }

    }

    /**
     * Method to select Multiple/single image from gallery.
     */
    private void openGalleryForImageSelection() {
        Intent intent = new Intent(this, GalleryActivity.class);
        SelectionSpec.getInstance().mimeTypeSet = MimeType.ofImage();
        SelectionSpec.getInstance().showSingleMediaType = true;
        startActivityForResult(intent, Constants.ACTION_CODE_PICK_FROM_GALLERY);

    }

    private void showMapViewIfLocatorIsEnabled(boolean shouldShowView, boolean firstTimeForPlotOnMap) {
        if ((AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_TARGET_LOCATOR, false) || isPlotOnMap) && shouldShowView) {
            if (mapFragment == null) {
                mapFragment = (MapViewFragment) getSupportFragmentManager().findFragmentById(R.id.mapFragment);
            }
            if (mapFragment != null) {
                mapFragment.binding.ivDummyViewOnMap.setOnClickListener(this);
                mapFragment.binding.tvBack.setOnClickListener(this);
                mapFragment.binding.tvNext.setOnClickListener(this);
                mapFragment.setPlotOnMap(isPlotOnMap);
                mapFragment.setImageData(formData.getImageData());
                if (firstTimeForPlotOnMap) {
                    mapFragment.showNextButton();
                } else {
                    mapFragment.showBackButton();
                }
            }
            if (firstTimeForPlotOnMap) {
                minimizeMapView();
                new Handler().postDelayed(() -> {
                    if (mapFragment != null) {
                        showMapOnDialog(true);
                        binding.mapLayout.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
                    }
                }, 300);
            } else {
                binding.mapLayout.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
                if (mapFragment != null) {
                    if (mapFragment.isFullScreen()) {
                        if (!mapFragment.isMarkerAdded())
                            mapFragment.showNextButton();
                        else
                            mapFragment.showBackButton();
                    } else {
                        minimizeMapView();
                    }
                } else {
                    minimizeMapView();
                }

            }
            if (mapFragment != null) {
                binding.mapLayout.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
                if (isGalleryMode && mLocationExif != null && (mLocationExif.getLatitude() != 0.0 && mLocationExif.getLongitude() != 0.0)) {
                    mapFragment.resetMap(mLocationExif.getLatitude(), mLocationExif.getLongitude());
                } else {
                    mapFragment.resetMap(MainActivity.currentLatitude, MainActivity.currentLongitude);
                }
            }
        } else {
            binding.mapLayout.rlCameraMapLocatorView.setVisibility(View.GONE);
        }
    }

    private void apiCallForWeather() {
        ApiInterface apiService = RetrofitProvider.createServiceString(ApiInterface.class);

        HashMap<String, Object> params = new HashMap<>();
        params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
        params.put(Constants.PARAM_LAT, String.valueOf(MainActivity.currentLatitude));
        params.put(Constants.PARAM_LNG, String.valueOf(MainActivity.currentLongitude));
        params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        StaticUtils.addCommonData(params);
        Call<String> call = apiService.getWeatherApi(params);
        call.enqueue(new Callback<String>() {
            @Override
            public void onResponse(Call<String> call, Response<String> response) {
                try {
                    if (response.body() != null) {
                        stopProgress();

                        if (!TextUtils.isEmpty(response.body())) {
                            isEditModeCaption = true;
                            strWeather = response.body();
                            binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
                            binding.cameraPreview.edtTextCaption.setText(binding.cameraPreview.edtTextCaption.getText().toString().trim().concat(" ").concat(strWeather));
                            hideSoftKeyboard(CameraActivity.this);
                            binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
                        } else {
                            showForeGroundToast(getString(R.string.some_error_occured));
                        }
                    } else if (response.body() == null) {
                        stopProgress();
                        showForeGroundToast(getString(R.string.some_error_occured));
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                } finally {
                    stopProgress();
                }
            }

            @Override
            public void onFailure(Call<String> call, Throwable t) {
                stopProgress();
                showForeGroundToast(getString(R.string.some_error_occured));
            }
        });

    }

    private void minimizeMapView() {

        binding.cameraPreview.tvPropertyName.setVisibility(View.VISIBLE);
        new Handler().postDelayed(() -> {
            try {
                if (mapFragment != null) {
                    resizeMapView(false);
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }, 100);
        binding.cameraPreview.imgBtnDelete.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnWeather.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnEdit.setVisibility(View.VISIBLE);
        setVisibilityTagAndAddTextView(true);
        binding.cameraPreview.imgBtnRefresh.setVisibility(View.VISIBLE);

    }

    private void showMapOnDialog(boolean firstTimeForPlotOnMap) {
        if (mapFragment != null) {
            mapFragment.binding.ivDummyViewOnMap.setVisibility(View.GONE);
            mapFragment.binding.clToolbar.setVisibility(View.VISIBLE);
            if (firstTimeForPlotOnMap) {
                mapFragment.showNextButton();
                mapFragment.addAllMarker(formData.getImageData(), true);
            } else {
                mapFragment.showBackButton();
            }
            mapFragment.checkAndShowMapChooserView();
            mapFragment.setFullScreenMode(true);
            mapFragment.readyBitmapFromMap();
        }
        binding.cameraPreview.tvPropertyName.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnDelete.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnEdit.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnWeather.setVisibility(View.GONE);
        setVisibilityTagAndAddTextView(false);
        binding.cameraPreview.imgBtnRefresh.setVisibility(View.GONE);
        resizeMapView(true);

        runOnUiThread(() -> {
            if (mapFragment != null) {
                mapFragment.AnimateCameraToLocationWhenVisible();
                mapFragment.checkAndShowMapChooserView();
            }
        });
    }

    /**
     * Method to set Size of target locator map view.
     *
     * @param isOpen
     */
    private void resizeMapView(boolean isOpen) {
        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) binding.mapLayout.rlCameraMapLocatorView.getLayoutParams();
        if (isOpen) {
            params.width = RelativeLayout.LayoutParams.MATCH_PARENT;
            params.height = RelativeLayout.LayoutParams.MATCH_PARENT;
            binding.mapLayout.rlCameraMapLocatorView.setLayoutParams(params);
            ViewGroup.MarginLayoutParams margin = (ViewGroup.MarginLayoutParams) binding.mapLayout.rlCameraMapLocatorView.getLayoutParams();
            margin.setMargins(0, 0, 0, 0);
            mapFragment.binding.ivDummyViewOnMap.setVisibility(View.GONE);
        } else {
            if (mapFragment != null) {
                mapFragment.binding.clToolbar.setVisibility(View.GONE);
                mapFragment.binding.ivDummyViewOnMap.setVisibility(View.VISIBLE);
                mapFragment.binding.rlPin.setVisibility(View.GONE);
                mapFragment.setFullScreenMode(false);
                params.width = mapFragment.binding.ivDummyViewOnMap.getWidth();
                params.height = mapFragment.binding.ivDummyViewOnMap.getHeight();
                mapFragment.binding.ivFakeView.setVisibility(View.GONE);
            }
            binding.mapLayout.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
            binding.mapLayout.rlCameraMapLocatorView.setLayoutParams(params);
            binding.mapLayout.rlCameraMapLocatorView.requestFocus();
            ViewGroup.MarginLayoutParams margin = (ViewGroup.MarginLayoutParams) binding.mapLayout.rlCameraMapLocatorView.getLayoutParams();
            margin.setMargins(40, 150, 0, 0);
        }
    }

    private void showGPSSettingAlert(boolean isForAddProperty) {
        String message;
        if (isForAddProperty) {
            message = getString(R.string.to_add_new_property_sitefotos_requires_access_to_your_location);
        } else {
            message = getString(R.string.gps_not_enabled);
        }
        PopUtils.displayLocationSettingsRequest(this, LOCATION_REQUEST_CODE);
        /*if (!isDialogueToShow) {
            PopUtils.showGPSSettingsAlert(context, message,
                    (dialogInterface, i) -> navigateToGPSSettingScreen(),
                    (dialogInterface, i) -> isGpsEnableCalled = true);
        }*/
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {

        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        switch (requestCode) {
            case CAMERA_PERMISSIONS_REQUEST:
                isPermissionCameraCalled = true;
                init();
                break;
            case LOCATION_PERMISSION_REQUEST:

                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    isPermissionLocationCalled = true;
                    startLocationServiceIfNotStarted();
                } else {

                    PopUtils.showCustomTwoButtonAlertDialog(this, null, getString(R.string.access_camera_permission_message),
                            getString(R.string.open), getString(R.string.txt_cancel), false,
                            (dialog, which) -> PermissionUtils.requestPermission(context, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST), (dialog, which) -> {

                            });

                    //PermissionUtils.showDialogWhenDeniedPermission(context, getString(R.string.to_determine_position_sitefotos_requires_access_to), v -> PermissionUtils.requestPermission(context, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST), v -> {

                    // });

                }
                break;
            case Constants.EXTERNAL_STORAGE_REQUEST:
                break;
        }
    }

    public void captureImage(View v) {
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            if (MainActivity.currentLatitude != 0 && MainActivity.currentLongitude != 0) {
                //take the picture
                if (binding.cameraView.camera.isTakingPicture())
                    return;
                isDialogueToShow = true;
                showOrHideProgressBar(true);
                if (binding.cameraView.camera.getFlash().equals(Flash.ON) || binding.cameraView.camera.getFlash().equals(Flash.AUTO)) {
                    binding.cameraView.camera.takePicture();
                } else {
                    binding.cameraView.camera.takePictureSnapshot();
                }
            } else {
                trashData();
                //start location service if stopped
                startLocationServiceIfNotStarted();
                showForeGroundToastLong(getString(R.string.msg_capture_photo_location_error));
            }
        }
    }

    public void handleVisibilityOFElements(boolean checkMapLocator) {
        try {
            if (isPreviewMode) {
                binding.cameraPreview.rlCameraPreview.setVisibility(View.VISIBLE);
                binding.rlCameraMainView.setVisibility(View.GONE);
                // Set caption layout visibility
                if (isEditModeCaption && !isEditModeDrawing) {
                    binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
                } else {
                    binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
                }
                setVisibilityTagAndAddTextView(true);
                binding.rlMainPreview.setVisibility(View.VISIBLE);
                // Visible drawing layout if drawing mode is on
                if (isEditModeDrawing) {
                    binding.cameraPreview.rlCameraPreview.setVisibility(View.GONE);
                    binding.cameraDraw.rlDrawView.setVisibility(View.VISIBLE);
                    binding.cameraDraw.imgBtnEditDrawingToggle.setVisibility(View.VISIBLE);
                    binding.cameraDraw.imgBtnDrawingDone.setVisibility(View.VISIBLE);

                    if (myViewDrawing != null && myViewDrawing.pathsStackDrawing.size() > 0) {
                        binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.VISIBLE);
                    } else {
                        binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.GONE);
                    }
                    binding.cameraDraw.verticalColorPicker.setVisibility(View.VISIBLE);
                    if (checkMapLocator)
                        showMapViewIfLocatorIsEnabled(false, false);
                    binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR));
                    binding.cameraDraw.imgBtnDrawingUndo.setBackgroundColor(AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR));
                } else {
                    binding.cameraDraw.rlDrawView.setVisibility(View.GONE);
                    binding.cameraPreview.rlCameraPreview.setVisibility(View.VISIBLE);
                    binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
                    if (checkMapLocator)
                        showMapViewIfLocatorIsEnabled(true, false);
                }
            } else {
                binding.cameraPreview.rlCameraPreview.setVisibility(View.GONE);
                binding.cameraDraw.rlDrawView.setVisibility(View.GONE);
                binding.rlMainPreview.setVisibility(View.GONE);
                binding.rlCameraMainView.setVisibility(View.VISIBLE);
                if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
                    binding.cameraPermission.llCameraPermissionView.setVisibility(View.GONE);
                    binding.cameraView.camera.setVisibility(View.VISIBLE);
                } else {
                    checkCameraPermission();
                    binding.cameraView.camera.setVisibility(View.GONE);
                    binding.cameraPermission.llCameraPermissionView.setVisibility(View.VISIBLE);
                }
                checkAndUpdateCameraFaceView();
                if (checkMapLocator)
                    showMapViewIfLocatorIsEnabled(false, false);
                if (mapFragment != null) {
                    mapFragment.setNullMarker(true);
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void checkAndUpdateCameraFaceView() {
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            checkAndUpdateCameraFace();
        } else {
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
        }
    }

    public void visibleDrawingViewActionsAndCaptureDrawingBitmap() {
        // Set visibility of elements
        binding.cameraPreview.imgBtnDelete.setVisibility(View.VISIBLE);
        setVisibilityTagAndAddTextView(true);
        binding.cameraPreview.imgBtnRefresh.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnEdit.setVisibility(View.VISIBLE);
        binding.cameraDraw.imgBtnDrawingDone.setVisibility(View.VISIBLE);
        binding.cameraPreview.tvPropertyName.setVisibility(View.VISIBLE);
        binding.cameraDraw.rlDrawingTopView.setVisibility(View.VISIBLE);

        binding.cameraPreview.edtTextCaption.setText("");
        isEditModeCaption = false;
        isEditModeDrawing = false;

        binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
    }

    private void setVisibilityTagAndAddTextView(boolean shouldShow) {

        if (shouldShow) {
            if (!TextUtils.isEmpty(AppPrefShared.getString(LOGGED_IN_USER_TAGS, null))) {
                binding.cameraPreview.imgBtnTextTag.setVisibility(View.VISIBLE);
                binding.cameraPreview.imgBtnText.setVisibility(View.GONE);
                if (TextUtils.isEmpty(binding.cameraPreview.tvTagCount.getText())) {
                    binding.cameraPreview.tvTagCount.setVisibility(View.GONE);
                } else {
                    binding.cameraPreview.tvTagCount.setVisibility(View.VISIBLE);
                }

                binding.cameraPreview.rlTagTextView.setVisibility(View.VISIBLE);
            } else {
                binding.cameraPreview.imgBtnText.setVisibility(View.VISIBLE);
                binding.cameraPreview.imgBtnTextTag.setVisibility(View.GONE);
                binding.cameraPreview.rlTagTextView.setVisibility(View.GONE);
            }
        } else {
            binding.cameraPreview.imgBtnText.setVisibility(View.GONE);
            binding.cameraPreview.imgBtnTextTag.setVisibility(View.GONE);
            binding.cameraPreview.rlTagTextView.setVisibility(View.GONE);
        }

    }


    public void hideDrawingViewActionsAndCaptureDrawingBitmap() {

        try {
            binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.INVISIBLE);
            if (bmpresizedImageTakenFromCamera == null)
                return;

            Bitmap bmpImageView = Bitmap.createBitmap(bmpresizedImageTakenFromCamera);
            if (bmpImageView != null) {
                if (bmpImageView.getHeight() < 0 || bmpImageView.getWidth() < 0)
                    return;

                double aspectRatioImage = (double) (bmpImageView.getHeight() / bmpImageView.getWidth());
                double aspectRatioScreen = (double) (Constants.CAM_SCREEN_HEIGHT / Constants.CAM_SCREEN_WIDTH);

                // get captured image scaled height & width comparing with screen dimensions.
                double scaledHeight, scaledWidth;

                // Get Top & Left offset here for drawing bitmap to merge.
                //double offsetTop = 0, offsetLeft = 0;

                if (aspectRatioImage < aspectRatioScreen) {

                    // Scale image height as per height of screen and store the dimensions.
                    scaledHeight = (double) Constants.CAM_SCREEN_HEIGHT;
                    scaledWidth = (double) (Constants.CAM_SCREEN_HEIGHT * bmpImageView.getWidth()) / bmpImageView.getHeight();

                    //offsetLeft = (scaledWidth - Constants.CAM_SCREEN_WIDTH) / 2;
                } else {

                    // Scale image width as per width of screen and store the dimensions.
                    scaledWidth = (double) Constants.CAM_SCREEN_WIDTH;
                    scaledHeight = (double) (Constants.CAM_SCREEN_WIDTH * bmpImageView.getHeight()) / bmpImageView.getWidth();
                    //offsetTop = (scaledHeight - Constants.CAM_SCREEN_HEIGHT) / 2;
                }

                Bitmap bitmap2 = null;
                if (myViewDrawing != null && myViewDrawing.getDrawingBitmap() != null) {
                    bitmap2 = ImageUtil.loadBitmapFromView(binding.rlMainPreview, true);
                }
                // Merge above 2 bitmaps into one on hte scaled image bitmap first.
                if (bitmap2 == null) {
                    bitmap2 = Bitmap.createScaledBitmap(bmpImageView, (int) scaledWidth, (int) scaledHeight, true);
                }
                bitmapCapturedImageWithDrawing = Bitmap.createScaledBitmap(bitmap2, bmpImageView.getWidth(), bmpImageView.getHeight(), true);

                //ImageUtil.recycleBitmap(bitmap2);
                //ImageUtil.recycleBitmap(bmpImageView);
                binding.cameraPreview.edtTextCaption.setText("");
                isEditModeCaption = false;
                isEditModeDrawing = false;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }


    }

    private void doAfterPictureCaptured(PictureResult result) {
        try {
            result.toBitmap(this::afterBitmapReady);
        } catch (UnsupportedOperationException e) {
            handleVisibilityOFElements(true);
            showOrHideProgressBar(false);
        }
    }

    private void afterBitmapReady(Bitmap bitmap) {
        Matrix matrix = new Matrix();
        if (binding.cameraView.camera.getFacing() == Facing.FRONT) {
            matrix.preScale(-1, 1);
        }
        if (bitmap != null) {
            bmpresizedImageTakenFromCamera = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(),
                    bitmap.getHeight(), matrix, true);
            binding.ivCapturedImage.setImageBitmap(bmpresizedImageTakenFromCamera);
        }
        isGalleryMode = false;
        dateExifImageCaptured = "";
        isPreviewMode = true;
        //ImageUtil.recycleBitmap(bitmap);
        if (!canGetLocation(context)) {
            MainActivity.currentLatitude = 0.0;
            MainActivity.currentLongitude = 0.0;
            MainActivity.distanceAccuracy = 0;
        }
        showOrHideProgressBar(false);
        if (isPlotOnMap) {
            //we do not show other view until we launch map locator view
            binding.rlCameraBottom.setVisibility(View.GONE);
            binding.imgBtnFormCancel.setVisibility(View.GONE);
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
            binding.imgBtnFlash.setVisibility(View.GONE);
            new Handler().postDelayed(() -> {
                binding.rlCameraBottom.setVisibility(View.VISIBLE);
                binding.imgBtnFlipCamera.setVisibility(View.VISIBLE);
                binding.imgBtnFlash.setVisibility(View.VISIBLE);
                binding.imgBtnFormCancel.setVisibility(View.VISIBLE);
                handleVisibilityOFElements(false);
            }, 800);
        } else {
            handleVisibilityOFElements(true);
        }
        doAfterImageIsCaptured();
    }

    /**
     * Common method to show or hide progress bar in screen
     *
     * @param shouldShow true/ false
     */
    private void showOrHideProgressBar(boolean shouldShow) {
        if (shouldShow) {
            binding.rlProgress.setVisibility(View.VISIBLE);
        } else {
            binding.rlProgress.setVisibility(View.GONE);
        }
    }

    private void hasFlash() {
        binding.imgBtnFlash.setVisibility(View.GONE);
        if (MyCameraUtils.hashCameraFlash(context, binding.cameraView.camera, isPreviewMode)) {
            binding.imgBtnFlash.setVisibility(View.VISIBLE);
            MyCameraUtils.updateFlashImage(binding.cameraView.camera, binding.imgBtnFlash);
        }
    }

    public void getPermissionToFineLocation(boolean checkExplicitly) {

        if (checkExplicitly) {
            requestLocationPermission();
        }
        if (!isPermissionLocationCalled) {
            isPermissionLocationCalled = true;
            requestLocationPermission();
        }
    }

    /**
     * temp
     * Check location permission. show required permission dialog if user set permanent denied
     */
    private void requestLocationPermission() {
        if (PermissionUtils.shouldShowRequestPermissions(context, PermissionUtils.getLocationPermissions())) {
            PermissionUtils.requestPermission(context, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
        } else {
            if (!PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
                PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to),
                        getString(R.string.open), getString(R.string.txt_cancel), false,
                        (dialog, which) -> PermissionUtils.navigateUserToPermissionScreen(this), (dialog, which) -> {

                        });
            }
        }
    }

    public void setColorPicker() {
        colorSelected = AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR);
        myViewDrawing.mPaintEditDrawing.setColor(colorSelected);
        binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(colorSelected);


        binding.cameraDraw.verticalColorPicker.setOnColorChangeListener(selectedColor -> {

            if (selectedColor != 0) {
                colorSelected = selectedColor;
                myViewDrawing.mPaintEditDrawing.setColor(colorSelected);
                binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(colorSelected);
                AppPrefShared.putValue(Constants.DRAWING_PAINT_STROKE_COLOR, colorSelected);
            }
        });

    }

    public void getNewDrawingView() {
        myViewDrawing = new CameraDrawingView(this);
        binding.flDrawingView.addView(myViewDrawing);
    }

    public void showDialogueOfPropertyUsingPolygon() {

        PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();

        boolean isPrivilegedUser = AppPrefShared.getString(Constants.LOGGED_IN_USER_TYPE, Constants.LOGGED_IN_USER_TYPE).equalsIgnoreCase(Constants.LOGGED_IN_USER_TYPE_PRIVILEGED_USER);

        buildingName = polygonCalculation.buildingName;
        showPropertyName();
        mBuildingId = polygonCalculation.buildingId;
        stopProgressDialog();
        showMapViewIfLocatorIsEnabled(true, false);
        if ((!isPrivilegedUser && lstProperty.size() == 0) || binding.cameraPreview.tvPropertyName.getText().toString().trim().length() > 0) {
        } else if (lstProperty.size() == 0 && !canGetLocation(context)) {

            PopUtils.showAlertDialogPositiveButtonOnlyEnableLocation(context, (dialogInterface, i) -> PopUtils.displayLocationSettingsRequest(this, LOCATION_REQUEST_CODE), (dialogInterface, i) -> {
                //doAfterNonOfAboveOptionClicked();
                stopProgress();
                dialogInterface.dismiss();
            });
            isDialogueToShow = false;
        } else {
            try {
                PopUtils.showPropertyDialog(CameraActivity.this, lstProperty,
                        canGetLocation(CameraActivity.this),
                        this::doAfterPropertySelected, view -> doAfterNewPropertyOptionClicked(), view -> doAfterNoneOfTheAboveSelected());
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
            isDialogueToShow = false;
        }
    }

    private void doAfterNoneOfTheAboveSelected() {
    }

    private void showPropertyName() {
        binding.cameraPreview.tvPropertyName.setText(buildingName);
        if (!TextUtils.isEmpty(buildingName)) {
            binding.cameraPreview.tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
        } else {
            binding.cameraPreview.tvPropertyName.setBackgroundColor(ContextCompat.getColor(CameraActivity.this, R.color.transparent));
        }
    }

    private void doAfterPropertySelected(int position) {
        if (lstProperty != null && lstProperty.size() > 0) {
            binding.cameraPreview.tvPropertyName.setVisibility(View.VISIBLE);
            binding.cameraPreview.tvPropertyName.setText(lstProperty.get(position).getPropertyName());
            binding.cameraPreview.tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
            mBuildingId = String.valueOf(lstProperty.get(position).getPropertyId());
        }
    }

    private void doAfterNewPropertyOptionClicked() {
        if (canGetLocation(context)) {
            navigateToPropertyScreen(Constants.MAP_ACTIVITY_REQUEST);
            showProgress(getString(R.string.gettingCurrentLocation));
        } else {
            if (!isGpsEnableCalled) {
                showGPSSettingAlert(false);
            }
        }
    }

    /*
    Save image and get imagePath for share the link
     */
    private void saveImageInSDCard() {
        try {
            if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_SAVE_COPY_TO_DEVICE, false)) {
                if (PermissionUtils.hasStoragePermissions(this)) {
                    if (!isGalleryMode) {
                        strMediaPath = ImageUtil.saveImage(CameraActivity.this, bitmapCapturedImageWithDrawing, true);
                        ImageUtil.writeExifDataInImage(CameraActivity.this, strMediaPath, MainActivity.currentLatitude, MainActivity.currentLongitude);
                    }
                }
            } else {
                strMediaPath = "";
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void goToFormDetailScreen(JSONArray jsonArray) {

        if (formData != null) {
            Intent intent = new Intent();
            intent.putExtra("imagePath", jsonArray.toString());
            intent.putExtra("tag", tagId);
            intent.putExtra("isFromMap", isFromMap);
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    private void setResultForDeletedForm() {
        if (formData != null) {
            Intent intent = new Intent();
            intent.putExtra("tag", tagId);
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    private boolean shouldShowPropertyDialog() {
        boolean shouldShowDialog = true;
        if (siteId > 0) {
            PropertiesVo propertiesVo = PropertyUtils.checkValidDistanceForProperty(context, (int) siteId, MainActivity.currentLatitude, MainActivity.currentLongitude);
            if (propertiesVo != null) {
                mBuildingId = String.valueOf(propertiesVo.getPropertyId());
                buildingName = propertiesVo.getPropertyName();
                showPropertyName();
                shouldShowDialog = false;
            }
        }
        return shouldShowDialog;
    }

    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        resetCameraView();
        // call visibility of elements on orientation change.
        handleVisibilityOFElements(true);
    }

    // Save captured image and start uploading task.
    public void saveCapturedImageAndStartUploadProcess() {
        if (binding.cameraPreview.edtTextCaption.getText() != null)
            strCaption = binding.cameraPreview.edtTextCaption.getText().toString().trim();
        hideDrawingViewActionsAndCaptureDrawingBitmap();
        binding.cameraPreview.imgBtnWeather.setSelected(false);
        isPreviewMode = false;

        handleVisibilityOFElements(true);
        new SaveImageDataForUploadAsyncTask().execute();
    }

    private void showPropertyDialog(boolean isFromGallery, boolean isRefresh) {
        startProgress();
        dbExecutorService.execute(() -> {
            PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
            if (!isRefresh) {
                lstIgnoreList.clear();
                mBuildingId = "0";
            }
            TblProperties tblProperties = new TblProperties(context);
            TblCluster tblCluster = new TblCluster(context);
            List<PropertiesVo> lstProperties = tblProperties.getAllProperties();
            List<Cluster> lstClusters = tblCluster.getAllClusterData();
            lstProperty = polygonCalculation.getFilteredPropertyListFromPolygon(context, isFromGallery, mLocationExif, MainActivity.currentLatitude, MainActivity.currentLongitude, MainActivity.distanceAccuracy, lstIgnoreList, lstClusters, lstProperties);
            new Handler(Looper.getMainLooper()).post(() -> {
                if (isFinishing())
                    return;
                stopProgressDialog();
                showDialogueOfPropertyUsingPolygon();
            });
        });
    }

    private boolean checkSDCardPermission() {

        if (PermissionUtils.hasStoragePermissions(CameraActivity.this)) {
            return true;
        } else {
            showPermissionRequiredInfoDialog(getString(R.string.sitefotos_is_not_allowed_to_store_the_captured_properties_fotos), Constants.EXTERNAL_STORAGE_REQUEST);
            return false;
        }
    }

    private void showPermissionRequiredInfoDialog(String message, final int requestCode) {
        PopUtils.dismissAlertDialog();

        PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), message,
                getString(R.string.open), getString(R.string.txt_cancel), false,
                (dialog, which) -> {
                    if (!hasStoragePermissions(CameraActivity.this)) {
                        StaticUtils.openSettingScreen(CameraActivity.this, requestCode);
                    } else {
                        PermissionUtils.requestStoragePermission(CameraActivity.this, requestCode);
                    }
                }, (dialog, which) -> {

                });

    }

    private void resetMapExtraLocation() {
        try {
            if (mapFragment != null) {
                mapFragment.updatedExtraLatitude = 0.0;
                mapFragment.updatedExtraLongitude = 0.0;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        stopProgress();
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constants.ACTION_CODE_PICK_FROM_GALLERY) {
            isGalleryClicked = true;
            try {
                if (data != null) {
                    List<AllImageModel> lstSelectedImages = (ArrayList<AllImageModel>) data.getSerializableExtra("mediaData");
                    isPermissionCameraCalled = true;
                    if (lstSelectedImages != null && !lstSelectedImages.isEmpty()) {
                        if (lstSelectedImages.size() == 1) {
                            String outputPath = ImageUtil.rotateImageAndGetNewPath(this, lstSelectedImages.get(0).getImagePath());
                            bmpresizedImageTakenFromCamera = ImageUtil.getBitmapFromPath(outputPath);
                            binding.ivCapturedImage.setImageBitmap(bmpresizedImageTakenFromCamera);
                            isGalleryMode = true;
                            isPreviewMode = true;

                            if (isPlotOnMap) {
                                //we do not show other view until we launch map locator view
                                binding.rlCameraBottom.setVisibility(View.GONE);
                                binding.imgBtnFormCancel.setVisibility(View.GONE);
                                binding.imgBtnFlipCamera.setVisibility(View.GONE);
                                binding.imgBtnFlash.setVisibility(View.GONE);
                                new Handler().postDelayed(() -> {
                                    binding.rlCameraBottom.setVisibility(View.VISIBLE);
                                    binding.imgBtnFlipCamera.setVisibility(View.VISIBLE);
                                    binding.imgBtnFlash.setVisibility(View.VISIBLE);
                                    binding.imgBtnFormCancel.setVisibility(View.VISIBLE);
                                    handleVisibilityOFElements(false);
                                }, 800);
                            } else {
                                handleVisibilityOFElements(true);
                            }
                            new UploadSingleImage(lstSelectedImages.get(0).getImagePath()).execute("");
                        } else {
                            visibleDrawingViewActionsAndCaptureDrawingBitmap();
                            //filter images with exif date and skip images without date
                            int dataCountBeforeIteration = lstSelectedImages.size();
                            MyCameraUtils.filterImageOnExifDate(lstSelectedImages);
                            if (!lstSelectedImages.isEmpty()) {
                                isPreviewMode = false;
                                handleVisibilityOFElements(true);
                                binding.flDrawingView.removeAllViews();
                                myViewDrawing = null;
                                showOrHideProgressBar(true);
                                new UploadMultipleImagesTask(CameraActivity.this, lstSelectedImages, MainActivity.currentLatitude, MainActivity.currentLongitude, MainActivity.distanceAccuracy,
                                        lstIgnoreList, formData, isTMForm, tagId, isIssue, this::doAfterAllPhotoSaveAndReadyForUploading).execute();

                                showForeGroundToastLong(getString(R.string.msg_image_process_successfully, lstSelectedImages.size()));
                                CustomLogKt.errorLog("Selected Images Count","Count:::"+ dataCountBeforeIteration);
                                CustomLogKt.errorLog("Selected Filtered Count","Filtered Count:::"+ lstSelectedImages);
                                if (dataCountBeforeIteration > lstSelectedImages.size()) {
                                    showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location_for_some));
                                }

                            } else {
                                showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location_for_all));
                            }
                        }
                    }
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

        } else if (requestCode == Constants.MAP_ACTIVITY_REQUEST) {
            if (resultCode == RESULT_OK && data != null && data.getExtras() != null) {
                isPermissionCameraCalled = true;
                //Response of Adding New property api in AddNewProperty activity
                try {
                    if (data.getExtras() != null && !TextUtils.isEmpty(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                        switch (data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA)) {
                            case Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN:
                                PopUtils.showAlertDialogPositiveButtonOnly(CameraActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN, getString(R.string.joinAlertMessage));

                                break;

                            case Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX:
                                PopUtils.showAlertDialogPositiveButtonOnly(CameraActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX, getString(R.string.maxAlertMessage));
                                break;

                            case Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST:
                                PopUtils.showAlertDialogPositiveButtonOnly(CameraActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST, getString(R.string.pastAlertMessage));
                                break;

                            default:
                                showPropertyDialog(false, false);
                                break;
                        }
                    } else {
                        showPropertyDialog(false, false);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                    showPropertyDialog(false, false);
                }
            } else {
                onResume();
            }
        } else if (requestCode == LOCATION_REQUEST_CODE) {
            if (resultCode == RESULT_OK) {
                isGpsEnableCalled = true;
                startLocationServiceIfNotStarted();
            }
        } else {
            showForeGroundToast(getString(R.string.some_error_occured));
        }
    }

    /**
     * Method to start uploading process after Saved and prepared uploading structure.
     *
     * @param jsonArray final result from AsyncTask
     */
    private void doAfterAllPhotoSaveAndReadyForUploading(JSONArray jsonArray) {
        showOrHideProgressBar(false);
        if (formData != null) {
            goToFormDetailScreen(jsonArray);
        }
    }


    @Override
    public void onBackPressed() {
        binding.cameraPreview.imgBtnWeather.setSelected(false);
        if (isPreviewMode) {
            trashData();
        } else {
            super.onBackPressed();
        }
    }

    private void trashData() {
        hideSoftKeyboard(this);
        mBuildingId = "0";
        buildingName = "";
        binding.cameraPreview.tvPropertyName.setText("");
        binding.cameraPreview.tvPropertyName.setBackgroundColor(ContextCompat.getColor(CameraActivity.this, R.color.transparent));
        binding.cameraPreview.edtTextCaption.setText("");
        binding.cameraPreview.edtTextCaption.setText("");
        isEditModeCaption = false;

        try {
            PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
            polygonCalculation.buildingName = "";
            polygonCalculation.propertyName = "";
            polygonCalculation.buildingId = "0";
            lstIgnoreList.clear();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }

        resetMapExtraLocation();
        binding.flDrawingView.removeAllViews();
        myViewDrawing = null;
        binding.cameraPreview.imgBtnWeather.setSelected(false);

        isPreviewMode = false;
        isEditModeDrawing = false;
        resetTagData(lstTags, binding.cameraPreview.tvTagCount);
        handleVisibilityOFElements(true);
        if (mapFragment != null) {
            mapFragment.setFullScreenMode(false);
        }
    }


    @Override
    protected void onDestroy() {
        // Release camera and re assign the camera with applicable settings to sort out camera rotate issue.
        destroyObjectsAndUnregisterReceivers();
        super.onDestroy();
    }

    private void destroyObjectsAndUnregisterReceivers() {
        unregisterReceiver(formUpdateReceiver);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            try {
                unregisterReceiver(netWorkChangeReceiver);
            } catch (IllegalArgumentException e) {
                FirebaseEventUtils.logException(e);
            }
        }

        // ImageUtil.recycleBitmap(bitmapCapturedImageWithDrawing);
        // ImageUtil.recycleBitmap(bitmapCapturedImageWithDrawingLow);
        // ImageUtil.recycleBitmap(bmpresizedImageTakenFromCamera);
    }

    private void setOnClickListener() {
        binding.cameraPermission.tvCameraPermissionMessage.setOnClickListener(this);
        binding.imgBtnFlipCamera.setOnClickListener(this);
        binding.imgBtnFlash.setOnClickListener(this);
        binding.imgBtnImageUpload.setOnClickListener(this);
        binding.imgBtnFormCancel.setOnClickListener(this);
        binding.imgBtnCaptureImage.setOnClickListener(this);
        binding.imgBtnGallary.setOnClickListener(this);
        binding.cameraPreview.imgBtnDelete.setOnClickListener(this);
        binding.cameraPreview.imgBtnRefresh.setOnClickListener(this);
        binding.cameraPreview.imgBtnText.setOnClickListener(this);
        binding.cameraPreview.imgBtnTextTag.setOnClickListener(this);
        binding.cameraPreview.imgBtnEdit.setOnClickListener(this);
        binding.cameraPreview.tvCaptionDone.setOnClickListener(this);
        binding.cameraPreview.imgBtnPreviewDone.setOnClickListener(this);
        binding.cameraPreview.imgBtnWeather.setOnClickListener(this);
        binding.cameraDraw.imgBtnDrawingDone.setOnClickListener(this);
        binding.cameraDraw.imgBtnDrawingUndo.setOnClickListener(this);
        binding.cameraPreview.edtTextCaption.setOnClickListener(this);
        binding.cameraPreview.rlTags.setOnClickListener(this);
        binding.cameraPreview.imgBtnTags.setOnClickListener(this);
        binding.cameraDraw.imgBtnEditDrawingToggle.setOnClickListener(this);
        binding.mapLayout.rlCameraMapLocatorView.setOnClickListener(this);
        binding.cameraPermission.btnPermission.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.tvCameraPermissionMessage) {
            checkCameraPermission();
        } else if (viewId == R.id.imgBtnFlipCamera) {
            flipCameraOnClick();
        } else if (viewId == R.id.imgBtnFlash) {
            MyCameraUtils.changeFlashSettings(this, binding.cameraView.camera, binding.imgBtnFlash);
        } else if (viewId == R.id.imgBtnImageUpload) {
            navigateToUploadActivityScreen(binding.imgBtnImageUpload);
        } else if (viewId == R.id.imgBtnFormCancel) {
            finish();
        } else if (viewId == R.id.imgBtnCaptureImage) {
            captureImage(view);
        } else if (viewId == R.id.imgBtnGallary) {
            if (checkSDCardPermission()) {
                openGalleryForImageSelection();
            }
        } else if (viewId == R.id.imgBtnDelete) {
            trashData();
        } else if (viewId == R.id.imgBtnRefresh) {
            refreshPropertyData();
        } else if (viewId == R.id.edtTextCaption) {
            enableEditCaption();
        } else if (viewId == R.id.imgBtnText || viewId == R.id.imgBtnTextTag) {
            showCaptionView();
        } else if (viewId == R.id.tvCaptionDone) {
            obDoneEditCaption();
        } else if (viewId == R.id.imgBtnEdit) {
            showDrawingView();
        } else if (viewId == R.id.imgBtnPreviewDone) {
            checkAndStartUploadProcess();
        } else if (viewId == R.id.imgBtnWeather) {
            addWeather();
        } else if (viewId == R.id.imgBtnDrawingDone || viewId == R.id.imgBtnEditDrawingToggle) {
            isEditModeDrawing = false;
            handleVisibilityOFElements(true);
        } else if (viewId == R.id.imgBtnDrawingUndo) {
            myViewDrawing.undo();
        } else if (viewId == R.id.tvNext) {
            if (mapFragment != null && !mapFragment.isMarkerAdded()) {
                showForeGroundToastLong(getString(R.string.msg_drop_pin_on_map));
            } else {
                if (mapFragment != null) {
                    mapFragment.setFakeMapView();
                }
                new Handler().postDelayed(this::minimizeMapView, 100);
                new Handler().postDelayed(() -> {
                    showPropertyDialog(isGalleryMode, false);
                }, 700);
            }
        } else if (viewId == R.id.tvBack) {
            if (mapFragment != null) {
                mapFragment.setFakeMapView();
            }
            new Handler().postDelayed(this::minimizeMapView, 100);
        } else if (viewId == R.id.rlMapLocatorView || viewId == R.id.ivDummyViewOnMap) {
            showMapOnDialog(false);
        } else if (viewId == R.id.rlTags || viewId == R.id.imgBtnTags) {
            showTagView();
        } else if (viewId == R.id.btnPermission) {
            navigateUserToPermissionScreen(this);
        }

    }

    private void showTagView() {
        if (lstTags.isEmpty()) {
            lstTags = getTagData();
        }
        PopUtils.showTagSelectionView(this, lstTags, new OnTagSelected() {
            @Override
            public void onSelected(List<Tags> lstTag) {
                lstTags = lstTag;
            }

            @Override
            public void onItemSelected(boolean isSelected) {
                updateTagCount(binding.cameraPreview.tvTagCount, isSelected);
            }
        });
    }


    private void showDrawingView() {
        if (!isEditModeDrawing) {
            hideSoftKeyboard(this);
            isEditModeDrawing = true;
            handleVisibilityOFElements(true);
            if (myViewDrawing == null) {
                getNewDrawingView();
            }
            setColorPicker();
        }
    }


    private void showCaptionView() {
        if (!isEditModeCaption) {
            isEditModeCaption = true;
        }
        binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
        if (binding.cameraPreview.edtTextCaption.getText() != null)
            binding.cameraPreview.edtTextCaption.setSelection(binding.cameraPreview.edtTextCaption.getText().toString().trim().length());
        enableDisableCaptionEditView(true);
    }


    private void enableEditCaption() {
        if (isEditModeCaption) {
            if (binding.cameraPreview.edtTextCaption.getText() != null)
                binding.cameraPreview.edtTextCaption.setSelection(binding.cameraPreview.edtTextCaption.getText().toString().trim().length());
            enableDisableCaptionEditView(true);

        } else {
            binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
        }
        handleVisibilityOFElements(true);
    }

    /**
     * Method to enable/disable caption edit text focus
     *
     * @param isEnable true: need to set focusable else disable edittext focus and hide keyboard
     */
    private void enableDisableCaptionEditView(boolean isEnable) {
        if (isEnable) {
            binding.cameraPreview.tvCaptionDone.setVisibility(View.VISIBLE);
            binding.cameraPreview.edtTextCaption.setFocusableInTouchMode(true);
            binding.cameraPreview.edtTextCaption.setFocusable(true);
            binding.cameraPreview.edtTextCaption.requestFocus();
            showKeyboard(binding.cameraPreview.edtTextCaption);
        } else {
            hideSoftKeyboard(CameraActivity.this);
            binding.cameraPreview.edtTextCaption.setFocusable(false);
        }
    }

    private void obDoneEditCaption() {
        if (isEditModeCaption) {
            binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
            enableDisableCaptionEditView(false);
            if (binding.cameraPreview.edtTextCaption.getText() != null && binding.cameraPreview.edtTextCaption.getText().toString().trim().length() > 0) {
                binding.cameraPreview.tvCaptionDone.setVisibility(View.INVISIBLE);
            } else {
                isEditModeCaption = false;
                binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
                binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
            }
        } else {
            binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
        }
        handleVisibilityOFElements(true);
    }

    private void addWeather() {
        if (BaseApplication.getInstance().isOnline(context)) {
            if (!binding.cameraPreview.imgBtnWeather.isSelected()) {

                apiCallForWeather();
                showProgress(getString(R.string.getting_weather_info));
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_WEATHER_DISPLAYED, true);

                binding.cameraPreview.imgBtnWeather.setSelected(true);
            } else {
                if (!isEditModeCaption) {
                    isEditModeCaption = true;
                    binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
                    if (binding.cameraPreview.edtTextCaption.getText() != null)
                        binding.cameraPreview.edtTextCaption.setSelection(binding.cameraPreview.edtTextCaption.getText().toString().trim().length());
                    enableDisableCaptionEditView(true);

                }
            }
        } else {
            showForeGroundToast(getString(R.string.enableMobileData));
        }
    }

    private void flipCameraOnClick() {
        if (binding.cameraView.camera.isTakingPicture())
            return;
        if (!PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            return;
        }
        Facing facing = binding.cameraView.camera.toggleFacing();
        AppPrefShared.putValue(Constants.CAMERA_FLIP_MODE, facing.ordinal() == 1);
        hasFlash();
    }

    public class SaveImageDataForUploadAsyncTask extends AsyncTask<Void, Integer, JSONArray> {
        String strDescription = "";

        @Override
        protected void onPreExecute() {
            strDescription = strCaption;
            strCaption = "";
            binding.rlProgress.setVisibility(View.VISIBLE);
        }

        @Override
        protected JSONArray doInBackground(Void... params) {
            JSONArray imageArray = new JSONArray();
            double extraLatitude = 0.0, extraLongitude = 0.0;
            String imagePathLow = "", imagePathHigh = "";
            try {
                saveImageInSDCard();
                try {
                    if ((AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_TARGET_LOCATOR, false) || isPlotOnMap) && mapFragment != null) {
                        extraLatitude = mapFragment.updatedExtraLatitude;
                        extraLongitude = mapFragment.updatedExtraLongitude;
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                }

                bitmapCapturedImageWithDrawingLow = ImageUtil.createScaledBitmap(bitmapCapturedImageWithDrawing);
                if (bitmapCapturedImageWithDrawingLow != null) {
                    imagePathLow = ImageUtil.saveImageFromBitmap(context, bitmapCapturedImageWithDrawingLow, "", false);
                    strMediaPath = ImageUtil.saveImageFromBitmap(context, bitmapCapturedImageWithDrawingLow, "", false);
                    //plotOnMap is enabled and write exif data we use lat lang from extra latitude and extra longitude
                    if (!isPlotOnMap)
                        ImageUtil.writeExifDataInImage(CameraActivity.this, imagePathLow, MainActivity.currentLatitude, MainActivity.currentLongitude);
                    else
                        ImageUtil.writeExifDataInImage(CameraActivity.this, imagePathLow, extraLatitude, extraLongitude);
                }

                if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false)) {
                    imagePathHigh = ImageUtil.saveImageFromBitmap(context, bitmapCapturedImageWithDrawing, "", true);
                    //plotOnMap is enabled and write exif data we use lat lang from extra latitude and extra longitude
                    if (!isPlotOnMap)
                        ImageUtil.writeExifDataInImage(CameraActivity.this, imagePathHigh, MainActivity.currentLatitude, MainActivity.currentLongitude);
                    else
                        ImageUtil.writeExifDataInImage(CameraActivity.this, imagePathHigh, extraLatitude, extraLongitude);
                }

                if (formData != null) {
                    JSONObject imageObject = new JSONObject();
                    imageObject.put(IMAGEPATHLOW, imagePathLow);
                    imageObject.put(IMAGEPATHHIGH, imagePathHigh);
                    if (isPlotOnMap) {

                        imageObject.put(Constants.PARAM_LAT, mapFragment.updatedExtraLatitude);
                        imageObject.put(Constants.PARAM_LON, mapFragment.updatedExtraLongitude);
                        if (mapFragment.selectedPinData != null) {
                            imageObject.put(Constants.MAP_PIN_URL, mapFragment.selectedPinData.getPngURL());
                            imageObject.put(Constants.PIN_LABEL, mapFragment.selectedPinData.getPinLabel());
                        }
                    }
                    imageArray.put(imageObject);
                }

                DBUtils.insertImageUploadData(CameraActivity.this, imagePathLow, imagePathHigh, strDescription, dateExifImageCaptured, mBuildingId,
                        StaticUtils.getLocation(mLocationExif, MainActivity.currentLatitude, MainActivity.currentLongitude, false),
                        extraLatitude, extraLongitude, formData, isTMForm, tagId, isIssue, lstTags, false,isGalleryMode);
                mBuildingId = "0";
                buildingName = "";
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            } finally {
                resetMapExtraLocation();
                return imageArray;

            }
        }


        @Override
        protected void onPostExecute(JSONArray result) {
            isGalleryMode = false;
            resetTagData(lstTags, binding.cameraPreview.tvTagCount);
            if (mBuildingId.trim().equalsIgnoreCase("0")) {
                binding.cameraPreview.tvPropertyName.setText("");
                binding.cameraPreview.tvPropertyName.setBackgroundColor(ContextCompat.getColor(CameraActivity.this, R.color.transparent));
            }

            sendBroadCastForStartOtherDataUpload();
            binding.cameraPreview.edtTextCaption.setText("");
            binding.cameraPreview.edtTextCaption.setText("");
            isEditModeCaption = false;
            isEditModeDrawing = false;

            binding.flDrawingView.removeAllViews();
            myViewDrawing = null;
            resetMapExtraLocation();
            binding.rlProgress.setVisibility(View.GONE);
            if (formData != null) {
                goToFormDetailScreen(result);
            }
        }
    }

    private void doAfterImageIsCaptured() {
        checkLocationPermissionAndShowPropertyDialog();
    }

    //Async task for processing selected gallery image
    private class UploadSingleImage extends AsyncTask<String, Void, Boolean> {
        String imagePath;

        UploadSingleImage(String imagePath) {
            this.imagePath = imagePath;
        }

        @Override
        protected Boolean doInBackground(String... params) {
            try {
                dateExifImageCaptured = "";
                String path = StaticUtils.getRealPathFromURI(context, Uri.parse(imagePath));
                if (TextUtils.isEmpty(path)) {
                    path = imagePath;
                }
                if (!TextUtils.isEmpty(path)) {
                    mLocationExif = ImageUtil.getLocationFromImage(path);
                    dateExifImageCaptured = DateUtil.convertExpectedDateFormat(DateUtil.getDateFromExifMetadata(path));
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
            return true;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            if (!TextUtils.isEmpty(dateExifImageCaptured) && mLocationExif != null && (mLocationExif.getLatitude() != 0.0 && mLocationExif.getLongitude() != 0.0)) {
                checkLocationPermissionAndShowPropertyDialog();
            } else {
                //Show message to the user if date is not extracted from image and trash selected image from camera screen
                showForeGroundToastLong(getString(R.string.msg_capture_photo_location_error));
                trashData();
            }
        }
    }

    private void checkLocationPermissionAndShowPropertyDialog() {
        if (isGalleryMode && mLocationExif != null) {
            if (shouldShowPropertyDialog() || !isPlotOnMap) {
                if (MainActivity.currentLatitude != 0 && MainActivity.currentLongitude != 0) {
                    showPropertyDialog(isGalleryMode, false);
                    binding.cameraPreview.imgBtnWeather.setSelected(false);
                } else {
                    trashData();
                    showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location));
                }
            }

        } else {
            if (siteId > 0) {
                isDialogueToShow = true;
            }
            if (AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 1 || isPlotOnMap) {
                isDialogueToShow = false;
            }
            if (isDialogueToShow) {
                if (PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
                    if (shouldShowPropertyDialog()) {
                        showPropertyDialog(isGalleryMode, false);
                    }
                } else {
                    getPermissionToFineLocation(true);
                }
                if (canGetLocation(context)) {
                    if (isPermissionLocationCalled) {
                        binding.cameraPreview.imgBtnWeather.setSelected(false);
                    }
                } else {
                    binding.cameraPreview.imgBtnWeather.setSelected(true);
                }

                binding.cameraPreview.imgBtnWeather.setSelected(false);
            }
        }
        if (isPlotOnMap) {
            showMapViewIfLocatorIsEnabled(true, true);
        }
        sendBroadCastForStartOtherDataUpload();
    }

    private class CamListener extends CameraListener {
        @Override
        public void onCameraOpened(@NonNull CameraOptions options) {
            super.onCameraOpened(options);
            if (!isPreviewMode) {
                setUpCamera();
            }
        }

        @Override
        public void onPictureTaken(@NonNull PictureResult result) {
            super.onPictureTaken(result);
            doAfterPictureCaptured(result);
        }

        @Override
        public void onAutoFocusStart(@NonNull PointF point) {
            if (binding.cameraView.camera.isTakingPicture())
                return;
            MyCameraUtils.showTouchView(binding.ivAutoFocusAnim, point);
            super.onAutoFocusStart(point);
        }

        @Override
        public void onAutoFocusEnd(boolean successful, @NonNull PointF point) {
            super.onAutoFocusEnd(successful, point);
        }
    }


    private void checkCameraPermission() {
        if (!PermissionUtils.hasPermission(this, CAMERA_PERMISSION)) {
            showPermissionRequiredInfoDialog(getString(R.string.sitefotos_is_not_allowed_to_store_the_captured_properties_fotos), CAMERA_PERMISSIONS_REQUEST);
        } else {
            initCameraPermissionGranted();
        }
    }
}
