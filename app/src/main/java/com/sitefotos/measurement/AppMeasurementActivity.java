package com.sitefotos.measurement;

import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.Context;
import android.os.Bundle;
import android.view.View;

import androidx.recyclerview.widget.DividerItemDecoration;

import com.sitefotos.BaseActivity;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityMeasurementBinding;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.models.MeasureData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

public class AppMeasurementActivity extends BaseActivity implements View.OnClickListener {

    List<MeasureData> lstMeasurement = new ArrayList<>();
    private MeasureAdapter measureAdapter;
    private ActivityMeasurementBinding binding;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        setOnClickListener();
        initView();
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(newBase);
    }

    private void initBinding() {
        binding = ActivityMeasurementBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void initView() {
        lstMeasurement = StaticUtils.getMeasurementData(this);
        setActionBarVisibility(false);
        setActionBarData();
        setAdapter();
    }

    private void setOnClickListener() {
        binding.tlOther.imgBtnBack.setOnClickListener(this);
    }

    private void setAdapter() {
        measureAdapter = new MeasureAdapter(lstMeasurement, position -> {
            PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name),
                    getString(R.string.change_measurement_confirm_message), getString(R.string.yes), getString(R.string.no),
                    true, (dialog, which) -> setSelectedMeasurementUnit(position), (dialog, which) -> {
                        lstMeasurement.clear();
                        lstMeasurement = StaticUtils.getMeasurementData(AppMeasurementActivity.this);
                        measureAdapter.updateList(lstMeasurement);
                    });
        });
        binding.rvMeasurement.setHasFixedSize(true);
        binding.rvMeasurement.setAdapter(measureAdapter);
        binding.rvMeasurement.setNestedScrollingEnabled(false);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.rvMeasurement.getContext(), 1);
        binding.rvMeasurement.addItemDecoration(dividerItemDecoration);

    }

    private void setSelectedMeasurementUnit(int position) {
        AppPrefShared.putValue(Constants.IS_UNIT_IN_MILES, position == 0); // true means miles, false means kilometers
        measureAdapter.updateList(lstMeasurement);
        finish();
    }

    private void setActionBarData() {
        binding.tlOther.tvTitle.setText(getString(R.string.unit_of_measurement));
        setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(this, binding.tlOther.llTitle);

    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
    }


    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            onBackPressed();
        }
    }


    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

}
