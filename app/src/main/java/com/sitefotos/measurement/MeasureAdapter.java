package com.sitefotos.measurement;

import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemLanguageBinding;
import com.sitefotos.models.MeasureData;
import com.sitefotos.models.MeasureData;

import java.util.List;


public class MeasureAdapter extends RecyclerView.Adapter<MeasureAdapter.ViewHolder> {

    private List<MeasureData> lstMeasurement;
    private final ViewClicked viewClicked;
    private  ItemLanguageBinding binding;
    public interface ViewClicked {
        void onItemClicked(int position);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvTitle;
        TextView tvLang;
        AppCompatCheckBox cbLang;
        public ViewHolder(ItemLanguageBinding binding) {
            super(binding.getRoot());
            tvTitle = binding.tvTitle;
            tvLang = binding.tvLang;
            cbLang = binding.cbLang;
        }
    }

    public MeasureAdapter(List<MeasureData> lstMeasurement, ViewClicked viewClicked) {
        this.lstMeasurement = lstMeasurement;
        this.viewClicked = viewClicked;

    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemLanguageBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<MeasureData> lstMeasurement) {
        this.lstMeasurement = lstMeasurement;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        MeasureData MeasureData = lstMeasurement.get(position);
        holder.tvTitle.setText(MeasureData.getTitle());
        holder.tvLang.setText(MeasureData.getSubtitle());
        holder.cbLang.setChecked(MeasureData.getSelected());

        holder.cbLang.setOnCheckedChangeListener(null);
        holder.itemView.setOnClickListener(view -> {
            if (!MeasureData.getSelected()) {
                viewClicked.onItemClicked(position);
            }
        });

        holder.cbLang.setClickable(false);

    }

    @Override
    public int getItemCount() {
        return lstMeasurement.size();
    }

}
