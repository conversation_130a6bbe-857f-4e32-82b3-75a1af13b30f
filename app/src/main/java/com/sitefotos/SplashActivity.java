package com.sitefotos;

import static com.sitefotos.Constants.PREF_MD5_KEY_DATA;
import static com.sitefotos.util.PermissionUtils.PERMISSIONS;
import static com.sitefotos.util.PermissionUtils.PERMISSIONS_29;
import static com.sitefotos.util.PermissionUtils.PERMISSIONS_33;

import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.login.LoginActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.CheckInDetails;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.service.FileUploaderService;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.StaticUtils;

public class SplashActivity extends BaseActivity {

    private final int PERMISSION = 1210;

    private Handler handler = new Handler();
    private Thread thread;
    private Context context;
    private Intent intent;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_splash_activity);
        setActionBarVisibility(false);
        context = this;
        checkAndAddCheckInDataInDatabase();
        md5Keys = StaticUtils.getMd5ClassData(AppPrefShared.getString(PREF_MD5_KEY_DATA, ""));

        DBOpenHelper.getInstance(context);
        BaseApplication.getInstance().checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);

        //startOrStopBreadCrumbService(this);
        StaticUtils.setTime(0);

        AppPrefShared.putValue(Constants.CAMERA_FLASH_MODE, "AUTO");

        if (PermissionUtils.checkAllPermission(SplashActivity.this)) {
            startThread();
        } else {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU)
                    ActivityCompat.requestPermissions(this, PERMISSIONS_33, PERMISSION);
                else
                    ActivityCompat.requestPermissions(this, PERMISSIONS_29, PERMISSION);
            } else {
                ActivityCompat.requestPermissions(this, PERMISSIONS, PERMISSION);
            }
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        if (isLocationAndGPSEnabled()) {
            startLocationServiceIfNotStarted();
        }
    }

    private void startThread() {
        thread = new Thread(() -> {
            try {
                Thread.sleep(3000);
                handler.post(() -> {
                    // show the signup or login screen
                    TblUploadImage tblUploadImage = new TblUploadImage(this);
                    if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_LOGIN_STATUS, false)) {
                        //requestServerForUserSettingData(this);
                        checkLoginDeeplinkData();
                        try {
                            TblUploadData tblUploadData = new TblUploadData(this);
                            if (tblUploadData.getDataCount() > 0) {
                                FirebaseEventUtils.PendingOtherDataEvent(this, tblUploadData.getDataCount());
                            }
                        } catch (Exception e) {
                            FirebaseEventUtils.logException(e);
                        }

                        if (tblUploadImage.getDataCount() > 0) {
                            FirebaseEventUtils.PendingImageFilesEvent(this, tblUploadImage.getDataCount());
                        }
                        sendBroadCastForBothUploading();
                        intent = new Intent(context, MainActivity.class);
                        getSiteAndFormDataThroughDeepLinking(intent);

                    } else {
                        intent = new Intent(context, LoginActivity.class);
                        setDeepLinkingDataIfAvailable();
                    }
                    startActivity(intent);
                    finish();
                });
            } catch (InterruptedException ignored) {
            }
        });
        thread.start();
    }

    /**
     * Check User navigation source and show message if users is already logged in and trying to click on login link again.
     */
    private void checkLoginDeeplinkData() {
        try {
            Intent viewIntent = getIntent();
            if (Intent.ACTION_VIEW.equals(viewIntent.getAction())) {
                Uri uri = viewIntent.getData();
                if (uri != null) {
                    if (!uri.getQueryParameterNames().isEmpty()) {
                        if (!TextUtils.isEmpty(uri.getQueryParameter("email")) && !TextUtils.isEmpty(uri.getQueryParameter("accesscode"))) {
                            showForeGroundToast(getString(R.string.deeplink_already_login));
                        }
                    } else {
                        showForeGroundToast(getString(R.string.deeplink_invalid_link));
                    }
                }
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Method to set MailId and companyId if user came in the app from http link.
     * Example https://www.sitefotos.com/action=login?accesscode=9b04d152845ec0a378394003c96da594&email=<EMAIL>
     */
    private void setDeepLinkingDataIfAvailable() {
        Intent viewIntent = getIntent();
        if (Intent.ACTION_VIEW.equals(viewIntent.getAction())) {
            Uri uri = viewIntent.getData();
            if (uri != null) {
                intent.putExtra("url", uri.toString());
                if (!uri.getQueryParameterNames().isEmpty()) {
                    intent.putExtra("email", !TextUtils.isEmpty(uri.getQueryParameter("email")) ? uri.getQueryParameter("email") : "");
                    intent.putExtra("companyId", !TextUtils.isEmpty(uri.getQueryParameter("accesscode")) ? uri.getQueryParameter("accesscode") : "");
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    if (!TextUtils.isEmpty(uri.getQueryParameter("form")) || !TextUtils.isEmpty(uri.getQueryParameter("site"))) {
                        showForeGroundToast(getString(R.string.deeplink_need_login));
                    }
                } else {
                    intent.putExtra("email", "");
                    intent.putExtra("companyId", "");
                    intent.putExtra("url", uri.toString());
                    showForeGroundToast(getString(R.string.deeplink_invalid_link));
                }
            }
        }
    }


    /**
     * Get Site and form data through deep linking when user is logged into.
     * Example https://sitefotos.com/mobile?site=23423&form=234
     */
    private void getSiteAndFormDataThroughDeepLinking(Intent intent) {
        Intent viewIntent = getIntent();
        long formId = -1;
        long siteId = -1;
        if (Intent.ACTION_VIEW.equals(viewIntent.getAction())) {
            Uri uri = viewIntent.getData();
            if (uri == null) {
                return;
            }
            if (uri.getQueryParameterNames().isEmpty()) {
                return;
            }
            if (uri.getQueryParameterNames().contains("form")) {
                try {
                    formId = Long.parseLong(uri.getQueryParameter("form"));
                } catch (NumberFormatException ignored) {
                }
            }
            if (uri.getQueryParameterNames().contains("site")) {
                try {
                    if (uri.getQueryParameter("site").isEmpty()){
                        siteId = 0;
                    }else {
                        try {
                            siteId = Long.parseLong(uri.getQueryParameter("site"));
                        } catch (NumberFormatException ignored) {
                            siteId = -1;
                        }
                    }
                } catch (Exception e) {
                }
            }
            intent.putExtra("formId", formId);
            intent.putExtra("siteId", siteId);
            if (formId > 0) {
                intent.putExtra("autoFill", !TextUtils.isEmpty(uri.getQueryParameter("autofill")) ? uri.getQueryParameter("autofill") : "");
            }
            intent.setFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
        }

        if (getIntent().hasExtra("notificationUrl")) {
            String url = getIntent().getStringExtra("notificationUrl");
            Uri notificationURI = Uri.parse(url);
            if (notificationURI.getQueryParameterNames().isEmpty()) {
                return;
            }
            try {
                formId = Long.parseLong(notificationURI.getQueryParameter("form"));
            } catch (NumberFormatException ignored) {
            }
            try {
                siteId = Long.parseLong(notificationURI.getQueryParameter("site"));
            } catch (NumberFormatException ignored) {
            }
            intent.putExtra("formId", formId);
            intent.putExtra("siteId", siteId);
            intent.setFlags(Intent.FLAG_ACTIVITY_BROUGHT_TO_FRONT);
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {

        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSION) {
            startThread();
        }
    }

    /**
     * Method to add check in data from shared preference to database (table tblCheckInMap).
     * Method is added in version 2.3.0 and before that We were allowed only single check in form
     * but now onward we are allowing user to have multiple check in forms and to manage this all data
     * those are in shared preference is add into database and remove preference key (formCheckInDetails)
     * from shared preference
     */
    private void checkAndAddCheckInDataInDatabase() {
        String valueJson = AppPrefShared.getString(Constants.PREF_FORM_CHECKIN_DETAIL, "");
        if (TextUtils.isEmpty(valueJson)) {
            return;
        }
        //migration from db version 13 to 14
        CheckInDetails checkInDetails = StaticUtils.getCheckInDetailData();
        CheckInMap checkInMap = new CheckInMap();

        checkInMap.setSiteId(checkInDetails.getSiteId());
        checkInMap.setFormId(checkInDetails.getFormId());
        checkInMap.setSiteName(checkInDetails.getSiteName());
        checkInMap.setFormName(checkInDetails.getFormName());
        checkInMap.setFormPkId(checkInDetails.getFormPkId());
        checkInMap.setCreatedDate(System.currentTimeMillis());

        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        tblCheckInMap.insertSingleData(checkInMap);

        AppPrefShared.removeKey(Constants.PREF_FORM_CHECKIN_DETAIL);

    }

    @Override
    protected void onDestroy() {
        if (thread != null) {
            thread.interrupt();
        }
        super.onDestroy();
    }

}
