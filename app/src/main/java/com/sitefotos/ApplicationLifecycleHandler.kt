package com.sitefotos

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.content.ComponentCallbacks2
import android.content.res.Configuration
import android.os.Bundle
import com.sitefotos.interfaces.MainAppCallBackEvent
import com.sitefotos.storage.AppPrefShared

class ApplicationLifecycleHandler internal constructor(private val mainAppCallBackEvent: MainAppCallBackEvent) : ActivityLifecycleCallbacks, ComponentCallbacks2 {
    override fun onActivityCreated(activity: Activity, bundle: Bundle?) {}
    override fun onActivityStarted(activity: Activity) {}
    override fun onActivityResumed(activity: Activity) {
        if (AppPrefShared.getBoolean("isAppInBackground", true)) {
            mainAppCallBackEvent.appInForeground()
            AppPrefShared.putValue("isAppInBackground", false)
        }
    }

    override fun onActivityPaused(activity: Activity) {}
    override fun onActivityStopped(activity: Activity) {}
    override fun onActivitySaveInstanceState(activity: Activity, bundle: Bundle) {}
    override fun onActivityDestroyed(activity: Activity) {}
    override fun onConfigurationChanged(configuration: Configuration) {}
    override fun onLowMemory() {}
    override fun onTrimMemory(i: Int) {
        if (i == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            mainAppCallBackEvent.appInBackground()
            AppPrefShared.putValue("isAppInBackground", true)
        }
    }

}