package com.sitefotos.main;

import static com.sitefotos.Constants.CAMERA_PERMISSIONS_REQUEST;
import static com.sitefotos.Constants.EXTERNAL_STORAGE_REQUEST;
import static com.sitefotos.Constants.FORM_DETAIL_RESULT;
import static com.sitefotos.Constants.LOCATION_PERMISSION_REQUEST;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE_CAMERA_SCREEN;
import static com.sitefotos.Constants.LOGGED_IN_USER_COMPANY_ID;
import static com.sitefotos.Constants.LOGGED_IN_USER_EMAIL_ADDRESS;
import static com.sitefotos.Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT;
import static com.sitefotos.Constants.MAP_ACTIVITY_REQUEST;
import static com.sitefotos.Constants.PARAM_ACCESS_CODE;
import static com.sitefotos.Constants.PARAM_EMAIL;
import static com.sitefotos.Constants.PARAM_LAT;
import static com.sitefotos.Constants.PARAM_LON;
import static com.sitefotos.Constants.SITE_DETAIL_REQUEST_CODE;
import static com.sitefotos.util.StaticUtils.checkAndDecompressAutoFillData;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.Constants;
import com.sitefotos.PermissionOverlayActivity;
import com.sitefotos.R;
import com.sitefotos.Settings.SettingFragment;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.databinding.ActivityMainBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.AppStatusChangedEvent;
import com.sitefotos.event.GPSSettingEventEvent;
import com.sitefotos.event.TMFormCheckInOutEvent;
import com.sitefotos.form.FormDetailActivity;
import com.sitefotos.form.FormListingFragment;
import com.sitefotos.form.TMFormDetailActivity;
import com.sitefotos.gallery.GalleryActivity;
import com.sitefotos.gallery.MimeType;
import com.sitefotos.gallery.SelectionSpec;
import com.sitefotos.main.fragment.camera.CameraFragment;
import com.sitefotos.main.fragment.clock.ClockFragment;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.FormData;
import com.sitefotos.models.Routes;
import com.sitefotos.models.SiteData;
import com.sitefotos.service.FileUploaderService;
import com.sitefotos.site.detail.SiteFormFragment;
import com.sitefotos.site.screens.SitesAndMapListFragment;
import com.sitefotos.site.screens.WorkFragment;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.storage.tables.TblRoutes;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PolygonCalculation;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import retrofit2.Response;

public class MainActivity extends BaseActivity implements OnAppDataApiResponse {
    public static double currentLatitude = 0.0;
    public static double currentLongitude = 0.0;


    public static double lastLatitude = 0.0;
    public static double lastLongitude = 0.0;


    public static long distanceAccuracy = 0;
    public boolean isGpsEnableCalled, isDialogueToHide = false;

    private WorkFragment workFragment = new WorkFragment();
    private FormListingFragment formListingFragment = new FormListingFragment();
    private SettingFragment settingFragment = new SettingFragment();
    public CameraFragment cameraFragment = new CameraFragment();
    private ClockFragment clockFragment = new ClockFragment();
    public Fragment activeFragment;
    private boolean isCameraVisible;
    SiteFormFragment siteFormFragment;
    SitesAndMapListFragment sitesAndMapListFragment;
    String currentActiveWorkFragment;
    private static final int TIME_INTERVAL = 1500;
    private long mBackPressedTime;
    private String selectedRouteId;
    private String selectedRouteName;
    private List<Long> selectedSiteIds;
    private SiteData selectedSiteData;
    public ActivityMainBinding binding;
    boolean isFinish;

    public ExecutorService dbExecutorService = Executors.newSingleThreadExecutor();

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        BaseApplication.getInstance().checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);
        setActionBarVisibility(false);
        StaticUtils.setPreferredDistanceUnit(this);
        initView();
    }

    private void initBinding() {
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onSaveInstanceState(Bundle outState) {
        //super.onSaveInstanceState(outState);
        //No call for super(). Bug on API Level > 11.
    }

    @Override
    protected void onStart() {
        super.onStart();
        checkLocationAvailabilityAndShowAlert();
        startLocationServiceIfNotStarted();

        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }


    @Override
    protected void onResume() {
        super.onResume();
        startLocationServiceIfNotStarted();
        try {
            new Handler().postDelayed(() -> callApiForAppData(false, false), 1500);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppStatusChangedEvent(AppStatusChangedEvent event) {
        if (event.isForeground) {
            try {
                new Handler().postDelayed(() -> callApiForAppData(false, true), 1500);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }

    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void GPSSettingEventEventEvent(GPSSettingEventEvent event) {
        if (event.gpsStatus == (GPSSettingEventEvent.GPSStatus.Enabled)) {
            startLocationServiceIfNotStarted();
        }
    }

    public void callApiForAppData(boolean fromLogin, boolean farceApiCall) {
        if (BaseApplication.getInstance().isOnline(this)) {
            if (!canGetLocation(this) && !TextUtils.isEmpty(PermissionOverlayActivity.class.getCanonicalName()) && BaseApplication.getInstance().getTopActivityName() != null && !BaseApplication.getInstance().getTopActivityName().equalsIgnoreCase(PermissionOverlayActivity.class.getCanonicalName())) {
                checkLocationAvailabilityAndShowAlert();
            }

            requestServerForUserData(this, fromLogin, farceApiCall, getUserParamsForAppDataApiCall());
        } else {
            onNoInternetConnection();
        }
    }

    private void initView() {
        binding.navView.setOnNavigationItemSelectedListener(mOnNavigationItemSelectedListener);
        addFragments();
        addOrRemoveTabBasedOnSettings();
        setActiveFragment();
        getBundleData();
        try {
            new Handler().postDelayed(this::checkAndShowPendingUploadDialog, 1000);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Function to get data from previous activity.
     */
    private void getBundleData() {
        if (getIntent() != null) {
            long formId = -1L;
            long siteId = -1L;
            if (getIntent().hasExtra("formId")) {
                formId = getIntent().getLongExtra("formId", -1);
            }
            if (getIntent().hasExtra("siteId")) {
                siteId = getIntent().getLongExtra("siteId", -1);
            }
            if (!getIntent().hasExtra("formId") && !getIntent().hasExtra("siteId")) {
                return;
            }
            if (siteId > 0) {
                String autoFill = getIntent().getStringExtra("autoFill");
                autoNavigateUserToSiteFormDetailScreen(siteId, formId, checkAndDecompressAutoFillData(autoFill));
                return;
            }

            if (siteId == 0 && formId > 0) {
                //Check Property by location match and show near ny properties if siteId is 0 and valid formId
                String autoFill = getIntent().getStringExtra("autoFill");
                getAndFilterProperties(formId, autoFill);
                return;
            }
            if (formId > 0 & siteId == -1) {
                //NavigateUser to Form detail through form listing.
                String autoFill = getIntent().getStringExtra("autoFill");
                autoNavigateUserToNormalFormScreen(formId, checkAndDecompressAutoFillData(autoFill));
                return;
            }

            if (formId == -1 && siteId == 0 || formId == 0 && siteId == 0 || formId == -1 && siteId == -1) {
                showForeGroundToast(getString(R.string.invalid_link));
            }
        }
    }

    private void getAndFilterProperties(long formId, String formData) {
        dbExecutorService.execute(() -> {
            TblProperties tblProperties = new TblProperties(this);
            TblCluster tblCluster = new TblCluster(this);
            List<PropertiesVo> lstProperties = tblProperties.getAllProperties();
            List<Cluster> lstClusters = tblCluster.getAllClusterData();
            PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
            polygonCalculation.buildingId = "0";
            List<PropertiesVo> lstProperty = polygonCalculation.getFilteredPropertyListFromPolygon(this, false, null, currentLatitude,
                    currentLongitude, distanceAccuracy, new ArrayList<>(), lstClusters, lstProperties);
            String mBuildingId = polygonCalculation.buildingId;
            new Handler(Looper.getMainLooper()).post(() -> {
                if (isFinishing())
                    return;
                long siteId = 0;
                // If it is inside property then skip property dialog view and navigate user to site's form list screen
                if (!TextUtils.isEmpty(mBuildingId)) {
                    try {
                        siteId = Long.parseLong(mBuildingId);
                    } catch (NumberFormatException ignored) {
                    }
                    if (siteId > 0) {
                        doAfterPropertySelected(siteId, formId, formData);
                    }
                }
                if (siteId == 0) {
                    try {
                        PopUtils.showPropertyDialog(this, lstProperty, false,
                                position -> {
                                    doAfterPropertySelected(lstProperty.get(position).getPropertyId(), formId, formData);
                                },
                                view -> {
                                },
                                view -> {
                                });
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            });
        });

    }

    private void doAfterPropertySelected(long siteId, long formId, String formData) {
        autoNavigateUserToSiteFormDetailScreen(siteId, formId, checkAndDecompressAutoFillData(formData));
    }

    /**
     * Function to navigate user to route, sitelist, siteFormList form detail screen based on deeplink data
     *
     * @param siteId siteId in long
     * @param formId formId in long
     */
    private void autoNavigateUserToSiteFormDetailScreen(long siteId, long formId, String autoFill) {
        try {
            setActiveFragment(workFragment);
            binding.navView.setSelectedItemId(R.id.navigation_work);
            //Check for Routes
            TblRoutes tblRoutes = new TblRoutes(this);
            Routes routes = tblRoutes.getRouteDataBySiteId(siteId);
            if (!TextUtils.isEmpty(routes.getRouteId())) {
                //Traverse through route fragments
                replaceSiteMapListFragment(true, routes.getRouteId(), routes.getRouteName(), routes.getSiteList());
            }
            autoNavigateToSiteFormDetailScreen(siteId, formId, autoFill);
        } catch (
                Exception e) {
            FirebaseEventUtils.logException(e);
        }

    }

    private void autoNavigateToSiteFormDetailScreen(long siteId, long formId, String autoFill) {
        TblTMForms tblForms = new TblTMForms(this);
        TblSites tblSites = new TblSites(this);
        SiteData siteData = tblSites.getDataFromSiteId(siteId);
        if (siteData.getSiteId() > 0) {
            if (formId > 0) {
                FormData formData = tblForms.getDataFromSiteAndFormId(siteId, formId);
                if (formData.getFormId() > 0) {
                    navigateToFormDetailScreen(siteData, formData, SITE_DETAIL_REQUEST_CODE, true, autoFill);
                } else {
                    replaceWorkFormListFragment(true, siteData);
                    showForeGroundToast(getString(R.string.deeplink_form_id_not_found));
                }
            } else {
                replaceWorkFormListFragment(true, siteData);
                if (formId > -1) {
                    showForeGroundToast(getString(R.string.deeplink_form_id_not_found));
                }
            }
        } else {
            showForeGroundToast(getString(R.string.deeplink_site_id_not_found));
        }
    }

    /**
     * Function to navigate user to form list or detail screen based on deeplink data
     *
     * @param formId formId in long
     */
    private void autoNavigateUserToNormalFormScreen(long formId, String autoFill) {
        try {
            setActiveFragment(formListingFragment);
            binding.navView.setSelectedItemId(R.id.navigation_form);
            //Check for form Data
            TblForms tblForms = new TblForms(this);
            FormData formData = tblForms.getFormDataFromId(formId);
            if (formData.getFormId() > 0) {
                Intent intent = new Intent(this, FormDetailActivity.class);
                intent.putExtra("mFormPkId", formData.getFormPKId());
                if (!TextUtils.isEmpty("autoFill")) {
                    intent.putExtra("autoFill", autoFill);
                }
                startActivityForResult(intent, FORM_DETAIL_RESULT);
                overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
            } else {
                showForeGroundToast(getString(R.string.deeplink_form_id_not_found));
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void checkAndShowPendingUploadDialog() {
        TblUploadImage tblUploadImage = new TblUploadImage(this);
        if ((tblUploadImage.getDataCount() > 0)) {
            ShowPendingImageUploadPrecessDialog();
        }
        requestToUpdateFCMToken();
    }

    private void addOrRemoveTabBasedOnSettings() {
        checkAndSetClockFragment();
        checkAndSetWorkFragment();
        checkAndSetFormFragment();
        checkAndSetCameraFragment();
    }

    private void addFragments() {
        getSupportFragmentManager().beginTransaction().add(R.id.main_container, settingFragment, "1").hide(settingFragment).commitAllowingStateLoss();

    }

    private void checkAndSetWorkFragment() {
        if (AppPrefShared.getInt(Constants.APP_SITE_PERMISSION, 0) > 0) {
            if (!workFragment.isAdded()) {
                getSupportFragmentManager().beginTransaction().add(R.id.main_container, workFragment, "3").hide(workFragment).commitAllowingStateLoss();
            }
            binding.navView.getMenu().findItem(R.id.navigation_work).setVisible(true);
        } else {
            binding.navView.getMenu().findItem(R.id.navigation_work).setVisible(false);
            if (activeFragment == workFragment) {
                setActiveFragment(cameraFragment);
                binding.navView.setSelectedItemId(R.id.navigation_camera);
            }
        }
    }

    private void checkAndSetFormFragment() {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_IS_FORM, 0) > 0) {
            if (!formListingFragment.isAdded()) {
                getSupportFragmentManager().beginTransaction().add(R.id.main_container, formListingFragment, "4").hide(formListingFragment).commitAllowingStateLoss();
            }
            binding.navView.getMenu().findItem(R.id.navigation_form).setVisible(true);
        } else {
            binding.navView.getMenu().findItem(R.id.navigation_form).setVisible(false);
            if (activeFragment == formListingFragment) {
                setActiveFragment(cameraFragment);
                binding.navView.setSelectedItemId(R.id.navigation_camera);
            }
        }
    }

    private void checkAndSetClockFragment() {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) > 0) {
            if (!clockFragment.isAdded()) {
                getSupportFragmentManager().beginTransaction().add(R.id.main_container, clockFragment, "2").hide(clockFragment).commitAllowingStateLoss();
            }
            binding.navView.getMenu().findItem(R.id.navigation_clock).setVisible(true);
        } else {
            binding.navView.getMenu().findItem(R.id.navigation_clock).setVisible(false);
            if (activeFragment == clockFragment) {
                if (AppPrefShared.getInt(Constants.APP_SITE_PERMISSION, 0) > 0) {
                    setActiveFragment(workFragment);
                    binding.navView.setSelectedItemId(R.id.navigation_work);
                } else {
                    setActiveFragment(settingFragment);
                    binding.navView.setSelectedItemId(R.id.navigation_setting);
                }
            }

        }
    }

    private void checkAndSetCameraFragment() {
        if (!AppPrefShared.getBoolean(Constants.APP_DISABLE_MAIN_CAMERA, false)) {
            if (!cameraFragment.isAdded()) {
                getSupportFragmentManager().beginTransaction().add(R.id.main_container, cameraFragment, "5").hide(cameraFragment).commitAllowingStateLoss();
            }
            binding.navView.getMenu().findItem(R.id.navigation_camera).setVisible(true);
        } else {
            binding.navView.getMenu().findItem(R.id.navigation_camera).setVisible(false);
            isCameraVisible = false;
            if (activeFragment == cameraFragment) {
                setActiveFragment(settingFragment);
                binding.navView.setSelectedItemId(R.id.navigation_setting);
            }
        }
    }

    private BottomNavigationView.OnNavigationItemSelectedListener mOnNavigationItemSelectedListener = item -> {
        int itemId = item.getItemId();
        if (itemId == R.id.navigation_camera) {
            if (activeFragment != cameraFragment) {
                setActiveFragment(cameraFragment);
                cameraFragment.checkAndAskForCameraPermission();
            }
            return true;
        } else if (itemId == R.id.navigation_clock) {
            if (activeFragment != clockFragment) {
                setActiveFragment(clockFragment);
            }
            return true;
        } else if (itemId == R.id.navigation_work) {
            if (activeFragment != workFragment) {
                if (currentActiveWorkFragment != null) {
                    if (currentActiveWorkFragment.equals(SiteFormFragment.class.getName())) {
                        replaceWorkFormListFragment(false, selectedSiteData);
                    } else if (currentActiveWorkFragment.equals(SitesAndMapListFragment.class.getName())) {
                        replaceSiteMapListFragment(false, selectedRouteId, selectedRouteName, selectedSiteIds);
                    } else {
                        setActiveFragment(workFragment);
                    }
                } else {
                    setActiveFragment(workFragment);
                }
            }
            return true;
        } else if (itemId == R.id.navigation_form) {
            if (activeFragment != formListingFragment) {
                setActiveFragment(formListingFragment);
                //formListingFragment.uploadProcessDoneCallBack();
                formListingFragment.checkAndNavigateToDetailScreen();
            }
            return true;
        } else if (itemId == R.id.navigation_setting) {
            if (activeFragment != settingFragment) {
                setActiveFragment(settingFragment);
                //settingFragment.uploadProcessDoneCallBack();
            }
            return true;
        }
        return false;
    };


    private void setActiveFragment(Fragment selectedFragment) {
        if (activeFragment != selectedFragment) {
            if (activeFragment == null) {
                getSupportFragmentManager().beginTransaction().show(selectedFragment).commitAllowingStateLoss();
            } else if (activeFragment == siteFormFragment || activeFragment == sitesAndMapListFragment) {
                FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
                transaction.setCustomAnimations(R.anim.enter_from_left, R.anim.exit_to_right, R.anim.enter_from_left, R.anim.exit_to_right);
                transaction.remove(activeFragment).show(selectedFragment).commitAllowingStateLoss();
            } else {
                getSupportFragmentManager().beginTransaction().hide(activeFragment).show(selectedFragment).commitAllowingStateLoss();
            }
            activeFragment = selectedFragment;
            updateCurrentWorkFragmentName();
        }
        if (selectedFragment == cameraFragment) {
            isCameraVisible = true;
            cameraFragment.enableDisableCamera(true, true);
        } else {
            if (!AppPrefShared.getBoolean(Constants.APP_DISABLE_MAIN_CAMERA, false)) {
                isCameraVisible = false;
                cameraFragment.enableDisableCamera(true, false);
            }
        }
    }


    private void ShowPendingImageUploadPrecessDialog() {
        PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.txt_pending_image_upload), getString(R.string.upload), getString(R.string.ignore), false, (dialog, which) -> navigateToUploadActivityScreen(null), (dialog, which) -> {
        });
    }


    private void setActiveFragment() {
        int screenType = AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_SCREEN_TYPE, 0);
        int sum = 0;
        try {
            TblClockCrew tblClockCrew = new TblClockCrew(this);
            int resumedData = tblClockCrew.getAllResumedWorkCrewData().size();
            int breakedData = tblClockCrew.getAllBreakedCrewData().size();
            sum = resumedData + breakedData;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        if (sum == 0 && AppPrefShared.getInt(LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) > 0) {
            setActiveFragment(clockFragment);
            binding.navView.setSelectedItemId(R.id.navigation_clock);
        } else {
            switch (screenType) {
                case 1:
                    setActiveFragment(formListingFragment);
                    if (formListingFragment != null) formListingFragment.checkAndNavigateToDetailScreen();
                    binding.navView.setSelectedItemId(R.id.navigation_form);
                    break;
                case 2:
                    if (AppPrefShared.getInt(Constants.APP_SITE_PERMISSION, 0) > 0) {
                        if (sum > 0) {
                            setActiveFragment(workFragment);
                            binding.navView.setSelectedItemId(R.id.navigation_work);
                        } else {
                            if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 0) {
                                setActiveFragment(workFragment);
                                binding.navView.setSelectedItemId(R.id.navigation_work);
                            } else {
                                setActiveFragment(clockFragment);
                                binding.navView.setSelectedItemId(R.id.navigation_clock);
                            }
                        }
                    } else if (!AppPrefShared.getBoolean(Constants.APP_DISABLE_MAIN_CAMERA, false)) {
                        setActiveFragment(cameraFragment);
                        binding.navView.setSelectedItemId(R.id.navigation_camera);
                    } else {
                        setActiveFragment(settingFragment);
                        binding.navView.setSelectedItemId(R.id.navigation_setting);
                    }
                    break;
                case 0:
                default:
                    checkAndSetCameraFragment();
                    break;
            }
        }
    }

    public void showGPSSettingAlert(boolean isForAddProperty, int locationRequestCode) {
        String message;
        if (isForAddProperty) {
            message = getString(R.string.to_add_new_property_sitefotos_requires_access_to_your_location);
        } else {
            message = getString(R.string.gps_not_enabled);
        }
        if (!isDialogueToHide) {
            PopUtils.showGPSSettingsAlert(this, message, (dialogInterface, i) -> PopUtils.displayLocationSettingsRequest(this, locationRequestCode), (dialogInterface, i) -> isGpsEnableCalled = true);
        }
    }


    private HashMap<String, Object> getUserParamsForAppDataApiCall() {
        HashMap<String, Object> params = new HashMap<>();
        params.put(PARAM_ACCESS_CODE, AppPrefShared.getString(LOGGED_IN_USER_COMPANY_ID, ""));
        params.put(PARAM_EMAIL, AppPrefShared.getString(LOGGED_IN_USER_EMAIL_ADDRESS, ""));
        params.put(PARAM_LAT, currentLatitude);
        params.put(PARAM_LON, currentLongitude);
        params.put(Constants.PARAM_LANG, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());

        return params;
    }


    @SuppressLint("MissingPermission")
    public void requestAPIToUpdateLocation() {
    }

    /**
     * temp
     * Check location permission. show required permission dialog if user set permanent denied
     */
    public void requestLocationPermission() {
        if (PermissionUtils.shouldShowRequestPermissions(this, PermissionUtils.getLocationPermissions())) {
            PermissionUtils.requestPermission(this, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
        } else {
            if (!PermissionUtils.hasPermissions(this, PermissionUtils.getLocationPermissions())) {
                PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to), getString(R.string.open), getString(R.string.txt_cancel), false, (dialog, which) -> PermissionUtils.navigateUserToPermissionScreen(this), (dialog, which) -> {

                });
            }

        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        switch (requestCode) {
            case CAMERA_PERMISSIONS_REQUEST:
            case EXTERNAL_STORAGE_REQUEST:
                if (cameraFragment != null)
                    cameraFragment.onRequestPermissionsResult(requestCode, permissions, grantResults);
                break;
            case LOCATION_PERMISSION_REQUEST:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    startLocationServiceIfNotStarted();
                } else {
                    PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to), getString(R.string.open), getString(R.string.txt_cancel), false, (dialog, which) -> {
                        PermissionUtils.requestPermission(this, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
                    }, (dialog, which) -> {

                    });
                }
                break;
        }
    }


    public void replaceWorkFormListFragment(boolean shouldAnimate, SiteData siteData) {
        siteFormFragment = new SiteFormFragment();
        Bundle args = new Bundle();
        args.putParcelable("siteData", siteData);
        siteFormFragment.setArguments(args);
        selectedSiteData = siteData;
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        if (shouldAnimate) transaction.setCustomAnimations(R.anim.enter_from_right, R.anim.exit_to_left, R.anim.enter_from_right, R.anim.exit_to_left);
        transaction.add(R.id.main_container, siteFormFragment, "6").hide(activeFragment);
        transaction.commitAllowingStateLoss();
        activeFragment = siteFormFragment;
        updateCurrentWorkFragmentName();
    }

    boolean isFromWorkFragment;

    public void setIsFromWorkFragment(boolean isFromWorkFragment) {
        this.isFromWorkFragment = isFromWorkFragment;
    }

    public void replaceSiteMapListFragment(boolean shouldAnimate, String routeId, String routeName, List<Long> siteIds) {
        sitesAndMapListFragment = new SitesAndMapListFragment();
        Bundle args = new Bundle();
        args.putString("siteIds", StaticUtils.getStringFromList(siteIds));
        args.putString("routeName", routeName);
        args.putInt("routeId", Integer.parseInt(routeId));
        sitesAndMapListFragment.setArguments(args);

        selectedRouteId = routeId;
        selectedRouteName = routeName;
        selectedSiteIds = siteIds;

        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        if (shouldAnimate) {
            transaction.setCustomAnimations(R.anim.enter_from_right, R.anim.exit_to_left, R.anim.enter_from_right, R.anim.exit_to_left);
        }
        transaction.add(R.id.main_container, sitesAndMapListFragment, "7").hide(activeFragment).commitAllowingStateLoss();
        activeFragment = sitesAndMapListFragment;
        updateCurrentWorkFragmentName();
    }

    private void updateCurrentWorkFragmentName() {
        if (activeFragment.getClass().getName().equals(WorkFragment.class.getName()) || activeFragment.getClass().getName().equals(SiteFormFragment.class.getName()) || activeFragment.getClass().getName().equals(SitesAndMapListFragment.class.getName())) {
            currentActiveWorkFragment = activeFragment.getClass().getName();
        }
    }

    /**
     * Method to select Multiple/single image from gallery.
     */
    public void openGalleryForImageSelection() {
        Intent intent = new Intent(this, GalleryActivity.class);
        SelectionSpec.getInstance().mimeTypeSet = MimeType.ofImage();
        SelectionSpec.getInstance().showSingleMediaType = true;
        startActivityForResult(intent, Constants.ACTION_CODE_PICK_FROM_GALLERY);


    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case Constants.ACTION_CODE_PICK_FROM_GALLERY:
            case LOCATION_REQUEST_CODE_CAMERA_SCREEN:
                if (cameraFragment != null)
                    cameraFragment.onActivityResult(requestCode, resultCode, data);
                break;
            case LOCATION_REQUEST_CODE:
                if (resultCode == RESULT_OK) {
                    doAfterUserOnGPSLocation(LOCATION_REQUEST_CODE);
                    isGpsEnableCalled = true;
                }
                break;
            case MAP_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (cameraFragment != null)
                        cameraFragment.onActivityResult(requestCode, resultCode, data);
                }
                break;
            case Constants.SITE_DETAIL_REQUEST_CODE:
                if (resultCode == Activity.RESULT_OK) {
                    if (data.getIntExtra("ResultType", 1) == 2) {
                        setActiveFragment(clockFragment);
                        binding.navView.setSelectedItemId(R.id.navigation_clock);
                    }
                }
                break;

            case Constants.OVERLAY_ACTIVITY_REQUEST:
                if (resultCode == Activity.RESULT_OK) {
                    startLocationServiceIfNotStarted();
                    if (workFragment != null) {
                        workFragment.onPullToRefresh();
                    }
                }
                break;
        }
    }

    public void doAfterUserOnGPSLocation(int requestCode) {
        isGpsEnableCalled = true;
        startLocationServiceIfNotStarted();
    }

    public void showClockScreen() {
        if (mOnNavigationItemSelectedListener != null) {
            mOnNavigationItemSelectedListener.onNavigationItemSelected(binding.navView.getMenu().findItem(R.id.navigation_clock));
            binding.navView.setSelectedItemId(R.id.navigation_clock);
        }
    }

    public void navigateUserToDefaultMapApp(double latitude, double longitude) {
        String uri = String.format(Locale.ENGLISH, "http://maps.google.com/maps?daddr=%f,%f", latitude, longitude);
        Intent intent = new Intent(android.content.Intent.ACTION_VIEW, Uri.parse(uri));
        startActivity(intent);
    }


    public void navigateToFormDetailScreenFromClockScreen(CheckInMap checkInMap, int requestCode) {
        TblTMForms tblTMForms = new TblTMForms(this);
        TblSites tblSites = new TblSites(this);
        SiteData siteData = tblSites.getDataFromSiteId(checkInMap.getSiteId());
        if (siteData.getSiteId() < 0) {
            siteData.setSiteName(checkInMap.getSiteName());
            siteData.setSiteId(checkInMap.getSiteId());
        }
        FormData formData = tblTMForms.getFormDataByPKId(checkInMap.getFormPkId());
        if (formData.getFormPKId() > 0) {
            navigateToDetailScreen(siteData, checkInMap.getFormPkId(), requestCode, "");
        } else {
            deleteCheckInMapDataByPK(checkInMap.getPkId());
        }
    }

    public void navigateToFormDetailScreen(SiteData siteData, FormData formData, int requestCode, boolean autoNavigate,boolean isFromRoutes) {
        navigateToFormDetailScreen(siteData, formData, requestCode, autoNavigate);
        if (isFromRoutes) {
            sitesAndMapListFragment = null;
        }
    }

    public void navigateToFormDetailScreen(SiteData siteData, FormData formData, int requestCode, boolean autoNavigate) {
        navigateToFormDetailScreen(siteData, formData, requestCode, autoNavigate, "");
    }

    public void navigateToFormDetailScreen(SiteData siteData, FormData formData, int requestCode, boolean autoNavigate, String autoFill) {
        dbExecutorService.execute(() -> {
            List<FormData> lstForms = getSiteFormList(siteData);
            new Handler(Looper.getMainLooper()).post(() -> {
                if (isFinishing())
                    return;
                if (lstForms.size() == 1) {
                    FormData data = lstForms.get(0);
                    navigateToDetailScreen(siteData, data.getFormPKId(), requestCode, autoFill);
                } else {
                    replaceWorkFormListFragment(true, siteData);
                    if (autoNavigate && formData != null) {
                        navigateToTNMFormDetailScreen(siteData, formData, autoFill);
                    }
                }
            });
        });
    }

    private void navigateToTNMFormDetailScreen(SiteData siteData, FormData data, String autoFill) {
        Intent intent = new Intent(this, TMFormDetailActivity.class);
        intent.putExtra("formPkId", data.getFormPKId());
        intent.putExtra("siteData", siteData);
        intent.putExtra("autoFill", autoFill);
        startActivityForResult(intent, Constants.SITE_DETAIL_REQUEST_CODE);
        overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
    }

    public List<FormData> getSiteFormList(SiteData siteData) {
        TblTMForms tblTMForms = new TblTMForms(this);
        return tblTMForms.getAllFormsBySiteId(siteData.getSiteId());
    }


    private void navigateToDetailScreen(SiteData siteData, int formPkId, int requestCode, String autoFill) {
        Intent intent = new Intent(this, TMFormDetailActivity.class);
        intent.putExtra("formPkId", formPkId);
        intent.putExtra("siteData", siteData);
        intent.putExtra("autoFill", autoFill);
        startActivityForResult(intent, requestCode);
        overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
    }


    public void clearFormData(boolean isCheckInForm, SiteData siteData, FormData formData) {
        clearDataFromDatabase(formData);
        if (isCheckInForm) {
            //If form is already checked in then only send checkout breadcrumb
            if (formData.getCheckin_time() > 0) {
                checkAndDeleteCheckInMapData(formData);
                // send checkout with type 3 with one additional parameter (cancelCheckIn)
                prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), true, false, 0);
                EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
            }
        }
        showForeGroundToast(getString(R.string.msg_form_reset));
    }

    public void clearDataFromDatabase(FormData formData) {
        TblTMForms tblTMForms = new TblTMForms(this);
        if (formData.hasSubForm()) {
            //Delete All SubForm of the form
            tblTMForms.deleteDataByMainFormPKId(TblTMForms.TABLE_NAME, formData.getFormPKId());
        }
        tblTMForms.resetFormDataByPkId(formData.getFormPKId(), formData.getFormData(), formData.isCheckInOutComplete());
        formData.setModifiedFormData(formData.getFormData());
    }

    public void checkAndDeleteCheckInMapData(FormData formData) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        if (tblCheckInMap.isDataExist()) {
            tblCheckInMap.deleteDataById(formData.getFormPKId());
        }
    }

    /**
     * This method is managed the visibility of bottom menu
     *
     * @param visibility VISIBLE or GONE
     */
    public void manageBottomViewVisibility(int visibility) {
        binding.navView.setVisibility(visibility);
    }

    public boolean getCameraVisibilityStatus() {
        return isCameraVisible;
    }

    @Override
    public void onSuccessResponse(Response<AppDataResponse> response) {
        EventBus.getDefault().post(new AppDataCallBackEvent(response));
        addOrRemoveTabBasedOnSettings();
    }

    @Override
    public void onFailureResponse(Throwable t) {
        EventBus.getDefault().post(new AppDataCallBackEvent(t));
    }

    @Override
    public void onNoInternetConnection() {
        EventBus.getDefault().post(new AppDataCallBackEvent(true));
    }

    @Override
    public void onUpdateAppVersion(int versionCode, boolean isForceUpdate) {
        checkAndShowAppUpdateDialog(versionCode, isForceUpdate);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }

    @Override
    public void onBackPressed() {
        if (activeFragment == cameraFragment && cameraFragment != null && cameraFragment.isPreviewMode) {
            //Remove captured photo at back pressed
            cameraFragment.trashData();
            manageBottomViewVisibility(View.VISIBLE);
        } else if (sitesAndMapListFragment != null && activeFragment == siteFormFragment) {
            if (workFragment.isRoute() && !workFragment.isAllSiteSelected()) setActiveFragment(sitesAndMapListFragment);
            else setActiveFragment(workFragment);

        } else if (activeFragment == sitesAndMapListFragment || activeFragment == siteFormFragment) {
            setActiveFragment(workFragment);
        } else {
            if (mBackPressedTime + TIME_INTERVAL > System.currentTimeMillis()) {
                //System.exit(0);
                isFinish = true;
                //manageMainIcon();
                finishAffinity();
                return;
            } else {
                showForeGroundToast(getString(R.string.close_app_confirm_message));
            }
            mBackPressedTime = System.currentTimeMillis();
        }
    }
}
