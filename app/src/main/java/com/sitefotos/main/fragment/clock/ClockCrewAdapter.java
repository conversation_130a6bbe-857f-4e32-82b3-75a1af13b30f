package com.sitefotos.main.fragment.clock;

import android.content.Context;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Chronometer;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.ItemClockCrewMemberBinding;
import com.sitefotos.databinding.ItemClockStickyHeaderBinding;
import com.sitefotos.models.ClockCrewData;
import com.sitefotos.models.ClockHeader;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.views.StickHeaderItemDecoration;

import java.util.ArrayList;

public class ClockCrewAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements StickHeaderItemDecoration.StickyHeaderInterface {

    private Context context;
    private ArrayList<Object> mList = new ArrayList<>();
    private OnItemSelectedListener onItemSelectedListener;
    private ItemClockStickyHeaderBinding stickyHeaderBinding;
    private ItemClockCrewMemberBinding binding;

    public interface OnItemSelectedListener {
        void onItemClicked(int position, View view);

        void onHeaderBtnClicked(int position, View view);

        void onCrewChecked(int position, boolean isChecked,ClockCrewData data);

        void onLongPressed(int position, View view);

        void onImageCheckedChangeListener(int position, boolean isChecked);
    }

    public ClockCrewAdapter(Context context, OnItemSelectedListener onItemSelected) {
        this.context = context;
        this.onItemSelectedListener = onItemSelected;
    }


    public void updateList(ArrayList<Object> mList) {
        this.mList = mList;
        notifyDataSetChanged();
    }


    public ArrayList<Object> getList() {
        return mList;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case Constants.ADAPTER_VIEW_TYPE_HEADER:
                stickyHeaderBinding = ItemClockStickyHeaderBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
                return new HeaderViewHolder(stickyHeaderBinding.getRoot());
            default:
                binding = ItemClockCrewMemberBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
                return new ViewHolder(binding);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        if (holder instanceof ViewHolder) {
            ((ViewHolder) holder).bindData(position);
        } else if (holder instanceof HeaderViewHolder) {
            ((HeaderViewHolder) holder).bindData(position);
        }
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }

    @Override
    public int getItemViewType(int position) {
        if (position < 0||position >= mList.size()) {
            return Constants.ADAPTER_VIEW_TYPE_HEADER;
        }
        if (mList.get(position) instanceof ClockHeader) {
            return Constants.ADAPTER_VIEW_TYPE_HEADER;
        }
        return Constants.ADAPTER_VIEW_TYPE_DEFAULT;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvCrewMemberName;
        Chronometer cmTime;
        AppCompatImageView ivStatus;
        AppCompatCheckBox cbCrew;

        ViewHolder(ItemClockCrewMemberBinding binding) {
            super(binding.getRoot());
            tvCrewMemberName = binding.tvCrewMemberName;
            cmTime = binding.cmTime;
            ivStatus = binding.ivStatus;
            cbCrew = binding.cbCrew;
        }

        void bindData(int position) {
            ClockCrewData data = (ClockCrewData) mList.get(position);
            tvCrewMemberName.setText(data.getCrewName());
            if (data.getCrewId() == StaticUtils.getEmployeeIdInInt()) {
                tvCrewMemberName.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
            } else {
                tvCrewMemberName.setTextColor(ContextCompat.getColor(context, R.color.black));
            }
            cbCrew.setOnCheckedChangeListener(null);
            cbCrew.setChecked(data.isSelected());
            if (data.getStartSession() > 0) {
                ivStatus.setVisibility(View.VISIBLE);
                long totalTime;
                if (data.getBreakTime() > 0) {
                    cmTime.setVisibility(View.VISIBLE);
                    totalTime = data.getTotalSession();
                    cmTime.setBase(SystemClock.elapsedRealtime() - totalTime);
                    cmTime.setText(getChronometerTime(SystemClock.elapsedRealtime() - cmTime.getBase()));
                    cmTime.stop();
                    ivStatus.setBackgroundResource(R.drawable.drawable_circle_gray);
                } else {
                    totalTime = data.getTotalSession() + (System.currentTimeMillis() - data.getStartSession());
                    cmTime.setBase(SystemClock.elapsedRealtime() - totalTime);
                    cmTime.start();
                    ivStatus.setBackgroundResource(R.drawable.drawable_circle_green);
                }
                cmTime.setVisibility(View.VISIBLE);
            } else {
                cmTime.setBase(SystemClock.elapsedRealtime());
                cmTime.setText("00:00:00");
                cmTime.setVisibility(View.GONE);
                ivStatus.setVisibility(View.INVISIBLE);
                cmTime.stop();
            }

            cmTime.setOnChronometerTickListener(null);
            cmTime.setOnChronometerTickListener(chronometer -> {
                long time = SystemClock.elapsedRealtime() - chronometer.getBase();
                chronometer.setText(getChronometerTime(time));
            });

            cbCrew.setOnCheckedChangeListener((buttonView, isChecked) -> {
                onItemSelectedListener.onCrewChecked(position, isChecked,data);

            });
            itemView.setOnClickListener(v -> onItemSelectedListener.onCrewChecked(position, !data.isSelected(),data));
            itemView.setOnLongClickListener(v -> {
                onItemSelectedListener.onLongPressed(position, v);
                return false;
            });
        }
    }

    private String getChronometerTime(long time) {
        int h = (int) (time / 3600000);
        int m = (int) (time - h * 3600000) / 60000;
        int s = (int) (time - h * 3600000 - m * 60000) / 1000;
        return (h) + ":" + (m < 10 ? "0" + m : m) + ":" + (s < 10 ? "0" + s : s);
    }

    class HeaderViewHolder extends RecyclerView.ViewHolder {

        AppCompatTextView tvHeader;
        AppCompatTextView btnBreakResume;
        RelativeLayout llRootLayout;
        ImageView ivHeaderCrewSelect;

        HeaderViewHolder(View itemView) {
            super(itemView);
            tvHeader = itemView.findViewById(R.id.tvHeaderTitle);
            btnBreakResume = itemView.findViewById(R.id.btnBreakResume);
            llRootLayout = itemView.findViewById(R.id.llRootLayout);
            ivHeaderCrewSelect = itemView.findViewById(R.id.ivHeaderCrewSelect);
        }

        void bindData(int position) {
            ClockHeader headerData = ((ClockHeader) mList.get(position));
            tvHeader.setText(headerData.getTitle());
            ivHeaderCrewSelect.setTag(headerData);
            updateSelection(headerData, ivHeaderCrewSelect);
            switch (headerData.getHeaderId()) {
                case 1:
                    btnBreakResume.setText(context.getString(R.string.txt_break));
                    btnBreakResume.setBackgroundResource(R.drawable.shape_button_background);
                    headerData.setBtnName(context.getString(R.string.txt_break));
                    llRootLayout.setVisibility(View.VISIBLE);
                    break;
                case 2:
                    btnBreakResume.setText(context.getString(R.string.txt_start));
                    btnBreakResume.setBackgroundResource(R.drawable.shape_button_background);
                    llRootLayout.setVisibility(View.VISIBLE);
                    headerData.setBtnName(context.getString(R.string.txt_start));
                    break;
                case 3:
                    btnBreakResume.setText("");
                    btnBreakResume.setBackgroundResource(R.drawable.transparent_background);
                    llRootLayout.setVisibility(View.VISIBLE);
                    headerData.setBtnName("");
                    break;
                case 4:
                    btnBreakResume.setText(context.getString(R.string.remove));
                    btnBreakResume.setBackgroundResource(R.drawable.shape_button_background);
                    llRootLayout.setVisibility(View.VISIBLE);
                    headerData.setBtnName(context.getString(R.string.remove));
                    break;
            }

            btnBreakResume.setOnClickListener(v ->
                    onItemSelectedListener.onHeaderBtnClicked(position, v));

            ivHeaderCrewSelect.setOnClickListener(v -> {
                headerData.setAllSelect(!headerData.isAllSelect());
                updateSelection(headerData, ivHeaderCrewSelect);
                onItemSelectedListener.onImageCheckedChangeListener(position, headerData.isAllSelect());
            });
        }
    }

    private void updateSelection(ClockHeader headerData, ImageView ivHeaderCrewSelect) {
        if (headerData.isAllSelect()) {
            ivHeaderCrewSelect.setImageResource(R.drawable.ic_checkbox_selected);
        } else {
            ivHeaderCrewSelect.setImageResource(R.drawable.ic_checkbox_unselected);
        }
    }

    @Override
    public int getHeaderPositionForItem(int itemPosition) {
        int headerPosition = -1;
        do {
            if (this.isHeader(itemPosition)) {
                headerPosition = itemPosition;
                break;
            }
            itemPosition -= 1;
        } while (itemPosition >= 0);
        return headerPosition;
    }

    @Override
    public int getHeaderLayout(int headerPosition) {
        return R.layout.item_clock_sticky_header;
    }

    @Override
    public void bindHeaderData(View header, int headerPosition) {
        new HeaderViewHolder(header).bindData(headerPosition);
    }

    @Override
    public boolean isHeader(int itemPosition) {
        return getItemViewType(itemPosition) == Constants.ADAPTER_VIEW_TYPE_HEADER;
    }
}
