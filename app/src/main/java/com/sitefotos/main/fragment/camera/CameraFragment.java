package com.sitefotos.main.fragment.camera;

import static android.app.Activity.RESULT_OK;
import static com.sitefotos.Constants.CAMERA_PERMISSIONS_REQUEST;
import static com.sitefotos.Constants.EXTERNAL_STORAGE_REQUEST;
import static com.sitefotos.Constants.LOCATION_PERMISSION_REQUEST;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE_CAMERA_SCREEN;
import static com.sitefotos.Constants.LOGGED_IN_USER_PARAM_SKIP_GEO;
import static com.sitefotos.Constants.LOGGED_IN_USER_TAGS;
import static com.sitefotos.util.MyCameraUtils.getTagData;
import static com.sitefotos.util.MyCameraUtils.resetTagData;
import static com.sitefotos.util.MyCameraUtils.updateTagCount;
import static com.sitefotos.util.PermissionUtils.CAMERA_PERMISSION;
import static com.sitefotos.util.PermissionUtils.hasStoragePermissions;
import static com.sitefotos.util.PermissionUtils.navigateUserToPermissionScreen;

import android.animation.LayoutTransition;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.PointF;
import android.location.Location;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.otaliastudios.cameraview.CameraException;
import com.otaliastudios.cameraview.CameraListener;
import com.otaliastudios.cameraview.CameraOptions;
import com.otaliastudios.cameraview.PictureResult;
import com.otaliastudios.cameraview.controls.Facing;
import com.otaliastudios.cameraview.controls.Flash;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.asynk.UploadMultipleImagesTask;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.databinding.FragmentCameraBinding;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.gallery.PathUtils;
import com.sitefotos.interfaces.OnTagSelected;
import com.sitefotos.main.MainActivity;
import com.sitefotos.map.MapViewFragment;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.Tags;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.MyCameraUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PolygonCalculation;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;
import com.sitefotos.util.views.CameraDrawingView;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class CameraFragment extends BaseFragment implements View.OnClickListener {

    private CameraDrawingView myViewDrawing;
    private int colorSelected;
    private Bitmap bmpresizedImageTakenFromCamera;
    private Bitmap bitmapCapturedImageWithDrawing, bitmapCapturedImageWithDrawingLow;
    private boolean isGalleryMode = false;
    public boolean isEditModeDrawing = false, isEditModeCaption = false, isPreviewMode = false, isDialogueToShow = false;
    private List<PropertiesVo> lstProperty = new ArrayList<>();
    private String mBuildingId = "0", buildingName = "";
    private String dateExifImageCaptured = "";
    public Boolean isGpsEnableCalled = false;
    private boolean isPermissionLocationCalled = false;
    private String strWeather = "", strCaption = "";
    // Get exif data from ExifInterface
    private Location mLocationExif = null;
    private List<String> lstIgnoreList = new ArrayList<>();
    private MapViewFragment mapFragment;
    private String strMediaPath = "";
    private boolean isGalleryClicked;
    private Context context;
    private CamListener camListener;
    private List<Tags> lstTags = new ArrayList<>();
    public FragmentCameraBinding binding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentCameraBinding.inflate(inflater, container, false);
        context = getContext();

        enableLayoutTransition();
        initView();
        return binding.getRoot();
    }


    private void initView() {
        init();
        setOnClickListener();
    }

    public void init() {
        initCameraPermissionGranted();
        setUpCamera();
        enableDisableCamera(false, true);
    }

    /**
     * Function to enable layout change animation to show or hide target locator view.
     */
    private void enableLayoutTransition() {
        LayoutTransition layoutTransition = new LayoutTransition();
        layoutTransition.enableTransitionType(LayoutTransition.CHANGING);
        layoutTransition.setAnimateParentHierarchy(true);
        binding.cameraMapView.rlCameraMapLocatorView.setLayoutTransition(layoutTransition);
    }

    private void initCameraPermissionGranted() {
        // camera preview surface variables
        if (getActivity() != null)
            ((BaseActivity) getActivity()).progressBarWebView.setVisibility(View.GONE);
        handleVisibilityOFElements();
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        if (isGalleryClicked) {
            isGalleryClicked = false;
            return;
        }
        try {
            if (getActivity() != null && !((BaseActivity) requireActivity()).canGetLocation(getActivity())) {
                binding.cameraPreview.imgBtnWeather.setSelected(true);
            } else {
                binding.cameraPreview.imgBtnWeather.setSelected(false);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }

        if (isCameraVisible() && binding.cameraView.camera.getCameraOptions() == null) {
            init();
        }
    }

    private boolean isCameraVisible() {
        if (getActivity() != null) {
            return ((MainActivity) requireActivity()).getCameraVisibilityStatus();
        } else {
            return true;
        }

    }

    private void handleVisibilityOFElements() {
        try {
            if (isPreviewMode) {
                binding.cameraPreview.rlCameraPreview.setVisibility(View.VISIBLE);
                binding.rlCameraMainView.setVisibility(View.GONE);
                // Set caption layout visibility
                if (isEditModeCaption && !isEditModeDrawing) {
                    binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
                } else {
                    binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
                }
                binding.rlMainPreview.setVisibility(View.VISIBLE);
                setVisibilityTagAndAddTextView(true);
                // Visible drawing layout if drawing mode is on
                if (isEditModeDrawing) {
                    binding.cameraPreview.rlCameraPreview.setVisibility(View.GONE);
                    binding.cameraDraw.rlDrawView.setVisibility(View.VISIBLE);
                    binding.cameraDraw.imgBtnEditDrawingToggle.setVisibility(View.VISIBLE);
                    binding.cameraDraw.imgBtnDrawingDone.setVisibility(View.VISIBLE);

                    if (myViewDrawing != null && !myViewDrawing.pathsStackDrawing.isEmpty()) {
                        binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.VISIBLE);
                    } else {
                        binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.GONE);
                    }
                    binding.cameraDraw.verticalColorPicker.setVisibility(View.VISIBLE);
                    showMapViewIfLocatorIsEnabled(false);
                    binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR));
                    binding.cameraDraw.imgBtnDrawingUndo.setBackgroundColor(AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR));
                } else {
                    binding.cameraDraw.rlDrawView.setVisibility(View.GONE);
                    binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
                    showMapViewIfLocatorIsEnabled(true);
                    checkAndUpdateUploadView();
                }
            } else {
                binding.cameraPreview.rlCameraPreview.setVisibility(View.GONE);
                binding.cameraDraw.rlDrawView.setVisibility(View.GONE);
                binding.rlMainPreview.setVisibility(View.GONE);
                binding.rlCameraMainView.setVisibility(View.VISIBLE);
                if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
                    binding.cameraPermission.llCameraPermissionView.setVisibility(View.GONE);
                    if (isCameraVisible()) {
                        binding.cameraView.camera.setVisibility(View.VISIBLE);
                    }
                } else {
                    binding.cameraView.camera.setVisibility(View.GONE);
                    binding.cameraPermission.llCameraPermissionView.setVisibility(View.VISIBLE);
                }
                checkAndUpdateCameraFaceView();
                checkAndUpdateUploadView();
                showMapViewIfLocatorIsEnabled(false);
                if (mapFragment != null) {
                    mapFragment.setNullMarker(true);
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Method to check pending upload data set visibility of upload icon on action bar
     */
    private void checkAndUpdateUploadView() {
        TblUploadData tblUploadData = new TblUploadData(getActivity());
        TblUploadImage tblUploadImage = new TblUploadImage(getActivity());
        if ((tblUploadData.getDataCount() + tblUploadImage.getDataCount()) > 0) {
            binding.imgBtnImageUpload.setVisibility(View.VISIBLE);
        } else {
            binding.imgBtnImageUpload.setVisibility(View.GONE);
        }
    }


    private class CamListener extends CameraListener {
        @Override
        public void onCameraOpened(@NonNull CameraOptions options) {
            super.onCameraOpened(options);
            if (!isPreviewMode) {
                setUpCamera();
                enableDisableCamera(false, isCameraVisible());
            } else {
                enableDisableCamera(false, isCameraVisible());
            }
        }

        @Override
        public void onCameraError(@NonNull CameraException exception) {
            super.onCameraError(exception);
            if (exception.getMessage() != null && exception.getMessage().contains("setParameters failed")) {
                if (!isPreviewMode) {
                    setUpCamera();
                    enableDisableCamera(false, isCameraVisible());
                } else {
                    enableDisableCamera(false, isCameraVisible());
                }
            }
        }


        @Override
        public void onPictureTaken(@NonNull PictureResult result) {
            super.onPictureTaken(result);
            doAfterPictureCaptured(result);
        }

        @Override
        public void onAutoFocusStart(@NonNull PointF point) {
            if (binding.cameraView.camera.isTakingPicture())
                return;
            MyCameraUtils.showTouchView(binding.ivAutoFocusAnim, point);
            super.onAutoFocusStart(point);
        }

        @Override
        public void onAutoFocusEnd(boolean successful, @NonNull PointF point) {
            super.onAutoFocusEnd(successful, point);
        }
    }


    private void doAfterPictureCaptured(PictureResult result) {
        if (getActivity() != null)
            ((MainActivity) requireActivity()).requestAPIToUpdateLocation();
        try {
            result.toBitmap(this::afterBitmapReady);
        } catch (UnsupportedOperationException e) {
            handleVisibilityOFElements();
            showOrHideProgressBar(false);
        }
    }

    private void afterBitmapReady(Bitmap bitmap) {
        Matrix matrix = new Matrix();
        if (binding.cameraView.camera.getFacing() == Facing.FRONT) {
            matrix.preScale(-1, 1);
        }
        if (bitmap != null && bitmap.getWidth() > 0) {
            bmpresizedImageTakenFromCamera = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
            binding.ivCapturedImage.setImageBitmap(bmpresizedImageTakenFromCamera);
            isGalleryMode = false;
            dateExifImageCaptured = "";
            isPreviewMode = true;
            if (getActivity() != null && !((BaseActivity) getActivity()).canGetLocation(context)) {
                MainActivity.currentLatitude = 0.0;
                MainActivity.currentLongitude = 0.0;
                MainActivity.distanceAccuracy = 0;
            }
            showOrHideProgressBar(false);
            handleVisibilityOFElements();
            DoAfterImageIsCaptured();
        } else {
            isGalleryMode = false;
        }
    }

    /**
     * Common method to show or hide progress bar in screen
     *
     * @param shouldShow true/ false
     */
    private void showOrHideProgressBar(boolean shouldShow) {
        if (getActivity() == null)
            return;

        if (shouldShow) {
            binding.rlProgress.setVisibility(View.VISIBLE);
        } else {
            binding.rlProgress.setVisibility(View.GONE);
        }
    }

    private void hasFlash() {
        binding.imgBtnFlash.setVisibility(View.GONE);
        if (MyCameraUtils.hashCameraFlash(context, binding.cameraView.camera, isPreviewMode)) {
            binding.imgBtnFlash.setVisibility(View.VISIBLE);
            MyCameraUtils.updateFlashImage(binding.cameraView.camera, binding.imgBtnFlash);
        }
    }

    private void setColorPicker() {
        try {
            colorSelected = AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR);
            myViewDrawing.mPaintEditDrawing.setColor(colorSelected);
            binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(colorSelected);
            binding.cameraDraw.verticalColorPicker.setOnColorChangeListener(selectedColor -> {
                if (selectedColor != 0) {
                    colorSelected = selectedColor;
                    myViewDrawing.mPaintEditDrawing.setColor(colorSelected);
                    binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(colorSelected);
                    AppPrefShared.putValue(Constants.DRAWING_PAINT_STROKE_COLOR, colorSelected);
                }
            });
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }

    }

    private void getNewDrawingView() {
        if (getActivity() != null) {
            myViewDrawing = new CameraDrawingView(requireActivity());
            binding.flDrawingView.addView(myViewDrawing);
        }
    }

    private void checkAndUpdateCameraFaceView() {
        if (!isCameraVisible())
            return;
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            checkAndUpdateCameraFace();
        } else {
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
        }
    }

    private void visibleDrawingViewActionsAndCaptureDrawingBitmap() {
        // Set visibility of elements
        binding.cameraPreview.imgBtnDelete.setVisibility(View.VISIBLE);
        setVisibilityTagAndAddTextView(true);
        binding.cameraPreview.imgBtnText.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnRefresh.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnEdit.setVisibility(View.VISIBLE);
        binding.cameraDraw.imgBtnDrawingDone.setVisibility(View.VISIBLE);
        binding.cameraPreview.tvPropertyName.setVisibility(View.VISIBLE);
        binding.cameraDraw.rlDrawingTopView.setVisibility(View.VISIBLE);

        binding.cameraPreview.edtTextCaption.setText("");
        isEditModeCaption = false;
        isEditModeDrawing = false;

        binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
    }

    private void setVisibilityTagAndAddTextView(boolean shouldShow) {

        if (shouldShow) {
            if (!TextUtils.isEmpty(AppPrefShared.getString(LOGGED_IN_USER_TAGS, null))) {
                binding.cameraPreview.imgBtnTextTag.setVisibility(View.VISIBLE);
                binding.cameraPreview.imgBtnText.setVisibility(View.GONE);
                if (TextUtils.isEmpty(binding.cameraPreview.tvTagCount.getText())) {
                    binding.cameraPreview.tvTagCount.setVisibility(View.GONE);
                } else {
                    binding.cameraPreview.tvTagCount.setVisibility(View.VISIBLE);
                }

                binding.cameraPreview.rlTagTextView.setVisibility(View.VISIBLE);
            } else {
                binding.cameraPreview.imgBtnText.setVisibility(View.VISIBLE);
                binding.cameraPreview.imgBtnTextTag.setVisibility(View.GONE);
                binding.cameraPreview.rlTagTextView.setVisibility(View.GONE);
            }
        } else {
            binding.cameraPreview.imgBtnText.setVisibility(View.GONE);
            binding.cameraPreview.imgBtnTextTag.setVisibility(View.GONE);
            binding.cameraPreview.rlTagTextView.setVisibility(View.GONE);
        }

    }

    private void hideDrawingViewActionsAndCaptureDrawingBitmap() {
        try {
            binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.INVISIBLE);
            Bitmap bmpImageView = null;
            if (bmpresizedImageTakenFromCamera != null && !bmpresizedImageTakenFromCamera.isRecycled()) {
                bmpImageView = Bitmap.createBitmap(bmpresizedImageTakenFromCamera);
            }
            if (bmpImageView != null) {

                if (bmpImageView.getHeight() < 0 || bmpImageView.getWidth() < 0)
                    return;

                double aspectRatioImage = (double) bmpImageView.getHeight() / bmpImageView.getWidth();
                double aspectRatioScreen = (double) Constants.CAM_SCREEN_HEIGHT / Constants.CAM_SCREEN_WIDTH;

                // get captured image scaled height & width comparing with screen dimensions.
                double scaledHeight, scaledWidth;

                // Get Top & Left offset here for drawing bitmap to merge.
                double offsetTop = 0, offsetLeft = 0;

                if (aspectRatioImage < aspectRatioScreen) {

                    // Scale image height as per height of screen and store the dimensions.
                    scaledHeight = Constants.CAM_SCREEN_HEIGHT;
                    scaledWidth = (double) (Constants.CAM_SCREEN_HEIGHT * bmpImageView.getWidth()) / bmpImageView.getHeight();

                    offsetLeft = (scaledWidth - Constants.CAM_SCREEN_WIDTH) / 2;
                } else if (aspectRatioImage > aspectRatioScreen) {

                    // Scale image width as per width of screen and store the dimensions.
                    scaledWidth = Constants.CAM_SCREEN_WIDTH;
                    scaledHeight = (double) (Constants.CAM_SCREEN_WIDTH * bmpImageView.getHeight()) / bmpImageView.getWidth();
                    offsetTop = (scaledHeight - Constants.CAM_SCREEN_HEIGHT) / 2;
                } else {

                    // Scale image width as per width of screen and store the dimensions.
                    scaledWidth = Constants.CAM_SCREEN_WIDTH;
                    scaledHeight = (double) (Constants.CAM_SCREEN_WIDTH * bmpImageView.getHeight()) / bmpImageView.getWidth();
                    offsetTop = (scaledHeight - Constants.CAM_SCREEN_HEIGHT) / 2;
                }

                Bitmap bitmap2 = null;
                if (myViewDrawing != null && myViewDrawing.getDrawingBitmap() != null) {
                    bitmap2 = myViewDrawing.getDrawingBitmap(); // Drawing view bitmap
                }

                // Merge above 2 bitmaps into one on hte scaled image bitmap first.
                Bitmap scaledImageBitmap = Bitmap.createScaledBitmap(bmpImageView, (int) scaledWidth, (int) scaledHeight, true);
                Bitmap bitmapScaledCapturedImage = Bitmap.createBitmap((int) scaledWidth, (int) scaledHeight, Bitmap.Config.ARGB_8888);
                Canvas canvas = new Canvas(bitmapScaledCapturedImage);
                canvas.drawBitmap(scaledImageBitmap, 0, 0, null);
                if (bitmap2 != null) {
                    canvas.drawBitmap(bitmap2, (int) offsetLeft, (int) offsetTop, null);
                }

                bitmapCapturedImageWithDrawing = Bitmap.createScaledBitmap(bitmapScaledCapturedImage, bmpImageView.getWidth(), bmpImageView.getHeight(), true);

                ImageUtil.recycleBitmap(bitmapScaledCapturedImage);
                ImageUtil.recycleBitmap(bmpImageView);
                binding.cameraPreview.edtTextCaption.setText("");
                isEditModeCaption = false;
                isEditModeDrawing = false;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void getPermissionToFineLocation(boolean checkExplicitly) {

        if (checkExplicitly && getActivity() != null) {
            ((MainActivity) getActivity()).requestLocationPermission();
        }
        if (!isPermissionLocationCalled) {
            isPermissionLocationCalled = true;
            if (getActivity() != null)
                ((MainActivity) getActivity()).requestLocationPermission();
        }
    }


    private void refreshPropertyData() {
        if (PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
            if (!TextUtils.isEmpty(binding.cameraPreview.tvPropertyName.getText().toString().trim())) {
                lstIgnoreList.add(mBuildingId);
                PopUtils.showConformAlertDialogForProperty(getActivity(), binding.cameraPreview.tvPropertyName.getText().toString(),
                        (dialogInterface, i) -> showPropertyDialog(isGalleryMode, true), (dialogInterface, i) -> {
                        });
            } else {
                showPropertyDialog(isGalleryMode, true);
            }
        } else {
            getPermissionToFineLocation(true);
        }

    }


    private void showMapViewIfLocatorIsEnabled(boolean shouldShowView) {
        if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_TARGET_LOCATOR, false) && shouldShowView) {
            if (mapFragment == null) {
                mapFragment = (MapViewFragment) getChildFragmentManager().findFragmentById(R.id.mapFragment);
                if (mapFragment != null) {
                    mapFragment.binding.ivDummyViewOnMap.setOnClickListener(this);
                    mapFragment.binding.tvBack.setOnClickListener(this);
                }
            }
            binding.cameraMapView.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
            minimizeMapView();
            if (isGalleryMode && mLocationExif != null) {

                if (mapFragment != null) {

                    if (mLocationExif.getLatitude() != 0.0 && mLocationExif.getLongitude() != 0.0) {
                        mapFragment.resetMap(mLocationExif.getLatitude(), mLocationExif.getLongitude());
                    } else {
                        mapFragment.resetMap(MainActivity.currentLatitude, MainActivity.currentLongitude);
                    }
                }
            } else {
                if (mapFragment != null) {

                    mapFragment.resetMap(MainActivity.currentLatitude, MainActivity.currentLongitude);
                }
            }

        } else {
            binding.cameraMapView.rlCameraMapLocatorView.setVisibility(View.GONE);
        }
    }

    private void apiCallForWeather() {
        ApiInterface apiService = RetrofitProvider.createServiceString(ApiInterface.class);

        HashMap<String, Object> params = new HashMap<>();
        params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
        params.put(Constants.PARAM_LAT, String.valueOf(MainActivity.currentLatitude));
        params.put(Constants.PARAM_LNG, String.valueOf(MainActivity.currentLongitude));
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        StaticUtils.addCommonData(params);
        Call<String> call = apiService.getWeatherApi(params);
        call.enqueue(new Callback<String>() {
            @Override
            public void onResponse(Call<String> call, Response<String> response) {
                try {
                    if (response.body() != null) {
                        if (getActivity() != null) {
                            ((BaseActivity) getActivity()).stopProgress();
                        }

                        if (!TextUtils.isEmpty(response.body())) {
                            isEditModeCaption = true;
                            strWeather = response.body();
                            binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
                            if (binding.cameraPreview.edtTextCaption != null && binding.cameraPreview.edtTextCaption.getText() != null)
                                binding.cameraPreview.edtTextCaption.setText(binding.cameraPreview.edtTextCaption.getText().toString().trim().concat(" ").concat(strWeather));
                            ((BaseActivity) getActivity()).hideSoftKeyboard(getActivity());
                            binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
                        } else {
                            if (getActivity() != null) {
                                showForeGroundToast(getString(R.string.some_error_occured));
                            }
                        }
                    } else if (response.body() == null) {
                        if (getActivity() != null) {
                            ((BaseActivity) getActivity()).stopProgress();
                            showForeGroundToast(getString(R.string.some_error_occured));
                            showForeGroundToast(getString(R.string.some_error_occured));
                        }
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                } finally {
                    if (getActivity() != null) {
                        ((BaseActivity) getActivity()).stopProgress();
                    }
                }
            }

            @Override
            public void onFailure(Call<String> call, Throwable t) {
                if (getActivity() != null) {
                    ((BaseActivity) getActivity()).stopProgress();
                    showForeGroundToast(getString(R.string.some_error_occured));
                }
            }
        });

    }

    private void minimizeMapView() {
        binding.cameraPreview.imgBtnDelete.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnWeather.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnEdit.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnText.setVisibility(View.VISIBLE);
        setVisibilityTagAndAddTextView(true);
        binding.cameraPreview.imgBtnRefresh.setVisibility(View.VISIBLE);
        binding.cameraPreview.imgBtnShare.setVisibility(View.VISIBLE);
        new Handler().postDelayed(() -> {
            try {
                resizeMapView(false);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }, 100);
    }

    private void showMapOnDialog() {
        if (mapFragment == null && AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_TARGET_LOCATOR, false)) {
            mapFragment = (MapViewFragment) getChildFragmentManager().findFragmentById(R.id.mapFragment);

        }
        if (mapFragment != null) {
            mapFragment.readyBitmapFromMap();
        }
        binding.cameraPreview.tvPropertyName.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnDelete.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnEdit.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnWeather.setVisibility(View.GONE);
        setVisibilityTagAndAddTextView(false);
        binding.cameraPreview.imgBtnText.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnRefresh.setVisibility(View.GONE);
        binding.cameraPreview.imgBtnShare.setVisibility(View.GONE);
        resizeMapView(true);
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (mapFragment != null) {
                    mapFragment.AnimateCameraToLocationWhenVisible();
                    mapFragment.checkAndShowMapChooserView();
                }
            });
        }
    }

    /**
     * Method to set Size of target locator map view.
     *
     * @param isOpen
     */
    private void resizeMapView(boolean isOpen) {

        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) binding.cameraMapView.rlCameraMapLocatorView.getLayoutParams();
        if (isOpen) {
            if (getActivity() != null) {
                ((MainActivity) getActivity()).manageBottomViewVisibility(View.GONE);
            }
            params.width = RelativeLayout.LayoutParams.MATCH_PARENT;
            params.height = RelativeLayout.LayoutParams.MATCH_PARENT;
            binding.cameraMapView.rlCameraMapLocatorView.setLayoutParams(params);
            ViewGroup.MarginLayoutParams margin = (ViewGroup.MarginLayoutParams) binding.cameraMapView.rlCameraMapLocatorView.getLayoutParams();
            margin.setMargins(0, 0, 0, 0);
            binding.cameraMapView.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
            if (mapFragment != null) {
                mapFragment.binding.clToolbar.setVisibility(View.VISIBLE);
                mapFragment.binding.ivDummyViewOnMap.setVisibility(View.GONE);
                mapFragment.readyBitmapFromMap();
            }
        } else {
            if (getActivity() != null) {
                ((MainActivity) getActivity()).manageBottomViewVisibility(View.VISIBLE);
            }

            if (mapFragment != null) {
                mapFragment.binding.clToolbar.setVisibility(View.GONE);
                mapFragment.binding.ivDummyViewOnMap.setVisibility(View.VISIBLE);
                params.width = mapFragment.binding.ivDummyViewOnMap.getWidth();
                params.height = mapFragment.binding.ivDummyViewOnMap.getHeight();
                mapFragment.binding.ivFakeView.setVisibility(View.GONE);
            }
            binding.cameraMapView.rlCameraMapLocatorView.setVisibility(View.VISIBLE);
            binding.cameraPreview.tvPropertyName.setVisibility(View.VISIBLE);
            binding.cameraMapView.rlCameraMapLocatorView.setLayoutParams(params);
            binding.cameraMapView.rlCameraMapLocatorView.requestFocus();
            ViewGroup.MarginLayoutParams margin = (ViewGroup.MarginLayoutParams) binding.cameraMapView.rlCameraMapLocatorView.getLayoutParams();
            margin.setMargins(40, 150, 0, 0);
        }
    }

    @Override
    public void onStop() {
        super.onStop();
    }

    private void checkAndStartUploadProcess() {
        if (checkSDCardPermission()) {
            if (checkFoPropertyTag()) {
                askUserToRefreshLocation();
            } else {
                saveCapturedImageAndStartUploadProcess();
            }
        }
    }

    private void askUserToRefreshLocation() {
        PopUtils.showCustomTwoButtonAlertDialog(getActivity(), getString(R.string.app_name), getString(R.string.site_untag_message),
                getString(R.string.refresh), getString(R.string.ignore), false,
                (dialog, which) -> refreshLocation(), (dialog, which) -> saveCapturedImageAndStartUploadProcess());
    }


    private boolean checkFoPropertyTag() {
        if (AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 1) {
            return false;
        }
       /* if (isNoneOfTheAboveSelected)
            return false;*/
        return mBuildingId.equalsIgnoreCase("0");

    }

    private void refreshLocation() {
        if (getActivity() != null)
            ((MainActivity) requireActivity()).requestAPIToUpdateLocation();
        showOrHideProgressBar(true);
        new Handler().postDelayed(() -> {
            showOrHideProgressBar(false);
            showPropertyDialog(isGalleryMode, false);
        }, 1500);

    }

    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        resetCameraView();
        // call visibility of elements on orientation change.
        handleVisibilityOFElements();
    }

    /**
     * Remove camera view and add again at position 0 to manage camera orientation
     */
    private void resetCameraView() {
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            enableDisableCamera(false, false);
            enableDisableCamera(false, true);
        }
    }

    private void setUpCamera() {
        if (isCameraVisible()) {
            if (camListener != null) {
                binding.cameraView.camera.removeCameraListener(camListener);
            }
            camListener = new CamListener();
            binding.cameraView.camera.setLifecycleOwner(this);
            binding.cameraView.camera.addCameraListener(camListener);
            binding.cameraView.camera.setFocusable(true);
            checkAndUpdateCameraFace();
        }
    }

    private void checkAndUpdateCameraFace() {

        Collection<Facing> facings;
        try {
            if (binding.cameraView.camera.getCameraOptions() == null)
                return;
            facings = binding.cameraView.camera.getCameraOptions().getSupportedFacing();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

            return;
        }

        if (facings.size() == 1 && facings.contains(Facing.BACK)) {
            binding.cameraView.camera.setFacing(Facing.BACK);
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
        } else if (facings.size() == 1 && facings.contains(Facing.FRONT)) {
            binding.cameraView.camera.setFacing(Facing.FRONT);
            binding.imgBtnFlipCamera.setVisibility(View.GONE);
        } else {
            if (AppPrefShared.getBoolean(Constants.CAMERA_FLIP_MODE, false)) {
                binding.cameraView.camera.setFacing(Facing.FRONT);
            } else {
                binding.cameraView.camera.setFacing(Facing.BACK);
            }
            binding.imgBtnFlipCamera.setVisibility(View.VISIBLE);
        }
        hasFlash();
    }

    // Save captured image and start uploading task.
    private void saveCapturedImageAndStartUploadProcess() {

        if (binding.cameraPreview.edtTextCaption.getText() != null)
            strCaption = binding.cameraPreview.edtTextCaption.getText().toString().trim();

        // Get bitmap of captured image with caption & drawing first. After that, set preview mode to false & get upload URLs for image.
        hideDrawingViewActionsAndCaptureDrawingBitmap();

        binding.cameraPreview.imgBtnWeather.setSelected(false);

        isPreviewMode = false;


        // Release camera and re assign the camera with applicable settings to sort out camera rotate issue.

        handleVisibilityOFElements();
        new SaveImageDataForUploadAsyncTask().execute();

    }

    private void showPropertyDialog(boolean isFromGallery, boolean isRefresh) {

        if (getActivity() != null) {
            showOrHideProgressBar(true);
            binding.cameraPreview.imgBtnRefresh.setEnabled(false);
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
                if (!isRefresh) {
                    lstIgnoreList.clear();
                    mBuildingId = "0";
                }

                TblProperties tblProperties = new TblProperties(context);
                TblCluster tblCluster = new TblCluster(context);

                List<PropertiesVo> lstProperties = tblProperties.getAllProperties();
                List<Cluster> lstClusters = tblCluster.getAllClusterData();
                lstProperty = polygonCalculation.getFilteredPropertyListFromPolygon(context, isFromGallery, mLocationExif,
                        MainActivity.currentLatitude, MainActivity.currentLongitude, MainActivity.distanceAccuracy, lstIgnoreList, lstClusters, lstProperties);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    binding.cameraPreview.imgBtnRefresh.setEnabled(true);
                    showOrHideProgressBar(false);
                    showDialogueOfPropertyUsingPolygon();
                });
            });
        }
    }

    private boolean checkSDCardPermission() {

        if (hasStoragePermissions(getActivity())) {
            return true;
        } else {
            showPermissionRequiredInfoDialog(getString(R.string.sitefotos_is_not_allowed_to_store_the_captured_properties_fotos), Constants.EXTERNAL_STORAGE_REQUEST);
            return false;
        }
    }

    private void checkCameraPermission() {
        if (!PermissionUtils.hasPermission(getActivity(), CAMERA_PERMISSION)) {
            showPermissionRequiredInfoDialog(getString(R.string.sitefotos_is_not_allowed_to_store_the_captured_properties_fotos), CAMERA_PERMISSIONS_REQUEST);
        }
    }

    private void showPermissionRequiredInfoDialog(String message, int requestCode) {
        PopUtils.dismissAlertDialog();

        PopUtils.showCustomTwoButtonAlertDialog(getActivity(), "", message,
                getString(R.string.open), getString(R.string.txt_cancel), false,
                (dialog, which) -> {
                    if (getActivity() == null) {
                        return;
                    }
                    if (!hasStoragePermissions(getActivity())) {
                        StaticUtils.openSettingScreen(getActivity(), requestCode);
                    } else {
                        PermissionUtils.requestStoragePermission(getActivity(), requestCode);
                    }
                }, (dialog, which) -> {

                });
    }

    private void resetMapExtraLocation() {
        try {
            if (mapFragment != null) {
                mapFragment.updatedExtraLatitude = 0.0;
                mapFragment.updatedExtraLongitude = 0.0;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Method to start uploading process after Saved and prepared uploading structure.
     */
    private void notifyServerToUploadUploading(JSONArray result) {
        AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());
    }

    public void trashData() {
        try {
            mBuildingId = "0";
            buildingName = "";
            binding.cameraPreview.tvPropertyName.setText("");
            if (getActivity() != null)
                binding.cameraPreview.tvPropertyName.setBackgroundColor(ContextCompat.getColor(getActivity(), R.color.transparent));
            binding.cameraPreview.edtTextCaption.setText("");
            isEditModeCaption = false;

            try {
                PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();

                polygonCalculation.buildingName = "";
                polygonCalculation.propertyName = "";
                polygonCalculation.buildingId = "0";
                lstIgnoreList.clear();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

            resetMapExtraLocation();
            binding.flDrawingView.removeAllViews();
            myViewDrawing = null;

            isPreviewMode = false;
            isEditModeDrawing = false;
            destroyHeavyObjects();
            resetTagData(lstTags, binding.cameraPreview.tvTagCount);
            binding.cameraPreview.imgBtnShare.setSelected(false);
            binding.cameraPreview.imgBtnWeather.setSelected(false);

            isPreviewMode = false;
            isEditModeDrawing = false;
            handleVisibilityOFElements();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    @Override
    public void onDestroy() {
        // Release camera and re assign the camera with applicable settings to sort out camera rotate issue.
        destroyHeavyObjects();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
        super.onDestroy();
    }

    private void destroyHeavyObjects() {
        ImageUtil.recycleBitmap(bitmapCapturedImageWithDrawing);
        ImageUtil.recycleBitmap(bitmapCapturedImageWithDrawingLow);
        ImageUtil.recycleBitmap(bmpresizedImageTakenFromCamera);
        System.gc();
    }

    private void setOnClickListener() {
        binding.cameraPermission.tvCameraPermissionMessage.setOnClickListener(this);
        binding.imgBtnFlipCamera.setOnClickListener(this);
        binding.imgBtnFlash.setOnClickListener(this);
        binding.imgBtnImageUpload.setOnClickListener(this);
        binding.imgBtnFormCancel.setOnClickListener(this);
        binding.imgBtnCaptureImage.setOnClickListener(this);
        binding.imgBtnGallary.setOnClickListener(this);
        binding.cameraPreview.imgBtnDelete.setOnClickListener(this);
        binding.cameraPreview.imgBtnRefresh.setOnClickListener(this);
        binding.cameraPreview.imgBtnText.setOnClickListener(this);
        binding.cameraPreview.imgBtnTextTag.setOnClickListener(this);
        binding.cameraPreview.imgBtnEdit.setOnClickListener(this);
        binding.cameraPreview.tvCaptionDone.setOnClickListener(this);
        binding.cameraPreview.imgBtnPreviewDone.setOnClickListener(this);
        binding.cameraPreview.imgBtnWeather.setOnClickListener(this);
        binding.cameraPreview.imgBtnShare.setOnClickListener(this);
        binding.cameraDraw.imgBtnDrawingDone.setOnClickListener(this);
        binding.cameraDraw.imgBtnDrawingUndo.setOnClickListener(this);
        binding.cameraPreview.edtTextCaption.setOnClickListener(this);
        binding.cameraPreview.rlTags.setOnClickListener(this);
        binding.cameraPreview.imgBtnTags.setOnClickListener(this);
        binding.cameraDraw.imgBtnEditDrawingToggle.setOnClickListener(this);
        binding.cameraMapView.rlCameraMapLocatorView.setOnClickListener(this);
        binding.cameraPermission.btnPermission.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.tvCameraPermissionMessage) {
            checkCameraPermission();
        } else if (viewId == R.id.imgBtnFlipCamera) {
            flipCameraOnClick();
        } else if (viewId == R.id.imgBtnFlash) {
            MyCameraUtils.changeFlashSettings(getActivity(), binding.cameraView.camera, binding.imgBtnFlash);
        } else if (viewId == R.id.imgBtnImageUpload) {
            if (getActivity() != null)
                ((BaseActivity) requireActivity()).navigateToUploadActivityScreen(binding.imgBtnImageUpload);
        } else if (viewId == R.id.imgBtnCaptureImage) {
            takePicture(view);
        } else if (viewId == R.id.imgBtnGallary) {
            if (checkSDCardPermission()) {
                if (getActivity() != null) {
                    ((MainActivity) getActivity()).openGalleryForImageSelection();
                }
            }
        } else if (viewId == R.id.imgBtnDelete) {
            trashData();
        } else if (viewId == R.id.imgBtnRefresh) {
            refreshPropertyData();
        } else if (viewId == R.id.edtTextCaption) {
            enableEditCaption();
        } else if (viewId == R.id.imgBtnText || viewId == R.id.imgBtnTextTag) {
            showCaptionView();
        } else if (viewId == R.id.tvCaptionDone) {
            onDoneEditCaption();
        } else if (viewId == R.id.imgBtnEdit) {
            showDrawingView();
        } else if (viewId == R.id.imgBtnPreviewDone) {
            checkAndStartUploadProcess();
        } else if (viewId == R.id.imgBtnWeather) {
            addWeather();
        } else if (viewId == R.id.imgBtnShare) {
            shareImage();
        } else if (viewId == R.id.imgBtnDrawingDone || viewId == R.id.imgBtnEditDrawingToggle) {
            isEditModeDrawing = false;
            handleVisibilityOFElements();
        } else if (viewId == R.id.imgBtnDrawingUndo) {
            myViewDrawing.undo();
        } else if (viewId == R.id.tvBack) {
            if (mapFragment != null) {
                mapFragment.setFakeMapView();
            }
            new Handler().postDelayed(this::minimizeMapView, 100);
        } else if (viewId == R.id.rlMapLocatorView || viewId == R.id.ivDummyViewOnMap) {
            showMapOnDialog();
        } else if (viewId == R.id.rlTags || viewId == R.id.imgBtnTags) {
            showTagView();
        } else if (viewId == R.id.btnPermission) {
            if (getActivity() != null)
                navigateUserToPermissionScreen(getActivity());
        }
    }

    private void showTagView() {
        if (lstTags.isEmpty()) {
            lstTags = getTagData();
        }
        PopUtils.showTagSelectionView(getActivity(), lstTags, new OnTagSelected() {
            @Override
            public void onSelected(List<Tags> lstTag) {
                lstTags = lstTag;
            }

            @Override
            public void onItemSelected(boolean isSelected) {
                updateTagCount(binding.cameraPreview.tvTagCount, isSelected);
            }
        });
    }


    private void takePicture(View v) {
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            //take the picture

            if (MainActivity.currentLatitude != 0.0 && MainActivity.currentLongitude != 0) {
                if (binding.cameraView.camera.isTakingPicture())
                    return;
                isDialogueToShow = true;

                showOrHideProgressBar(true);
                if (binding.cameraView.camera.getFlash().equals(Flash.ON) || binding.cameraView.camera.getFlash().equals(Flash.AUTO)) {
                    binding.cameraView.camera.takePicture();
                } else {
                    binding.cameraView.camera.takePictureSnapshot();
                }
            } else {
                trashData();
                //start location service if stopped
                if (context != null && context instanceof MainActivity) {
                    CustomLogKt.errorLog("CameraFragment", "context not null");
                    ((MainActivity) context).startLocationServiceIfNotStarted();
                }
                showForeGroundToastLong(getString(R.string.msg_capture_photo_location_error));
            }
        }
    }

    private void showDrawingView() {
        if (!isEditModeDrawing) {
            isEditModeDrawing = true;
            handleVisibilityOFElements();
            if (myViewDrawing == null) {
                getNewDrawingView();
            }
            setColorPicker();
        }
    }

    private void shareImage() {
        if (AppPrefShared.getBoolean(Constants.IS_SHARE_CAPTURED_IMAGE, false)) {
            AppPrefShared.putValue(Constants.IS_SHARE_CAPTURED_IMAGE, false);
            binding.cameraPreview.imgBtnShare.setSelected(false);
        } else {
            AppPrefShared.putValue(Constants.IS_SHARE_CAPTURED_IMAGE, true);
            binding.cameraPreview.imgBtnShare.setSelected(true);
        }
    }


    private void showCaptionView() {
        if (!isEditModeCaption) {
            isEditModeCaption = true;

        }
        binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
        if (binding.cameraPreview.edtTextCaption.getText() != null)
            binding.cameraPreview.edtTextCaption.setSelection(binding.cameraPreview.edtTextCaption.getText().toString().trim().length());
        enableDisableCaptionEditView(true);
    }


    private void enableEditCaption() {
        if (isEditModeCaption) {
            if (binding.cameraPreview.edtTextCaption.getText() != null)
                binding.cameraPreview.edtTextCaption.setSelection(binding.cameraPreview.edtTextCaption.getText().toString().trim().length());
            enableDisableCaptionEditView(true);

        } else {
            binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
        }
        handleVisibilityOFElements();
    }

    /**
     * Method to enable/disable caption edit text focus
     *
     * @param isEnable true: need to set focusable else disable edit text focus and hide keyboard
     */
    private void enableDisableCaptionEditView(boolean isEnable) {
        if (isEnable) {
            binding.cameraPreview.tvCaptionDone.setVisibility(View.VISIBLE);
            binding.cameraPreview.edtTextCaption.setFocusableInTouchMode(true);
            binding.cameraPreview.edtTextCaption.setFocusable(true);
            binding.cameraPreview.edtTextCaption.requestFocus();
            if (getActivity() != null)
                ((BaseActivity) getActivity()).showKeyboard(binding.cameraPreview.edtTextCaption);
        } else {
            if (getActivity() != null)
                ((BaseActivity) getActivity()).hideSoftKeyboard(getActivity());
            binding.cameraPreview.edtTextCaption.setFocusable(false);
        }
    }

    private void onDoneEditCaption() {
        if (isEditModeCaption) {
            binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
            enableDisableCaptionEditView(false);
            if (binding.cameraPreview.edtTextCaption.getText() != null && binding.cameraPreview.edtTextCaption.getText().toString().trim().length() > 0) {
                binding.cameraPreview.tvCaptionDone.setVisibility(View.INVISIBLE);
            } else {
                isEditModeCaption = false;
                binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
                binding.cameraPreview.rlEditCapturedImageDone.setVisibility(View.VISIBLE);
            }
        } else {
            binding.cameraPreview.llTextCaptionView.setVisibility(View.GONE);
        }
        handleVisibilityOFElements();
    }

    private void addWeather() {
        if (BaseApplication.getInstance().isOnline(context)) {
            if (!binding.cameraPreview.imgBtnWeather.isSelected()) {

                apiCallForWeather();
                ((BaseActivity) requireActivity()).showProgress(getString(R.string.getting_weather_info));
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_WEATHER_DISPLAYED, true);

                binding.cameraPreview.imgBtnWeather.setSelected(true);
            } else {
                if (!isEditModeCaption) {
                    isEditModeCaption = true;
                    binding.cameraPreview.llTextCaptionView.setVisibility(View.VISIBLE);
                    if (binding.cameraPreview.edtTextCaption.getText() != null)
                        binding.cameraPreview.edtTextCaption.setSelection(binding.cameraPreview.edtTextCaption.getText().toString().trim().length());
                    enableDisableCaptionEditView(true);

                }
            }
        } else {
            showForeGroundToast(getString(R.string.enableMobileData));
        }
    }


    private void flipCameraOnClick() {
        if (binding.cameraView.camera.isTakingPicture())
            return;
        if (!PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            return;
        }
        Facing facing = binding.cameraView.camera.toggleFacing();
        AppPrefShared.putValue(Constants.CAMERA_FLIP_MODE, facing.ordinal() == 1);
        hasFlash();
    }


    public class SaveImageDataForUploadAsyncTask extends AsyncTask<Void, Integer, JSONArray> {
        String strDescription = "";

        @Override
        protected void onPreExecute() {
            strDescription = strCaption;
            strCaption = "";
        }

        @Override
        protected JSONArray doInBackground(Void... params) {
            JSONArray imageArray = new JSONArray();
            double extraLatitude = 0.0, extraLongitude = 0.0;
            String imagePathLow = "", imagePathHigh = "";
            try {
                saveImageInSDCard();

                try {
                    if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_TARGET_LOCATOR, false) && mapFragment != null) {
                        extraLatitude = mapFragment.updatedExtraLatitude;
                        extraLongitude = mapFragment.updatedExtraLongitude;
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                }
                // Check and proceed if bitmap is not recycled
                if (bitmapCapturedImageWithDrawing != null && !bitmapCapturedImageWithDrawing.isRecycled()) {
                    bitmapCapturedImageWithDrawingLow = ImageUtil.createScaledBitmap(bitmapCapturedImageWithDrawing);
                }

                if (bitmapCapturedImageWithDrawingLow != null) {
                    imagePathLow = ImageUtil.saveImageFromBitmap(context, bitmapCapturedImageWithDrawingLow, "", false);
                    ImageUtil.writeExifDataInImage(getActivity(), imagePathLow, MainActivity.currentLatitude, MainActivity.currentLongitude);
                }
                //Check if Bitmap is not null, not recycled and high resolution image option is on.
                if (bitmapCapturedImageWithDrawing != null && !bitmapCapturedImageWithDrawing.isRecycled() && AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false)) {
                    imagePathHigh = ImageUtil.saveImageFromBitmap(context, bitmapCapturedImageWithDrawing, "", true);
                    ImageUtil.writeExifDataInImage(getActivity(), imagePathHigh, MainActivity.currentLatitude, MainActivity.currentLongitude);
                }
                DBUtils.insertImageUploadData(getActivity(), imagePathLow, imagePathHigh, strDescription, dateExifImageCaptured, mBuildingId,
                        StaticUtils.getLocation(mLocationExif, MainActivity.currentLatitude, MainActivity.currentLongitude, false),
                        extraLatitude, extraLongitude, null, false, 0, false, lstTags, false,isGalleryMode);
                mBuildingId = "0";
                buildingName = "";

            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            } finally {
                resetMapExtraLocation();
                return imageArray;

            }
        }


        @Override
        protected void onPostExecute(JSONArray result) {
            if (getActivity() != null && getActivity().isFinishing()) {
                return;
            }
            resetTagData(lstTags, binding.cameraPreview.tvTagCount);
            isGalleryMode = false;
            if (mBuildingId.trim().equalsIgnoreCase("0")) {
                try {
                    binding.cameraPreview.tvPropertyName.setText("");
                    binding.cameraPreview.tvPropertyName.setBackgroundColor(ContextCompat.getColor((getActivity()), R.color.transparent));
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                }
            }

            AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());
            try {
                binding.cameraPreview.edtTextCaption.setText("");
                binding.cameraPreview.edtTextCaption.setText("");
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
            isEditModeCaption = false;
            isEditModeDrawing = false;

            try {
                binding.flDrawingView.removeAllViews();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
            myViewDrawing = null;
            resetMapExtraLocation();

            if (binding.cameraPreview.imgBtnShare.isSelected()) {
                PopUtils.showDialogSMSOrEmail(getActivity(), binding.cameraPreview.imgBtnShare,
                        v -> StaticUtils.shareShareImage(getActivity(), false, strDescription, strMediaPath, bitmapCapturedImageWithDrawing),
                        v -> StaticUtils.shareShareImage(getActivity(), true, strDescription, strMediaPath, bitmapCapturedImageWithDrawing),
                        v -> {
                        });
            } else {
                destroyHeavyObjects();
            }
            showOrHideProgressBar(false);
        }
    }

    private void showDialogueOfPropertyUsingPolygon() {
        PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
        boolean isPrivilegedUser = AppPrefShared.getString(Constants.LOGGED_IN_USER_TYPE, Constants.LOGGED_IN_USER_TYPE).equalsIgnoreCase(Constants.LOGGED_IN_USER_TYPE_PRIVILEGED_USER);
        buildingName = polygonCalculation.buildingName;
        showPropertyName();
        mBuildingId = polygonCalculation.buildingId;
        showOrHideProgressBar(false);
        showMapViewIfLocatorIsEnabled(true);

        if ((!isPrivilegedUser && lstProperty.size() == 0) || (binding.cameraPreview.tvPropertyName != null && binding.cameraPreview.tvPropertyName.getText().toString().trim().length() > 0)) {
        } else if (lstProperty.size() == 0 && !((BaseActivity) requireActivity()).canGetLocation(context)) {
            PopUtils.showAlertDialogPositiveButtonOnlyEnableLocation(context, (dialogInterface, i) ->
                    PopUtils.displayLocationSettingsRequest(getActivity(), LOCATION_REQUEST_CODE_CAMERA_SCREEN), (dialogInterface, i) -> {
                //doAfterNonOfAboveOptionClicked();
                dialogInterface.dismiss();
            });
            isDialogueToShow = false;
        } else {
            try {
                PopUtils.showPropertyDialog(getActivity(), lstProperty,
                        ((BaseActivity) requireActivity()).canGetLocation(getActivity()),
                        this::doAfterPropertySelected, view -> doAfterNewPropertyOptionClicked(), view -> doAfterNoneOfTheAboveSelected());
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
            isDialogueToShow = false;
        }
    }

    private void doAfterNoneOfTheAboveSelected() {

    }

    private void showPropertyName() {
        binding.cameraPreview.tvPropertyName.setText(buildingName);
        if (!TextUtils.isEmpty(buildingName)) {
            binding.cameraPreview.tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
        } else {
            binding.cameraPreview.tvPropertyName.setBackgroundColor(ContextCompat.getColor(requireActivity(), R.color.transparent));
        }
    }

    private void doAfterPropertySelected(int position) {
        try {
            binding.cameraPreview.tvPropertyName.setVisibility(View.VISIBLE);
            binding.cameraPreview.tvPropertyName.setText(lstProperty.get(position).getPropertyName());
            binding.cameraPreview.tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
            mBuildingId = String.valueOf(lstProperty.get(position).getPropertyId());
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void doAfterNewPropertyOptionClicked() {
        if (((BaseActivity) requireActivity()).canGetLocation(context)) {
            ((BaseActivity) requireActivity()).navigateToPropertyScreen(Constants.MAP_ACTIVITY_REQUEST);
        } else {
            if (!isGpsEnableCalled) {
                if (getActivity() != null) {
                    ((MainActivity) getActivity()).showGPSSettingAlert(false, LOCATION_REQUEST_CODE_CAMERA_SCREEN);

                }
            }
        }
    }


    /*
   Save image and get imagePath for share the link
    */
    private void saveImageInSDCard() {
        try {
            if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_SAVE_COPY_TO_DEVICE, false)) {
                if (PermissionUtils.hasStoragePermissions(getActivity())) {
                    if (!isGalleryMode) {
                        strMediaPath = ImageUtil.saveImage(getActivity(), bitmapCapturedImageWithDrawing, true);
                        ImageUtil.writeExifDataInImage(getActivity(), strMediaPath, MainActivity.currentLatitude, MainActivity.currentLongitude);
                    }
                }
            } else {
                strMediaPath = "";
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    private void DoAfterImageIsCaptured() {
        if (AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 1) {
            isDialogueToShow = false;
        }
        if (isDialogueToShow) {
            if (PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
                showPropertyDialog(false, false);
            } else {
                getPermissionToFineLocation(true);
            }
            if (getActivity() != null && ((BaseActivity) getActivity()).canGetLocation(context)) {
                if (isPermissionLocationCalled) {
                    binding.cameraPreview.imgBtnWeather.setSelected(false);
                }
            } else {
                binding.cameraPreview.imgBtnWeather.setSelected(true);

            }

        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case CAMERA_PERMISSIONS_REQUEST:
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    setUpCamera();
                    enableDisableCamera(false, true);
                    handleVisibilityOFElements();
                }
                break;
            case LOCATION_PERMISSION_REQUEST:
            case EXTERNAL_STORAGE_REQUEST:
                break;
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (getActivity() != null)
            ((BaseActivity) getActivity()).stopProgress();
        if (requestCode == Constants.ACTION_CODE_PICK_FROM_GALLERY) {
            isGalleryClicked = true;
            try {
                if (data != null) {
                    List<AllImageModel> lstSelectedImages = (ArrayList<AllImageModel>) data.getSerializableExtra("mediaData");

                    if (lstSelectedImages != null && !lstSelectedImages.isEmpty()) {

                        if (lstSelectedImages.size() == 1) {

                            String outputPath = ImageUtil.rotateImageAndGetNewPath(getContext(), lstSelectedImages.get(0).getImagePath());
                            bmpresizedImageTakenFromCamera = ImageUtil.getBitmapFromPath(outputPath);
                            binding.ivCapturedImage.setImageBitmap(bmpresizedImageTakenFromCamera);
                            isGalleryMode = true;
                            isPreviewMode = true;
                            handleVisibilityOFElements();
                            new UploadSingleImage(lstSelectedImages.get(0).getImagePath()).execute("");
                        } else {
                            visibleDrawingViewActionsAndCaptureDrawingBitmap();
                            //filter images with exif date and skip images without date
                            int dataCountBeforeIteration = lstSelectedImages.size();
                            MyCameraUtils.filterImageOnExifDate(lstSelectedImages);

                            if (!lstSelectedImages.isEmpty()) {
                                isPreviewMode = false;
                                handleVisibilityOFElements();

                                binding.flDrawingView.removeAllViews();
                                myViewDrawing = null;
                                new UploadMultipleImagesTask(context, lstSelectedImages, MainActivity.currentLatitude, MainActivity.currentLongitude, MainActivity.distanceAccuracy,
                                        lstIgnoreList, null, false, 0, false, this::notifyServerToUploadUploading).execute();

                                showForeGroundToastLong(getString(R.string.msg_image_process_successfully,lstSelectedImages.size()));

                                if (dataCountBeforeIteration > lstSelectedImages.size()) {
                                    showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location_for_some));
                                }

                            } else {
                                showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location_for_all));
                            }
                        }
                    }
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

        } else if (requestCode == Constants.MAP_ACTIVITY_REQUEST) {
            if (resultCode == RESULT_OK && data != null) {
                //Response of Adding New property api in AddNewProperty activity
                try {
                    if (data.getExtras() != null && !TextUtils.isEmpty(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                        switch (data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA)) {
                            case Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN:
                                PopUtils.showAlertDialogPositiveButtonOnly(getActivity(), Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN, getString(R.string.joinAlertMessage));

                                break;

                            case Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX:
                                PopUtils.showAlertDialogPositiveButtonOnly(getActivity(), Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX, getString(R.string.maxAlertMessage));
                                break;

                            case Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST:
                                PopUtils.showAlertDialogPositiveButtonOnly(getActivity(), Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST, getString(R.string.pastAlertMessage));
                                break;

                            default:
                                showPropertyDialog(false, false);
                                break;
                        }
                    } else {
                        showPropertyDialog(false, false);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                    showPropertyDialog(false, false);
                }
            } else {
                onResume();
            }
        } else if (requestCode == LOCATION_REQUEST_CODE_CAMERA_SCREEN) {
            if (resultCode == RESULT_OK && getActivity() != null) {
                isGpsEnableCalled = true;
                ((MainActivity) getActivity()).doAfterUserOnGPSLocation(LOCATION_REQUEST_CODE_CAMERA_SCREEN);
            }
        } else {
            if (getActivity() != null)
                showForeGroundToast(getString(R.string.some_error_occured));
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getActivity() == null)
            return;
        ((BaseActivity) getActivity()).visibleUploadImageViewInScreen(binding.imgBtnImageUpload);
    }

    //Async task for processing selected gallery image
    private class UploadSingleImage extends AsyncTask<String, Void, Boolean> {

        String imagePath;

        UploadSingleImage(String imagePath) {
            this.imagePath = imagePath;
        }

        @Override
        protected Boolean doInBackground(String... params) {
            try {
                dateExifImageCaptured = "";
                String path = PathUtils.getPath(context, Uri.parse(imagePath));
                if (TextUtils.isEmpty(path))
                    path = imagePath;
                if (!TextUtils.isEmpty(path)) {
                    mLocationExif = ImageUtil.getLocationFromImage(path);
                    String exifDate = DateUtil.getDateFromExifMetadata(path);
                    dateExifImageCaptured = DateUtil.convertExpectedDateFormat(exifDate);
                }

            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
            return true;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            if (!TextUtils.isEmpty(dateExifImageCaptured) && mLocationExif != null && (mLocationExif.getLatitude() != 0.0 && mLocationExif.getLongitude() != 0.0)) {
                showPropertyDialog(isGalleryMode, false);
                binding.cameraPreview.imgBtnWeather.setSelected(false);
                 /*else {

                    if (AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 1) {
                        showPropertyDialog(isGalleryMode, false);
                        binding.cameraPreview.imgBtnWeather.setSelected(false);
                    } else {
                        if (((BaseActivity) requireActivity()).canGetLocation(context)) {

                            if (MainActivity.currentLatitude > 0 && MainActivity.currentLongitude > 0) {
                                showPropertyDialog(isGalleryMode, false);
                                binding.cameraPreview.imgBtnWeather.setSelected(false);
                            } else {
                                trashData();
                                showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location));
                            }
                        } else {
                            if (getActivity() != null) {
                                ((MainActivity) getActivity()).showGPSSettingAlert(true, LOCATION_REQUEST_CODE_CAMERA_SCREEN);

                            }
                        }
                    }
                }*/
                AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());

            } else {
                //Show message to the user if date is not extracted from image or exif not found, trash selected image from camera screen
                showForeGroundToastLong(getString(R.string.msg_invalid_date_or_location));
                trashData();
            }
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (camListener != null) {
            binding.cameraView.camera.removeCameraListener(camListener);
        }
    }


    public void checkAndAskForCameraPermission() {
        new Handler().postDelayed(this::checkAndAskCameraPermission, 400);
    }

    private void checkAndAskCameraPermission() {
        if (!PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            if (getActivity() != null && isVisible() && ActivityCompat.shouldShowRequestPermissionRationale(getActivity(), CAMERA_PERMISSION)) {
                PermissionUtils.requestPermission(getActivity(), CAMERA_PERMISSION, CAMERA_PERMISSIONS_REQUEST);
            }
        }
    }

    public void enableDisableCamera(boolean callFromMainActivity, boolean enable) {
        if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
            try {
                if (callFromMainActivity && enable && isCameraVisible()) {
                    setUpCamera();
                } else if (enable && isAdded() && isResumed() && getUserVisibleHint() && isCameraVisible()) {
                    CustomLogKt.errorLog("Camera Status", "Open");
                    binding.cameraView.camera.open();
                    if (getActivity() != null) {
                        getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                        getActivity().getWindow().setStatusBarColor(ContextCompat.getColor(getActivity(), R.color.black));
                    }
                } else {
                    binding.cameraView.camera.close();
                    if (getActivity() != null) {
                        getActivity().getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
                        getActivity().getWindow().setStatusBarColor(ContextCompat.getColor(getActivity(), R.color.colorPrimary));
                    }
                    CustomLogKt.errorLog("Camera Status", "Close");
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
                setUpCamera();
            }
        }
    }

}