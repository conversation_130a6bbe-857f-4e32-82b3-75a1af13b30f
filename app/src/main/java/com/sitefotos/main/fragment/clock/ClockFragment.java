package com.sitefotos.main.fragment.clock;

import static android.content.Context.POWER_SERVICE;
import static com.sitefotos.Constants.CLOCK_BREAK;
import static com.sitefotos.Constants.CLOCK_NORMAL;
import static com.sitefotos.Constants.CLOCK_RESUME;
import static com.sitefotos.Constants.NOTIFICATION_BG_PERMISSION;
import static com.sitefotos.Constants.SITE_DETAIL_REQUEST_CODE;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.appinterface.OnClockScreenMultiCrewSelected;
import com.sitefotos.appinterface.OnStickyHeaderListener;
import com.sitefotos.databinding.FragmentClockBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.EmployeeSavedEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.models.ClockCrewData;
import com.sitefotos.models.ClockHeader;
import com.sitefotos.models.CrewSelectionData;
import com.sitefotos.models.Employees;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.storage.tables.TblEmployees;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.NotificationUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.views.StickHeaderItemDecoration;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;

import retrofit2.Response;

public class ClockFragment extends BaseFragment implements SwipeRefreshLayout.OnRefreshListener, View.OnClickListener {

    private Context context;
    private ClockCrewAdapter clockCrewAdapter;
    private ArrayList<Object> lstData = new ArrayList<>();
    private ArrayList<Object> lstSearchData = new ArrayList<>();
    private final int notificationHours = 8;
    private FragmentClockBinding binding;


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = FragmentClockBinding.inflate(inflater, container, false);
        init();
        return binding.getRoot();
    }

    public void init() {
        context = getActivity();
        setOnClickListener();
        setupActionBar();
        setEditTextChangeListener();
        binding.srlRefresh.setOnRefreshListener(this);
        setAdapter();
        setData();

    }

    private void setupActionBar() {
        binding.tlClock.tvTitle.setText(R.string.time_clock);
        binding.tlClock.ivUpload.setVisibility(View.GONE);
        if (getActivity() != null)
            ((BaseActivity) getActivity()).setVisibilityOfUploadView(binding.tlClock.ivUpload);
    }

    private void setData() {
        new GetDataFromDatabase().execute();

    }

    private void setVisibilityOfSearchView() {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            binding.edtSearch.setText("");
            binding.edtSearch.setVisibility(View.GONE);
        } else {
            binding.edtSearch.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            onSuccessResponse(event.response);
        }
        if (event.throwable != null) {
            onFailureResponse(event.throwable);
        }
        if (event.isNoInternetConnection) {
            onNoInternetConnection();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEmployeeSavedEvent(EmployeeSavedEvent event) {
        setData();
    }


    private void setEditTextChangeListener() {
        binding.edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String text = charSequence.toString();
                searchText(text);
                if (text.length() > 0) {
                    binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                } else {
                    binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }

    private void setAdapter() {
        if (clockCrewAdapter == null) {
            clockCrewAdapter = new ClockCrewAdapter(context, new ClockCrewAdapter.OnItemSelectedListener() {
                @Override
                public void onItemClicked(int position, View view) {
                }

                @Override
                public void onHeaderBtnClicked(int position, View view) {
                    manageHeaderButtonClicked(getHeaderId(position), view);
                }

                @Override
                public void onCrewChecked(int position, boolean isChecked, ClockCrewData data) {
                    managedOnCrewCheckedEvent(position, isChecked, data);
                }

                @Override
                public void onLongPressed(int position, View view) {
                    ClockCrewData crewData = (ClockCrewData) lstData.get(position);
                    if (!crewData.isCrew() && crewData.getClockInType().equals(CLOCK_NORMAL)) {
                        PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name),
                                context.getString(R.string.msg_remove_extended_crew, crewData.getCrewName()),
                                getString(R.string.remove)
                                , getString(R.string.txt_cancel), false, true, (dialog, which) -> {
                                    removeSelectedCrewFromList(position, crewData);
                                }, (dialog, which) -> dialog.dismiss());
                    }
                }

                @Override
                public void onImageCheckedChangeListener(int position, boolean isChecked) {
                    manageCheckedChangedListener(position, isChecked);
                }

            });
        }
        binding.rvClockCrew.setAdapter(clockCrewAdapter);
        binding.rvClockCrew.setEmptyView(binding.emptyView.llEmptyViewMain);
        binding.rvClockCrew.addItemDecoration(new StickHeaderItemDecoration(binding.rvClockCrew, clockCrewAdapter, new OnStickyHeaderListener() {
            @Override
            public void OnStickyHeaderClicked(AppCompatTextView view, String headerButtonName) {
                if (!TextUtils.isEmpty(headerButtonName)) {
                    if (headerButtonName.equalsIgnoreCase(context.getString(R.string.txt_break))) {
                        // Give them a break
                        giveBreakToCrew(view);
                    } else if (headerButtonName.equalsIgnoreCase(context.getString(R.string.txt_start))) {
                        //Resume their work
                        doClockInOrResumeCrew();
                    } else if (headerButtonName.equalsIgnoreCase(context.getString(R.string.remove))) {
                        //Remove extended crew from view
                        removeExtendedCrewFromList(view);
                    }
                }
            }

            @Override
            public void OnStickyHeaderCheckBoxClicked(int headerPosition, String headerButtonName, boolean isSelected) {
                manageCheckedChangedListener(headerPosition, isSelected);
            }
        }));
    }


    private void manageCheckedChangedListener(int position, boolean isChecked) {
        int headerId = getHeaderId(position);
        switch (headerId) {
            case 1:
                // select all resumed crew
                updateSelection(CLOCK_RESUME, isChecked, position, false);

                break;
            case 2:
                //select all break crews
                updateSelection(CLOCK_BREAK, isChecked, position, false);

                break;
            case 3:
                // select all normal crews
                updateSelection(CLOCK_NORMAL, isChecked, position, false);

                break;
            case 4:
                // select all extended crews
                updateSelection(CLOCK_NORMAL, isChecked, position, true);

                break;
        }
    }

    private void updateSelection(String type, boolean isChecked, int position, boolean isExtended) {
        if (!lstSearchData.isEmpty()) {
            if (lstSearchData.get(position) instanceof ClockHeader) {
                ((ClockHeader) lstSearchData.get(position)).setAllSelect(isChecked);
            }
            for (int i = 0; i < lstSearchData.size(); i++) {
                Object object = lstSearchData.get(i);
                if (object instanceof ClockCrewData) {
                    ClockCrewData clockCrewData = (ClockCrewData) object;
                    if (type.equals(CLOCK_NORMAL)) {
                        if (isExtended) {
                            if (clockCrewData.getClockInType().equalsIgnoreCase(type) && !(clockCrewData.isCrew())) {
                                clockCrewData.setSelected(isChecked);
                            }
                        } else {
                            if (clockCrewData.getClockInType().equalsIgnoreCase(type) && (clockCrewData.isCrew())) {
                                clockCrewData.setSelected(isChecked);
                            }
                        }
                    } else {
                        if (clockCrewData.getClockInType().equalsIgnoreCase(type)) {
                            clockCrewData.setSelected(isChecked);
                        }
                    }
                }
            }
        } else {
            if (lstData.get(position) instanceof ClockHeader) {
                ((ClockHeader) lstData.get(position)).setAllSelect(isChecked);
            }
            for (int i = 0; i < lstData.size(); i++) {
                Object object = lstData.get(i);
                if (object instanceof ClockCrewData) {
                    ClockCrewData clockCrewData = (ClockCrewData) object;
                    if (type.equals(CLOCK_NORMAL)) {
                        if (isExtended) {
                            if (clockCrewData.getClockInType().equalsIgnoreCase(type) && !(clockCrewData.isCrew())) {
                                clockCrewData.setSelected(isChecked);
                            }
                        } else {
                            if (clockCrewData.getClockInType().equalsIgnoreCase(type) && clockCrewData.isCrew()) {
                                clockCrewData.setSelected(isChecked);
                            }
                        }
                    } else {
                        if (clockCrewData.getClockInType().equalsIgnoreCase(type)) {
                            clockCrewData.setSelected(isChecked);
                        }
                    }
                }
            }
        }
        clockCrewAdapter.notifyDataSetChanged();
    }

    private void setSearchTimeSelectAllMarkUp(ArrayList<Object> lstData, boolean isNotify, String type, boolean isCrew) {
        boolean isAllResumeSelected = true;
        boolean isAllBreakSelected = true;
        boolean isAllCrewSelected = true;
        boolean isAllExtendedCrewSelected = true;

        int resumeHeaderPos = -1;
        int breakHeaderPos = -1;
        int crewPos = -1;
        int extendCrewPos = -1;
        for (int i = 0; i < lstData.size(); i++) {
            Object object = lstData.get(i);
            if (object instanceof ClockHeader) {
                ClockHeader clockHeader = (ClockHeader) object;
                if (clockHeader.getHeaderId() == 1) {
                    resumeHeaderPos = i;
                } else if (clockHeader.getHeaderId() == 2) {
                    breakHeaderPos = i;
                } else if (clockHeader.getHeaderId() == 3) {
                    crewPos = i;
                } else if (clockHeader.getHeaderId() == 4) {
                    extendCrewPos = i;
                }
            }

            if (object instanceof ClockCrewData) {
                ClockCrewData clockCrewData = ((ClockCrewData) lstData.get(i));
                if (clockCrewData.getClockInType().equalsIgnoreCase(CLOCK_RESUME)) {
                    if (!clockCrewData.isSelected()) {
                        isAllResumeSelected = false;
                    }
                }
                if (clockCrewData.getClockInType().equalsIgnoreCase(CLOCK_BREAK)) {
                    if (!clockCrewData.isSelected()) {
                        isAllBreakSelected = false;
                    }
                }
                if (clockCrewData.getClockInType().equalsIgnoreCase(CLOCK_NORMAL) && clockCrewData.isCrew()) {
                    if (!clockCrewData.isSelected()) {
                        isAllCrewSelected = false;
                    }
                }
                if (clockCrewData.getClockInType().equalsIgnoreCase(CLOCK_NORMAL) && !clockCrewData.isCrew()) {
                    if (!clockCrewData.isSelected()) {
                        isAllExtendedCrewSelected = false;
                    }
                }
            }
        }

        if (resumeHeaderPos != -1) {
            Object resumeHeader = lstData.get(resumeHeaderPos);
            if (resumeHeader instanceof ClockHeader) {
                ((ClockHeader) resumeHeader).setAllSelect(isAllResumeSelected);
                if (isNotify && type.equalsIgnoreCase(CLOCK_RESUME)) {
                    clockCrewAdapter.notifyItemChanged(resumeHeaderPos);
                }
            }
        }
        if (breakHeaderPos != -1) {
            Object breakHeader = lstData.get(breakHeaderPos);
            if (breakHeader instanceof ClockHeader) {
                ((ClockHeader) breakHeader).setAllSelect(isAllBreakSelected);
                if (isNotify && type.equalsIgnoreCase(CLOCK_BREAK)) {
                    clockCrewAdapter.notifyItemChanged(breakHeaderPos);
                }
            }
        }
        if (crewPos != -1) {
            Object crewHeader = lstData.get(crewPos);
            if (crewHeader instanceof ClockHeader) {
                ((ClockHeader) crewHeader).setAllSelect(isAllCrewSelected);
                if (isNotify && type.equalsIgnoreCase(CLOCK_NORMAL) && isCrew) {
                    clockCrewAdapter.notifyItemChanged(crewPos);
                }
            }
        }
        if (extendCrewPos != -1) {
            Object extCrewHeader = lstData.get(extendCrewPos);
            if (extCrewHeader instanceof ClockHeader) {
                ((ClockHeader) extCrewHeader).setAllSelect(isAllExtendedCrewSelected);
                if (isNotify && type.equalsIgnoreCase(CLOCK_NORMAL) && !isCrew) {
                    clockCrewAdapter.notifyItemChanged(extendCrewPos);
                }
            }
        }
        if (!isNotify) {
            clockCrewAdapter.updateList(lstData);
        }
    }

    private int getHeaderId(int position) {
        int headerId = 0;
        if (!lstSearchData.isEmpty()) {
            if (lstSearchData.get(position) instanceof ClockHeader) {
                headerId = ((ClockHeader) lstSearchData.get(position)).getHeaderId();
            }
        } else {
            if (lstData.get(position) instanceof ClockHeader) {
                headerId = ((ClockHeader) lstData.get(position)).getHeaderId();
            }
        }
        return headerId;
    }

    private void manageHeaderButtonClicked(int headerId, View view) {
        switch (headerId) {
            case 1:
                // Give them a break
                giveBreakToCrew(view);
                break;
            case 2:
                //Resume their work
                doClockInOrResumeCrew();
                break;
            case 4:
                //Remove extended crew
                removeExtendedCrewFromList(view);
                break;
        }
    }


    private void managedOnCrewCheckedEvent(int position, boolean isChecked, ClockCrewData data) {
        ClockCrewData crewData;
        if (!lstSearchData.isEmpty()) {
            crewData = (ClockCrewData) lstSearchData.get(position);
            crewData.setSelected(isChecked);
            int pos = getPositionOfMainList(crewData);
            getLstMainDataFromPosition(pos).setSelected(isChecked);
            clockCrewAdapter.notifyItemChanged(position);
            setSearchTimeSelectAllMarkUp(lstSearchData, true, crewData.getClockInType(), crewData.isCrew());
        } else {
            crewData = (ClockCrewData) lstData.get(position);
            crewData.setSelected(isChecked);
            clockCrewAdapter.notifyItemChanged(position);
            setSearchTimeSelectAllMarkUp(lstData, true, crewData.getClockInType(), crewData.isCrew());
        }

    }

    private void searchText(String text) {
        lstSearchData.clear();
        if (clockCrewAdapter == null)
            return;
        if (text.length() > 0) {
            String typedString = text.trim().toLowerCase();
            for (Object data : lstData) {
                if (data instanceof ClockCrewData) {
                    if (((ClockCrewData) data).getCrewName().toLowerCase().contains(typedString)) {
                        lstSearchData.add(data);
                    }
                } else {
                    lstSearchData.add(data);
                }
            }
            ArrayList<Object> lstFilterData = new ArrayList<>();
            // Now remove all empty headers from searched list
            ClockHeader previousData = null;
            for (int i = 0; i < lstSearchData.size(); i++) {
                Object data = lstSearchData.get(i);
                if (data instanceof ClockHeader) {
                    lstFilterData.add(data);
                    if ((previousData != null)) {
                        lstFilterData.remove(previousData);
                    }
                    previousData = (ClockHeader) data;

                } else if (data instanceof ClockCrewData) {
                    lstFilterData.add(data);
                    previousData = null;
                }
            }
            // Remove last empty header if it exists.
            if (!lstFilterData.isEmpty() && lstFilterData.get(lstFilterData.size() - 1) instanceof ClockHeader) {
                lstFilterData.remove(lstFilterData.size() - 1);
            }
            lstSearchData.clear();
            lstSearchData.addAll(lstFilterData);


            setSearchTimeSelectAllMarkUp(lstSearchData, false, "", false);
            // clockCrewAdapter.updateList(lstSearchData);
        } else {
            setSearchTimeSelectAllMarkUp(lstData, false, "", false);
            //clockCrewAdapter.updateList(lstData);
        }
    }


    /**
     * Method to remove selected extended crew from main clock list screen.
     *
     * @param position position of selected item
     */
    private void removeSelectedCrewFromList(int position, ClockCrewData crewData) {
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        tblClockCrew.deleteDataById(crewData.getCrewId());
        int mainPosition = 0;
        if (!lstSearchData.isEmpty()) {
            mainPosition = lstData.indexOf(crewData);
            lstSearchData.remove(position);
        }
        lstData.remove(mainPosition);
        getCrewDataFromDb();
        setVisibilityOfBottomButtons();

    }

    private ClockCrewData getLstMainDataFromPosition(int position) {
        return (ClockCrewData) lstData.get(position);
    }

    private int getPositionOfMainList(ClockCrewData crewData) {
        return lstData.indexOf(crewData);
    }

    private void setVisibilityOfBottomButtons() {
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        if (tblClockCrew.getAllNormalData().size() == 0) {
            binding.btnStart.setVisibility(View.GONE);
        } else {
            if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
                List<ClockCrewData> clockData = tblClockCrew.getAllData();
                for (ClockCrewData clockCrewData : clockData) {
                    if (clockCrewData.getCrewId() == StaticUtils.getEmployeeIdInInt()
                            && !clockCrewData.getClockInType().equalsIgnoreCase(Constants.CLOCK_NORMAL)) {
                        binding.btnStart.setVisibility(View.GONE);
                        break;
                    } else {
                        binding.btnStart.setVisibility(View.VISIBLE);
                    }
                }
            } else {
                binding.btnStart.setVisibility(View.VISIBLE);
            }
        }

        if (tblClockCrew.getClockedInDataCount() > 0) {
            binding.btnStop.setVisibility(View.VISIBLE);
        } else {
            binding.btnStop.setVisibility(View.GONE);
        }
    }

    private void getCrewDataFromDb() {
        getCrewDataFromDb(true);
    }

    private void getCrewDataFromDb(boolean shouldNotifyAdapter) {
        if (lstData.size() > 0) {
            lstData.clear();
        }
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        List<ClockCrewData> lstResumesCrews = tblClockCrew.getAllResumedWorkCrewData();
        if (lstResumesCrews.size() > 0) {
            ClockHeader clockHeader = new ClockHeader();
            clockHeader.setTitle(context.getString(R.string.clocked_in));
            clockHeader.setHeaderId(1);
            lstData.add(clockHeader);
            lstData.addAll(lstResumesCrews);
        }
        List<ClockCrewData> lstOfTakenBreakCrews = tblClockCrew.getAllBreakedCrewData();
        if (lstOfTakenBreakCrews.size() > 0) {
            ClockHeader clockHeader = new ClockHeader();
            clockHeader.setTitle(context.getString(R.string.on_break));
            clockHeader.setHeaderId(2);
            lstData.add(clockHeader);
            lstData.addAll(lstOfTakenBreakCrews);
        }
        List<ClockCrewData> lstNormalCrews = tblClockCrew.getAllNormalCrewData();
        if (lstNormalCrews.size() > 0) {
            ClockHeader clockHeader = new ClockHeader();
            clockHeader.setTitle(getString(R.string.txt_crew));
            clockHeader.setHeaderId(3);
            lstData.add(clockHeader);
            lstData.addAll(lstNormalCrews);
        }

        List<ClockCrewData> lstAddedCrew = tblClockCrew.getAllExtendedCrewData();
        if (lstAddedCrew.size() > 0) {
          /*  Collections.sort(lstAddedCrew, (clockCrewData1, clockCrewData2)
                    -> clockCrewData1.getCrewName().compareTo(clockCrewData2.getCrewName()));*/
            ClockHeader clockHeader = new ClockHeader();
            clockHeader.setTitle(getString(R.string.added_crew));
            clockHeader.setHeaderId(4);
            lstData.add(clockHeader);
            lstData.addAll(lstAddedCrew);
        }
        updateDataInAdapter(shouldNotifyAdapter);
    }

    private void updateDataInAdapter(boolean shouldNotifyAdapter) {
        if (!shouldNotifyAdapter)
            return;
        searchText("");
        clockCrewAdapter.updateList(lstData);
        if (lstData.size() == 0) {
            binding.rvClockCrew.setEmptyData(context.getString(R.string.msg_no_clock_in_member));
        }
    }


    private void setOnClickListener() {
        binding.btnStart.setOnClickListener(this);
        binding.btnStop.setOnClickListener(this);
        binding.tlClock.ivUpload.setOnClickListener(this);
        binding.tlClock.btnAddCrew.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.btnStart) {
            doClockInOrResumeCrew();
        } else if (viewId == R.id.btnStop) {
            doClockOut();
        }else if (viewId == R.id.ivUpload) {
            ((BaseActivity) requireActivity()).navigateToUploadActivityScreen(binding.tlClock.ivUpload);
        }else if (viewId == R.id.btnAddCrew) {
            showHiddenCrewView();
        }
    }

    private void showHiddenCrewView() {
        showExtendedCrewMemberView();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getActivity() == null)
            return;
        ((BaseActivity) getActivity()).visibleUploadImageViewInScreen(binding.tlClock.ivUpload);
    }


    private void updateCrewData() {
        TblEmployees tblEmployees = new TblEmployees(context);
        TblClockCrew tblClockCrew = new TblClockCrew(context);

        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            List<ClockCrewData> lstCrews = tblClockCrew.getAllClockedCrewData();
            List<Integer> lstIds = new ArrayList<>();
            for (ClockCrewData crewData : lstCrews) {
                lstIds.add(crewData.getCrewId());
            }

            tblClockCrew.deleteDataWithoutIds(lstIds);
            if (!lstIds.contains(StaticUtils.getEmployeeIdInInt())) {
                Employees employees = tblEmployees.getDataById(StaticUtils.getEmployeeIdInInt());
                ClockCrewData data = new ClockCrewData();
                data.setCrewId(employees.getEmployeeID());
                data.setCrew(employees.isCrew());
                data.setCrewName(StaticUtils.getSelectedCrewMemberName(employees));
                data.setClockInType(CLOCK_NORMAL);
                tblClockCrew.insertOrUpdateSingleData(data);
            }

        } else if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
            List<ClockCrewData> lstCrews = tblClockCrew.getAllData();
            List<Integer> lstIds = new ArrayList<>();
            for (ClockCrewData crewData : lstCrews) {
                lstIds.add(crewData.getCrewId());
            }
            List<Employees> lstEmployeeData = tblEmployees.getAllDataWithoutSelected(lstIds);
            List<ClockCrewData> lstCrewData = new ArrayList<>();
            for (Employees employees : lstEmployeeData) {
                ClockCrewData data = new ClockCrewData();
                data.setCrewId(employees.getEmployeeID());
                data.setCrew(employees.isCrew());
                data.setCrewName(StaticUtils.getSelectedCrewMemberName(employees));
                data.setClockInType(CLOCK_NORMAL);
                lstCrewData.add(data);
            }
            tblClockCrew.insertBulkData(lstCrewData);
        }
        getCrewDataFromDb(false);

    }

    private void doClockInOrResumeCrew() {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2 || AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            updateClockInAndResumeData();
        }
    }


    private void removeExtendedCrewFromList(View view) {
        List<Integer> lstDeleteDataIds = new ArrayList<>();
        List<Object> lstCrewData = new ArrayList<>();
        if (!lstSearchData.isEmpty()) {
            lstCrewData.addAll(lstSearchData);
        } else {
            lstCrewData.addAll(lstData);
        }
        for (Object data : lstCrewData) {
            if (data instanceof ClockCrewData) {
                {
                    ClockCrewData crewData = (ClockCrewData) data;
                    if (crewData.getClockInType().equalsIgnoreCase(CLOCK_NORMAL) && !crewData.isCrew() && crewData.isSelected()) {
                        lstDeleteDataIds.add(crewData.getCrewId());
                    }
                }
            }
        }
        if (lstDeleteDataIds.isEmpty()) {
            //check if any clocked extended crews are selected
            if (checkExtendedCrewsFromClockedInData()) {
                showForeGroundToast(getString(R.string.msg_remove_clocked_crew_before_remove));
                return;
                //check if any normal crews are selected(Normal/Resume/Break)
            } else if (checkExtendedCrewsFromInNormalData()) {
                showForeGroundToast(getString(R.string.msg_can_not_remove_normal_crew));
                return;
            } else {
                //Show crew selection message
                showForeGroundToast(getString(R.string.please_make_a_selection));
                return;
            }
        }
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        tblClockCrew.deleteDataByIds(lstDeleteDataIds);
        clockCrewAdapter.notifyDataSetChanged();
        showForeGroundToast(getString(R.string.msg_remove_crew_message));
        getCrewDataFromDb();
        setVisibilityOfBottomButtons();
        lstCrewData.clear();
        syncSearchResult();
    }

    private boolean checkExtendedCrewsFromClockedInData() {
        List<Integer> lstSelectedBreakOrResumeIds = getSelectedData(CLOCK_BREAK, "");
        lstSelectedBreakOrResumeIds.addAll(getSelectedData(CLOCK_RESUME, ""));
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        List<ClockCrewData> lstSelectedCrew = tblClockCrew.getDataByCrewIds(lstSelectedBreakOrResumeIds);
        for (Object data : lstSelectedCrew) {
            if (data instanceof ClockCrewData) {
                {
                    ClockCrewData crewData = (ClockCrewData) data;
                    if ((crewData.getClockInType().equalsIgnoreCase(CLOCK_BREAK) || crewData.getClockInType().equalsIgnoreCase(CLOCK_RESUME)) && !crewData.isCrew()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean checkExtendedCrewsFromInNormalData() {
        List<Integer> lstSelectedNormalIds = getSelectedData(CLOCK_NORMAL, "");
        lstSelectedNormalIds.addAll(getSelectedData(CLOCK_BREAK, ""));
        lstSelectedNormalIds.addAll(getSelectedData(CLOCK_RESUME, ""));
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        List<ClockCrewData> lstSelectedCrew = tblClockCrew.getDataByCrewIds(lstSelectedNormalIds);
        for (Object data : lstSelectedCrew) {
            if (data instanceof ClockCrewData) {
                {
                    ClockCrewData crewData = (ClockCrewData) data;
                    if ((crewData.getClockInType().equalsIgnoreCase(CLOCK_NORMAL)
                            || crewData.getClockInType().equalsIgnoreCase(CLOCK_BREAK)
                            || crewData.getClockInType().equalsIgnoreCase(CLOCK_RESUME)) && crewData.isCrew()) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private List<Integer> getSelectedData(String type, String type2) {
        List<Integer> lstSelectedData = new ArrayList<>();
        for (int i = 0; i < lstData.size(); i++) {
            Object object = lstData.get(i);
            if (object instanceof ClockCrewData) {
                ClockCrewData clockCrewData = ((ClockCrewData) object);
                boolean condition;
                if (TextUtils.isEmpty(type2)) {
                    condition = clockCrewData.getClockInType().equalsIgnoreCase(type);
                } else {
                    condition = (clockCrewData.getClockInType().equalsIgnoreCase(type) || clockCrewData.getClockInType().equalsIgnoreCase(type2));
                }
                if (clockCrewData.isSelected() && condition) {
                    lstSelectedData.add(clockCrewData.getCrewId());
                }
            }
        }
        return lstSelectedData;
    }

    private void giveBreakToCrew(View view) {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2 || AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            TblClockCrew tblClockCrew = new TblClockCrew(context);
            List<Integer> lstIds = getSelectedData(CLOCK_RESUME, "");
            List<ClockCrewData> lstSelectedCrew = tblClockCrew.getDataByCrewIds(lstIds);
            updateBreakData(lstIds, lstSelectedCrew);
        }
    }

    private void openCheckedInForm(CheckInMap checkInMap) {
        if (getActivity() != null)
            ((MainActivity) getActivity()).navigateToFormDetailScreenFromClockScreen(checkInMap, SITE_DETAIL_REQUEST_CODE);
    }

    private void updateClockInAndResumeData() {
        //Normal crews selected ids
        List<Integer> lstNormalIds = getSelectedData(CLOCK_NORMAL, "");
        //Selected break crew ids
        List<Integer> lstSelectedBreakIds = getSelectedData(CLOCK_BREAK, "");

        //If both are empty then show selection message.
        if (lstNormalIds.isEmpty() && lstSelectedBreakIds.isEmpty()) {
            showForeGroundToast(getString(R.string.please_make_a_selection));
            return;
        }

        //if normal crew is not empty then update data and send Start BreadCrumb
        if (!lstNormalIds.isEmpty()) {
            addClockedDataInDB();
            if (getActivity() != null && lstNormalIds.size() > 0) {
                ((BaseActivity) getActivity()).prepareDataForBreadCrumb(4, MainActivity.currentLatitude, MainActivity.currentLongitude, -1, -1, "", "",lstNormalIds);
            }
        }

        //if already break crews are selected then update data and send resume BreadCrumb
        if (!lstSelectedBreakIds.isEmpty()) {
            updateResumeData(lstSelectedBreakIds);
        }

        showForeGroundToast(getString(R.string.msg_time_tracking_resume_time));
        getCrewDataFromDb();
        setVisibilityOfBottomButtons();
        syncSearchResult();
    }

    private void syncSearchResult() {
        if (binding.edtSearch.getText() == null)
            return;
        if (!TextUtils.isEmpty(binding.edtSearch.getText().toString())) {
            searchText(binding.edtSearch.getText().toString());
        }
    }

    private void updateClockOutData(List<Integer> lstIds) {

        if (lstIds.size() == 0) {
            showForeGroundToast(getString(R.string.please_make_a_selection));
            return;
        }
        TblClockCrew tblClockCrew = new TblClockCrew(getActivity());
        List<ClockCrewData> lstClockedCrew = tblClockCrew.getDataByCrewIds(lstIds);
        if (getActivity() != null && lstIds.size() > 0) {
            ((BaseActivity) getActivity()).prepareDataForBreadCrumb(5, MainActivity.currentLatitude, MainActivity.currentLongitude, -1, -1, "", "",lstIds);
        }
        if (lstIds.contains(StaticUtils.getEmployeeIdInInt())) {
            NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(context);
        }

        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            List<Integer> lstDeleteDataIds = new ArrayList<>();
            for (Iterator<ClockCrewData> it = lstClockedCrew.iterator(); it.hasNext(); ) {
                ClockCrewData crewData = it.next();
                if (crewData.getCrewId() != StaticUtils.getEmployeeIdInInt()) {
                    lstDeleteDataIds.add(crewData.getCrewId());
                    it.remove();
                }
            }
            tblClockCrew.deleteDataByIds(lstDeleteDataIds);

        }

        if (lstClockedCrew != null && lstClockedCrew.size() > 0) {
            for (ClockCrewData crewData : lstClockedCrew) {
                crewData.setClockInType(CLOCK_NORMAL);
                crewData.setTotalSession(0);
                crewData.setStartSession(0);
                crewData.setBreakTime(0);
                // lstClockedCrew.add(crewData);
            }
            tblClockCrew.updateBulkData(lstClockedCrew);
        }
        getCrewDataFromDb();
        setVisibilityOfBottomButtons();
        showForeGroundToast(getString(R.string.msg_clock_out_crew_message));
        syncSearchResult();
    }

    private void updateBreakData(List<Integer> lstIds, List<ClockCrewData> lstSelectedCrew) {
        if (lstIds.size() == 0) {
            showForeGroundToast(getString(R.string.please_make_a_selection));
            return;
        }
        TblClockCrew tblClockCrew = new TblClockCrew(context);

        TblCheckInMap tblCheckInMap = new TblCheckInMap(context);

        HashMap<String, Object> formCrewMap = new HashMap<>();
        for (Iterator<ClockCrewData> lstItemIterator = lstSelectedCrew.iterator(); lstItemIterator.hasNext(); ) {
            ClockCrewData crewData = lstItemIterator.next();
            if (!crewData.getClockInType().equalsIgnoreCase(Constants.CLOCK_RESUME)) {
                //Remove All selection those are not started
                lstItemIterator.remove();
                lstIds.remove((Integer) crewData.getCrewId());
            }
            CheckInMap checkInMap = tblCheckInMap.getDataByCrewId(crewData.getCrewId());
            if (checkInMap.getPkId() > 0) {
                //If ids are assigned with form,map with key
                formCrewMap.put(String.valueOf(checkInMap.getPkId()), checkInMap);
            } else {
                //If not assigned with any form then store key as a crewId with 0_ prefix and crewId as a value.
                formCrewMap.put("0_" + crewData.getCrewId(), crewData.getCrewId());
            }
        }
        List<Integer> lstCrewToBreak = new ArrayList<>();
        boolean crewsAreAvailableForBreak = false;
        CheckInMap allAssignedData = null;
        //If User is self clock in then just check if any form is checked in. if any then dont allow him to take a break
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1 && tblCheckInMap.getDataCount() > 0) {
            allAssignedData = tblCheckInMap.getAllData().get(0);
        } else {
            for (HashMap.Entry hashMapEntry : formCrewMap.entrySet()) {
                String key = (String) hashMapEntry.getKey();
                String[] keyData = key.split("_");
                if (keyData.length > 1) {
                    int value = (int) hashMapEntry.getValue();
                    lstCrewToBreak.add(value);
                    crewsAreAvailableForBreak = true;
                } else {
                    TblTMForms tblTMForms = new TblTMForms(context);
                    //Check if all crews are selected those are assigned to a form. If yse then we prevent user to send those crew to break and
                    //ask to checkout form first and then send crew to break.
                    CheckInMap checkInMap = (CheckInMap) hashMapEntry.getValue();
                    int count = checkInMap.getCrewIds().size();
                    for (int crewId : checkInMap.getCrewIds()) {
                        if (lstIds.contains(crewId)) {
                            count--;
                        }
                    }
                    //if count >0 = all crews of form are not selected, so let them in break and update break data in check inmap and form table.
                    if (count > 0) {
                        List<Integer> lstBreakCrew = new ArrayList<>();
                        List<Integer> lstResumedCrew = new ArrayList<>();
                        for (int crewId : checkInMap.getCrewIds()) {
                            if (lstIds.contains(crewId)) {
                                lstBreakCrew.add(crewId);
                                lstCrewToBreak.add(crewId);
                            } else {
                                lstResumedCrew.add(crewId);
                            }
                        }
                        tblCheckInMap.updateBreakCrewInForm(checkInMap.getPkId(), lstResumedCrew, lstBreakCrew);
                        //Send break breadcrumb, update data in check in map and update data in form too.
                        tblTMForms.updateCrewInForm(checkInMap.getFormPkId(), lstResumedCrew);
                        sendBreadcrumbForBreak(checkInMap, lstBreakCrew);

                        crewsAreAvailableForBreak = true;
                    } else {
                        //All crews are selected for break. prevent them go in break and ask him to checkout form first then allow him to take a break.
                        for (int crewId : checkInMap.getCrewIds()) {
                            if (lstIds.contains(crewId)) {
                                lstIds.remove((Integer) crewId);
                            }
                            allAssignedData = checkInMap;
                        }
                    }
                }
            }
        }

        if (crewsAreAvailableForBreak) {
            List<ClockCrewData> lstUpdateData = new ArrayList<>();
            List<ClockCrewData> lstBreakCrewData = tblClockCrew.getDataByCrewIds(lstCrewToBreak);
            for (ClockCrewData clockCrewData : lstBreakCrewData) {
                clockCrewData.setClockInType(Constants.CLOCK_BREAK);
                clockCrewData.setBreakTime(System.currentTimeMillis());
                long totalSessionTime = clockCrewData.getBreakTime() - clockCrewData.getStartSession();
                clockCrewData.setTotalSession(clockCrewData.getTotalSession() + totalSessionTime);
                lstUpdateData.add(clockCrewData);
            }

            // tblEmployees.updateEmployeeSelectionOnDemand(tblEmployees.getAllDataByIds(lstCrewToBreak), false);
            tblClockCrew.insertOrUpdateBulkData(lstUpdateData);
            if (getActivity() != null && lstUpdateData.size() > 0) {
                ((BaseActivity) getActivity()).prepareDataForBreadCrumb(30, MainActivity.currentLatitude, MainActivity.currentLongitude, -1, -1, "", "",lstCrewToBreak);
            }
            if (lstCrewToBreak.contains(StaticUtils.getEmployeeIdInInt())) {
                NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(context);
            }
            getCrewDataFromDb();
            showForeGroundToast(getString(R.string.msg_break_crew_message));
        } else {
            if (allAssignedData != null)
                askUserToCheckOutFormBeforeSendAllCrewInBreak(allAssignedData);
        }
        setVisibilityOfBottomButtons();
        syncSearchResult();
    }

    /**
     * Method to send breadcrumb sequence Open form -> checkout crew -> close form
     *
     * @param checkInMap
     * @param lstBreakCrew
     */
    private void sendBreadcrumbForBreak(CheckInMap checkInMap, List<Integer> lstBreakCrew) {
        //open form breadcrumb
       /* ((BaseActivity) requireActivity()).prepareDataForBreadCrumb(6, MainActivity.currentLatitude, MainActivity.currentLongitude,
                checkInMap.getSiteId(), checkInMap.getFormId(), checkInMap.getFormName());*/
        //Crew Submit  breadcrumb
        sendCrewChangeBreadcrumb(checkInMap, new ArrayList<>(), lstBreakCrew);
        // close form breadcrumb
     /*   ((BaseActivity) requireActivity()).prepareDataForBreadCrumb(7, MainActivity.currentLatitude, MainActivity.currentLongitude,
                checkInMap.getSiteId(), checkInMap.getFormId(), checkInMap.getFormName());*/
    }

    private void sendCrewChangeBreadcrumb(CheckInMap checkInMap, List<Integer> lstNewSelected, List<Integer> lstDeSelected) {
        StaticUtils.crewSelectionData(context, MainActivity.currentLatitude, MainActivity.currentLongitude, checkInMap.getSiteId(),
                checkInMap.getFormId(), checkInMap.getFormName(),checkInMap.getFormSubmissionId(), lstNewSelected, lstDeSelected);
        if (getActivity() != null) {
            ((BaseActivity) getActivity()).sendBroadCastForStartOtherDataUpload();
        }
    }


    /**
     * Method to send breadcrumb sequence Open form -> checkin crew -> close form
     *
     * @param checkInMap
     * @param lstSelected
     */
    private void sendBreadcrumbForResume(CheckInMap checkInMap, List<Integer> lstSelected, List<Integer> lstDeselected) {
        //open form breadcrumb
        /*((BaseActivity) requireActivity()).prepareDataForBreadCrumb(6, MainActivity.currentLatitude, MainActivity.currentLongitude,
                checkInMap.getSiteId(), checkInMap.getFormId(), checkInMap.getFormName());*/
        //Crew Submit  breadcrumb
        sendCrewChangeBreadcrumb(checkInMap, lstSelected, lstDeselected);
        // close form breadcrumb
        /*((BaseActivity) requireActivity()).prepareDataForBreadCrumb(7, MainActivity.currentLatitude, MainActivity.currentLongitude,
                checkInMap.getSiteId(), checkInMap.getFormId(), checkInMap.getFormName());*/
    }

    /**
     * Method to show user popup with detail message and provide option to open form from the popup
     *
     * @param checkInMap assigned crews Object
     */
    private void askUserToCheckOutFormBeforeSendAllCrewInBreak(CheckInMap checkInMap) {
        PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name),
                context.getString(R.string.msg_check_out_before_crew_break, checkInMap.getFormName(), checkInMap.getSiteName()),
                getString(R.string.open)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    openCheckedInForm(checkInMap);
                }, (dialog, which) -> dialog.dismiss());
    }

    private void updateResumeData(List<Integer> lstIds) {

        TblClockCrew tblClockCrew = new TblClockCrew(context);
        List<ClockCrewData> lstSelectedCrew = tblClockCrew.getDataByCrewIds(lstIds);

        TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
        TblTMForms tblTMForms = new TblTMForms(context);
        HashMap<String, Object> formCrewMap = new HashMap<>();
        for (Iterator<ClockCrewData> lstItemIterator = lstSelectedCrew.iterator(); lstItemIterator.hasNext(); ) {
            ClockCrewData crewData = lstItemIterator.next();
            if (!crewData.getClockInType().equalsIgnoreCase(Constants.CLOCK_BREAK)) {
                //Remove All selection those are not in break
                lstItemIterator.remove();
                lstIds.remove((Integer) crewData.getCrewId());
            }
            CheckInMap checkInMap = tblCheckInMap.getBreakDataByCrewId(crewData.getCrewId());
            if (checkInMap.getPkId() > 0) {
                //If ids are assigned with form,map with key
                formCrewMap.put(String.valueOf(checkInMap.getPkId()), checkInMap);
            } else {
                //If not assigned with any form store key as a crewId with 0_ prefix and crewId as a value.
                formCrewMap.put("0_" + crewData.getCrewId(), crewData.getCrewId());
            }
        }
        if (getActivity() != null && lstIds.size() > 0) {
            ((BaseActivity) getActivity()).prepareDataForBreadCrumb(31, MainActivity.currentLatitude, MainActivity.currentLongitude, -1, -1, "","", lstIds);
        }
        for (HashMap.Entry hashMapEntry : formCrewMap.entrySet()) {
            String key = (String) hashMapEntry.getKey();
            String[] keyData = key.split("_");
            if (keyData.length == 1) {
                //group selected crews with selected crews and send single breadcrumb for it.
                CheckInMap checkInMap = (CheckInMap) hashMapEntry.getValue();
                List<Integer> lstResumedCrew = checkInMap.getCrewIds();
                List<Integer> lstBreakCrews = checkInMap.getBreakCrewIds();
                List<Integer> lstNewResumedCrew = new ArrayList<>();
                for (Iterator<Integer> lstBreakCrewIds = lstBreakCrews.iterator(); lstBreakCrewIds.hasNext(); ) {
                    Integer crewId = lstBreakCrewIds.next();
                    if (lstIds.contains(crewId)) {
                        lstResumedCrew.add(crewId);
                        lstNewResumedCrew.add(crewId);
                        lstBreakCrewIds.remove();
                    }
                }

                tblCheckInMap.updateBreakCrewInForm(checkInMap.getPkId(), lstResumedCrew, lstBreakCrews);
                //Update data in check in map and update data in form too.
                tblTMForms.updateCrewInForm(checkInMap.getFormPkId(), lstResumedCrew);
                //Crew Submit  breadcrumb
                sendBreadcrumbForResume(checkInMap, lstNewResumedCrew, new ArrayList<>());


            }
        }
        List<ClockCrewData> lstUpdateData = new ArrayList<>();
        for (ClockCrewData clockCrewData : lstSelectedCrew) {
            clockCrewData.setClockInType(Constants.CLOCK_RESUME);
            clockCrewData.setStartSession(System.currentTimeMillis());
            clockCrewData.setBreakTime(0);
            lstUpdateData.add(clockCrewData);
        }
        tblClockCrew.insertOrUpdateBulkData(lstUpdateData);
        if (lstIds.contains(StaticUtils.getEmployeeIdInInt())) {
            resetLocalNotification();
        }

        getCrewDataFromDb();
        setVisibilityOfBottomButtons();
        showForeGroundToast(getString(R.string.msg_time_tracking_resume_time));
        syncSearchResult();
    }

    private void resetLocalNotification() {
        TblClockCrew tblClockCrew = new TblClockCrew(context);
        ClockCrewData clockCrewData = tblClockCrew.getDataById(StaticUtils.getEmployeeIdInInt());
        long totalTime;
        if (clockCrewData.getBreakTime() > 0) {
            totalTime = clockCrewData.getTotalSession();

        } else {
            totalTime = clockCrewData.getTotalSession() + (System.currentTimeMillis() - clockCrewData.getStartSession());
        }
        String hours = DateUtil.getTimeHoursFromIntValue(totalTime).concat(" ");
        String mins = DateUtil.getTimeMinutesFromIntValue(totalTime).concat(" ");
        int hourDifferent = notificationHours - Integer.parseInt(hours.trim());
        if (hourDifferent < 0) {
            NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(context);
            return;
        }
        int minDifferent = 0;
        if (Integer.parseInt(mins.trim()) > 0) {
            minDifferent = 60 - Integer.parseInt(mins.trim());
            hourDifferent = hourDifferent - 1;
        }
        long targetTime = DateUtil.getTimeAfterAdditionOfSomeTime(System.currentTimeMillis(), hourDifferent, minDifferent);
        NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(context);
        NotificationUtils.setLocalNotification(context, targetTime);
    }


    private void addClockedDataInDB() {
        TblClockCrew tblClockCrew = new TblClockCrew(context);

        List<ClockCrewData> lstCrewData = new ArrayList<>();
        for (int i = 0; i < lstData.size(); i++) {
            Object object = lstData.get(i);
            if (object instanceof ClockCrewData && ((ClockCrewData) object).isSelected()
                    && ((ClockCrewData) lstData.get(i)).getClockInType().equals(CLOCK_NORMAL)) {
                ClockCrewData crewData = (ClockCrewData) object;
                crewData.setClockInType(Constants.CLOCK_RESUME);
                crewData.setStartSession(System.currentTimeMillis());
                crewData.setBreakTime(0);
                if (crewData.getCrewId() == StaticUtils.getEmployeeIdInInt()) {
                    checkAndAskBatteryOptimisationPermission();

                }
                lstCrewData.add(crewData);
            }
        }
        tblClockCrew.insertOrUpdateBulkData(lstCrewData);
    }

    private void checkAndAskBatteryOptimisationPermission() {
        Intent intent = new Intent();
        String packageName = context.getPackageName();
        PowerManager pm = (PowerManager) context.getSystemService(POWER_SERVICE);
        if (!pm.isIgnoringBatteryOptimizations(packageName)) {
            intent.setAction(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS);
            intent.setData(Uri.parse("package:" + packageName));
            startActivityForResult(intent,NOTIFICATION_BG_PERMISSION);
        } else {
            setNotification();
        }
    }

    private void setNotification() {
        NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(context);
        int notificationMinute = 0;
        long targetTime = DateUtil.getTimeAfterAdditionOfSomeTime(System.currentTimeMillis(), notificationHours, notificationMinute);
        NotificationUtils.setLocalNotification(context, targetTime);
    }

    private void doClockOut() {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2 || AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            TblClockCrew tblClockCrew = new TblClockCrew(context);
            List<Integer> lstIds = getSelectedData(CLOCK_BREAK, CLOCK_RESUME);
            List<ClockCrewData> lstSelected = tblClockCrew.getDataByCrewIds(lstIds);
            checkAndFilterAssignedCrewAndClockOutOthers(lstIds, lstSelected);
        }
    }

    private void checkAndFilterAssignedCrewAndClockOutOthers(List<Integer> lstIds, List<ClockCrewData> lstClockedCrew) {
        if (lstClockedCrew.isEmpty()) {
            showForeGroundToast(getString(R.string.please_make_a_selection));
            return;
        }

        TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
        HashMap<String, Object> formCrewMap = new HashMap<>();


        for (Iterator<ClockCrewData> lstItemIterator = lstClockedCrew.iterator(); lstItemIterator.hasNext(); ) {
            ClockCrewData crewData = lstItemIterator.next();
            CheckInMap checkInMap = tblCheckInMap.getDataByCrewId(crewData.getCrewId());
            if (checkInMap.getPkId() > 0) {
                //If ids are assigned with form,map with key
                formCrewMap.put(String.valueOf(checkInMap.getPkId()), checkInMap);
            } else {
                //If not assigned with any form store key as a crewId with 0_ prefix and crewId as a value.
                formCrewMap.put("0_" + crewData.getCrewId(), crewData.getCrewId());
            }
        }


        List<Integer> lstClockOutCrewId = new ArrayList<>();
        boolean crewsAreAvailableForClockOut = false;
        CheckInMap allAssignedData = null;
        //If User is self clock in then dont just check if any form is checked in. if any then dont allow him to clock out
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1 && tblCheckInMap.getDataCount() > 0) {
            allAssignedData = tblCheckInMap.getAllData().get(0);
        } else {
            for (HashMap.Entry hashMapEntry : formCrewMap.entrySet()) {
                String key = (String) hashMapEntry.getKey();
                String[] keyData = key.split("_");
                if (keyData.length > 1) {
                    int value = (int) hashMapEntry.getValue();
                    lstClockOutCrewId.add(value);
                    crewsAreAvailableForClockOut = true;
                } else {
                    //Check if all crews are selected those are assigned to a form. If yse then we prevent user to send those crew to break and
                    //ask to checkout form first and then send crew to break.
                    CheckInMap checkInMap = (CheckInMap) hashMapEntry.getValue();
                    int count = checkInMap.getCrewIds().size();
                    for (int crewId : checkInMap.getCrewIds()) {
                        if (lstIds.contains(crewId)) {
                            count--;
                        }
                    }
                    //if count >0 = all crews of form are not selected, so let them allow to clock out
                    if (count > 0) {
                        List<Integer> lstBreakCrews = checkInMap.getBreakCrewIds();
                        List<Integer> lstResumedCrew = checkInMap.getCrewIds();
                        List<Integer> lstSelectedCrewToClockOut = new ArrayList<>();

                        if (!lstBreakCrews.isEmpty()) {
                            for (Iterator<Integer> lstBreakCrewIds = lstBreakCrews.iterator(); lstBreakCrewIds.hasNext(); ) {
                                Integer crewId = lstBreakCrewIds.next();
                                if (lstIds.contains(crewId)) {
                                    lstBreakCrewIds.remove();
                                    //lstSelectedCrewToClockOut.add(crewId);
                                    lstClockOutCrewId.add(crewId);
                                }
                            }
                        }

                        if (!lstResumedCrew.isEmpty()) {
                            for (Iterator<Integer> lstResumedCrewIds = lstResumedCrew.iterator(); lstResumedCrewIds.hasNext(); ) {
                                Integer crewId = lstResumedCrewIds.next();
                                if (lstIds.contains(crewId)) {
                                    lstResumedCrewIds.remove();
                                    lstSelectedCrewToClockOut.add(crewId);
                                    lstClockOutCrewId.add(crewId);
                                }
                            }
                        }

                        TblTMForms tblTMForms = new TblTMForms(context);
                        tblCheckInMap.updateBreakCrewInForm(checkInMap.getPkId(), lstResumedCrew, lstBreakCrews);
                        tblTMForms.updateCrewInForm(checkInMap.getFormPkId(), lstResumedCrew);
                        sendBreadcrumbForBreak(checkInMap, lstSelectedCrewToClockOut);
                        crewsAreAvailableForClockOut = true;
                    } else {
                        //All crews are selected for break. prevent them go in break and ask him to checkout form first then allow him to take a break.
                        for (int crewId : checkInMap.getCrewIds()) {
                            if (lstIds.contains(crewId)) {
                                lstIds.remove((Integer) crewId);
                            }
                            allAssignedData = checkInMap;
                        }
                    }
                }
            }
        }

        if (crewsAreAvailableForClockOut) {
            updateClockOutData(lstClockOutCrewId);
            getCrewDataFromDb();
        } else {
            if (allAssignedData != null) {
                CheckInMap finalAllAssignedData = allAssignedData;
                PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name),
                        context.getString(R.string.clock_out_message, allAssignedData.getFormName(), allAssignedData.getSiteName()),
                        getString(R.string.open)
                        , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                            openCheckedInForm(finalAllAssignedData);
                        }, (dialog, which) -> dialog.dismiss());
            }
        }
        setVisibilityOfBottomButtons();
        syncSearchResult();
    }


    private void showExtendedCrewMemberView() {
        TblClockCrew tblClockCrew = new TblClockCrew(getActivity());
        TblEmployees tblEmployees = new TblEmployees(getActivity());
        List<Employees> lstSelectedCrewData = tblEmployees.getExtendedUserData();
        List<Integer> lstSelectedData = new ArrayList<>();
        List<ClockCrewData> lstCrewData = tblClockCrew.getAllExtendedCrewData();
        List<ClockCrewData> lstOccupiedCrewData = tblClockCrew.getAllExtendedClockedCrewData();
        for (int i = 0; i < lstCrewData.size(); i++) {
            ClockCrewData clockCrewData = lstCrewData.get(i);
            if (clockCrewData != null) {
                lstSelectedData.add(clockCrewData.getCrewId());
            }
        }


        for (int i = 0; i < lstOccupiedCrewData.size(); i++) {
            ClockCrewData clockCrewData = lstOccupiedCrewData.get(i);
            if (clockCrewData != null) {
                lstSelectedData.add(clockCrewData.getCrewId());
            }
        }

        if (!lstSelectedCrewData.isEmpty()) {
            PopUtils.showClockExtendedCrewSelectionView(getActivity(), lstSelectedCrewData, lstSelectedData, new OnClockScreenMultiCrewSelected() {
                @Override
                public void onSelected(List<CrewSelectionData> lstCrewData) {
                    onCrewSelected(lstCrewData);
                }

                @Override
                public void onCancel() {

                }
            });
        }
    }


    /**
     * Method to add extended crew in clock crew table of remove from table if any removed.
     *
     * @param lstSelectedEmployee lst of all selected extended crews
     */
    private void onCrewSelected(List<CrewSelectionData> lstSelectedEmployee) {
        if (lstSelectedEmployee != null) {
            List<ClockCrewData> lstCrewData = new ArrayList<>();
            TblClockCrew tblClockCrew = new TblClockCrew(getActivity());
            for (CrewSelectionData selectionData : lstSelectedEmployee) {
                ClockCrewData data = new ClockCrewData();
                data.setCrewId(selectionData.getCrewId());
                data.setCrew(selectionData.isCrew());
                data.setCrewName(selectionData.getCrewName());
                data.setClockInType(CLOCK_NORMAL);
                lstCrewData.add(data);
            }

            tblClockCrew.insertOrUpdateBulkData(lstCrewData);
            getCrewDataFromDb();
            setVisibilityOfBottomButtons();
            syncSearchResult();
        }
    }

    @Override
    public void onRefresh() {
        if (getActivity() == null)
            return;
        if (BaseApplication.getInstance().isOnline(getActivity())) {
            ((MainActivity) getActivity()).callApiForAppData(false, true);
        } else {
            binding.srlRefresh.setRefreshing(false);
        }

    }

    private void onSuccessResponse(Response<AppDataResponse> response) {
        if (response != null && response.code() == 304) {
            if (binding.srlRefresh != null) {
                binding.srlRefresh.setRefreshing(false);
            }
            return;
        }
        setData();
        showDataUpdateMessage();
        if (binding.srlRefresh != null) {
            binding.srlRefresh.setRefreshing(false);
        }
        syncSearchResult();

    }

    private void showDataUpdateMessage() {
        if (getActivity() != null && ((MainActivity) getActivity()).activeFragment instanceof ClockFragment) {
            showForeGroundToast(getString(R.string.msg_time_data_updated));
        }
    }

    public void onFailureResponse(Throwable t) {
        if (getActivity() == null)
            return;
        if (getActivity().isFinishing())
            return;
        binding.srlRefresh.setRefreshing(false);
    }

    public void onNoInternetConnection() {
        if (getActivity() == null)
            return;
        if (getActivity().isFinishing())
            return;
        binding.srlRefresh.setRefreshing(false);
    }

    protected class GetDataFromDatabase extends AsyncTask<Void, Void, Void> {

        @Override
        protected Void doInBackground(Void... params) {
            updateCrewData();
            return null;
        }

        @Override
        protected void onPostExecute(Void result) {
            updateDataInAdapter(true);
            setVisibilityOfBottomButtons();
            setVisibilityOfSearchView();
            manageSearchViewCloseTouchEvent(binding.edtSearch);
            binding.edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);

            if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_EXTENDED_SEARCH_ENABLE, false)) {
                binding.tlClock.btnAddCrew.setVisibility(View.VISIBLE);
                binding.tlClock.btnAddCrew.setEnabled(true);
            } else {
                binding.tlClock.btnAddCrew.setVisibility(View.GONE);
                binding.tlClock.btnAddCrew.setEnabled(false);
            }
        }
    }

    public void manageBGNotificationResponse(boolean permissionGranted) {
        if (permissionGranted) {
            setNotification();
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == NOTIFICATION_BG_PERMISSION) {
            manageBGNotificationResponse(resultCode == Activity.RESULT_OK);
        }
    }




    @Override
    public void onDestroyView() {
        super.onDestroyView();
        try {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }
}
