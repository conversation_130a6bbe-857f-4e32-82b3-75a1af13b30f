package com.sitefotos;

public class Constants {
    public static final String URL_PRIVACY_POLICY = BuildConfig.BASE_URL + "index/privacy";
    public static final String URL_TERMS_OF_SERVICE = BuildConfig.BASE_URL + "index/terms";

    public static final String OTHERDATA_UPLOAD_SERVICE_BROADCAST = "OtherDataUploadBroadcast";
    public static final String OTHERDATA_UPLOAD_DONE_SERVICE_BROADCAST = "OtherDataUploadDoneBroadcast";
    public static final String OTHERDATA_UPLOAD_FAIL_SERVICE_BROADCAST = "OtherDataUploadFailBroadcast";
    public static final String IMAGE_UPLOAD_SERVICE_BROADCAST = "ImageUploadBroadCase";
    public static final String IMAGE_UPLOAD_SYNC_BROADCAST = "ImageUploadSyncBroadCase";
    public static final String UPLOAD_OTHER_DATA_BROADCAST = "UploadOtherData";
    public static final String UPLOAD_BOTH_DATA_BROADCAST = "UploadBothData";
    public static final String UPLOAD_DATA_DONE_EVENT = "UploadDataDone";
    public static final String UPLOAD_DATA_START_EVENT = "UploadDataStart";
    public static final String APP_SITE_PERMISSION = "apptm";
    public static final String APP_DISABLE_MAIN_CAMERA = "disableMainCamera";
    public static final String POLYGONE_DISTANCE_DELTA = "distanceDelta";
    public static final String APP_BREADCRUMB = "breadcrumbs";
    public static final String PARAM_PROFILEID = "profileid";
    public static final String PARAM_APP_PROFILE_ID = "AppProfileID";
    public static final String PARAM_CREW = "crew[]";
    public static final String PARAM_DE_CREW = "deCrew[]";
    public static final String PARAM_SERVICEID = "serviceid";
    public static final String PARAM_SITEID = "siteid";
    public static final String PARAM_SITE_ID = "SiteID";
    public static final String PARAM_TITLE = "title";
    public static final String PARAM_PHOTOS = "Photos";
    public static final String PARAM_TYPE = "type";
    public static final String PARAM_DT = "dt";
    public static final String PARAM_STAG = "stag";
    public static final String PARAMS_WP_DATA = "wp_data";
    public static final String PARAM_WPSITEID = "SiteID";
    public static final String PARAM_WPPROFILEID = "AppProfileID";
    public static final String PARAM_CANCEL_CHECK_IN = "cancelCheckIn";
    public static final String PARAM_SERVICES = "services";
    //We have removed this param from app version 2.3.3. but keep for now to manage old data with post app version.
    public static final String PARAM_SUBMITTED_TIME = "SubmittedTime";
    public static final int ADAPTER_VIEW_TYPE_HEADER = 1;
    public static final int ADAPTER_VIEW_TYPE_DEFAULT = 2;
    public static final String CLOCK_RESUME = "Resume";
    public static final String CLOCK_BREAK = "Break";
    public static final String CLOCK_NORMAL = "Normal";
    public static final String PREF_NOTIFICATION_ID = "notificationId";
    public static final String PARAM_ISSUE_TIME_UNIX = "IssueTimeUNIX";
    public static final String PARAM_ISSUE = "Issues";
    public static final String PARAM_ISSUE_DATA = "issue_data";
    public static final String PARAM_SITE_FILTER_DISTANCE = "siteFilterDistance";
    public static final String PARAM_ADDRESS_NAME = "addressName";
    public static final String PARAM_STREET_ADDRESS = "streetAddress";
    public static final String PARAM_DEFAULT_MAP_PIN_URL = "defaultMapPinURL";
    public static final String PARAM_APP_VERSION = "appVersion";
    public static final String PARAM_APP_UDID = "udid";
    public static final String PARAM_DEVICE_TYPE = "deviceType";
    public static final String PARAM_DEVICE_MODEL = "deviceModel";
    public static final String PARAM_DEVICE_PLATFORM = "devicePlatform";
    public static final String DEVICE_ID = "deviceId";
    public static final String USER_FCM_TOKEN = "fcmToken";

    public static int SCREEN_HEIGHT, SCREEN_WIDTH, CAM_SCREEN_HEIGHT, CAM_SCREEN_WIDTH;

    public static final String CALL_TIME = "callTime";
    public static final String LOGGED_IN_USER_EMAIL_ADDRESS = "LOGGED_IN_USER_EMAIL_ADDRESS";
    public static final String LOGGED_IN_USER_COMPANY_ID = "LOGGED_IN_USER_COMPANY_ID";
    public static final String LOGGED_IN_USER_TYPE = "LOGGED_IN_USER_TYPE";
    public static final String LOGGED_IN_USER_COMPANY_NAME = "LOGGED_IN_USER_COMPANY_NAME";
    public static final String LOGGED_IN_USER_LOGIN_STATUS = "LOGGED_ID_USER_LOGIN_STATUS";
    public static final String LOGGED_IN_USER_TYPE_PRIVILEGED_USER = "PrivilegedUser";
    public static final String LOGGED_IN_USER_IMAGE_UPLOADS_QUEUE = "LOGGED_IN_USER_IMAGE_UPLOADS_QUEUE";
    public static final String LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE = "LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE";
    public static final String LOGGED_IN_USER_SAVE_COPY_TO_DEVICE = "LOGGED_IN_USER_SAVE_COPY_TO_DEVICE";
    public static final String LOGGED_IN_USER_UPLOAD_USING_CELLULAR_DATA = "LOGGED_IN_USER_UPLOAD_USING_CELLULAR_DATA";
    public static final String LOGGED_IN_USER_PARAM_SKIP_GEO = "skipGeo";
    public static final String LOGGED_IN_USER_PARAM_SCREEN_TYPE = "screenType";
    public static final String LOGGED_IN_USER_PARAM_EMPLOYEE_ID = "EmployeeID";
    public static final String LOGGED_IN_USER_TAGS = "tags";
    public static final String LOGGED_IN_USER_PARAM_FORM_LOCK = "formLock";
    public static final String LOGGED_IN_USER_TARGET_LOCATOR = "targetLocator";
    public static final String LOGGED_IN_USER_IS_FORM = "form";
    public static final String LOGGED_IN_USER_PARAM_CLOCKINOUT = "ClockInOut";
    public static final String LOGGED_IN_USER_PARAM_OLD_CLOCKINOUT = "OldClockInOut";
    public static final String LOGGED_IN_PARAM_IS_LOCATION_REQUIRED = "isGPSRequired";
    public static final String PARAM_SITE_ORDERED = "Ordered";
    public static final String PARAM_ALL_SITES = "AllSites";
    public static final String PARAM_ROUTES_ACTIVE = "RoutesActive";
    public static final String USER_CURRENT_LANGUAGE = "currentLanguage";
    public static final String LOGGED_IN_USER_FORCE_SITES = "showAllSites";
    public static final String LOGGED_IN_USER_WEATHER_DISPLAYED = "LOGGED_IN_USER_WEATHER_DISPLAYED";
    public static final String LOGGED_IN_USER_EXTENDED_SEARCH_ENABLE = "extendedCrewSearch";

    public static final String EXTRA_LOCATION_LATITUDE = "latExtra";
    public static final String EXTRA_LOCATION_LONGITUDE = "lonExtra";

    public static final String PARAM_INVALID_ACCESS_CODE = "InvalidAccessCode";
    public static final String PARAM_EMAIL = "email";
    public static final String PARAM_ACCESS_CODE = "accessCode";
    public static final String PARAM_BUILDING_ID = "bid";
    public static final String PARAM_PROPERTY_NAME = "name";
    public static final String PARAM_GEO_LOCATION = "geo";
    public static final String PARAM_LAT = "lat";
    public static final String PARAM_LNG = "lng";
    public static final String PARAM_AUTOSHARE = "autoshare";
    public static final String PARAM_CITY = "city";
    public static final String PARAM_STATE = "state";
    public static final String PARAM_ADDRESS = "address";
    public static final String PARAM_ZIP = "zip";
    public static final String PARAM_UUID = "uuid";
    public static final String PARAM_TAGS = "tags";
    public static final String PARAMS_FORM_DATA_APP = "sf_form_data_app";
    public static final String PARAM_FORM_ID = "sf_id";
    public static final String PARAM_BUIL = "buil";
    public static final String PARAM_FORM_KEY_DATA = "sfs_form_data";
    public static final String PARAM_LON = "lon";
    public static final String PARAM_LANG = "lang";
    public static final String PARAM_FCM_TOKEN = "deviceToken";
    public static final String PARAM_FORM_NAME = "sf_form_name";
    public static final String PARAM_SOURCE = "source";
    public static final String PARAM_CHECK_IN_TIME = "checkInTime";
    public static final String PARAM_CHECK_OUT_TIME = "checkOutTime";

    public static final String PARAM_CHECK_IN_DATE = "checkInDate";
    public static final String PARAM_CHECK_OUT_DATE = "checkOutDate";

    public static final String CAMERA_FLIP_MODE = "CAMERA_FLIP_MODE";
    public static final String CAMERA_FLASH_MODE = "FLASH_MODE_STRING";

    public static final String IS_UNIT_IN_MILES = "IS_UNIT_IN_MILES";

    public static final String KEY_ADD_NEW_PROPERTY_NEW_PROPERTY_BID = "0";

    public static final String KEY_ADD_PROPERTY_API_RESPONSE_MAX = "max";
    public static final String KEY_ADD_PROPERTY_API_RESPONSE_JOIN = "join";
    public static final String KEY_ADD_PROPERTY_API_RESPONSE_PAST = "past";
    public static final String KEY_INTENT_RETURN_DATA = "result";
    public static final String KEY_INTENT_ADDRESS = "intent_address";

    public static final String IS_ORIGINAL_ICON = "isOriginalIcon";

    public static final String KEY_LOCATION = "KEY_LOCATION";
    public static final String HAS_PERMISSION_REQUESTED = "has_permission_requested";

    public static final int PLACE_AUTOCOMPLETE_REQUEST_CODE = 601;

    public static final int CAMERA_PERMISSIONS_REQUEST = 134;
    public static final int EXTERNAL_STORAGE_REQUEST = 303;
    public static final int LOCATION_PERMISSION_REQUEST = 302;
    public static final int ADD_NEW_PROPERTY_REQUEST = 801;
    public static final int MAP_ACTIVITY_REQUEST = 901;
    public static final int OVERLAY_ACTIVITY_REQUEST = 902;
    public static final int ADD_ADDRESS_ACTIVITY_REQUEST = 903;
    public static final int NOTIFICATION_BG_PERMISSION = 904;

    public static final int ACTION_CODE_PICK_FROM_GALLERY = 501;
    public static final int SITE_DETAIL_REQUEST_CODE = 1011;
    public static final int LOCATION_REQUEST_CODE = 5112;
    public static final int LOCATION_REQUEST_CODE_CAMERA_SCREEN = 5114;

    public static final int SUB_FORM_CREATE_REQUEST_CODE = 5115;
    public static final String DRAWING_PAINT_STROKE_COLOR = "DRAWING_PAINT_STROKE_COLOR";
    public static final int DRAWING_PAINT_DEFAULT_STROKE_COLOR = R.color.blue_ink_color;

    public static final String UPLOAD_IMAGE_RESULT_PROGRESS = "UPLOAD_IMAGE_RESULT_PROGRESS";

    public static final String IS_SHARE_CAPTURED_IMAGE = "IS_SHARE_CAPTURED_IMAGE";
    public static final String LOGGED_IN_USER_APP_SETTINGS_HI_RES = "LOGGED_IN_USER_APP_SETTINGS_HI_RES";
    public static final String LOGGED_IN_USER_APP_SETTINGS_RAPID_RH = "LOGGED_IN_USER_APP_SETTINGS_RAPID_RH";

    public static final int FORM_DETAIL_RESULT = 1010;

    public static final int SUCCESS_RESULT = 0;

    public static final int FAILURE_RESULT = 1;

    public static final String PACKAGE_NAME = "com.sitefotos.android.appprojectstructure";
    public static final String RECEIVER = PACKAGE_NAME + ".RECEIVER";
    public static final String RESULT_DATA_KEY = PACKAGE_NAME + ".RESULT_DATA_KEY";
    public static final String RESULT_DATA_KEY_ADDRESS = PACKAGE_NAME + ".RESULT_DATA_KEY_ADDRESS";
    public static final String LOCATION_DATA_EXTRA = PACKAGE_NAME + ".LOCATION_DATA_EXTRA";


    //use for type
    public static final String TEXT = "text";
    public static final String PANEL = "panel";
    public static final String SPINNER = "dropdown";
    public static final String DYNAMIC_DROPDOWN = "dynamicDropdown";
    public static final String DROPDOWNMULTIPLE = "dropdownmultiple";
    public static final String IS_MANAGE_CREW_FIELD = "isManageCrewField";
    public static final String SF_ID = "sf_id";
    public static final String SITEID = "siteID";

    public static final String CHECKBOX = "checkbox";
    public static final String SEGMENT_INPUT = "segmentInput";
    public static final String RADIOGROUP = "radiogroup";
    public static final String IMAGE_UPLOAD = "file";
    public static final String SIGNATUREPAD = "signaturepad";
    public static final String SKETCH = "sketch";
    public static final String SUB_HEADER = "subHeader";
    public static final String COMMENT = "comment";
    public static final String ISWEATHERINPUT = "isWeatherInput";
    public static final String CHECKINOUT = "checkinout";
    public static final String CREW = "crew";
    public static final String MANAGE_CREW = "manageCrew";
    public static final String SERVICE = "service";
    public static final String MATERIAL = "material";
    public static final String SITEINFO = "siteinfo";
    public static final String ISSUES = "issues";
    public static final String ISSUES_ISSUBMITTED = "issuesIsSubmitted";
    public static final String ISSUES_TEXE = "issueText";
    public static final String TEMP_VALUE = "tempValue";
    public static final String ADDRESS = "address";
    public static final String AUTO_FILL_LOGGED_USERID = "autoFillLoggedUserID";

    public static final String PARAM_PRE_SELECT = "preSelect";

    //Use For Input type
    public static final String INPUTTYPE_DATE = "date";
    public static final String INPUTTYPE_DATE_TIME = "datetime-local";
    public static final String INPUTTYPE_EMAIL = "email";
    public static final String INPUTTYPE_PASSWORD = "password";
    public static final String INPUTTYPE_URL = "url";
    public static final String INPUTTYPE_COMMENT = "comment";
    public static final String INPUTTYPE_TEL = "tel";
    public static final String INPUTTYPE_NUMBER = "number";

    //elements
    public static final String TITLE = "title";
    public static final String NAME = "name";
    public static final String TYPE = "type";
    public static final String PEOPLE = "People";
    public static final String HOUR = "Hour";
    public static final String OPTIONS = "Options";
    public static final String ISREQUIRED = "isRequired";
    public static final String CHOICES = "choices";
    public static final String LIST_ITEMS = "listItems";
    public static final String INPUTTYPE = "inputType";
    public static final String TEXTDISPLAY = "textdisplay";
    public static final String ELEMENTS = "elements";
    public static final String SEGMENT = "segment";
    public static final String CHOICEVALUE = "choiceValue";
    public static final String GEO = "geo";
    public static final String VALUE = "value";
    public static final String VALUE_DISPLAY = "valueDisplay";
    public static final String VALUE_MATERIAL = "materialValue";
    public static final String VALUE_NAME = "valueName";
    public static final String ISSUE_VALUE = "value";
    public static final String SEPARATOR = ",";
    public static final String ID = "id";
    public static final String TEMP_TAG_ID = "tempTagId";
    public static final String IS_GROUP_CREATED = "isGroupCreated";
    public static final String PLACEHOLDER = "placeHolder";
    public static final String URL = "url";
    public static final String BUILDING_ID = "buildingID";
    public static final String BUILDING_NAME = "buildingName";
    public static final String SERVICETYPE = "serviceType";
    public static final String SERVICEID = "ServiceID";
    public static final String TIMELOG = "TimeLog";
    public static final String SERVICE_NAME = "ServiceName";
    public static final String TnMService = "TnMservice";
    public static final String MATERIAL_DATA = "materialData";
    public static final String UNIT = "unit";
    public static final String CHECK_CHILD_VIEW = "checkChildView";
    public static final String QUANTITY = "quantity";
    public static String COMPLETED = "Completed";
    public static final String START_TIME = "StartTime";
    public static final String STOP_TIME = "StopTime";
    public static final String TOTAL_SERVICE_TIME = "TotalServiceTime";
    public static final String PARENT_TAG = "panelTag";
    public static final String SELECTED_EQUIPMENTS = "selectedEquipments";
    public static final String SELECTED_VEHICLES = "selectedVehicles";
    public static final String DROPDOWN_VALUE_TYPE = "dropdownValueType";
    //Image json
    public static final String FORMID = "formId";
    public static final String LRIMAGE = "lrImageURL";
    public static final String HRIMAGE = "hrImageURL";
    public static final String IMAGE_UUID = "uuid";
    public static final String IS_SIGNATURE = "isSignature";
    public static final String TAGID = "tagId";
    public static final String IMAGEID = "imageId";
    public static final String IMAGEPATHHIGH = "localHighImagePath";
    public static final String IMAGEPATHLOW = "localLowImagePath";
    public static final String DATA = "data";
    public static final String FORMPKID = "formPKId";

    public static final String PAGES = "pages";

    // Upload other data
    public static final String FORM_DATA = "formData";
    public static final String RAPID_DATA = "rapidData";
    public static final String BREADCRUMBS_DATA = "breadcrumbs";
    public static final String WP_DATA = "workProfileData";
    public static final String CREW_DATA = "crewData";
    public static final String ISSUE_DATA = "issueData";

    public static final String DATE = "date";
    public static final String SENSOR_ID = "sensorid";
    public static final String SURFACE_TEMP = "surface_temp";

    public static final String METER_NAME = "meter_name";
    public static final String RH = "rh";
    public static final String TEMP = "temp";


    public static String AMB_RH = "amb_rh";
    public static String AMB_TEMP = "amb_temp";
    public static String HOLE_DEPTH = "hole_depth";
    public static String TARGET_RH = "target_rh";
    public static String DESC = "desc";

    //Broadcast
    public static String INTERNAL_IMAGE_BROADCAST = "ImageBroadCast";
    public static String INTERNAL_FORM_BROADCAST = "FormBroadcast";
    public static String INTERNAL_WP_BROADCAST = "WpBroadcast";

    public static final String INTERNAL_CURRENT_IMAGE_UPLOAD_PROGRESS = "imageCurrentUploadProgress";
    public static final String INTERNAL_PENDING_IMAGE_UPLOAD = "pendingImageUploadProgress";
    public static final String INTERNAL_DONE_IMAGE_UPLOAD = "imageUploadingDone";

    public static String UPDATED_FORM_LIST = "updatedFormList";
    public static String DELETED_FORM_LIST = "deletedFormList";
    public static String DELETED_FORM_ALL_DATA = "deletedFormAllData";
    public static String UPDATED_WP_LIST = "updatedWPList";
    public static String DELETED_WP_LIST = "deletedWPFormList";
    public static String DELETED_WP_FORM_ALL_DATA = "deletedWPFormAllData";

    public static String IS_FORM_COMPONENT = "isFormComponent";

    public static String TAG_ID = "tagId";

    public static String PREF_FORM_CHECKIN_DETAIL = "formCheckInDetails";

    public static String PREF_MD5_KEY_DATA = "md5KeyData";

    public static String IS_DEVELOPER_OPTION_ON = "isDeveloperOptionOn";
    public static String BASE_URL = "baseUrl";
    public static String IS_FLEET_VEHICLELIST = "isFleetVehicleList";
    public static String IS_FLEET_EQUIPMENTLIST = "isFleetEquipmentList";
    public static String SELECT_FROM_ALL_ITEMS = "selectFromAllItems";
    public static String DEVICE_PLATFORM = "Android";
    public static String FORM_SUBMISSION_ID = "formSubmissionID";

    public static enum CrewSelectionStatus {
        NormalCrew, SelectedCrew
    }

    public static String MAP_PIN_URL = "mapPinURL";
    public static String PIN_LABEL = "pinLabel";

    public static String PARAM_SUBMITTED_FOR = "formSubmittedFor";
    public static String FORM_SUBMITTED_BY = "formSubmittedBy";

    public static String AS_TASK = "AsTask";
    public static String AS_DETAIL = "AsDetail";
    public static String AS_TIMER = "AsTimer";
    public static double locationRadiusLimitToUpdateSiteData = 16; // radius in meters
    public static boolean shouldUpdateSiteDataInList = false;

}
