package com.sitefotos.service;

import static com.sitefotos.Constants.HRIMAGE;
import static com.sitefotos.Constants.IMAGEPATHHIGH;
import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.INTERNAL_CURRENT_IMAGE_UPLOAD_PROGRESS;
import static com.sitefotos.Constants.LRIMAGE;
import static com.sitefotos.Constants.PARAM_PHOTOS;
import static com.sitefotos.Constants.PARAM_PROFILEID;
import static com.sitefotos.Constants.PARAM_SITEID;
import static com.sitefotos.util.ImageUtil.deleteImagesFromStorage;
import static com.sitefotos.util.StaticUtils.concatImageDataWithTMFormData;

import android.app.Service;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Bitmap;
import android.net.ConnectivityManager;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.NetWorkChangeReceiver;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.ProgressRequestBody;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.models.FormData;
import com.sitefotos.models.SimpleResponse;
import com.sitefotos.models.UploadImageData;
import com.sitefotos.models.UploadImageUrlResponse;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.models.WorkLogProfileData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblSiteProfileData;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.RejectedExecutionException;

import okhttp3.ResponseBody;
import retrofit2.Call;
import retrofit2.Response;

public class FileUploaderService extends Service {
    NetWorkChangeReceiver netWorkChangeReceiver;
    UploadImageData uploadImageData;
    UploadOtherData uploadOtherData;

    BroadcastReceiver imageUploadStart = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    case Constants.IMAGE_UPLOAD_SERVICE_BROADCAST:
                        startImageUpload();
                        break;
                    case Constants.UPLOAD_OTHER_DATA_BROADCAST:
                        startOtherDataUpload();
                        break;
                    case Constants.UPLOAD_BOTH_DATA_BROADCAST:
                        startImageUpload();
                        startOtherDataUpload();
                        break;

                    case Constants.IMAGE_UPLOAD_SYNC_BROADCAST:
                        //Call this to sync progress with ui when Uploading activity launches
                        if (uploadImageData != null && (uploadImageData.isLowImageUploadProcessStarted() || uploadImageData.isHighImageUploadProcessStarted())) {
                            int progress = (int) (uploadImageData.getLowImageProgress() + uploadImageData.getHighImageProgress());
                            sendBroadCastForCurrentProgress(uploadImageData.getUploadId(), progress, uploadImageData.isUploadOriginalSize(), true);
                        }

                        //Call this to sync progress with ui when Uploading activity launches
                        if (uploadOtherData != null && uploadOtherData.isUploadProcessStart()) {
                            sendBroadCastForOtherDataUploadingStart(uploadOtherData.getId());
                        }
                        break;
                }
            }
        }
    };

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        throw new UnsupportedOperationException("Not yet implemented");
    }

    @Override
    public void onCreate() {
        super.onCreate();
        registerConnectionReceiver();
        registerUploadingDataReceiver();
        startImageUpload();
        startOtherDataUpload();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return START_STICKY;
    }

    /**
     * Method to create Connection change receiver and register with service
     */
    private void registerConnectionReceiver() {
        netWorkChangeReceiver = new NetWorkChangeReceiver();
        IntentFilter filter = new IntentFilter();
        filter.addAction(ConnectivityManager.CONNECTIVITY_ACTION);
        registerAppReceiver(netWorkChangeReceiver, filter);
    }

    /**
     * Method to create local broadcast receiver filters for image and other data uploading stuff and register with service
     */
    private void registerUploadingDataReceiver() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.IMAGE_UPLOAD_SERVICE_BROADCAST);
        intentFilter.addAction(Constants.UPLOAD_OTHER_DATA_BROADCAST);
        intentFilter.addAction(Constants.UPLOAD_BOTH_DATA_BROADCAST);
        intentFilter.addAction(Constants.IMAGE_UPLOAD_SYNC_BROADCAST);
        registerAppReceiver(imageUploadStart, intentFilter);
    }

    private void startImageUpload() {
        if (getApplicationContext() == null || !BaseApplication.getInstance().isInternetAvailable() || !PermissionUtils.hasStoragePermissions(BaseApplication.getInstance())) {
            sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_START_EVENT);
            return;
        }
        if (isImageUploadingDone()) {
            dataUploadingDone();
            return;
        }

        sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_START_EVENT);
        BaseApplication.getInstance().reInitiateImageExecutorServiceIfShutDown();
        BaseApplication.getInstance().imageExecutorService.submit(this::checkAndUploadImageDatInSafeThread);
    }

    private void checkAndUploadImageDatInSafeThread() {
        try {
            TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
            if (uploadImageData == null) {
                //NOTE: There is no item in uploading so fetch one from the db.
                uploadImageData = tblUploadImage.getNextUploadData();
            }
            if (uploadImageData == null) {
                return;
            }

            if (uploadImageData.getRetryCount() >= 4) {
                //NOTE: This item already exceeded retry count. move it to last and start next one.  updateTimeWhenFailed method will update DB for time and retry count.
                //NOTE: To manage stuck uploads. and loop running infinitely put a logic here if some item fails too many times then don't upload it again. may be. to be discussed
                if (!uploadImageData.isLowImageUploadProcessStarted() && !uploadImageData.isHighImageUploadProcessStarted()) {
                    uploadImageData.setRetryCount(0);
                    tblUploadImage.updateTimeWhenFailed(uploadImageData.getUploadId(), System.currentTimeMillis());
                    uploadImageData = null;
                    startImageUpload();
                }
            } else {
                boolean isUploaded = uploadImageData.isUploadOriginalSize() ? (uploadImageData.isLowImageUploaded() && uploadImageData.isHighImageUploaded()) : uploadImageData.isLowImageUploaded();
                if (isUploaded) {
                    if (!uploadImageData.isImageUploadProcessStart()) {
                        submitUploadedImageDataAfterCompletion();
                    }
                } else {
                    if (uploadImageData != null && !uploadImageData.isLowImageUploaded() && !uploadImageData.isLowImageUploadProcessStarted()) {
                        uploadImageData.setLowImageUploadProcessStarted(true);
                        getImageUploadURL(uploadImageData, true);
                    }
                    if (uploadImageData != null && uploadImageData.isUploadOriginalSize() && !uploadImageData.isHighImageUploaded() && !uploadImageData.isHighImageUploadProcessStarted()) {
                        uploadImageData.setHighImageUploadProcessStarted(true);
                        getImageUploadURL(uploadImageData, false);
                    }
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            CustomLogKt.errorLog("Service", "ImageDataInSafeThread:Thread UnLock:::ThreadID::" + Thread.currentThread().getId());
        }
    }

    private boolean isUploadURLValid(String uploadFullUrl) {
        if (TextUtils.isEmpty(uploadFullUrl)) {
            return false;
        }

        Uri uri = Uri.parse(uploadFullUrl);
        String expirationTimeString = null;
        try {
            expirationTimeString = uri.getQueryParameter("temp_url_expires");
        } catch (UnsupportedOperationException e) {
            FirebaseEventUtils.logException(e);
        }
        // if expirationTimeString is null means value of query parameter not exists
        if (TextUtils.isEmpty(expirationTimeString))
            return false;

        long expiredTimeStamp = Long.parseLong(expirationTimeString);
        //Expiration time is 24 hours after requested URL. here we check current time with expiration time.
        return expiredTimeStamp > (System.currentTimeMillis() / 1000);
    }

    // uploading done callback event
    private void dataUploadingDone() {
        sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_DONE_EVENT);
    }

    private void checkFailResponseForUploadImage(UploadImageData uploadImageData, Boolean isLowResolution, int responseCode) {
        if (responseCode > 300) {
            if (responseCode == 401) {
                BaseApplication.getInstance().logoutUser(true);
                return;
            }
            if (responseCode != 304) {
                try {
                    new Handler().postDelayed(() -> failedUploadingData(uploadImageData, isLowResolution), 10000);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                    failedUploadingData(uploadImageData, isLowResolution);
                }
            }
        } else {
            failedUploadingData(uploadImageData, isLowResolution);
        }
    }

    private void failedUploadingData(UploadImageData uploadImageData, Boolean isLowResolution) {
        if (isLowResolution != null) {
            if (isLowResolution) {
                uploadImageData.setLowImageUploadProcessStarted(false);
                uploadImageData.setLowImageProgress(0);
            } else {
                uploadImageData.setHighImageUploadProcessStarted(false);
                uploadImageData.setHighImageProgress(0);
            }
            sendBroadCastForPendingImage(uploadImageData.getUploadId(), isLowResolution);
        } else {
            uploadImageData.setImageUploadProcessStart(false);
            sendBroadCastForPendingImage(uploadImageData.getUploadId(), true);
        }
        uploadImageData.setRetryCount(uploadImageData.getRetryCount() + 1);
        TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
        tblUploadImage.updateRetryCount(uploadImageData.getUploadId(), uploadImageData.getRetryCount());
        startImageUpload();
    }

    // Get Image upload URL here.
    public void getImageUploadURL(UploadImageData uploadImageData, boolean isLowResolution) {

        int progress = (int) (uploadImageData.getLowImageProgress() + uploadImageData.getHighImageProgress());
        sendBroadCastForCurrentProgress(uploadImageData.getUploadId(), progress, uploadImageData.isUploadOriginalSize(), isLowResolution);

        String currentURL = isLowResolution ? uploadImageData.getUploadUrlLow() : uploadImageData.getUploadUrlHigh();
        if (isUploadURLValid(currentURL)) {
            // Current Upload url is still a live, start uploading
            checkFileExistBeforeStartUploading(uploadImageData, isLowResolution);
            return;
        }
        //NOTE: Don't pollute db with unnecessary calls.
        if (isLowResolution && !TextUtils.isEmpty(uploadImageData.getLrImage())) {
            uploadImageData.setLrImage("");
            uploadImageData.setUploadUrlLow("");
            TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
            tblUploadImage.updateLowImageURLs(uploadImageData);
        }
        if (!isLowResolution && !TextUtils.isEmpty(uploadImageData.getHrImage())) {
            uploadImageData.setHrImage("");
            uploadImageData.setUploadUrlHigh("");
            TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
            tblUploadImage.updateHighImageURLs(uploadImageData);
        }

        ApiInterface apiService = RetrofitProvider.createService(ApiInterface.class);
        Call<UploadImageUrlResponse> call = apiService.requestServerForGetUploadURL(AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, ""));
        try {
            Response<UploadImageUrlResponse> response = call.execute();
            UploadImageUrlResponse apiResponse = response.body();
            if (apiResponse != null && response.isSuccessful()) {
                handleImageUploadUrlResponse(uploadImageData, apiResponse, isLowResolution);
            } else {
                checkFailResponseForUploadImage(uploadImageData, isLowResolution, response.code());
            }
        } catch (IOException e) {
            checkFailResponseForUploadImage(uploadImageData, isLowResolution, 0);
        }
    }

    // {"uploadUrl":"https://storage101.dfw1.clouddrive.com/v1/MossoCloudFS_172d5ba5-e05d-4d29-b7d7-63e972ac2a04/demo_container_b42ceb5d5cb0/53e8c92ba4e3.jpg
    //?temp_url_sig=30833ae255793290640d6a60f497b5883f553090&temp_url_expires=1609898551",
    //"destination":"https://19b5bf454221a3009503-837ce6cc4941d82f7e9704a4735379b2.ssl.cf1.rackcdn.com/53e8c92ba4e3.jpg","status":0}
    private void handleImageUploadUrlResponse(UploadImageData uploadImageData, UploadImageUrlResponse response, boolean isLowResolution) {

        if (!TextUtils.isEmpty(response.getUploadUrl()) && !TextUtils.isEmpty(response.getDestination())) {
            TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
            if (isLowResolution) {
                //Store full response in setUploadUrlLow
                uploadImageData.setUploadUrlLow(response.getUploadUrl());
                uploadImageData.setLrImage(response.getDestination());
                tblUploadImage.updateLowImageURLs(uploadImageData);
            } else {
                //Store full response in setUploadUrlHigh
                uploadImageData.setUploadUrlHigh(response.getUploadUrl());
                uploadImageData.setHrImage(response.getDestination());
                tblUploadImage.updateHighImageURLs(uploadImageData);
            }
            // Upload URL get. Now uploading image to server.
            checkFileExistBeforeStartUploading(uploadImageData, isLowResolution);
        } else {
            // Error occurred.
            checkFailResponseForUploadImage(uploadImageData, isLowResolution, 0);
        }
    }

    private void checkFileExistBeforeStartUploading(UploadImageData uploadImageData, boolean isLowResolution) {
        //return operation if no storage permission
        if (!PermissionUtils.hasStoragePermissions(BaseApplication.getInstance())) {
            this.uploadOtherData = null;
            //If permission is not there for file reading then all operations must stop for uploading as we can not read any file.
            //            updateImageUploadStatus(uploadImageData, false);
            return;
        }
        File hqFile = new File(uploadImageData.getImagePathHigh() == null ? "" : uploadImageData.getImagePathHigh());
        File lqFile = new File(uploadImageData.getImagePathLow() == null ? "" : uploadImageData.getImagePathLow());
        //Remove entry if local image path is not exists
        boolean isFileExist = uploadImageData.isUploadOriginalSize() ? (hqFile.exists() || lqFile.exists()) : lqFile.exists();
        if (!isFileExist) {
            //Remove as both file does not exist.
            removeDataIfBothAreEmpty(uploadImageData);
            this.uploadImageData = null;
            startImageUpload();
            return;
        }
        if (hqFile.exists() && !lqFile.exists() && isLowResolution) {
            //Create low file from HQ as low does not exist.
            Bitmap bitmap = ImageUtil.getBitmapFromPath(StaticUtils.getRealPathFromURI(this, Uri.parse(uploadImageData.getImagePathHigh())));
            if (bitmap != null) {
                try {
                    Bitmap scaledBitmap = ImageUtil.createScaledBitmap(bitmap);
                    if (scaledBitmap != null) {
                        String imagePathLow = ImageUtil.saveImageFromBitmap(this, scaledBitmap, "", false);
                        TblUploadImage tblUploadImage = new TblUploadImage(this);
                        tblUploadImage.updateData(uploadImageData.getUploadId(), uploadImageData);
                        updateNewImageData(imagePathLow);
                        uploadImageData.setImagePathLow(imagePathLow);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        if (isLowResolution) {
            lqFile = new File(uploadImageData.getImagePathLow());
            if (lqFile.exists()) {
                uploadImageOnServer(uploadImageData, uploadImageData.getUploadUrlLow(), uploadImageData.getImagePathLow(), true);
            } else {
                this.uploadImageData = null;
            }
        } else {
            if (hqFile.exists()) {
                uploadImageOnServer(uploadImageData, uploadImageData.getUploadUrlHigh(), uploadImageData.getImagePathHigh(), false);
            } else {
                uploadImageData.setImagePathHigh("");
                uploadImageData.setUploadOriginalSize(false);
                TblUploadImage tblUploadImage = new TblUploadImage(this);
                tblUploadImage.changeOriginalDataStatus(uploadImageData.getUploadId(), false);
                if (!uploadImageData.isLowImageUploadProcessStarted() && uploadImageData.isLowImageUploaded()) {
                    checkBothFileUploadStatusAndSubmitData(uploadImageData);
                }
            }
        }
    }


    public void updateNewImageData(String newImagePath) {
        int formPKId = (int) uploadImageData.getFormPkId();
        int wpPKId = (int) uploadImageData.getWpPkId();
        int tmFormPKId = (int) uploadImageData.getTmFormPkId();
        if (formPKId > 0) {
            updateImageDataInForm(formPKId, newImagePath);
        } else if (wpPKId > 0) {
            updateImageDataInWPData(wpPKId, newImagePath);
        } else if (tmFormPKId > 0) {
            updateImageDataInTMForm(tmFormPKId, newImagePath);
        }
    }

    private void updateImageDataInForm(int formPKId, String newImagePath) {
        TblForms tblForms = new TblForms(getApplicationContext());
        FormData formData = tblForms.getFormDataByPKId(formPKId);
        String imagePathString = formData.getImageData();
        if (TextUtils.isEmpty(imagePathString))
            return;
        JSONObject imageData = updateNewImagePathInImageData(imagePathString, uploadImageData.getImagePathLow(), newImagePath);
        if (imageData != null)
            tblForms.updateImageDataByPkId(formPKId, imageData.toString());
    }

    private void updateImageDataInTMForm(int tmFormPKId, String newImagePath) {
        TblTMForms tblTMForms = new TblTMForms(getApplicationContext());
        FormData formData = tblTMForms.getFormDataByPKId(tmFormPKId);
        String imagePathString = formData.getImageData();
        if (TextUtils.isEmpty(imagePathString))
            return;
        JSONObject imageData = updateNewImagePathInImageData(imagePathString, uploadImageData.getImagePathLow(), newImagePath);
        if (imageData != null)
            tblTMForms.updateImageDataByPkId(tmFormPKId, imageData.toString());
    }


    private void updateImageDataInWPData(int wpPKId, String newImagePath) {
        WorkLogProfileData workLogProfileData;
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(getApplicationContext());
        workLogProfileData = tblSiteProfileData.getDataFromPKId(wpPKId);
        String imagePathString = workLogProfileData.getImageData();
        if (TextUtils.isEmpty(imagePathString))
            return;
        JSONObject imageData = updateNewImagePathInImageData(imagePathString, uploadImageData.getImagePathLow(), newImagePath);
        if (imageData != null)
            tblSiteProfileData.updateImageDataByPkId(wpPKId, imageData.toString());
    }

    public JSONObject updateNewImagePathInImageData(String imageString, String oldPath, String newLowImagePath) {
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(imageString);
            for (int i = 0; i < jsonObject.getJSONArray(Constants.DATA).length(); i++) {
                JSONObject jsonObjectInner = jsonObject.getJSONArray(Constants.DATA).getJSONObject(i);
                String lowPath = jsonObjectInner.getString(IMAGEPATHLOW);
                if (oldPath.equalsIgnoreCase(lowPath)) {
                    jsonObjectInner.put(IMAGEPATHLOW, newLowImagePath);
                    break;
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return jsonObject;
    }

    private void removeDataIfBothAreEmpty(UploadImageData uploadImageData) {
        TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
        int formPKId = (int) uploadImageData.getFormPkId();
        int wpPKId = (int) uploadImageData.getWpPkId();
        int tmFormPKId = (int) uploadImageData.getTmFormPkId();
        int tagId = uploadImageData.getTagId();
        if (formPKId > 0) {
            removeDataFromFormTableIfFileNotFound(formPKId, tagId);
        } else if (wpPKId > 0) {
            removeDataFromWPTableIfFileNotFound(wpPKId);
        } else if (tmFormPKId > 0) {
            removeDataFromTMFormTableIfFileNotFound(tmFormPKId, tagId);
        }
        if (!TextUtils.isEmpty(uploadImageData.getUuid())) {
            FirebaseEventUtils.NotFoundFilesEvent(getApplicationContext(), uploadImageData.getUuid());
        }
        tblUploadImage.deleteDataByUploadId(uploadImageData.getUploadId());
        dataUploadingDone();
    }

    public void removeDataFromFormTableIfFileNotFound(int formPKId, int tagId) {
        TblForms tblForms = new TblForms(getApplicationContext());
        FormData formData = tblForms.getFormDataByPKId(formPKId);
        String imagePathString = formData.getImageData();
        if (TextUtils.isEmpty(imagePathString)) {
            return;
        }
        updateFormPendingCount(formPKId);
        updateFormPendingIssueCount(formPKId, tagId);

        if (StaticUtils.getFormPendingIssueCount(formPKId, tagId) == 0) {
            afterIssueImageUploaded(formPKId, tagId, tblForms.getFormDataByPKId(formPKId), false);
        }

        if (StaticUtils.getPendingCount(formPKId) == 0) {
            concatImageDataAndSubmitForm(formPKId, "", "");
        }
    }

    public void removeDataFromWPTableIfFileNotFound(int wpPKId) {
        WorkLogProfileData workLogProfileData;
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(getApplicationContext());
        workLogProfileData = tblSiteProfileData.getDataFromPKId(wpPKId);
        String imagePathString = workLogProfileData.getImageData();
        if (TextUtils.isEmpty(imagePathString)) {
            return;
        }
        updateWPPhotoPendingCount(wpPKId);

        if (StaticUtils.getWPPhotoPendingCount(wpPKId) == 0) {
            concatImageDataAndSubmitForm(wpPKId, "", "");
        }
    }

    public void removeDataFromTMFormTableIfFileNotFound(int tmFormPkId, int tagId) {
        TblTMForms tblTNMForms = new TblTMForms(getApplicationContext());
        FormData formData = tblTNMForms.getFormDataByPKId(tmFormPkId);
        String imagePathString = formData.getImageData();
        if (TextUtils.isEmpty(imagePathString)) {
            return;
        }
        updateTmFormPendingCount(tmFormPkId);
        updateTmFormPendingIssueCount(tmFormPkId, tagId);
        if (StaticUtils.getTMFormPendingIssueCount(tmFormPkId, uploadImageData.getTagId()) == 0) {
            afterIssueImageUploaded(tmFormPkId, uploadImageData.getTagId(), tblTNMForms.getFormDataByPKId(tmFormPkId), true);
        }

        if (StaticUtils.getTMFormPendingCount(tmFormPkId) == 0) {
            concatImageDataAndSubmitTMForm(tmFormPkId, "", "");
        }

    }


    private void updateFormPendingIssueCount(int formPkId, int tagId) {
        if (tagId > 0) {
            if (StaticUtils.getFormPendingIssueCount(formPkId, uploadImageData.getTagId()) > 0)
                StaticUtils.setFormPendingIssueCount(formPkId, uploadImageData.getTagId(), StaticUtils.getFormPendingIssueCount(formPkId, uploadImageData.getTagId()) - 1);

        }
    }

    private void updateTmFormPendingIssueCount(int tmFormPkId, int tagId) {
        if (tagId > 0) {
            if (StaticUtils.getTMFormPendingIssueCount(tmFormPkId, tagId) > 0)
                StaticUtils.setTMFormPendingIssueCount(tmFormPkId, tagId, StaticUtils.getTMFormPendingIssueCount(tmFormPkId, tagId) - 1);
        }
    }

    private void updateWPPhotoPendingCount(int wpPKId) {
        if (StaticUtils.getWPPhotoPendingCount(wpPKId) > 0)
            StaticUtils.setWPPhotoPendingCount(wpPKId, StaticUtils.getWPPhotoPendingCount(wpPKId) - 1);
    }

    private void updateFormPendingCount(int formPKId) {
        if (StaticUtils.getPendingCount(formPKId) > 0) {
            StaticUtils.setPendingCount(formPKId, StaticUtils.getPendingCount(formPKId) - 1);
        }
    }


    private void updateTmFormPendingCount(int formPKId) {
        if (StaticUtils.getTMFormPendingCount(formPKId) > 0) {
            StaticUtils.setTMFormPendingCount(formPKId, StaticUtils.getTMFormPendingCount(formPKId) - 1);
        }
    }


    /**
     * Upload image on rack space server
     *
     * @param strUploadURL strUploadURL
     * @param imagePath    imagePath
     */
    //https://storage101.dfw1.clouddrive.com/v1/MossoCloudFS_172d5ba5-e05d-4d29-b7d7-63e972ac2a04/demo_container_b42ceb5d5cb0/262481429dcb.jpg?temp_url_sig=40e78c4c9b773529c6caf4b5a972f875173ef2ae&temp_url_expires=1564970809
    private void uploadImageOnServer(UploadImageData uploadImageData, final String strUploadURL,
                                     final String imagePath, final boolean isLowResolution) {
        //NOTE: Possibly set request time out to infinite. previous values were not good. i was getting time out for high images. and for request at other places too in uploading.
        //new UploadImageAsync(imagePath, strUploadURL, isLowResolution).execute();
        File imageFile = new File(imagePath);
        ProgressRequestBody fileBody = new ProgressRequestBody(imageFile, new ProgressRequestBody.UploadCallbacks() {
            @Override
            public void onProgressUpdate(int percentage) {
                try {
                    if (isLowResolution) {
                        if (uploadImageData.getLowImageProgress() + 10 < percentage) {
                            uploadImageData.setLowImageProgress(percentage);
                            int progress = (int) (uploadImageData.getLowImageProgress() + uploadImageData.getHighImageProgress());
                            sendBroadCastForCurrentProgress(uploadImageData.getUploadId(), progress, uploadImageData.isUploadOriginalSize(), true);
                        }
                    } else {
                        if (uploadImageData.getHighImageProgress() + 10 < percentage) {
                            uploadImageData.setHighImageProgress(percentage);
                            int progress = (int) (uploadImageData.getLowImageProgress() + uploadImageData.getHighImageProgress());
                            sendBroadCastForCurrentProgress(uploadImageData.getUploadId(), progress, uploadImageData.isUploadOriginalSize(), false);
                        }
                    }
                } catch (Exception ex) {
                    FirebaseEventUtils.logException(ex);
                }
            }
        });
        ApiInterface apiService = RetrofitProvider.createServiceDynamic(ApiInterface.class);
        Call<ResponseBody> call = apiService.uploadImage(strUploadURL, fileBody);

        try {
            Response<ResponseBody> response = call.execute();
            if (!response.isSuccessful()) {
                checkFailResponseForUploadImage(uploadImageData, isLowResolution, response.code());
                return;
            }
            //  CustomLogKt.errorLog("Result", "onResponse::" + response.code() + "::::" + isLowResolution);
            TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
            if (isLowResolution) {
                uploadImageData.setLowImageUploadProcessStarted(false);
                uploadImageData.setLowImageUploaded(true);
                tblUploadImage.updateLowImageUploaded(uploadImageData.getUploadId());
            } else {
                uploadImageData.setHighImageUploadProcessStarted(false);
                uploadImageData.setHighImageUploaded(true);
                tblUploadImage.updateHighImageUploaded(uploadImageData.getUploadId());
            }
            checkBothFileUploadStatusAndSubmitData(uploadImageData);
        } catch (IOException e) {
            checkFailResponseForUploadImage(uploadImageData, isLowResolution, 0);
        }
    }

    private void sendBroadCastForCurrentProgress(int uploadId, int progress,
                                                 boolean isUploadOriginalSize, boolean isLowResolution) {
        Intent intent = new Intent(INTERNAL_CURRENT_IMAGE_UPLOAD_PROGRESS);
        intent.putExtra("uploadId", uploadId);
        intent.putExtra("progress", progress);
        intent.putExtra("isUploadOriginalSize", isUploadOriginalSize);
        intent.putExtra("isLowResolution", isLowResolution);
        sendBroadcast(intent);
    }

    private void sendBroadCastForPendingImage(int uploadId, boolean isLowResolution) {
        Intent intent = new Intent(Constants.INTERNAL_PENDING_IMAGE_UPLOAD);
        intent.putExtra("uploadId", uploadId);
        intent.putExtra("isLowResolution", isLowResolution);
        sendBroadcast(intent);
    }

    private void checkBothFileUploadStatusAndSubmitData(UploadImageData uploadImageData) {
        if (uploadImageData.isUploadOriginalSize()) {
            if (uploadImageData.isHighImageUploaded() && uploadImageData.isLowImageUploaded()) {
                submitUploadedImageDataAfterCompletion();
            }
        } else if (uploadImageData.isLowImageUploaded()) {
            submitUploadedImageDataAfterCompletion();
        }
    }


    // Get Image upload URL here.
    public void submitUploadedImageDataAfterCompletion() {
        if (!BaseApplication.getInstance().isInternetAvailable()) return;
        if (uploadImageData == null) return;
        uploadImageData.setImageUploadProcessStart(true);
        // Prepare form of post request parameters
        if (!uploadImageData.isFormSign()) {
            HashMap<String, Object> params = new HashMap<>();
            params.put("accessCode", AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, ""));
            if (uploadImageData.getLrImage() == null)
                uploadImageData.setLrImage("");
            params.put("lrimg", uploadImageData.getLrImage());
            if (uploadImageData.getHrImage() == null)
                uploadImageData.setHrImage(null);
            params.put("hrimg", uploadImageData.getHrImage());
            if (uploadImageData.getDescription() == null)
                uploadImageData.setDescription(null);
            params.put("desc", uploadImageData.getDescription());
            params.put("lat", String.valueOf(uploadImageData.getLatitude()).replaceAll(",", "."));
            params.put("lon", String.valueOf(uploadImageData.getLongitude()).replaceAll(",", "."));
            if (uploadImageData.getDate() == null) {
                uploadImageData.setDate(null);
            }
            params.put("dt", uploadImageData.getDate());
            params.put("dtunix", uploadImageData.getTimeInUnix());
            if (uploadImageData.getBuilding() == null) {
                uploadImageData.setBuilding("0");
            }
            params.put("buil", uploadImageData.getBuilding());
            if (!TextUtils.isEmpty(uploadImageData.getTags())) {
                String[] lstData = StaticUtils.getStringArrayFromString(uploadImageData.getTags());
                for (int i = 0; i < lstData.length; i++) {
                    params.put("tags[" + i + "]", lstData[i].trim());
                }
            }
            params.put("siteId", uploadImageData.getWpId());
            params.put("profileid", uploadImageData.getFormId());
            if (TextUtils.isEmpty(uploadImageData.getAppVersion())){
                params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            }else {
                params.put(Constants.PARAM_APP_VERSION, uploadImageData.getAppVersion());
            }
            //Send key with value only if image is captured from app version 4.5.10 or image is not signature or sketch
            if (uploadImageData.getCameraImage() ==1){
                params.put("isFromCamera", true);
            }else if (uploadImageData.getCameraImage() ==2){
                params.put("isFromCamera", false);
            }
            StaticUtils.addCommonData(params);
            if (!TextUtils.isEmpty(uploadImageData.getUuid()))
                params.put(Constants.PARAM_UUID, uploadImageData.getUuid());
            try {
                params.put(Constants.EXTRA_LOCATION_LATITUDE, uploadImageData.getExtraLatitude());
                params.put(Constants.EXTRA_LOCATION_LONGITUDE, uploadImageData.getExtraLongitude());
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

            if (uploadImageData.getFormId() > 0) {
                params.put(PARAM_SITEID, String.valueOf(uploadImageData.getWpId()));
                params.put(PARAM_PROFILEID, String.valueOf(uploadImageData.getFormId()));
            }
            params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
            ApiInterface apiService = RetrofitProvider.createServiceString(ApiInterface.class);
            Call<String> call = apiService.requestToPostImageUploadData(params);
            call.enqueue(new retrofit2.Callback<String>() {
                @Override
                public void onResponse(Call<String> call, retrofit2.Response<String> response) {
                    try {
                        BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                        BaseApplication.getInstance().dataExecutorService.submit(() -> manageImageUploadDataResponseWithSafeThread(response));
                    } catch (RejectedExecutionException e) {
                        FirebaseEventUtils.logException(e);
                    }

                }

                @Override
                public void onFailure(Call<String> call, Throwable t) {
                    try {
                        BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                        BaseApplication.getInstance().dataExecutorService.submit(() -> checkFailResponseForUploadImage(uploadImageData, null, 0));
                    } catch (
                            RejectedExecutionException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            });
        } else {
            doAfterImageUploadCompletion();
        }

    }

    private void manageImageUploadDataResponseWithSafeThread(retrofit2.Response<String> response) {
        if (response.isSuccessful()) {
            doAfterImageUploadCompletion();
        } else {
            checkFailResponseForUploadImage(uploadImageData, null, response.code());
        }
    }

    private void doAfterImageUploadCompletion() {
        TblUploadImage tblUploadImage = new TblUploadImage(getApplicationContext());
        if (uploadImageData == null) return;
        int formPKId = (int) uploadImageData.getFormPkId();
        int wpPKId = (int) uploadImageData.getWpPkId();
        int tmFormPKId = (int) uploadImageData.getTmFormPkId();

        if (formPKId > 0) {
            updateImagePathInFormTable(formPKId);
        } else if (tmFormPKId > 0) {
            updateImagePathInTMFormTable(tmFormPKId);
        } else if (wpPKId > 0) {
            updateImagePathInWPTable(wpPKId);
        } else {
            String imageUrlLow = uploadImageData.getImagePathLow();
            String imageUrlHigh = uploadImageData.getImagePathHigh();
            deleteImageFromSDCard(imageUrlHigh, imageUrlLow);
        }
        tblUploadImage.deleteDataByUploadId(uploadImageData.getUploadId());
        uploadImageData.setImageUploadProcessStart(false);
        this.uploadImageData = null;
        dataUploadingDone();
        startImageUpload();

        if (isImageUploadingDone()) {
            startOtherDataUpload();
        }
    }

    public void deleteImageFromSDCard(String imageUrlHigh, String imageUrlLow) {
        ImageUtil.deleteImageFromSDCard(imageUrlHigh);
        ImageUtil.deleteImageFromSDCard(imageUrlLow);
    }

    private void updateImagePathInWPTable(int wpPKId) {

        WorkLogProfileData workLogProfileData;
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(getApplicationContext());

        workLogProfileData = tblSiteProfileData.getDataFromPKId(wpPKId);
        String imagePathString = workLogProfileData.getImageData();

        if (TextUtils.isEmpty(imagePathString)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(imagePathString);
            for (int i = 0; i < jsonObject.getJSONArray(Constants.DATA).length(); i++) {
                JSONObject jsonObjectInner = jsonObject.getJSONArray(Constants.DATA).getJSONObject(i);
                if ((!TextUtils.isEmpty(uploadImageData.getImagePathLow()) && uploadImageData.getImagePathLow().equalsIgnoreCase(jsonObjectInner.getString(IMAGEPATHLOW)))
                        || (!TextUtils.isEmpty(uploadImageData.getImagePathHigh()) && uploadImageData.getImagePathHigh().equalsIgnoreCase(jsonObjectInner.getString(IMAGEPATHHIGH)))) {
                    jsonObjectInner.put(Constants.HRIMAGE, uploadImageData.getHrImage() == null ? "" : uploadImageData.getHrImage());
                    jsonObjectInner.put(Constants.LRIMAGE, uploadImageData.getLrImage() == null ? "" : uploadImageData.getLrImage());
                    jsonObjectInner.put(Constants.IMAGE_UUID, uploadImageData.getUuid());
                    addPinDataWithImageData(jsonObjectInner);
                }
            }
            updateWPPhotoPendingCount(wpPKId);
            tblSiteProfileData.updateImageDataByPkId(wpPKId, jsonObject.toString());

            if (StaticUtils.getWPPhotoPendingCount(wpPKId) == 0) {
                concatImageDataAndSubmitWP(wpPKId, uploadImageData.getImagePathLow(), uploadImageData.getImagePathHigh());
            }

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void updateImagePathInFormTable(int formPKId) {
        FormData formData;
        TblForms tblForms = new TblForms(getApplicationContext());
        formData = tblForms.getFormDataByPKId(formPKId);
        String imagePathString = formData.getImageData();
        if (TextUtils.isEmpty(imagePathString)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(imagePathString);

            for (int i = 0; i < jsonObject.getJSONArray(Constants.DATA).length(); i++) {
                JSONObject jsonObjectInner = jsonObject.getJSONArray(Constants.DATA).getJSONObject(i);
                if ((!TextUtils.isEmpty(uploadImageData.getImagePathLow()) && uploadImageData.getImagePathLow().equalsIgnoreCase(jsonObjectInner.getString(IMAGEPATHLOW)))
                        || (!TextUtils.isEmpty(uploadImageData.getImagePathHigh()) && uploadImageData.getImagePathHigh().equalsIgnoreCase(jsonObjectInner.getString(IMAGEPATHHIGH)))) {
                    jsonObjectInner.put(Constants.HRIMAGE, uploadImageData.getHrImage() == null ? "" : uploadImageData.getHrImage());
                    jsonObjectInner.put(Constants.LRIMAGE, uploadImageData.getLrImage() == null ? "" : uploadImageData.getLrImage());
                    jsonObjectInner.put(Constants.IMAGE_UUID, uploadImageData.getUuid());
                }
            }

            updateFormPendingCount(formPKId);
            tblForms.updateImageDataByPkId(formPKId, jsonObject.toString());
            if (uploadImageData.getTagId() > 0) {
                updateFormPendingIssueCount(formPKId, uploadImageData.getTagId());
                if (StaticUtils.getFormPendingIssueCount(formPKId, uploadImageData.getTagId()) == 0) {
                    afterIssueImageUploaded(formPKId, uploadImageData.getTagId(), tblForms.getFormDataByPKId(formPKId), false);
                }
            }
            if (StaticUtils.getPendingCount(formPKId) == 0) {
                concatImageDataAndSubmitForm(formPKId, uploadImageData.getImagePathLow(), uploadImageData.getImagePathHigh());
            }

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void updateImagePathInTMFormTable(int tmFormPKId) {
        FormData formData;
        TblTMForms tblTNMForms = new TblTMForms(getApplicationContext());
        formData = tblTNMForms.getFormDataByPKId(tmFormPKId);
        String imagePathString = formData.getImageData();
        if (TextUtils.isEmpty(imagePathString)) {
            return;
        }
        try {
            JSONObject jsonObject = new JSONObject(imagePathString);
            for (int i = 0; i < jsonObject.getJSONArray(Constants.DATA).length(); i++) {
                JSONObject jsonObjectInner = jsonObject.getJSONArray(Constants.DATA).getJSONObject(i);
                if ((!TextUtils.isEmpty(uploadImageData.getImagePathLow()) && uploadImageData.getImagePathLow().equalsIgnoreCase(jsonObjectInner.getString(IMAGEPATHLOW)))
                        || (!TextUtils.isEmpty(uploadImageData.getImagePathHigh()) && uploadImageData.getImagePathHigh().equalsIgnoreCase(jsonObjectInner.getString(IMAGEPATHHIGH)))) {
                    jsonObjectInner.put(Constants.HRIMAGE, uploadImageData.getHrImage() == null ? "" : uploadImageData.getHrImage());
                    jsonObjectInner.put(Constants.LRIMAGE, uploadImageData.getLrImage() == null ? "" : uploadImageData.getLrImage());
                    jsonObjectInner.put(Constants.IMAGE_UUID, uploadImageData.getUuid());
                    addPinDataWithImageData(jsonObjectInner);
                }
            }
            updateTmFormPendingCount(tmFormPKId);

            tblTNMForms.updateImageDataByPkId(tmFormPKId, jsonObject.toString());
            if (uploadImageData.getTagId() > 0) {
                updateTmFormPendingIssueCount(tmFormPKId, uploadImageData.getTagId());
                if (StaticUtils.getTMFormPendingIssueCount(tmFormPKId, uploadImageData.getTagId()) == 0) {
                    afterIssueImageUploaded(tmFormPKId, uploadImageData.getTagId(), tblTNMForms.getFormDataByPKId(tmFormPKId), true);
                }
            }

            if (StaticUtils.getTMFormPendingCount(tmFormPKId) == 0) {
                concatImageDataAndSubmitTMForm(tmFormPKId, uploadImageData.getImagePathLow(), uploadImageData.getImagePathHigh());
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private static void addPinDataWithImageData(JSONObject jsonObjectInner) {
        try {
            if (jsonObjectInner.has(Constants.MAP_PIN_URL)) {
                if (jsonObjectInner.has(Constants.PIN_LABEL)) {
                    jsonObjectInner.put(Constants.PIN_LABEL, jsonObjectInner.getString(Constants.PIN_LABEL));
                } else {
                    jsonObjectInner.put(Constants.MAP_PIN_URL, jsonObjectInner.getString(Constants.MAP_PIN_URL));
                }
            }
            if (jsonObjectInner.has(Constants.PARAM_LAT))
                jsonObjectInner.put(Constants.PARAM_LAT, jsonObjectInner.getDouble(Constants.PARAM_LAT));
            if (jsonObjectInner.has(Constants.PARAM_LON))
                jsonObjectInner.put(Constants.PARAM_LON, jsonObjectInner.getDouble(Constants.PARAM_LON));
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void afterIssueImageUploaded(int formPKId, int tagId, FormData formData,
                                         boolean isTMForm) {
        String imagePathString = formData.getImageData();
        TblUploadData tblUploadData = new TblUploadData(getApplicationContext());
        UploadOtherData uploadOtherData = tblUploadData.getUploadDataByFormId(formPKId, tagId, isTMForm);
        //Here we will get data id greater than 0 if Issue is submitted else will get 0.
        if (uploadOtherData.getId() > 0) {
            String requestedData = uploadOtherData.getRequestedData();
            String requestedDataString = AddImageUrlAndUpdateIssueData(requestedData, imagePathString);
            tblUploadData.updateRequestedIssueData(formPKId, tagId, requestedDataString, isTMForm);
            tblUploadData.updateImageStatusByFormPKID(formPKId, tagId, true, isTMForm);
            startOtherDataUpload();
        }
    }

    private String AddImageUrlAndUpdateIssueData(String requestedData, String imagePathString) {
        JSONObject mainData = null;
        try {
            mainData = new JSONObject(requestedData);
            JSONObject issueDataObject = mainData.getJSONObject(Constants.PARAM_ISSUE_DATA);
            JSONArray issueDataArray = issueDataObject.getJSONArray(Constants.PARAM_ISSUE);

            for (int i = 0; i < issueDataArray.length(); i++) {
                JSONObject issueJSONData = issueDataArray.getJSONObject(i);
                JSONArray photoJson = issueJSONData.getJSONArray(PARAM_PHOTOS);
                for (int j = 0; j < photoJson.length(); j++) {
                    JSONObject photoObject = photoJson.getJSONObject(j);
                    JSONObject lrhrData = StaticUtils.getHrAndLrFromImageData(imagePathString, photoObject.getString(IMAGEPATHLOW));
                    String lrImage = "";
                    String hrImage = "";

                    if (lrhrData != null) {
                        lrImage = lrhrData.getString(Constants.LRIMAGE);
                        hrImage = lrhrData.getString(Constants.HRIMAGE);
                    }
                    photoObject.put(LRIMAGE, lrImage);
                    photoObject.put(HRIMAGE, hrImage);
                    photoObject.put(Constants.IMAGE_UUID, uploadImageData.getUuid());

                    if (photoObject.has(IMAGEPATHLOW)) {
                        photoObject.remove(IMAGEPATHLOW);
                    }

                    if (photoObject.has(IMAGEPATHHIGH)) {
                        photoObject.remove(IMAGEPATHHIGH);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return mainData != null ? mainData.toString() : null;
    }

    private void concatImageDataAndSubmitForm(int formPKId, String localLowPath, String localHighPath) {
        TblForms tblForms = new TblForms(getApplicationContext());
        FormData formData = tblForms.getFormDataByPKId(formPKId);
        String formString = formData.getModifiedFormData();

        JSONObject combinedModifiedDataJson = concatImageDataWithTMFormData(false, getApplicationContext(), formPKId, formString);
        if (combinedModifiedDataJson != null) {
            tblForms.updateModifiedFormByPkId(formData.getFormPKId(), combinedModifiedDataJson.toString());
        }
        checkForFormSubmission(formData.getFormPKId(), localHighPath, localLowPath);
        imageUpdatedImageBroadCast(formData.getFormPKId());
    }

    private void concatImageDataAndSubmitTMForm(int formPKId, String localLowPath, String localHighPath) {
        TblTMForms tblTMForms = new TblTMForms(getApplicationContext());
        FormData formData = tblTMForms.getFormDataByPKId(formPKId);
        String formString = formData.getModifiedFormData();
        JSONObject combinedModifiedDataJson = concatImageDataWithTMFormData(true, getApplicationContext(), formPKId, formString);
        if (combinedModifiedDataJson != null) {
            tblTMForms.updateModifiedFormByPkId(formData.getFormPKId(), combinedModifiedDataJson.toString());
        }
        checkForTMFormSubmission(formData.getFormPKId(), localHighPath, localLowPath);
        imageUpdatedImageBroadCast(formData.getFormPKId());
    }

    private void imageUpdatedImageBroadCast(int formPKId) {
        Intent intent = new Intent(Constants.INTERNAL_IMAGE_BROADCAST);
        intent.putExtra("formPkId", formPKId);
        sendBroadcast(intent);
    }

    private void concatImageDataAndSubmitWP(int wpPKId, String localLowPath, String
            localHighPath) {
        WorkLogProfileData workLogProfileData;
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(getApplicationContext());
        workLogProfileData = tblSiteProfileData.getDataFromPKId(wpPKId);
        checkForWPSubmission(workLogProfileData.getPkId(), localHighPath, localLowPath);
    }

    private void checkForFormSubmission(int formPKId, String localHighPath, String localLowPath) {
        TblForms tblForms = new TblForms(getApplicationContext());
        FormData formData = tblForms.getFormDataByPKId(formPKId);
        TblUploadData tblUploadData = new TblUploadData(this);
        if (formData.isFormSubmitted()) {
            UploadOtherData uploadOtherData = tblUploadData.getUploadDataByFormPKId(formPKId, false);
            if (uploadOtherData != null)
                tblUploadData.updateImageStatus(uploadOtherData.getId(), true);
            /*//If form is sub form then check if this is last one then prepare and upload main form in upload queue
            if (formData.isSubForm()) {
                //addFormDataInUploadQueue(formPKId, false);
            }*//* else if (formData.hasSubForm() && !isSubFormUploadingPending(formData.getSiteId(),formData.getFormId(), true)) {
                addFormDataInUploadQueue(formPKId, false);
            }*//* else if (!formData.hasSubForm()) {
                //addFormDataInUploadQueue(formPKId, false);
            }*/
            deleteImageFromSDCard(localHighPath, localLowPath);
        }

    }

    private void checkForTMFormSubmission(int formPKId, String localHighPath, String localLowPath) {
        TblTMForms tblForms = new TblTMForms(getApplicationContext());
        FormData formData = tblForms.getFormDataByPKId(formPKId);
        TblUploadData tblUploadData = new TblUploadData(this);
        if (formData.isFormSubmitted()) {
            UploadOtherData uploadOtherData = tblUploadData.getUploadDataByFormPKId(formPKId, true);
            if (uploadOtherData != null)
                tblUploadData.updateImageStatus(uploadOtherData.getId(), true);
            deleteImageFromSDCard(localHighPath, localLowPath);
        }
    }

    private void changeFormUploadedStatusInFormTable(int formPkId, int tmFormPkId) {
        //check for its mainForm and add it in upload table if this is last subform
        FormData formData;
        if (tmFormPkId > 0) {
            TblTMForms tblTMForms = new TblTMForms(this);
            formData = tblTMForms.getFormDataByPKId(tmFormPkId);
            if (formData.isSubForm()) {
                tblTMForms.updateFormUploaded(TblTMForms.TABLE_NAME, formData.getFormPKId());
            }
        } else {
            TblForms tblForms = new TblForms(this);
            formData = tblForms.getFormDataByPKId(formPkId);
            if (formData.isSubForm()) {
                tblForms.updateFormUploaded(TblForms.TABLE_NAME, formData.getFormPKId());
            }
        }

    }

    private void checkForWPSubmission(int wpPKId, String localHighPath, String localLowPath) {
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(getApplicationContext());
        WorkLogProfileData workLogProfileData = tblSiteProfileData.getDataFromPKId(wpPKId);

        if (workLogProfileData.isProfileSubmitted()) {
            addWpDataInUploadQueue(wpPKId);
            deleteImageFromSDCard(localHighPath, localLowPath);

        }
    }

    private void addWpDataInUploadQueue(int wpPKId) {

        TblUploadData tblUploadData = new TblUploadData(this);
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(this);

        WorkLogProfileData workLogProfileData = tblSiteProfileData.getDataFromPKId(wpPKId);
        UploadOtherData uploadOtherData = new UploadOtherData();

        long currentTime = System.currentTimeMillis();
        uploadOtherData.setDataType(Constants.WP_DATA);
        uploadOtherData.setCreatedAt(currentTime);
        uploadOtherData.setUpdatedAt(currentTime);
        uploadOtherData.setProcessStartTime(currentTime);
        uploadOtherData.setWpPKId(workLogProfileData.getPkId());
        WorkLogProfileData tempData = tblSiteProfileData.getDataFromPKId(workLogProfileData.getPkId());

        String data = tempData.getModifiedData();
        JSONObject dataJson = new JSONObject();
        try {
            dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            dataJson.put(Constants.PARAMS_WP_DATA, StaticUtils.getWPDataFromWorkLogProfile(workLogProfileData, null));
            dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
            dataJson.put(Constants.PARAM_LON, workLogProfileData.getLongitude());
            dataJson.put(Constants.PARAM_LAT, workLogProfileData.getLatitude());
            dataJson.put(Constants.PARAM_WPPROFILEID, workLogProfileData.getProfileID());
            dataJson.put(Constants.PARAM_SUBMITTED_TIME, System.currentTimeMillis() / 1000);
            dataJson.put(Constants.PARAM_DT, System.currentTimeMillis() / 1000);
            dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        uploadOtherData.setImageUploaded(true);
        uploadOtherData.setRequestedData(dataJson.toString());
        uploadOtherData.setData(data);
        tblUploadData.insertWPData(uploadOtherData);
        removeDataFromTableAfterWPSubmitted(wpPKId);
        startOtherDataUpload();

    }

    public void startOtherDataUpload() {
        if (!BaseApplication.getInstance().isInternetAvailable()) {
            sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_START_EVENT);
            return;
        }
        if (isOtherDataUploadingDone()) {
            sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_DONE_EVENT);
            sendBroadCastForOtherDataUploadingDone();
            return;
        }
        sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_START_EVENT);
        try {
            BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
            BaseApplication.getInstance().dataExecutorService.submit(this::checkAndUploadOtherDataInSafeThread);
        } catch (RejectedExecutionException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void checkAndUploadOtherDataInSafeThread() {
        try {
            if (uploadOtherData == null) {
                TblUploadData tblUploadData = new TblUploadData(this);
                //NOTE: There is no item in uploading so fetch one from the db.
                uploadOtherData = tblUploadData.getNextUploadData();
                //check if current upload data is form and it has sub forms which
                // are not yet submitted then hold main form and let proceed subform first.
                if (uploadOtherData != null) {
                    boolean shouldSkipUploading = checkSubFormSubmissionAndSkipUploadingMainForm();
                    if (shouldSkipUploading) {
                        // delay form data update time by 15 seconds.
                        //long updateTime = uploadOtherData.getUpdatedAt() + 15000;
                        tblUploadData.updateProcessTimeAndFlag(uploadOtherData.getId(), System.currentTimeMillis());
                        UploadOtherData uploadData = tblUploadData.checkNotLargeSiteUploadData();
                        if (isImageUploadingDone() || uploadData != null && uploadData.getId() > 0) {
                            this.uploadOtherData = null;
                            startOtherDataUpload();
                        } else {
                            if (uploadOtherData == null) {
                                uploadData = tblUploadData.getNextUploadData();
                                if (uploadData != null && uploadData.getId() > 0) {
                                    startOtherDataUpload();
                                }
                            }
                        }
                    }
                }
                // uploadOtherData = null  is possible if form has images and those are not yet uploaded
                if (uploadOtherData == null) {
                    //check if images count are mismatch
                    checkDataIfSignatureIsNotAddedInData();
                    return;
                }

                if (TextUtils.isEmpty(uploadOtherData.getRequestedData())) {
                    tblUploadData.deleteDataAfterUploading(uploadOtherData.getId());
                    uploadOtherData = null;
                    startOtherDataUpload();
                } else {
                    if (uploadOtherData.isDataUploaded()) {
                        removeCurrentDataAndExecuteNextOperation(uploadOtherData);
                    } else {
                        if (!uploadOtherData.isUploadProcessStart()) {
                            if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.FORM_DATA)) {
                                if (!uploadOtherData.hasSubForm() && !uploadOtherData.isSubForm()) {
                                    StartOtherDataUpload(uploadOtherData);
                                } else {
                                    List<Integer> lstSubForm = getSubFormsIdOfMainForm(true);
                                    if (lstSubForm.isEmpty()) {
                                        StartOtherDataUpload(uploadOtherData);
                                    } else {
                                        if (isImageUploadingDone()) {
                                            this.uploadOtherData = null;
                                            startOtherDataUpload();
                                        }
                                    }
                                }
                            } else {
                                StartOtherDataUpload(uploadOtherData);
                            }
                        }
                    }
                }
            } else {
                if (!uploadOtherData.isUploadProcessStart()) {
                    this.uploadOtherData = null;
                    startOtherDataUpload();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            CustomLogKt.errorLog("Service", "OtherDataInSafeThread::Thread UnLock:::ThreadID::" + Thread.currentThread().getId());
        }
    }

    /**
     * Function to check main form has any sub form data that are not yet uploaded.
     *
     * @return
     */
    private boolean checkSubFormSubmissionAndSkipUploadingMainForm() {
        //Check only if update data type is Form data else skip operation
        if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.FORM_DATA)) {
            if (uploadOtherData.getTmFormPKId() > 0) {
                // Do not check for sub forms and normal forms
                if (!uploadOtherData.hasSubForm()) {
                    return false;
                }
                // Get sub forms pk ids from table and return false if there are no any sub forms
                List<Integer> lstSubForm = getSubFormsIdOfMainForm(true);
                if (lstSubForm.isEmpty()) {
                    return false;
                }
                TblUploadData tblUploadData = getTblUploadData(lstSubForm);
                // return true if main form has submitted sub forms in upload table
                return tblUploadData.isSubFormDataExistForTmForm(lstSubForm);
            } else if (uploadOtherData.getFormPKId() > 0) {
                // Do not check for sub forms
                if (!uploadOtherData.hasSubForm()) {
                    return false;
                }
                List<Integer> lstSubForm = getSubFormsIdOfMainForm(false);
                if (lstSubForm.isEmpty()) {
                    return false;
                }
                TblUploadData tblUploadData = getTblUploadData(lstSubForm);
                // return true if main form has submitted sub forms in upload table
                return tblUploadData.isSubFormDataExistForNormalForm(lstSubForm);
            }
            return false;
        }
        return false;
    }

    private TblUploadData getTblUploadData(List<Integer> lstSubForm) {
        TblUploadData tblUploadData = new TblUploadData(this);
        //Check if all image data for form is uploaded then check its status in DB
        for (Integer formPkID : lstSubForm) {
            UploadOtherData data = tblUploadData.getUploadDataByFormPKId(formPkID, true);
            if (data != null && !data.isImageUploaded() && isImageUploadingDone()) {
                tblUploadData.updateImageStatus(data.getId(), true);
            }
        }
        return tblUploadData;
    }

    private List<Integer> getSubFormsIdOfMainForm(boolean isTmForm) {
        if (isTmForm)
            return new TblTMForms(this).getPkIdsOfSubmittedSubForm(TblTMForms.TABLE_NAME, uploadOtherData.getTmFormPKId());
        else {
            return new TblForms(this).getPkIdsOfSubmittedSubForm(TblForms.TABLE_NAME, uploadOtherData.getFormPKId());
        }
    }

    /**
     * Method is created to upload data in which signature is not attached(critical bug in 2.3.9)
     */
    private void checkDataIfSignatureIsNotAddedInData() {
        if (isImageUploadingDone()) {
            TblUploadData tblUploadData = new TblUploadData(this);
            UploadOtherData uploadOtherData = tblUploadData.getNextUploadDataWithImageStatusZero();
            if (uploadOtherData != null) {
                if (uploadOtherData.getTmFormPKId() > 0) {
                    if (StaticUtils.getTMFormPendingCount(uploadOtherData.getTmFormPKId()) <= 0) {
                        tblUploadData.updateImageStatus(uploadOtherData.getId(), true);
                        StaticUtils.setTMFormPendingCount(uploadOtherData.getTmFormPKId(), 0);
                        startOtherDataUpload();
                    }
                } else {
                    if (StaticUtils.getPendingCount(uploadOtherData.getFormPKId()) <= 0) {
                        tblUploadData.updateImageStatus(uploadOtherData.getId(), true);
                        StaticUtils.setPendingCount(uploadOtherData.getFormPKId(), 0);
                        startOtherDataUpload();
                    }
                }
            }
        }
    }

    private void sendBroadCastToChangeUploadStatus(String action) {
        sendBroadcast(new Intent(action));
    }

    private void StartOtherDataUpload(UploadOtherData uploadOtherData) {
        if (!BaseApplication.getInstance().isInternetAvailable()) {
            sendBroadCastToChangeUploadStatus(Constants.UPLOAD_DATA_START_EVENT);
            sendBroadCastForOtherDataUploadingFail(uploadOtherData.getId());
            return;
        }

        uploadOtherData.setUploadProcessStart(true);
        sendBroadCastForOtherDataUploadingStart(uploadOtherData.getId());
        if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.FORM_DATA)) {
            requestServerToPostFormData(uploadOtherData);
        }  else if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.BREADCRUMBS_DATA)) {
            requestServerToPostBreadCrumbsData(uploadOtherData);
        } else if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.WP_DATA)) {
            requestServerToPostWPData(uploadOtherData);
        } else if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.CREW_DATA)) {
            requestServerToPostCrewData(uploadOtherData);
        } else if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.ISSUE_DATA)) {
            requestServerToPostIssueData(uploadOtherData);
        }
    }

    private void removeCurrentDataAndExecuteNextOperation(UploadOtherData uploadOtherData) {
        uploadOtherData.setDataUploaded(true);
        uploadOtherData.setUploadProcessStart(false);
        TblUploadData tblUploadData = new TblUploadData(this);
        tblUploadData.deleteDataAfterUploading(uploadOtherData.getId());
        if (uploadOtherData.getDataType().equalsIgnoreCase(Constants.FORM_DATA)) {
            changeFormUploadedStatusInFormTable(uploadOtherData.getFormPKId(), uploadOtherData.getTmFormPKId());
            removeDataFromTableAfterFormSubmitted(uploadOtherData.getFormPKId(), uploadOtherData.getTmFormPKId(), uploadOtherData.getTmFormPKId() > 0);
        }
        this.uploadOtherData = null;
        sendBroadCastForOtherDataUploadingDone();
        startOtherDataUpload();
    }

    private void updateUpdatedDateOfUploadingData(UploadOtherData uploadOtherData,
                                                  boolean noInternetException, boolean canChangeOrderOnException) {
        TblUploadData tblUploadData = new TblUploadData(this);
        if (!noInternetException && canChangeOrderOnException) {
            // Error occurred in api call. change time in Db and start other uploading
            tblUploadData.updateProcessTimeAndFlag(uploadOtherData.getId(), System.currentTimeMillis());
            this.uploadOtherData = null;
        }
        uploadOtherData.setUploadProcessStart(false);
        this.uploadOtherData.setUploadProcessStart(false);
        sendBroadCastForOtherDataUploadingDone();
        startOtherDataUpload();
    }

    private void removeDataFromTableAfterFormSubmitted(int formPKId, int formTMPKId, boolean isTmForm) {
        if (isTmForm) {
            TblTMForms tblTNMForms = new TblTMForms(getApplicationContext());
            FormData formData = tblTNMForms.getFormDataByPKId(formTMPKId);
            if (formData.getFormId() > 0)
                deleteImagesFromStorage(formData.getImageData());
            if (!formData.isSubForm()) {
                if (formData.hasSubForm()) {
                    //Delete All SubForm of the form
                    tblTNMForms.deleteDataByMainFormPKId(TblTMForms.TABLE_NAME, formData.getFormPKId());
                }
                tblTNMForms.deleteDataByPKId(formTMPKId);
            }
        } else {
            TblForms tblForms = new TblForms(getApplicationContext());
            FormData formData = tblForms.getFormDataByPKId(formPKId);
            if (formData.getFormId() > 0) {
                deleteImagesFromStorage(formData.getImageData());
            }
            if (!formData.isSubForm()) {
                if (formData.hasSubForm()) {
                    //Delete All SubForm of the form
                    tblForms.deleteDataByMainFormPKId(TblForms.TABLE_NAME, formData.getFormPKId());
                }
                tblForms.deleteDataByPKId(formPKId);
            }
        }
    }


    private void callAfterResponse(int result, UploadOtherData uploadOtherData, boolean noInternetException, boolean canChangeOrderOnException) {
        if (result == 1) {
            removeCurrentDataAndExecuteNextOperation(uploadOtherData);
        } else {
            sendBroadCastForOtherDataUploadingFail(uploadOtherData.getId());
            updateUpdatedDateOfUploadingData(uploadOtherData, noInternetException, canChangeOrderOnException);
        }

    }


    private void requestServerToPostFormData(UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = StaticUtils.getHashMapFromStringForForm(getApplicationContext(), uploadOtherData);
        Call<SimpleResponse> call = RetrofitProvider.createService(ApiInterface.class).requestToSaveForm(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(Call<SimpleResponse> call, retrofit2.Response<SimpleResponse> response) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageResponse(uploadOtherData, response, true));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onFailure(Call<SimpleResponse> call, Throwable throwable) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageFailResponse(uploadOtherData, throwable, true));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });


    }

    private void requestServerToPostWPData(UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = StaticUtils.getHashMapFromStringForWPData(uploadOtherData);
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        //Removed WP API call (/vpics/uploadtmdatav3) on 2.4.7 and set Form API call
        Call<SimpleResponse> call = RetrofitProvider.createService(ApiInterface.class).requestToSubmitWorkLogProfileData(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(Call<SimpleResponse> call, retrofit2.Response<SimpleResponse> response) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageResponse(uploadOtherData, response, false));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onFailure(Call<SimpleResponse> call, Throwable throwable) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageFailResponse(uploadOtherData, throwable, false));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });

    }

    private void requestServerToPostCrewData(UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = StaticUtils.getHashMapFromStringForCrewData(uploadOtherData);
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        Call<SimpleResponse> call = RetrofitProvider.createService(ApiInterface.class).requestToPostCrewData(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(Call<SimpleResponse> call, retrofit2.Response<SimpleResponse> response) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageResponse(uploadOtherData, response, false));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onFailure(Call<SimpleResponse> call, Throwable throwable) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageFailResponse(uploadOtherData, throwable, false));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });

    }

    private void requestServerToPostIssueData(UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = StaticUtils.getHashMapFromStringForIssueData(uploadOtherData);
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        Call<SimpleResponse> call = RetrofitProvider.createService(ApiInterface.class).requestToSubmitIssueData(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(Call<SimpleResponse> call, retrofit2.Response<SimpleResponse> response) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageResponse(uploadOtherData, response, true));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onFailure(Call<SimpleResponse> call, Throwable throwable) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageFailResponse(uploadOtherData, throwable, true));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });

    }

    private void requestServerToPostBreadCrumbsData(UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = StaticUtils.getHashMapFromStringForBreadCrumb(uploadOtherData);
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        Call<SimpleResponse> call = RetrofitProvider.createService(ApiInterface.class).requestToPostBreadCrumbsData(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(@NonNull Call<SimpleResponse> call, @NonNull retrofit2.Response<SimpleResponse> response) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageResponse(uploadOtherData, response, false));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }

            @Override
            public void onFailure(@NonNull Call<SimpleResponse> call, @NonNull Throwable throwable) {
                try {
                    BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
                    BaseApplication.getInstance().dataExecutorService.submit(() -> manageFailResponse(uploadOtherData, throwable, false));
                } catch (RejectedExecutionException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });

    }

    private void removeDataFromTableAfterWPSubmitted(int wpPKId) {
        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(getApplicationContext());
        tblSiteProfileData.deleteDataByPKId(wpPKId);
    }

    private void manageResponse(UploadOtherData uploadOtherData, retrofit2.Response<SimpleResponse> response,
                                boolean canChangeOrderOnException) {
        int result = 0;
        if (response != null && response.body() != null && response.isSuccessful()) {
            if (!response.body().getIsError()) {
                result = 1;
            }
        }

        if (response != null && response.code() == 401) {
            //Un Authorised user. Logout user and clear Database
            BaseApplication.getInstance().logoutUser(true);
            return;
        }
        if (response != null && response.code() != 304 && result == 0) {
            //Time delay for next call
            try {
                int finalResult = result;
                new Handler().postDelayed(() -> callAfterResponse(finalResult, uploadOtherData, false, canChangeOrderOnException), 10000);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

                callAfterResponse(result, uploadOtherData, false, canChangeOrderOnException);
            }
        } else {
            callAfterResponse(result, uploadOtherData, false, canChangeOrderOnException);
        }
    }

    private void manageFailResponse(UploadOtherData uploadOtherData, Throwable throwable,
                                    boolean canChangeOrderOnException) {
        if (throwable.getMessage() != null && throwable.getMessage().contains(": No address associated with hostname")) {
            callAfterResponse(0, uploadOtherData, true, canChangeOrderOnException);
        } else {
            callAfterResponse(0, uploadOtherData, false, canChangeOrderOnException);
        }
    }

    private void sendBroadCastForOtherDataUploadingDone() {
        sendBroadcast(new Intent(Constants.OTHERDATA_UPLOAD_DONE_SERVICE_BROADCAST));
    }

    private void sendBroadCastForOtherDataUploadingStart(int uploadId) {
        sendBroadcast(new Intent(Constants.OTHERDATA_UPLOAD_SERVICE_BROADCAST).putExtra("uploadId", uploadId));
    }

    private void sendBroadCastForOtherDataUploadingFail(int uploadId) {
        sendBroadcast(new Intent(Constants.OTHERDATA_UPLOAD_FAIL_SERVICE_BROADCAST).putExtra("uploadId", uploadId));
    }
    public boolean isImageUploadingDone() {
        return new TblUploadImage(getApplicationContext()).getDataCount() == 0;
    }

    public boolean isOtherDataUploadingDone() {
        return new TblUploadData(getApplicationContext()).getDataCount() == 0;
    }

    public void registerAppReceiver(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            //We need to set RECEIVER_EXPORTED as we upload our data on another process
            registerReceiver(broadcastReceiver, intentFilter, Context.RECEIVER_EXPORTED);
        } else {
            registerReceiver(broadcastReceiver, intentFilter);
        }
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterReceiver(netWorkChangeReceiver);
        unregisterReceiver(imageUploadStart);
        BaseApplication.getInstance().shutDownExecutorService();
    }

}
