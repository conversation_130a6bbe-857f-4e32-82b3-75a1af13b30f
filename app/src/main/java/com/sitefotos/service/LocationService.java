package com.sitefotos.service;

import static com.sitefotos.Constants.locationRadiusLimitToUpdateSiteData;
import static com.sitefotos.Constants.shouldUpdateSiteDataInList;

import android.Manifest;
import android.app.ActivityManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Location;
import android.os.IBinder;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationAvailability;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.Priority;
import com.sitefotos.event.LocationUpdateEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.util.PropertyUtils;
import com.sitefotos.util.logger.CustomLogKt;

import org.greenrobot.eventbus.EventBus;


public class LocationService extends Service {
    public static final String START_ACTION = "location.action.start";
    public static int INTERVAL = 2000 * 3;
    public static int FASTED_INTERVAL = 6000;
    static FusedLocationProviderClient fusedLocationProviderClient;
    private static LocationCallback mLocationCallback;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mLocationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(@NonNull LocationResult locationResult) {
                super.onLocationResult(locationResult);
                onNewLocation(locationResult.getLastLocation());
            }

            @Override
            public void onLocationAvailability(@NonNull LocationAvailability locationAvailability) {
                super.onLocationAvailability(locationAvailability);
                //If location stop service is called then start location update on GPS is enabled again
                if (locationAvailability.isLocationAvailable()) {
                    reStartLocationService(getApplicationContext());
                }
            }
        };
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stopLocation();
    }


    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent == null || intent.getAction() == null) {
            stopForeground(true);
            stopSelf();
            return START_NOT_STICKY;
        }
        if (START_ACTION.equals(intent.getAction())) {
            startLocation(getApplicationContext());
            return START_STICKY;
        } else {
            stopForeground(true);
            stopSelf();
        }
        return START_NOT_STICKY;
    }

    private static void startLocation(Context context) {
        LocationRequest mLocationRequest = new LocationRequest.Builder(INTERVAL)
                .setPriority(Priority.PRIORITY_BALANCED_POWER_ACCURACY)
                .setIntervalMillis(FASTED_INTERVAL)
                .setWaitForAccurateLocation(false)
                .setMinUpdateIntervalMillis(FASTED_INTERVAL)
                .setMaxUpdateDelayMillis(FASTED_INTERVAL * 3L)
                .build();

        fusedLocationProviderClient = LocationServices.getFusedLocationProviderClient(context);
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            return;
        }

        // Got last known location. In some rare situations this can be null.
        fusedLocationProviderClient.getLastLocation().addOnSuccessListener(LocationService::onNewLocation);
        fusedLocationProviderClient.requestLocationUpdates(mLocationRequest,
                mLocationCallback, Looper.myLooper());

    }


    private static void onNewLocation(Location location) {
         setLocation(location);
    }


    private static void setLocation(Location lastLocation) {
        if (lastLocation != null) {
            MainActivity.currentLatitude = lastLocation.getLatitude();
            MainActivity.currentLongitude = lastLocation.getLongitude();
            MainActivity.distanceAccuracy = Math.abs((long) lastLocation.getAccuracy());
            EventBus.getDefault().post(new LocationUpdateEvent(lastLocation));
            updateCurrentLocationRadius(lastLocation);

            CustomLogKt.errorLog("MainActivity Location", "currentLatitude:: " + MainActivity.currentLatitude + "    currentLongitude:: " + MainActivity.currentLongitude);
        } else {
            CustomLogKt.errorLog(" MainActivity lastLocation", "Last known location null");
        }

    }

    /**
     * Function to update location if it is greater than 16 meters of the user current location
     * @param location Location
     */

    private static void updateCurrentLocationRadius(Location location) {
        if (location != null) {
            // If last location is not set then set it to current location
            if (MainActivity.lastLatitude == 0.0 && MainActivity.lastLongitude == 0.0) {
                MainActivity.lastLatitude = location.getLatitude();
                MainActivity.lastLongitude = location.getLongitude();
            }

            // If last location is set and distance to current location is greater than radius then update last location and update site data
            if (PropertyUtils.distanceToCurrentLocation(MainActivity.lastLatitude, MainActivity.lastLongitude) > locationRadiusLimitToUpdateSiteData) {
                MainActivity.lastLatitude = location.getLatitude();
                MainActivity.lastLongitude = location.getLongitude();
                shouldUpdateSiteDataInList = true;
            }
        }
    }

    public void stopLocation() {
        if (fusedLocationProviderClient != null && mLocationCallback != null) {
            fusedLocationProviderClient.removeLocationUpdates(mLocationCallback);
            fusedLocationProviderClient = null;
        }
    }


    public static boolean serviceIsRunningInForeground(Context context) {
        ActivityManager manager = (ActivityManager) context.getSystemService(
                Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(
                Integer.MAX_VALUE)) {
            if (LocationService.class.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    public static void startService(Context context) {
        try {
            if (!serviceIsRunningInForeground(context)) {
                Intent startIntent = new Intent(context, LocationService.class);
                startIntent.setAction(LocationService.START_ACTION);
                context.startService(startIntent);
            } else {
                reStartLocationService(context);
            }
        } catch (Exception ignored) {
            // No need to capture this exception as system now do not allow to upload when app goes in BG.
            //Also Some provider instant stop service and some do not. So no need to set any other condition to manage this flow.
        }
    }

    private static void reStartLocationService(Context context) {
        if (MainActivity.currentLongitude == 0.0) {
           startLocation(context);
        }
    }

    public static void stopService(Context context) {
        context.stopService(new Intent(context, LocationService.class));
    }
}