package com.sitefotos.uploadImage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityUploadingBinding;
import com.sitefotos.models.UploadImageData;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.service.FileUploaderService;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.logger.CustomLogKt;

import java.util.ArrayList;

public class UploadImagesActivity extends BaseActivity implements View.OnClickListener {

    public Context context;
    public ArrayList<UploadImageData> lstUploadImageData = new ArrayList<>();
    public UploadImageAdapter uploadImageAdapter;
    ArrayList<UploadOtherData> lstUploadOtherData = new ArrayList<>();
    UploadOtherDataAdapter uploadOtherDataAdapter;
    private ActivityUploadingBinding binding;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }


    BroadcastReceiver otherData = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    case Constants.OTHERDATA_UPLOAD_SERVICE_BROADCAST:
                        int uploadId = intent.getIntExtra("uploadId", 0);
                        onProgressOtherDataUploading(uploadId, true);
                        break;
                    case Constants.OTHERDATA_UPLOAD_DONE_SERVICE_BROADCAST:
                        updateOtherData();
                        break;

                    case Constants.OTHERDATA_UPLOAD_FAIL_SERVICE_BROADCAST:
                        int uploadId1 = intent.getIntExtra("uploadId", 0);
                        onProgressOtherDataUploading(uploadId1, false);
                        break;
                }
            }
        }
    };


    BroadcastReceiver uploadProgressStatusReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {

            String action = intent.getAction();
            int uploadId;
            if (action != null) {
                switch (action) {
                    case Constants.INTERNAL_CURRENT_IMAGE_UPLOAD_PROGRESS:
                        uploadId = intent.getIntExtra("uploadId", 0);
                        int progress = intent.getIntExtra("progress", 0);
                        onProgressImageUploading(uploadId, progress, true);
                        break;
                    case Constants.INTERNAL_PENDING_IMAGE_UPLOAD:
                        uploadId = intent.getIntExtra("uploadId", 0);
                        onProgressImageUploading(uploadId, 0, true);
                        break;
                    case Constants.UPLOAD_DATA_DONE_EVENT:
                        getImageData();

                        break;
                }
            }
        }
    };


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        initBinding();
        registerBroadCastReceiver();
        registerBroadCastForImageUploadingStatus();
        setActionBarVisibility(false);
        init();
        setupActionBar();

        try {
            BaseApplication.getInstance().checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        sendBroadCastForBothUploading();
    }


    private void setupActionBar() {
        binding.tlMain.tvTitle.setText(R.string.uploads_title);
    }

    private void registerBroadCastReceiver() {
        IntentFilter otherDataIntentFilter = new IntentFilter();
        otherDataIntentFilter.addAction(Constants.OTHERDATA_UPLOAD_SERVICE_BROADCAST);
        otherDataIntentFilter.addAction(Constants.OTHERDATA_UPLOAD_DONE_SERVICE_BROADCAST);
        otherDataIntentFilter.addAction(Constants.OTHERDATA_UPLOAD_FAIL_SERVICE_BROADCAST);
        registerAppReceiver(otherData, otherDataIntentFilter);
    }


    private void registerBroadCastForImageUploadingStatus() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.INTERNAL_CURRENT_IMAGE_UPLOAD_PROGRESS);
        intentFilter.addAction(Constants.INTERNAL_PENDING_IMAGE_UPLOAD);
        intentFilter.addAction(Constants.UPLOAD_DATA_DONE_EVENT);
        registerAppReceiver(uploadProgressStatusReceiver, intentFilter);
    }


    private void initBinding() {
        binding = ActivityUploadingBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    public void init() {
        setToolbarVisibility(true);
        setOnClickListener();
        setAdapterForOtherData();
        setAdapter();
    }

    private void setOnClickListener() {
        binding.tlMain.imgBtnBack.setOnClickListener(this);
    }


    private void setAdapterForOtherData() {
        binding.rvUploadOtherData.setLayoutManager(new LinearLayoutManager(this));
        uploadOtherDataAdapter = new UploadOtherDataAdapter(this, lstUploadOtherData);
        binding.rvUploadOtherData.setAdapter(uploadOtherDataAdapter);
    }

    public void setAdapter() {
        if (uploadImageAdapter == null) {
            binding.rvUploadImage.setVisibility(View.VISIBLE);
            binding.rvUploadImage.setLayoutManager(new GridLayoutManager(this, 2));
            uploadImageAdapter = new UploadImageAdapter(context, lstUploadImageData);
            binding.rvUploadImage.setAdapter(uploadImageAdapter);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        CustomLogKt.errorLog("TEST", "onResume  started ");
        getImageData();
        updateOtherData();
        sendImageSyncBroadCast();


    }

    private void sendImageSyncBroadCast() {
        Intent intent = new Intent(Constants.IMAGE_UPLOAD_SYNC_BROADCAST);
        sendBroadcast(intent);
    }

    public void updateImageUploadData() {
        if (!lstUploadImageData.isEmpty()) {
            binding.tvUploadImageError.setVisibility(View.GONE);
            binding.rvUploadImage.setVisibility(View.VISIBLE);
            uploadImageAdapter.updateData(lstUploadImageData);
        } else {
            binding.rvUploadImage.setVisibility(View.GONE);
            binding.tvUploadImageError.setVisibility(View.VISIBLE);
            binding.tvUploadImageError.setText(getString(R.string.no_uploads_message));
        }
    }

    private void updateOtherData() {
        lstUploadOtherData = getListFromUploadOtherDataDb();
        uploadOtherDataAdapter.updateData(lstUploadOtherData);
        if (!lstUploadOtherData.isEmpty()) {
            binding.tvUploadOtherDataErrorMessage.setVisibility(View.GONE);
        } else {
            binding.tvUploadOtherDataErrorMessage.setVisibility(View.VISIBLE);
            binding.tvUploadOtherDataErrorMessage.setText(R.string.uploadotherdata_error_msg);
        }
    }

    public void onProgressImageUploading(int uploadId, int total, boolean uploading) {
        UploadImageData uploadImageData = getCurrentUploadingData(uploadId);
        if (uploadImageData == null) {
            return;
        }

        int position = lstUploadImageData.indexOf(uploadImageData);
        uploadImageData.setLowImageUploadProcessStarted(uploading); // we need this check to update UI in adapter. so it doesn't matter it is for low or high.
        uploadImageData.setTotalProgress(total);
        lstUploadImageData.set(position, uploadImageData);
        if (uploading) {
            uploadImageAdapter.updateItemChanged(position, uploadImageData);
        } else {
            uploadOtherDataAdapter.notifyDataSetChanged();
        }
    }

    public void onProgressOtherDataUploading(int uploadId, boolean uploadingStarted) {
        UploadOtherData uploadingOtherData = getCurrentUploadingOtherData(uploadId);

        if (uploadingOtherData == null) {
            return;
        }

        int position = lstUploadOtherData.indexOf(uploadingOtherData);
        uploadingOtherData.setUploadProcessStart(uploadingStarted);
        lstUploadOtherData.set(position, uploadingOtherData);
        if (uploadingStarted) {
            runOnUiThread(() -> uploadOtherDataAdapter.notifyItemChanged(position));
        } else {
            uploadOtherDataAdapter.notifyDataSetChanged();
        }
    }

    private UploadImageData getCurrentUploadingData(int uploadId) {
        if (uploadId <= 0) {
            return null;
        }
        for (UploadImageData uploadImageData : lstUploadImageData) {
            if (uploadImageData.getUploadId() == uploadId) {
                return uploadImageData;
            } else {
                uploadImageData.setLowImageUploadProcessStarted(false);
            }
        }
        return null;
    }

    private UploadOtherData getCurrentUploadingOtherData(int uploadId) {
        if (uploadId <= 0) {
            return null;
        }
        for (UploadOtherData uploadOtherData : lstUploadOtherData) {
            if (uploadOtherData.getId() == uploadId) {
                return uploadOtherData;
            } else {
                uploadOtherData.setUploadProcessStart(false);
            }
        }
        return null;

    }

    private void getImageData() {
        final ArrayList[] lstUploadImageDataUpdated = new ArrayList[]{new ArrayList<>()};
        final Handler handler = new Handler();
        new Thread(() -> {
            try {
                lstUploadImageDataUpdated[0] = getListFromDb();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
            handler.post(() -> {
                try {
                    if (lstUploadImageData != null)
                        lstUploadImageData.clear();
                    if (uploadImageAdapter != null)
                        uploadImageAdapter.notifyDataSetChanged();
                    lstUploadImageData = lstUploadImageDataUpdated[0];
                    updateImageUploadData();
                    sendImageSyncBroadCast();
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            });
        }).start();
        //new SetListUploadImagesAsyncTask().execute();
    }

    public class SetListUploadImagesAsyncTask extends AsyncTask<Void, Boolean, Boolean> {
        ArrayList<UploadImageData> lstUploadImageDataUpdated = new ArrayList<>();

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }


        @Override
        protected Boolean doInBackground(Void... params) {
            lstUploadImageDataUpdated = getListFromDb();
            return true;
        }

        @Override
        protected void onPostExecute(Boolean result) {
            try {
                if (result) {
                    if (lstUploadImageData != null)
                        lstUploadImageData.clear();
                    if (uploadImageAdapter != null)
                        uploadImageAdapter.notifyDataSetChanged();
                    lstUploadImageData = lstUploadImageDataUpdated;
                }
                updateImageUploadData();
                sendImageSyncBroadCast();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    private ArrayList<UploadImageData> getListFromDb() {
        TblUploadImage tblUploadImage = new TblUploadImage(UploadImagesActivity.this);
        return tblUploadImage.getAllUploadImageData();

    }


    private ArrayList<UploadOtherData> getListFromUploadOtherDataDb() {
        TblUploadData tblUploadData = new TblUploadData(UploadImagesActivity.this);
        return tblUploadData.getAllUploadImageData();

    }


    @Override
    public void onClick(View view) {
        if (view.getId() == R.id.imgBtnBack) {
            onBackPressed();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        unregisterReceiver(otherData);
        unregisterReceiver(uploadProgressStatusReceiver);
    }
}

