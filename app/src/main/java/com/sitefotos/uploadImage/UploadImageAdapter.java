package com.sitefotos.uploadImage;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;

import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.BaseApplication;
import com.sitefotos.databinding.ItemUploadingDataBinding;
import com.sitefotos.models.UploadImageData;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.ProgressWheel;

import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.ArrayList;


public class UploadImageAdapter extends RecyclerView.Adapter<UploadImageAdapter.ViewHolder> {

    public Context context;
    private ArrayList<UploadImageData> lstUploadImageData;
    public UploadImagesActivity activity;
    private ItemUploadingDataBinding binding;

    public UploadImageAdapter(Context context, ArrayList<UploadImageData> lstUploadImageData) {
        this.context = context;
        this.lstUploadImageData = lstUploadImageData;
        this.activity = (UploadImagesActivity) context;
    }

    @Override
    public UploadImageAdapter.ViewHolder onCreateViewHolder(@NotNull ViewGroup parent, int viewType) {
        binding = ItemUploadingDataBinding.inflate( LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NotNull UploadImageAdapter.ViewHolder holder, int position) {
        UploadImageData uploadImageData = lstUploadImageData.get(position);
        if (BaseApplication.getInstance().isInternetAvailable() && (uploadImageData.isLowImageUploadProcessStarted())) {
            holder.pwUploading.setVisibility(View.VISIBLE);
            holder.pbImageUpload.setVisibility(View.VISIBLE);
            int progress = uploadImageData.isUploadOriginalSize() ? (int) (uploadImageData.getTotalProgress() / 2) : (int) (uploadImageData.getTotalProgress());
            holder.pbImageUpload.setProgress(progress);

        } else {
            holder.pwUploading.setVisibility(View.GONE);
            holder.pbImageUpload.setProgress(0);
            holder.pbImageUpload.setVisibility(View.GONE);
        }
        if(new File(uploadImageData.getImagePathLow()).exists()){
            ImageUtil.loadImageInGlide(holder.ivImage, uploadImageData.getImagePathLow());
        }else {
            ImageUtil.loadImageInGlide(holder.ivImage, uploadImageData.getImagePathHigh());
        }

    }

    @Override
    public int getItemCount() {
        return lstUploadImageData.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        ImageView ivImage;
        ProgressWheel pwUploading;
        ProgressBar pbImageUpload;

        public ViewHolder(ItemUploadingDataBinding binding) {
            super(binding.getRoot());
            ivImage =binding.ivImage;
            pwUploading = binding.pwUploading;
            pbImageUpload = binding.ProgressBar;
        }

    }

    public void updateData(ArrayList<UploadImageData> lstUploadImageData) {
        this.lstUploadImageData = lstUploadImageData;
        notifyDataSetChanged();
    }

    void updateItemChanged(int position, UploadImageData uploadImageData) {
        notifyItemChanged(position, uploadImageData);
    }
}
