package com.sitefotos.uploadImage;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.BaseApplication;
import com.sitefotos.databinding.ItemOtherdataBinding;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.util.ProgressWheel;

import java.util.ArrayList;


public class UploadOtherDataAdapter extends RecyclerView.Adapter<UploadOtherDataAdapter.ViewHolder> {

    public Context context;
    private ArrayList<UploadOtherData> lstUploadOtherData;
    public UploadImagesActivity activity;
    private ItemOtherdataBinding binding;

    public UploadOtherDataAdapter(Context context, ArrayList<UploadOtherData> lstUploadOtherData) {
        this.context = context;
        this.lstUploadOtherData = lstUploadOtherData;
        this.activity = (UploadImagesActivity) context;
    }

    @Override
    public UploadOtherDataAdapter.ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        binding = ItemOtherdataBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(UploadOtherDataAdapter.ViewHolder holder, int position) {
        UploadOtherData uploadOtherData = lstUploadOtherData.get(position);
        holder.tvUploadDataTitle.setText(uploadOtherData.getTitle());
        if(BaseApplication.getInstance().isInternetAvailable() && !TextUtils.isEmpty(uploadOtherData.getRequestedData())&& uploadOtherData.isUploadProcessStart()){
            holder.progressWheelUploadAdapter.setVisibility(View.VISIBLE);
        }else{
            holder.progressWheelUploadAdapter.setVisibility(View.GONE);
        }
    }

    @Override
    public int getItemCount() {
        return lstUploadOtherData.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        ProgressWheel progressWheelUploadAdapter;
        RelativeLayout llMain;
        TextView tvUploadDataTitle;

        public ViewHolder(ItemOtherdataBinding binding) {
            super(binding.getRoot());
            progressWheelUploadAdapter = binding.pwUploadDataProgress;
            llMain = binding.llMain;
            tvUploadDataTitle = binding.tvUploadDataTitle;
        }
    }

    public void updateData(ArrayList<UploadOtherData> lstUploadOtherData) {
        this.lstUploadOtherData = lstUploadOtherData;
        notifyDataSetChanged();
    }
}
