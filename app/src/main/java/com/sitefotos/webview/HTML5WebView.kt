package com.sitefotos.webview

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.*
import android.webkit.*
import android.webkit.WebChromeClient.CustomViewCallback
import android.widget.FrameLayout
import com.sitefotos.R

class HTML5WebView : WebView {
    private var mContext: Context? = null
    private var mCustomView: View? = null
    private var mCustomViewContainer: FrameLayout? = null
    private var mCustomViewCallback: CustomViewCallback? = null
    private var mContentView: FrameLayout? = null
    private var mBrowserFrameLayout: FrameLayout? = null
    var layout: FrameLayout? = null
        private set

    private fun init(context: Context) {
        mContext = context
        layout = FrameLayout(context)
        mBrowserFrameLayout = LayoutInflater.from(mContext).inflate(R.layout.layout_webview_html5_video_play, null) as FrameLayout
        mContentView = mBrowserFrameLayout?.findViewById(R.id.main_content)
        mCustomViewContainer = mBrowserFrameLayout?.findViewById(R.id.fullscreen_custom_content)
        layout?.addView(mBrowserFrameLayout, COVER_SCREEN_PARAMS)

        // Configure the webview
        val webSettings = settings
        webSettings.builtInZoomControls = true
        webSettings.layoutAlgorithm = WebSettings.LayoutAlgorithm.NARROW_COLUMNS
        webSettings.useWideViewPort = true
        webSettings.loadWithOverviewMode = true
        // s.setSavePassword(true);
        webSettings.javaScriptEnabled = true
        // Set permissions to load file from local storage
        webSettings.builtInZoomControls = true
        webChromeClient = MyWebChromeClient()
        webViewClient = WebViewClient()
        scrollBarStyle = View.SCROLLBARS_INSIDE_OVERLAY
        // enable Web Storage: localStorage, sessionStorage
        webSettings.domStorageEnabled = true
        mContentView?.addView(this)
    }

    constructor(context: Context) : super(context) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs) {
        init(context)
    }

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        init(context)
    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent): Boolean {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            if (mCustomView == null && canGoBack()) {
                goBack()
                return true
            }
        }
        return super.onKeyDown(keyCode, event)
    }

    private inner class MyWebChromeClient : WebChromeClient() {
        private var mVideoProgressView: View? = null
        override fun onShowCustomView(view: View,
                                      callback: CustomViewCallback) {
            visibility = View.GONE
            // if a view already exists then immediately terminate the new one
            if (mCustomView != null) {
                callback.onCustomViewHidden()
                return
            }
            mCustomViewContainer?.addView(view)
            mCustomView = view
            mCustomViewCallback = callback
            mCustomViewContainer?.visibility = View.VISIBLE
        }

        override fun onHideCustomView() {
            if (mCustomView == null) return

            // Hide the custom view.
            mCustomView?.visibility = View.GONE

            // Remove the custom view from its container.
            mCustomViewContainer?.removeView(mCustomView)
            mCustomView = null
            mCustomViewContainer?.visibility = View.GONE
            mCustomViewCallback?.onCustomViewHidden()
            visibility = View.VISIBLE
            goBack()
        }

        override fun getVideoLoadingProgressView(): View? {
            if (mVideoProgressView == null) {
                val inflater = LayoutInflater.from(mContext)
                mVideoProgressView = inflater.inflate(R.layout.layout_progress_video, null)
            }
            return mVideoProgressView
        }

        override fun onReceivedTitle(view: WebView, title: String) {
            (mContext as Activity?)?.title = title
        }

        override fun onProgressChanged(view: WebView, newProgress: Int) {
            (mContext as Activity?)?.window?.setFeatureInt(Window.FEATURE_PROGRESS, newProgress * 100)
        }

        override fun onGeolocationPermissionsShowPrompt(origin: String, callback: GeolocationPermissions.Callback) {
            callback.invoke(origin, true, false)
        }
    }

    companion object {
        val COVER_SCREEN_PARAMS = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
    }
}