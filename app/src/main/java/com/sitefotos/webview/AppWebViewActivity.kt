package com.sitefotos.webview

import android.os.Bundle
import android.view.View
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebView
import android.webkit.WebViewClient
import com.sitefotos.BaseActivity
import com.sitefotos.R
import com.sitefotos.appinterface.OnAppDataApiResponse
import com.sitefotos.databinding.ActivityWebviewBinding

class AppWebViewActivity : BaseActivity(), View.OnClickListener {


    private var loadUrl: String? = null
    private var title: String? = ""
    private lateinit var binding: ActivityWebviewBinding

    override fun getApiCallBack(): OnAppDataApiResponse? {
        return null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initBinding()
        setActionBarVisibility(false)
        initView()
    }
    private fun initBinding() {
        binding = ActivityWebviewBinding.inflate(layoutInflater)
        setContentView(binding.getRoot())
    }

    private fun initView() {
        getBundleData()
        setupActionBar()
        binding.layoutToolbar.imgBtnBack.setOnClickListener(this)
        loadWebData()
    }

    private fun setupActionBar() {
        binding.layoutToolbar.tvTitle.text = title
    }


    /**
     * Method to get intent data from other activity/ fragment screen and assign it with global variable.
     */
    private fun getBundleData() {
        loadUrl = intent.getStringExtra("url")
        title = intent.getStringExtra("title");
    }

    private fun loadWebData() {
        //if (BaseApplication.getInstance().isOnline(this)) {
        showProgressLoadingView()
        binding.webView.webViewClient = object : WebViewClient() {

            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                hideProgressLoadingView()
            }

            override fun onReceivedError(
                view: WebView?,
                request: WebResourceRequest?,
                error: WebResourceError?
            ) {
                hideProgressLoadingView()
                super.onReceivedError(view, request, error)
            }
        }
        binding.webView.clearCache(true)
        loadUrl?.let { binding.webView.loadUrl(it) }
        //}


        binding.webView.settings.javaScriptCanOpenWindowsAutomatically = true
        binding.webView.settings.builtInZoomControls = true
        binding.webView.setInitialScale(1)
        binding.webView.settings.loadWithOverviewMode = true
        binding.webView.settings.useWideViewPort = true
        binding.webView.settings.javaScriptEnabled = true
    }

    override fun onClick(v: View) {
        when (v.id) {
            R.id.imgBtnBack -> {
                backUserToPreviousScreen()
            }
        }
    }

    private fun backUserToPreviousScreen() {
        onBackPressed()
        overridePendingTransition(
            R.anim.enter_from_left,
            R.anim.exit_to_right
        )

    }


    /**
     * Method to show loader on screen
     */
    private fun showProgressLoadingView() {
        binding.llProgress.rlProgressView.isClickable = false
        binding.llProgress.rlProgressView.isEnabled = false
        binding.llProgress.rlProgressView.visibility = View.VISIBLE
        binding.llProgress.rlProgressView.visibility = View.VISIBLE
    }

    /**
     * Method to hide loading view
     */
    private fun hideProgressLoadingView() {
        binding.llProgress.rlProgressView.isClickable = true
        binding.llProgress.rlProgressView.isEnabled = true
        binding.llProgress.rlProgressView.visibility = View.GONE
        binding.llProgress.rlProgressView.visibility = View.GONE
    }


    override fun onBackPressed() {
        if (binding.webView.canGoBack()) {
            binding.webView.goBack()
        } else {
            super.onBackPressed()
        }

    }
}