package com.sitefotos.webview

import android.graphics.Bitmap
import android.os.Bundle
import android.view.LayoutInflater
import android.webkit.WebChromeClient
import android.webkit.WebView
import android.webkit.WebViewClient
import com.sitefotos.BaseActivity
import com.sitefotos.appinterface.OnAppDataApiResponse

class WebViewActivity : BaseActivity() {
    var mWebView: HTML5WebView? = null
    private var strURLToLoad = ""

    override fun getApiCallBack(): OnAppDataApiResponse? {
        return null
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        inflater = LayoutInflater.from(this)
        setActionBarVisibility(false)
        if (intent.hasExtra("link")) {
            strURLToLoad = intent.getStringExtra("link")?.trim { it <= ' ' }.toString()
        }
        mWebView = HTML5WebView(this)
        mWebView?.webViewClient = object : WebViewClient() {

            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                view.loadUrl(url)
                return true
            }

            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
                setVisibilityProgressBar(false)
                progressBarWebView?.progress = 100
            }

            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                setVisibilityProgressBar(true)
                progressBarWebView?.progress = 0
            }
        }
        mWebView?.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                progressBarWebView.progress = newProgress
            }
        }
        mWebView?.loadUrl(strURLToLoad)
        linearContentLayout.addView(mWebView?.layout)
        setListener()
    }

    private fun setListener() {}
    override fun onBackPressed() {
        mWebView?.loadUrl("about:blank")
        finish()
    }

    public override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mWebView?.saveState(outState)
    }

    public override fun onStop() {
        super.onStop()
        mWebView?.stopLoading()
    }


}