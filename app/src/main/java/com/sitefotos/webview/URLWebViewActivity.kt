package com.sitefotos.webview

import android.os.Bundle
import android.view.LayoutInflater
import android.webkit.CookieManager
import android.webkit.WebStorage
import android.webkit.WebView
import com.sitefotos.BaseActivity
import com.sitefotos.appinterface.OnAppDataApiResponse

class URLWebViewActivity : BaseActivity() {
    var mWebView: URLHTML5WebView? = null
    private var strURLToLoad = ""
    private var title = ""
    private var useCachedData = false

    var inflater: LayoutInflater? = null

    override fun getApiCallBack(): OnAppDataApiResponse? {
        return null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        inflater = LayoutInflater.from(this)
        setActionBarVisibility(false)
        if (intent.hasExtra("link")) {
            strURLToLoad = intent.getStringExtra("link")?.trim { it <= ' ' }.toString()
        }

        if (intent.hasExtra("title")) {
            title = intent.getStringExtra("title")?.trim { it <= ' ' }.toString()
        }
        if (intent.hasExtra("useCached")) {
            useCachedData = intent.getBooleanExtra("useCached", false)
        }
       /* mWebView = URLHTML5WebView(this, useCachedData)
        mWebView?.tvTitle?.text = title
        mWebView?.webViewClient = object : WebViewClient() {

            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                view.loadUrl(url)
                return true
            }

            override fun onPageFinished(view: WebView, url: String) {
                super.onPageFinished(view, url)
                mWebView?.progressBar?.visibility = View.GONE
                mWebView?.progressBar?.progress = 100
            }

            override fun onPageStarted(view: WebView, url: String, favicon: Bitmap?) {
                super.onPageStarted(view, url, favicon)
                mWebView?.progressBar?.visibility = View.VISIBLE
                mWebView?.progressBar?.progress = 0
            }
        }
        mWebView?.webChromeClient = object : WebChromeClient() {
            override fun onProgressChanged(view: WebView, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                mWebView?.progressBar?.progress = newProgress
            }
        }
        mWebView?.loadUrl(strURLToLoad)
        linearContentLayout.addView(mWebView?.layout)*/

        /*Intent webViewIntent = new Intent(this, URLWebViewActivity.class);
        webViewIntent.putExtra("link", link);
        webViewIntent.putExtra("title", title);
        webViewIntent.putExtra("useCached", useCached);
        startActivity(webViewIntent);*/

        setListener()
    }

    private fun clearWebView(webView: WebView) {
        try {// Clear all the Application Cache, Web SQL Database and the HTML5 Web Storage
            WebStorage.getInstance().deleteAllData()

            // Clear all the cookies
            CookieManager.getInstance().removeAllCookies(null)
            CookieManager.getInstance().flush()

            webView.clearCache(true)
            webView.clearFormData()
            webView.clearHistory()
            webView.clearSslPreferences()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setListener() {
        mWebView?.imgBtnBack?.setOnClickListener {
            finish()
        }

    }

    override fun onBackPressed() {
        super.onBackPressed()
    }

    public override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        mWebView?.saveState(outState)
    }

    public override fun onStop() {
        super.onStop()
        mWebView?.stopLoading()
    }


}