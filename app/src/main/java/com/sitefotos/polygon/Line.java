package com.sitefotos.polygon;

/**
 * Line is defined by starting point and ending point on 2D dimension.<br>
 * 
 * <AUTHOR> (<EMAIL>)
 */
public class Line
{
	private final Point _start;
	private final Point _end;
	private double _a = Float.NaN;
	private double _b = Float.NaN;
	private boolean _vertical = false;

	public Line(Point start, Point end)
	{
		_start = start;
		_end = end;

		if (_end.mLatitude - _start.mLatitude != 0)
		{
			_a = ((_end.mLongitude - _start.mLongitude) / (_end.mLatitude - _start.mLatitude));
			_b = _start.mLongitude - _a * _start.mLatitude;
		}

		else
		{
			_vertical = true;
		}
	}

	/**
	 * Indicate whereas the point lays on the line.
	 * 
	 * @param point
	 *            - The point to check
	 * @return <code>True</code> if the point lays on the line, otherwise return <code>False</code>
	 */
	public boolean isInside(Point point)
	{
		double maxX = _start.mLatitude > _end.mLatitude ? _start.mLatitude : _end.mLatitude;
		double minX = _start.mLatitude < _end.mLatitude ? _start.mLatitude : _end.mLatitude;
		double maxY = _start.mLongitude > _end.mLongitude ? _start.mLongitude : _end.mLongitude;
		double minY = _start.mLongitude < _end.mLongitude ? _start.mLongitude : _end.mLongitude;

        return (point.mLatitude >= minX && point.mLatitude <= maxX) && (point.mLongitude >= minY && point.mLongitude <= maxY);
    }

	/**
	 * Indicate whereas the line is vertical. <br>
	 * For example, line like mLatitude=1 is vertical, in other words parallel to axis Y. <br>
	 * In this case the A is (+/-)infinite.
	 * 
	 * @return <code>True</code> if the line is vertical, otherwise return <code>False</code>
	 */
	public boolean isVertical()
	{
		return _vertical;
	}

	/**
	 * mLongitude = <b>A</b>mLatitude + B
	 * 
	 * @return The <b>A</b>
	 */
	public double getA()
	{
		return _a;
	}

	/**
	 * mLongitude = Ax + <b>B</b>
	 * 
	 * @return The <b>B</b>
	 */
	public double getB()
	{
		return _b;
	}

	/**
	 * Get start point
	 * 
	 * @return The start point
	 */
	public Point getStart()
	{
		return _start;
	}

	/**
	 * Get end point
	 * 
	 * @return The end point
	 */
	public Point getEnd()
	{
		return _end;
	}

	@Override
	public String toString()
	{
		return String.format("%s-%s", _start.toString(), _end.toString());
	}
}
