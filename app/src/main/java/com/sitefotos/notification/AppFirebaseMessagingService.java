package com.sitefotos.notification;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.SplashActivity;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.NotificationUtils;

public class AppFirebaseMessagingService extends FirebaseMessagingService {


    @Override
    public void onMessageReceived(@NonNull RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);

        String title = remoteMessage.getNotification().getTitle();
        String message = remoteMessage.getNotification().getBody();
        String url = remoteMessage.getData().get("url");

        //Show Notification if user is logged in
        if (!TextUtils.isEmpty(AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""))) {
            showNotification(this, title, message, url);
        }
    }

    @Override
    public void onNewToken(@NonNull String newToken) {
        super.onNewToken(newToken);
        AppPrefShared.putValue(Constants.USER_FCM_TOKEN, newToken);
    }


    /**
     * Set Push Notification for app.
     *
     * @param context of Activity or Fragment
     */
    private void showNotification(Context context, String title, String message, String url) {
        try {
            Intent notificationIntent;
            notificationIntent = new Intent(context, SplashActivity.class);
            notificationIntent.putExtra("notificationUrl", url);
            notificationIntent.putExtra("fromNotification", true);
            notificationIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);

            final int flag;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
                flag = PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE;
            }else{
                flag = PendingIntent.FLAG_UPDATE_CURRENT;
            }
            PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, notificationIntent, flag);
            // Set some dummy action on the Intent, otherwise extras are dropped.

            String channelId = getString(R.string.default_notification_channel_id);
            Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (notificationManager == null)
                return;

            // Since android Oreo notification channel is needed.
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (notificationManager.getNotificationChannel(channelId) == null) {
                    NotificationChannel channel = new NotificationChannel(channelId,
                            "Work",
                            NotificationManager.IMPORTANCE_HIGH);

                    channel.setDescription(message);
                    channel.enableLights(true);
                    channel.enableVibration(true);
                    channel.setShowBadge(true);

                    notificationManager.createNotificationChannel(channel);
                }
            }


            NotificationCompat.Builder notificationBuilder =
                    new NotificationCompat.Builder(context, channelId)
                            .setSmallIcon(R.mipmap.ic_launcher)
                            .setAutoCancel(true)
                            .setContentTitle(title)
                            .setContentText(message)
                            .setSound(defaultSoundUri)
                            .setContentIntent(pendingIntent)
                            .setPriority(Notification.PRIORITY_HIGH)
                            .setColor(ContextCompat.getColor(context, R.color.colorAccent))
                            .setChannelId(channelId);

            notificationManager.notify(Math.abs((int) System.currentTimeMillis()), notificationBuilder.build());
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }
}

