package com.sitefotos.notification;

import static android.content.Context.ACTIVITY_SERVICE;

import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;

import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.sitefotos.R;
import com.sitefotos.SplashActivity;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.NetworkUtil;
import com.sitefotos.util.NotificationUtils;

import java.util.List;

public class LocalNotificationReceiver extends BroadcastReceiver {

    public void onReceive(Context context, Intent intent) {
        showNotification(context);
    }

    public void showNotification(Context context) {
        try {
            Intent notificationIntent;
            if (!NotificationUtils.checkIfActivityInForeGround(context)) {
                notificationIntent = new Intent(context, SplashActivity.class);

                notificationIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
            } else {
                notificationIntent = new Intent();
            }
            final int flag = PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE;
            PendingIntent pendingIntent = PendingIntent.getActivity(context, 0, notificationIntent, flag);
            // Set some dummy action on the Intent, otherwise extras are dropped.

            String channelId = "Clock";
            Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

            NotificationManager notificationManager =
                    (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);

            if (notificationManager == null)
                return;

            // Since android Oreo notification channel is needed.
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                if (notificationManager.getNotificationChannel(channelId) == null) {
                    NotificationChannel channel = new NotificationChannel(channelId,
                            "Clock Reminder",
                            NotificationManager.IMPORTANCE_HIGH);

                    channel.setDescription(context.getString(R.string.local_notification_message));
                    channel.enableLights(true);
                    channel.enableVibration(true);
                    channel.setShowBadge(true);

                    notificationManager.createNotificationChannel(channel);
                }
            }


            NotificationCompat.Builder notificationBuilder =
                    new NotificationCompat.Builder(context, channelId)
                            .setSmallIcon(R.mipmap.ic_launcher)
                            .setAutoCancel(true)
                            .setContentTitle(context.getString(R.string.local_notification_title))
                            .setContentText(context.getString(R.string.local_notification_message))
                            .setSound(defaultSoundUri)
                            .setContentIntent(pendingIntent)
                            .setPriority(Notification.PRIORITY_HIGH)
                            .setColor(ContextCompat.getColor(context, R.color.colorAccent))
                            .setChannelId(channelId);

            notificationManager.notify(Math.abs((int) System.currentTimeMillis()), notificationBuilder.build());
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


}