package com.sitefotos.event;

import com.sitefotos.models.AppDataResponse;

import retrofit2.Response;

public class AppDataCallBackEvent {

    public Response<AppDataResponse> response;
    public Throwable throwable;
    public boolean isNoInternetConnection;

    public AppDataCallBackEvent(Response<AppDataResponse> response) {
        this.response = response;
    }

    public AppDataCallBackEvent(Throwable throwable) {
        this.throwable = throwable;

    }

    public AppDataCallBackEvent(boolean isNoInternetConnection) {
        this.isNoInternetConnection = isNoInternetConnection;
    }
}
