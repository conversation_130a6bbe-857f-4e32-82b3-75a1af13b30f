package com.sitefotos.event;

import android.view.View;

import com.sitefotos.models.Employees;

import org.json.JSONObject;

import java.util.List;

public class CrewSelectionEvent {

    private List<Employees> lstSelectedCrew;
    private List<Integer> lstDeSelectedEmployee;
    private List<Integer> lstNewSelected;
    private View crewView;
    private JSONObject object;

    public CrewSelectionEvent(JSONObject object, List<Employees> lstSelectedEmployee,
                              List<Integer> lstDeSelectedEmployee, List<Integer> lstNewSelected, View crewView) {
        this.object = object;
        this.lstSelectedCrew = lstSelectedEmployee;
        this.lstDeSelectedEmployee = lstDeSelectedEmployee;
        this.lstNewSelected = lstNewSelected;
        this.crewView = crewView;
    }

    public List<Employees> getLstSelectedCrew() {
        return lstSelectedCrew;
    }

    public void setLstSelectedCrew(List<Employees> lstSelectedCrew) {
        this.lstSelectedCrew = lstSelectedCrew;
    }

    public List<Integer> getLstDeSelectedEmployee() {
        return lstDeSelectedEmployee;
    }

    public void setLstDeSelectedEmployee(List<Integer> lstDeSelectedEmployee) {
        this.lstDeSelectedEmployee = lstDeSelectedEmployee;
    }

    public List<Integer> getLstNewSelected() {
        return lstNewSelected;
    }

    public void setLstNewSelected(List<Integer> lstNewSelected) {
        this.lstNewSelected = lstNewSelected;
    }

    public View getCrewView() {
        return crewView;
    }

    public void setCrewView(View crewView) {
        this.crewView = crewView;
    }

    public JSONObject getObject() {
        return object;
    }

    public void setObject(JSONObject object) {
        this.object = object;
    }
}
