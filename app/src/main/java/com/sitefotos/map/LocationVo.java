package com.sitefotos.map;

import java.io.Serializable;

public class LocationVo implements Serializable {

    public String addNewPropertyAddress;
    public String addNewPropertyCity;
    public String addNewPropertyState;
    public String addNewPropertyCountry;
    public String addNewPropertyZipCode;
    public String addNewPropertyLat;
    public String addNewPropertyLng;
    public String addNewPropertyEmails;

    public String getAddNewPropertyGeoLocation() {
        return addNewPropertyGeoLocation;
    }

    public void setAddNewPropertyGeoLocation(String addNewPropertyGeoLocation) {
        this.addNewPropertyGeoLocation = addNewPropertyGeoLocation;
    }

    public String addNewPropertyGeoLocation;

    public int addNewPropertyAutoShare;


    public String getAddNewPropertyLng() {
        return addNewPropertyLng;
    }

    public void setAddNewPropertyLng(String addNewPropertyLng) {
        this.addNewPropertyLng = addNewPropertyLng;
    }

    public int getAddNewPropertyAutoShare() {
        return addNewPropertyAutoShare;
    }

    public void setAddNewPropertyAutoShare(int addNewPropertyAutoShare) {
        this.addNewPropertyAutoShare = addNewPropertyAutoShare;
    }

    public String getAddNewPropertyLat() {
        return addNewPropertyLat;
    }

    public void setAddNewPropertyLat(String addNewPropertyLat) {
        this.addNewPropertyLat = addNewPropertyLat;
    }

    public String getAddNewPropertyEmails() {
        return addNewPropertyEmails;
    }

    public void setAddNewPropertyEmails(String addNewPropertyEmails) {
        this.addNewPropertyEmails = addNewPropertyEmails;
    }

    public String getAddNewPropertyCity() {
        return addNewPropertyCity;
    }

    public void setAddNewPropertyCity(String addNewPropertyCity) {
        this.addNewPropertyCity = addNewPropertyCity;
    }

    public String getAddNewPropertyState() {
        return addNewPropertyState;
    }

    public void setAddNewPropertyState(String addNewPropertyState) {
        this.addNewPropertyState = addNewPropertyState;
    }

    public String getAddNewPropertyAddress() {
        return addNewPropertyAddress;
    }

    public void setAddNewPropertyAddress(String addNewPropertyAddress) {
        this.addNewPropertyAddress = addNewPropertyAddress;
    }

    public String getAddNewPropertyCountry() {
        return addNewPropertyCountry;
    }

    public void setAddNewPropertyCountry(String addNewPropertyCountry) {
        this.addNewPropertyCountry = addNewPropertyCountry;
    }

    public String getAddNewPropertyZipCode() {
        return addNewPropertyZipCode;
    }

    public void setAddNewPropertyZipCode(String addNewPropertyZipCode) {
        this.addNewPropertyZipCode = addNewPropertyZipCode;
    }
}
