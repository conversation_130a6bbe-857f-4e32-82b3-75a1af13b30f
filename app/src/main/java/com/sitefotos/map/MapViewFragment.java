package com.sitefotos.map;

import android.animation.LayoutTransition;
import android.graphics.Bitmap;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;

import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.MapView;
import com.google.android.gms.maps.MapsInitializer;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.databinding.FragmentMapBinding;
import com.sitefotos.interfaces.OnMarkerPinSelected;
import com.sitefotos.models.MapPinData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblMapPinData;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.views.CustomInfoWindowAdapter;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.List;

public class MapViewFragment extends DialogFragment implements View.OnClickListener {

    MapView mapView;
    private GoogleMap googleMap;
    double mCurrentLatitude, mCurrentLongitude;
    public double updatedExtraLatitude, updatedExtraLongitude;
    private Marker marker;
    boolean isFirstTime = true;
    public FragmentMapBinding binding;
    public boolean plotOnMap;
    private List<MapPinData> lstPinData = null;
    public MapPinData selectedPinData;
    private String imageData;
    private String mapPinRootPath;
    private boolean fullScreenMode;
    private Bitmap fakeMapBitmap;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        binding = FragmentMapBinding.inflate(inflater, container, false);
        mapView = binding.mapView;
        mapView.onCreate(savedInstanceState);

        LayoutTransition layoutTransition = new LayoutTransition();
        layoutTransition.enableTransitionType(LayoutTransition.CHANGING);
        binding.rlMapLocatorView.setLayoutTransition(layoutTransition);
        setonClickListener();
        mapView.onResume(); // needed to get the map to display immediately
        try {
            if (getActivity() != null) {
                MapsInitializer.initialize(getActivity().getApplicationContext());
                mapPinRootPath = ImageUtil.getMapPinRootFolder(getActivity());
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        mapView.getMapAsync(mMap -> {
            googleMap = mMap;
            googleMap.setMapType(GoogleMap.MAP_TYPE_SATELLITE);
            googleMap.getUiSettings().setMapToolbarEnabled(false);
            googleMap.setOnMapClickListener(latLng -> {
                clearAndResetPin(latLng);
            });


        });
        return binding.getRoot();
    }

    public void addAllMarker(String imageData, boolean shouldCheckSelectedPin) {
        if (plotOnMap) {
            JSONObject jsonImageData;
            JSONArray jsonImageDataArray;
            try {
                jsonImageData = new JSONObject(imageData);
                jsonImageDataArray = jsonImageData.getJSONArray(Constants.DATA);
                if (jsonImageDataArray.length() > 0) {
                    for (int i = 0; i < jsonImageDataArray.length(); i++) {
                        JSONObject imageDataObject = jsonImageDataArray.getJSONObject(i);
                        Location location = ImageUtil.getLocationFromImage(imageDataObject.get(Constants.IMAGEPATHLOW).toString());
                        if (location != null && getActivity() != null) {
                            if (imageDataObject.has(Constants.MAP_PIN_URL) && !TextUtils.isEmpty(imageDataObject.getString(Constants.MAP_PIN_URL))) {
                                googleMap.addMarker(new MarkerOptions()
                                        .position(new LatLng(location.getLatitude(), location.getLongitude()))
                                        .title(imageDataObject.get(Constants.IMAGEPATHLOW).toString())
                                        .icon(ImageUtil.getMarkerBitmapFromPath(mapPinRootPath.concat(new File(imageDataObject.getString(Constants.MAP_PIN_URL)).getName()))
                                        ));
                                googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(location.getLatitude(), location.getLongitude()), 19));
                                googleMap.setInfoWindowAdapter(new CustomInfoWindowAdapter(getActivity()));
                            }

                            //select last pin if no pin is yet selected from chooser
                            if (selectedPinData == null && (i == jsonImageDataArray.length() - 1)) {
                                if (imageDataObject.has(Constants.MAP_PIN_URL) && !TextUtils.isEmpty(imageDataObject.getString(Constants.MAP_PIN_URL))) {
                                    ImageUtil.loadActualImageInGlide(binding.ivPin, mapPinRootPath.concat(new File(imageDataObject.getString(Constants.MAP_PIN_URL)).getName()));
                                }
                                for (MapPinData mapPinData : lstPinData) {
                                    if (imageDataObject.has(Constants.MAP_PIN_URL) && !TextUtils.isEmpty(imageDataObject.getString(Constants.MAP_PIN_URL)) && mapPinData.getPngURL().equalsIgnoreCase(imageDataObject.getString(Constants.MAP_PIN_URL))) {
                                        selectedPinData = mapPinData;
                                        mapPinData.setSelected(true);
                                        break;
                                    }
                                }
                                if (selectedPinData == null) {
                                    if (imageDataObject.has(Constants.MAP_PIN_URL) && !TextUtils.isEmpty(imageDataObject.getString(Constants.MAP_PIN_URL))) {
                                        selectedPinData = new MapPinData();
                                        selectedPinData.setPngURL(imageDataObject.getString(Constants.MAP_PIN_URL));
                                    } else {
                                        if (lstPinData != null && !lstPinData.isEmpty()) {
                                            selectedPinData = lstPinData.get(0);
                                            selectedPinData.setSelected(true);
                                            ImageUtil.loadActualImageInGlide(binding.ivPin, mapPinRootPath.concat(new File(selectedPinData.getPngURL()).getName()));
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else if (shouldCheckSelectedPin) {
                    if (lstPinData != null && !lstPinData.isEmpty()) {
                        selectedPinData = lstPinData.get(0);
                        selectedPinData.setSelected(true);
                        ImageUtil.loadActualImageInGlide(binding.ivPin, mapPinRootPath.concat(new File(selectedPinData.getPngURL()).getName()));
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

    }

    private void clearAndResetPin(LatLng latLng) {
        googleMap.clear();
        addAllMarker(imageData, false);
        updateMarker(latLng);
    }

    private void setonClickListener() {
        binding.rlPin.setOnClickListener(this);
    }

    public void AnimateCameraToLocationWhenVisible() {
        if (mCurrentLatitude != 0.0 && mCurrentLongitude != 0.0) {
            if (isFirstTime) {
                LatLng latLng = new LatLng(mCurrentLatitude, mCurrentLongitude);
                animateMapToLocation(latLng, 19);
                isFirstTime = false;
            }
        }
    }

    private void animateMapToLocation(LatLng currentMapLatlng, int zoomLevel) {
        if (isFirstTime) {
            CameraPosition cameraPosition = new CameraPosition.Builder().target(currentMapLatlng).zoom(zoomLevel).build();
            googleMap.animateCamera(CameraUpdateFactory.newCameraPosition(cameraPosition));
            isFirstTime = false;
        }
    }

    private void updateMarker(LatLng latLng) {
        mCurrentLatitude = latLng.latitude;
        mCurrentLongitude = latLng.longitude;
        AppPrefShared.putValue(Constants.EXTRA_LOCATION_LATITUDE, String.valueOf(mCurrentLatitude));
        AppPrefShared.putValue(Constants.EXTRA_LOCATION_LONGITUDE, String.valueOf(mCurrentLongitude));
        updatedExtraLatitude = mCurrentLatitude;
        updatedExtraLongitude = mCurrentLongitude;
        BitmapDescriptor bitmapDescriptor;
        if (plotOnMap && marker == null && !TextUtils.isEmpty(AppPrefShared.getString(Constants.PARAM_DEFAULT_MAP_PIN_URL, ""))) {
            bitmapDescriptor = ImageUtil.getMarkerBitmapFromPath(mapPinRootPath.concat(new File(AppPrefShared.getString(Constants.PARAM_DEFAULT_MAP_PIN_URL, "")).getName()));
        } else if (plotOnMap && selectedPinData != null) {
            bitmapDescriptor = ImageUtil.getMarkerBitmapFromPath(mapPinRootPath.concat(new File(selectedPinData.getPngURL()).getName()));
        } else {
            bitmapDescriptor = BitmapDescriptorFactory.fromResource(R.drawable.icn_marker_blue);
        }
        marker = googleMap.addMarker(new MarkerOptions()
                .position(latLng)
                .icon(bitmapDescriptor));
        googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(latLng, 19));
        readyBitmapFromMap();
    }


    @Override
    public void onResume() {
        super.onResume();
        if (mapView != null)
            mapView.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
        if (mapView != null)
            mapView.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (mapView != null)
            mapView.onDestroy();
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        if (mapView != null)
            mapView.onLowMemory();
    }

    public void resetMap(double currentLatitude, double currentLongitude) {
        if (googleMap != null) {
            if (currentLatitude != 0.0 && currentLongitude != 0.0) {
                this.mCurrentLatitude = currentLatitude;
                this.mCurrentLongitude = currentLongitude;
                LatLng currentMapLatlng = new LatLng(mCurrentLatitude, mCurrentLongitude);
                animateMapToLocation(currentMapLatlng, 19);
            }

        }
    }

    public void setPlotOnMap(boolean plotOnMap) {
        this.plotOnMap = plotOnMap;
        if (plotOnMap) {
            getPinData();
        }
    }

    private void getPinData() {
        if (getActivity() != null) {
            TblMapPinData tblMapPinData = new TblMapPinData(getActivity());
            lstPinData = tblMapPinData.getAllPinData();
        }
    }

    public void setNullMarker(boolean removeMarker) {
        isFirstTime = true;
        if (removeMarker) {
            if (marker != null) {
                marker.remove();
                marker = null;
                googleMap.clear();
            }
        }
    }

    public void showNextButton() {
        binding.tvBack.setVisibility(View.GONE);
        binding.tvNext.setVisibility(View.VISIBLE);
    }

    public void showBackButton() {
        binding.tvBack.setVisibility(View.VISIBLE);
        binding.tvNext.setVisibility(View.GONE);
    }

    @Override
    public void onClick(View view) {

        int viewId = view.getId();
        if (viewId == R.id.rlPin) {
            binding.rlPin.setEnabled(false);
            new Handler().postDelayed(() -> {
                try {
                    binding.rlPin.setEnabled(true);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                    binding.rlPin.setEnabled(true);
                }
            }, 300);
            showMarkerChooserOption();
        }
    }


    private void showMarkerChooserOption() {
        if (!isDataSelectedInList()) {
            for (MapPinData mapPinData : lstPinData) {
                if (selectedPinData != null && selectedPinData.getId() == mapPinData.getId()) {
                    mapPinData.setSelected(true);
                    break;
                }
            }
        }
        PopUtils.ShowMarkerPins(getActivity(), lstPinData, new OnMarkerPinSelected() {
            @Override
            public void onSelected(List<MapPinData> lstPin) {
                lstPinData = lstPin;
            }

            @Override
            public void onItemSelected(boolean isSelected, MapPinData mapPinData) {
                if (getActivity() != null) {
                    selectedPinData = mapPinData;
                    File fileName = new File(mapPinData.getPngURL());
                    ImageUtil.loadActualImageInGlide(binding.ivPin, mapPinRootPath.concat(fileName.getName()));
                    if (updatedExtraLatitude != 0.0 && updatedExtraLongitude != 0.0) {
                        clearAndResetPin(new LatLng(updatedExtraLatitude, updatedExtraLongitude));
                    }
                }
            }
        });
    }


    public void checkAndShowMapChooserView() {
        if (plotOnMap) {
            binding.rlPin.setVisibility(View.VISIBLE);
        } else {
            binding.rlPin.setVisibility(View.GONE);
        }
    }

    public boolean isMarkerAdded() {
        return marker != null;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }

    public boolean isDataSelectedInList() {
        for (MapPinData mapPinData : lstPinData) {
            if (mapPinData.isSelected()) {
                return true;
            }
        }
        return false;
    }

    public boolean isFullScreen() {
        return fullScreenMode;
    }

    public void setFullScreenMode(boolean isFullScreen) {
        fullScreenMode = isFullScreen;
    }

    public void readyBitmapFromMap() {
        try {
            new Handler().postDelayed(() -> googleMap.snapshot(bitmap -> {
                fakeMapBitmap = bitmap;
            }), 500);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }

    }

    public void setFakeMapView() {
        binding.ivFakeView.setVisibility(View.VISIBLE);
        binding.ivFakeView.setImageBitmap(fakeMapBitmap);
    }
}
