package com.sitefotos.map;

import static com.sitefotos.Constants.LOCATION_PERMISSION_REQUEST;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.PLACE_AUTOCOMPLETE_REQUEST_CODE;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.os.Bundle;
import android.os.Handler;
import android.os.ResultReceiver;
import android.view.LayoutInflater;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;

import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationResult;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.LocationSettingsRequest;
import com.google.android.gms.location.SettingsClient;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.libraries.places.api.model.Place;
import com.google.android.libraries.places.widget.Autocomplete;
import com.google.android.libraries.places.widget.model.AutocompleteActivityMode;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.sitefotos.BaseActivity;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.service.FetchAddressIntentService;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PopUtils;

import java.util.Arrays;
import java.util.List;

public class PropertyMapActivity extends BaseActivity implements OnMapReadyCallback, View.OnClickListener {

    protected static final String ADDRESS_REQUESTED_KEY = "address-request-pending";
    protected static final String LOCATION_ADDRESS_KEY = "location-address";

    private FusedLocationProviderClient fusedLocationClient;
    private LocationRequest locationRequest;
    private LocationCallback locationCallback;

    /**
     * Represents a geographical location.
     */
    protected Location mLastLocation;

    public Context context;
    public LayoutInflater inflater;
    public View view;
    public FloatingActionButton fabMapActivityAddProperty;
    public GoogleMap gMap;
    public String addNewPropertyAddress, addNewPropertyCity, addNewPropertyState,
            addNewPropertyZipCode, addNewPropertyCountry, geoLocation = "";
    private LocationVo obj;
    public Marker marker;
    private double currentLatitude = 0.0;
    private double currentLongitude = 0.0;
    private boolean isLocationDetectFirstTime = true;
    private AppCompatImageButton imgBtnBack;
    private AppCompatTextView tvTitle;
    private boolean isFormComponent;
    private int tagId;

    /**
     * Tracks whether the user has requested an address. Becomes true when the user requests an
     * address and false when the address (or an error message) is delivered.
     * The user requests an address by pressing the Fetch Address button. This may happen
     * before GoogleApiClient connects. This activity uses this boolean to keep track of the
     * user's intent. If the value is true, the activity tries to fetch the address as soon as
     * GoogleApiClient connects.
     */
    protected boolean mAddressRequested;

    /**
     * The formatted location address.
     */
    protected String mAddressOutput;

    //Receiver registered with this activity to get the response from FetchAddressIntentService.
    private AddressResultReceiver mResultReceiver;
    public boolean isLocationDialogShown = false;
    public Location currentLocationUser = null;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
//        setContentView(R.layout.layout_map_activity);
        initLocationService();
        setActionBarVisibility(false);
        init();
        setListeners();
        setTitle(null);

        mResultReceiver = new AddressResultReceiver(new Handler());
        // Set defaults, then update using values stored in the Bundle.
        mAddressRequested = false;
        mAddressOutput = "";
        updateValuesFromBundle(savedInstanceState);
        mAddressRequested = true;
    }


    @Override
    public void onSaveInstanceState(Bundle savedInstanceState) {
        // Save whether the address has been requested.
        savedInstanceState.putBoolean(ADDRESS_REQUESTED_KEY, mAddressRequested);

        // Save the address string.
        savedInstanceState.putString(LOCATION_ADDRESS_KEY, mAddressOutput);
        super.onSaveInstanceState(savedInstanceState);
    }

    public void init() {
        context = this;
        inflater = LayoutInflater.from(this);
        view = this.inflater.inflate(R.layout.layout_map_activity, getMiddleContent());
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.map);
        if (mapFragment != null)
            mapFragment.getMapAsync(this);
        fabMapActivityAddProperty = findViewById(R.id.fabMapActivityAddProperty);

        if (getIntent() != null) {
            if (getIntent().hasExtra(Constants.IS_FORM_COMPONENT)) {
                isFormComponent = getIntent().getBooleanExtra(Constants.IS_FORM_COMPONENT, false);
            }
            if (getIntent().hasExtra(Constants.TAG_ID)) {
                tagId = getIntent().getIntExtra(Constants.TAG_ID, 0);
            }
        }

        imgBtnBack = findViewById(R.id.imgBtnBack);
        tvTitle = findViewById(R.id.tvTitle);
    }

    public void setListeners() {
        fabMapActivityAddProperty.setOnClickListener(this);
        imgBtnBack.setOnClickListener(this);
        tvTitle.setOnClickListener(this);
    }

    public void initLocationService() {
        fusedLocationClient = LocationServices.getFusedLocationProviderClient(this);
        locationCallback = new LocationCallback() {
            @Override
            public void onLocationResult(LocationResult locationResult) {
                mLastLocation = locationResult.getLastLocation();
                if (mLastLocation != null) {
                    if (isLocationDetectFirstTime) {
                        // Determine whether a Geocoder is available.
                        if (!Geocoder.isPresent()) {
                            showForeGroundToastLong(getString(R.string.no_geocoder_available));
                            return;
                        }
                        // It is possible that the user presses the button to get the address before the GoogleApiClient object successfully connects.
                        // In such a case, mAddressRequested is set to true, but no attempt is made to fetch the address (see fetchAddressButtonHandler()).
                        // Instead, we start the intent service here if the user has requested an address, since we now have a connection to GoogleApiClient.
                        if (mAddressRequested) {
                            startIntentService();
                        }
                        currentLatitude = mLastLocation.getLatitude();
                        currentLongitude = mLastLocation.getLongitude();
                        setMarkerOnMap();
                        isLocationDetectFirstTime = false;
                    } else {
                        currentLatitude = mLastLocation.getLatitude();
                        currentLongitude = mLastLocation.getLongitude();
                    }
                    currentLocationUser = mLastLocation;
                }
            }
        };
        if (PermissionUtils.hasPermissions(this, PermissionUtils.getLocationPermissions())) {
            if (canGetLocation(this)) {
                locationRequest = setLocationRequest(this);
            } else {
                showGPSSettingAlert();
            }
        }
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.fabMapActivityAddProperty) {
            Intent intent = new Intent(context, NewPropertyActivity.class);
            if (obj != null) {
                intent.putExtra(Constants.KEY_LOCATION, obj);
                intent.putExtra(Constants.IS_FORM_COMPONENT, isFormComponent);
                startActivityForResult(intent, Constants.ADD_NEW_PROPERTY_REQUEST);
                overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
            }
        } else if (viewId == R.id.imgBtnBack) {
            super.onBackPressed();
        } else if (viewId == R.id.tvTitle) {
            findPlace(view);
        }
    }

    public void showGPSSettingAlert() {
        PopUtils.displayLocationSettingsRequest(this, LOCATION_REQUEST_CODE);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.enter_from_left, R.anim.exit_to_right);
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        gMap = googleMap;
        gMap.setMapType(GoogleMap.MAP_TYPE_SATELLITE);
        setMarkerOnMap();
    }

    private void setMarkerOnMap() {
        LatLng latlngCurrentPlace = new LatLng(currentLatitude, currentLongitude);
        marker = gMap.addMarker(new MarkerOptions().position(latlngCurrentPlace).draggable(true));
        gMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latlngCurrentPlace, 19));
        if (PermissionUtils.hasPermissions(this, PermissionUtils.getLocationPermissions())) {
            implementGoogleMapListeners();
        } else {
            if (!isLocationDialogShown) {
                requestLocationPermission();
                isLocationDialogShown = true;
            }
        }
        setMarkerDragListener();
        gMap.getFocusedBuilding();
    }

    private void setMarkerDragListener() {
        gMap.setOnMarkerDragListener(new GoogleMap.OnMarkerDragListener() {
            @Override
            public void onMarkerDragStart(Marker marker) {
            }

            @Override
            public void onMarkerDrag(Marker marker) {
            }

            @Override
            public void onMarkerDragEnd(Marker marker) {
                Location current = new Location("");
                current.setLatitude(marker.getPosition().latitude);
                current.setLongitude(marker.getPosition().longitude);
                current.setAccuracy(3333);
                current.setBearing(333);
                mLastLocation = current;
                startIntentService();
                mAddressRequested = true;
            }
        });
    }

    /**
     * Updates fields based on data stored in the bundle.
     */
    private void updateValuesFromBundle(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            // Check savedInstanceState to see if the address was previously requested.
            if (savedInstanceState.keySet().contains(ADDRESS_REQUESTED_KEY)) {
                mAddressRequested = savedInstanceState.getBoolean(ADDRESS_REQUESTED_KEY);
            }
            // Check savedInstanceState to see if the location address string was previously found
            // and stored in the Bundle. If it was found, display the address string in the UI.
            if (savedInstanceState.keySet().contains(LOCATION_ADDRESS_KEY)) {
                mAddressOutput = savedInstanceState.getString(LOCATION_ADDRESS_KEY);
            }
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        initLocationService();
        startLocationService(fusedLocationClient, locationRequest, locationCallback);
    }


    /**
     * Creates an intent, adds location data to it as an extra, and starts the intent service for
     * fetching an address.
     */
    protected void startIntentService() {
        Intent intent = new Intent(getApplicationContext(), FetchAddressIntentService.class);
        intent.putExtra(Constants.RECEIVER, mResultReceiver);
        intent.putExtra(Constants.LOCATION_DATA_EXTRA, mLastLocation);
        startService(intent);
    }

    /**
     * Receiver for data sent from FetchAddressIntentService.
     */
    class AddressResultReceiver extends ResultReceiver {
        public AddressResultReceiver(Handler handler) {
            super(handler);
        }

        /**
         * Receives data sent from FetchAddressIntentService and updates the UI in MainActivity.
         */
        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            // Display the address string or an error message sent from the intent service.
            // Show a toast message if an address was found.
            if (resultCode == Constants.SUCCESS_RESULT) {
                Address address = resultData.getParcelable(Constants.RESULT_DATA_KEY_ADDRESS);
                tvTitle.setText(getCurrentAddressMakerTitle(address));
                marker.setTitle(getCurrentAddressMakerTitle(address));
                marker.showInfoWindow();
                setObject(address, marker);
            } else if (resultCode == Constants.FAILURE_RESULT) {
                PopUtils.showAlertDialogPositiveButtonOnly(PropertyMapActivity.this, getString(R.string.error_title), resultData.getString(Constants.RESULT_DATA_KEY));
                tvTitle.setText(getCurrentAddressMakerTitle(null));
                marker.setTitle(" ");
                marker.showInfoWindow();
                setObject(null, marker);
            }
            // Reset. Enable the Fetch Address button and stop showing the progress bar.
            mAddressRequested = false;
        }

    }

    public void findPlace(View view) {
        try {
            List<Place.Field> fields = Arrays.asList(Place.Field.ID, Place.Field.NAME, Place.Field.LAT_LNG, Place.Field.ADDRESS);
            Intent intent = new Autocomplete.IntentBuilder(AutocompleteActivityMode.FULLSCREEN, fields).build(this);
            startActivityForResult(intent, PLACE_AUTOCOMPLETE_REQUEST_CODE);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void setObject(Address address, Marker marker) {
        if (address != null) {
            addNewPropertyAddress = getCurrentAddress(address);
            addNewPropertyCity = address.getLocality();
            addNewPropertyState = address.getAdminArea();
            addNewPropertyZipCode = address.getPostalCode();
            addNewPropertyCountry = address.getCountryName();
            if (marker != null) {
                geoLocation = "(".concat(String.valueOf(marker.getPosition().latitude - 0.0001))
                        + " , ".concat(String.valueOf(marker.getPosition().longitude + 0.0001))
                        + "),(".concat(String.valueOf(marker.getPosition().latitude + 0.0001))
                        + " , ".concat(String.valueOf(marker.getPosition().longitude + 0.0001))
                        + "),(".concat(String.valueOf(marker.getPosition().latitude + 0.0001))
                        + " , ".concat(String.valueOf(marker.getPosition().longitude - 0.0001))
                        + "),(".concat(String.valueOf(marker.getPosition().latitude - 0.0001))
                        + " , ".concat(String.valueOf(marker.getPosition().longitude - 0.0001))
                        + ")";
            } else {
                double currentLat = currentLatitude;
                double currentLong = currentLongitude;
                geoLocation = "(".concat(String.valueOf(currentLat - 0.0001))
                        + " , ".concat(String.valueOf(currentLong + 0.0001))
                        + "),(".concat(String.valueOf(currentLat + 0.0001))
                        + " , ".concat(String.valueOf(currentLong + 0.0001))
                        + "),(".concat(String.valueOf(currentLat + 0.0001))
                        + " , ".concat(String.valueOf(currentLong - 0.0001))
                        + "),(".concat(String.valueOf(currentLat - 0.0001))
                        + " , ".concat(String.valueOf(currentLong - 0.0001))
                        + ")";
            }

            obj = new LocationVo();
            obj.setAddNewPropertyCity(addNewPropertyCity);
            obj.setAddNewPropertyAddress(addNewPropertyAddress);
            obj.setAddNewPropertyState(addNewPropertyState);
            obj.setAddNewPropertyCountry(addNewPropertyCountry);
            obj.setAddNewPropertyZipCode(addNewPropertyZipCode);
            obj.setAddNewPropertyEmails("");
            if (marker != null) {
                obj.setAddNewPropertyLat(String.valueOf(marker.getPosition().latitude));
                obj.setAddNewPropertyLng(String.valueOf(marker.getPosition().longitude));
            } else {
                obj.setAddNewPropertyLat(String.valueOf(currentLatitude));
                obj.setAddNewPropertyLng(String.valueOf(currentLongitude));
            }
            obj.setAddNewPropertyGeoLocation(geoLocation);
            obj.setAddNewPropertyAutoShare(2);
        }
    }

    public String getCurrentAddress(Address addresses) {
        String address1 = "", address2 = "";

        if (addresses != null) {
            address1 = addresses.getAddressLine(0);
            address2 = addresses.getAddressLine(1);
            if (address1 != null && !address1.trim().isEmpty()) {
                addNewPropertyAddress = address1;
            }
            if (address2 != null && !address2.trim().isEmpty()) {
                addNewPropertyAddress = addNewPropertyAddress + " , " + address2;
            }
        } else {
            addNewPropertyAddress = "";
        }
        return addNewPropertyAddress;
    }

    public String getCurrentAddressMakerTitle(Address addresses) {
        if (addresses != null) {
            String strLocal = getCurrentAddress(addresses);
            String strCity = addresses.getLocality();
            String strState = addresses.getAdminArea();
            String strZipcode = addresses.getPostalCode();
            String strCountry = addresses.getCountryName();

            String strAddress = "";
            if (strLocal != null && !strLocal.trim().isEmpty()) {
                strAddress = strAddress + "" + strLocal;
            }

            if (strCity != null && !strCity.trim().isEmpty()) {
                strAddress = strAddress + ", " + strCity;
            }

            if (strState != null && !strState.trim().isEmpty()) {
                strAddress = strAddress + ", " + strState;
            }

            if (strCountry != null && !strCountry.trim().isEmpty()) {
                strAddress = strAddress + ", " + strCountry;
            }

            if (strZipcode != null && !strZipcode.trim().isEmpty()) {
                strAddress = strAddress + ", " + strZipcode;
            }

            addNewPropertyAddress = strAddress;
            return addNewPropertyAddress;
        } else {
            addNewPropertyAddress = "";
            return addNewPropertyAddress;
        }
    }

    /**
     * Check location permission. show required permission dialog if user set permanent denied
     */
    public void requestLocationPermission() {
        if (PermissionUtils.shouldShowRequestPermissions(this, PermissionUtils.getLocationPermissions())) {
            PermissionUtils.requestPermission(this, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
        } else {
            if (!PermissionUtils.hasPermissions(this, PermissionUtils.getLocationPermissions())) {
                PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to),
                        getString(R.string.open), getString(R.string.txt_cancel), false,
                        (dialog, which) -> PermissionUtils.navigateUserToPermissionScreen(this), (dialog, which) -> {
                        });
            }
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        switch (requestCode) {
            case LOCATION_PERMISSION_REQUEST:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    initLocationService();
                    startLocationService(fusedLocationClient, locationRequest, locationCallback);
                } else {
                    PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to),
                            getString(R.string.open), getString(R.string.txt_cancel), false,
                            (dialog, which) -> {
                                PermissionUtils.requestPermission(this, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
                            }, (dialog, which) -> {

                            });
                }
                break;
        }
    }

    private void implementGoogleMapListeners() {
        gMap.setIndoorEnabled(false);
        gMap.setMyLocationEnabled(true);
        gMap.setOnMyLocationButtonClickListener(() -> {
            if (PermissionUtils.hasPermissions(PropertyMapActivity.this, PermissionUtils.getLocationPermissions())) {
                if (marker != null) {
                    marker.remove();
                }
                Location locationCurrent = currentLocationUser;
                if (locationCurrent != null) {
                    LatLng latlngCurrentPlace = new LatLng(locationCurrent.getLatitude(), locationCurrent.getLongitude());
                    marker = gMap.addMarker(new MarkerOptions().position(latlngCurrentPlace).draggable(true));
                    gMap.moveCamera(CameraUpdateFactory.newLatLngZoom(latlngCurrentPlace, 20));
                    mLastLocation = locationCurrent;
                    startIntentService();
                    mAddressRequested = true;
                }
            }
            return true;
        });
        setMarkerDragListener();
        gMap.getFocusedBuilding();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case PLACE_AUTOCOMPLETE_REQUEST_CODE:
                if (resultCode == RESULT_OK) {
                    Place place = Autocomplete.getPlaceFromIntent(data);
                    tvTitle.setText(place.getAddress());
                    gMap.moveCamera(CameraUpdateFactory.newLatLngZoom(place.getLatLng(), 20));
                    if (marker != null) {
                        marker.remove();
                    }
                    try {
                        marker = gMap.addMarker(new MarkerOptions().position(place.getLatLng()).draggable(true));
                        Location current = new Location("");
                        current.setLatitude(marker.getPosition().latitude);
                        current.setLongitude(marker.getPosition().longitude);
                        current.setAccuracy(3333);
                        current.setBearing(333);
                        mLastLocation = current;
                        startIntentService();
                        mAddressRequested = true;
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
                break;
            case Constants.ADD_NEW_PROPERTY_REQUEST:
                if (resultCode == Activity.RESULT_OK && data.getExtras() != null) {
                    Intent returnIntent = new Intent();
                    returnIntent.putExtra(Constants.KEY_INTENT_RETURN_DATA, data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA));
                    returnIntent.putExtra(Constants.KEY_INTENT_ADDRESS, data.getExtras().getString(Constants.KEY_INTENT_ADDRESS));
                    if (isFormComponent) {
                        returnIntent.putExtra(Constants.PARAM_STATE, data.getExtras().getString(Constants.PARAM_STATE));
                        returnIntent.putExtra(Constants.PARAM_LAT, data.getExtras().getString(Constants.PARAM_LAT));
                        returnIntent.putExtra(Constants.PARAM_LON, data.getExtras().getString(Constants.PARAM_LON));
                        returnIntent.putExtra(Constants.PARAM_ZIP, data.getExtras().getString(Constants.PARAM_ZIP));
                        returnIntent.putExtra(Constants.PARAM_STREET_ADDRESS, data.getExtras().getString(Constants.PARAM_STREET_ADDRESS));
                        returnIntent.putExtra(Constants.PARAM_CITY, data.getExtras().getString(Constants.PARAM_CITY));
                        returnIntent.putExtra(Constants.TAG_ID, tagId);
                    }
                    returnIntent.putExtra(Constants.IS_FORM_COMPONENT, isFormComponent);
                    setResult(Activity.RESULT_OK, returnIntent);
                    finish();
                }
                break;
            case LOCATION_REQUEST_CODE:
                if (resultCode == RESULT_OK) {
                    initLocationService();
                    startLocationService(fusedLocationClient, locationRequest, locationCallback);
                }
                break;

        }
    }

    @Override
    protected void onStop() {
        stopLocationService(fusedLocationClient, locationCallback);
        super.onStop();
    }


    /**
     * Common method to set Location initial parameters with its default settings
     *
     * @param activity activity
     * @return LocationRequest
     */
    public LocationRequest setLocationRequest(Activity activity) {
        LocationRequest locationRequest = new LocationRequest();
        locationRequest.setPriority(LocationRequest.PRIORITY_HIGH_ACCURACY);
        /* 3 secs */
        long UPDATE_INTERVAL = 3 * 1000;
        locationRequest.setInterval(UPDATE_INTERVAL);
        /* 1 sec */
        long FASTEST_INTERVAL = 1000;
        locationRequest.setFastestInterval(FASTEST_INTERVAL);
        LocationSettingsRequest.Builder builder = new LocationSettingsRequest.Builder();
        builder.addLocationRequest(locationRequest);
        LocationSettingsRequest locationSettingsRequest = builder.build();
        SettingsClient settingsClient = LocationServices.getSettingsClient(activity);
        settingsClient.checkLocationSettings(locationSettingsRequest);
        return locationRequest;
    }
}
