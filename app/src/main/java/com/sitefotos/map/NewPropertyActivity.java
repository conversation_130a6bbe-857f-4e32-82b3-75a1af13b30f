package com.sitefotos.map;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ScrollView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.NewPropertyErrorResponse;
import com.sitefotos.models.NewPropertyResponse;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.regex.Pattern;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class NewPropertyActivity extends BaseActivity implements View.OnClickListener {

    public Context context;

    private View view;

    private EditText edtNewPropertyActivityEnterPropertyName,
            edtNewPropertyActivityStreetAddress,
            edtNewPropertyActivityCity,
            edtNewPropertyActivityState,
            edtNewPropertyActivityZipCode;

    private LocationVo location;
    private boolean isFormComponent;

    private ScrollView screenViewNewPropertyActivity;
    private AppCompatImageButton imgBtnBack;
    private AppCompatTextView tvSubmit, tvTitle;

    public boolean isManuallyEdited = false;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        init();
        setListeners();
        setActionBarVisibility(false);
        if (!isFormComponent) {
            tvTitle.setText(getString(R.string.addProperty));
        } else {
            tvTitle.setText(getString(R.string.add_address));
        }
    }

    public void init() {
        context = this;

        view = LayoutInflater.from(this).inflate(R.layout.layout_newproperty_activity, getMiddleContent());
        edtNewPropertyActivityEnterPropertyName = view.findViewById(R.id.edtNewPropertyActivityEnterPropertyName);
        edtNewPropertyActivityStreetAddress = view.findViewById(R.id.edtNewPropertyActivityStreetAddress);
        edtNewPropertyActivityCity = view.findViewById(R.id.edtNewPropertyActivityCity);
        edtNewPropertyActivityState = view.findViewById(R.id.edtNewPropertyActivityState);
        edtNewPropertyActivityZipCode = view.findViewById(R.id.edtNewPropertyActivityZipCode);
        imgBtnBack = view.findViewById(R.id.imgBtnBack);
        tvSubmit = view.findViewById(R.id.tvSubmit);
        tvTitle = view.findViewById(R.id.tvTitle);
        setDetails();
        if (isFormComponent) {
            edtNewPropertyActivityEnterPropertyName.setVisibility(View.GONE);
        }
    }

    public void setDetails() {
        isManuallyEdited = false;
        if (getIntent().hasExtra(Constants.KEY_LOCATION) && getIntent().getExtras().getSerializable(Constants.KEY_LOCATION) != null) {
            location = (LocationVo) getIntent().getExtras().getSerializable(Constants.KEY_LOCATION);
        }
        if (getIntent() != null && getIntent().hasExtra(Constants.IS_FORM_COMPONENT)) {
            isFormComponent = getIntent().getBooleanExtra(Constants.IS_FORM_COMPONENT, false);
        }

        if (location != null) {
            edtNewPropertyActivityCity.setText(location.getAddNewPropertyCity());
            edtNewPropertyActivityState.setText(location.getAddNewPropertyState());
            edtNewPropertyActivityZipCode.setText(location.getAddNewPropertyZipCode());
            edtNewPropertyActivityStreetAddress.setText(location.getAddNewPropertyAddress());
        }

        screenViewNewPropertyActivity = view.findViewById(R.id.scrviewNewPropertyActivity);

        edtNewPropertyActivityEnterPropertyName = view.findViewById(R.id.edtNewPropertyActivityEnterPropertyName);

       /* if (edtNewPropertyActivityEnterPropertyName.getText().toString().trim().length() < 1 && !isManuallyEdited) {
            edtNewPropertyActivityEnterPropertyName.setText(getString(R.string.anterPropertyName));
        }*/

    }

    public void setListeners() {

        edtNewPropertyActivityEnterPropertyName.setOnFocusChangeListener((v, hasFocus) -> {
            Log.d("FOCUS_CHANGE", "" + hasFocus);
            if (hasFocus) {
                if (edtNewPropertyActivityEnterPropertyName.getText().toString().trim().equalsIgnoreCase(getString(R.string.anterPropertyName))) {
                    edtNewPropertyActivityEnterPropertyName.setText("");
                }
            } else {
                if (edtNewPropertyActivityEnterPropertyName.getText().toString().trim().length() == 0) {
                    edtNewPropertyActivityEnterPropertyName.setText(getString(R.string.anterPropertyName));
                }
            }
        });
        imgBtnBack.setOnClickListener(this);
        tvSubmit.setOnClickListener(this);
    }

    private void requestForSaveNewProperty() {
        if (BaseApplication.getInstance().isOnline(context)) {
            showProgress(getString(R.string.saving));
            HashMap<String, Object> params = new HashMap<>();
            params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            params.put(Constants.PARAM_BUILDING_ID, Constants.KEY_ADD_NEW_PROPERTY_NEW_PROPERTY_BID);
            params.put(Constants.PARAM_PROPERTY_NAME, edtNewPropertyActivityEnterPropertyName.getText().toString().trim());
            params.put(Constants.PARAM_ADDRESS, edtNewPropertyActivityStreetAddress.getText().toString().trim());
            params.put(Constants.PARAM_CITY, edtNewPropertyActivityCity.getText().toString().trim());
            params.put(Constants.PARAM_STATE, edtNewPropertyActivityState.getText().toString().trim());
            params.put(Constants.PARAM_ZIP, edtNewPropertyActivityZipCode.getText().toString().trim());
            params.put(Constants.PARAM_GEO_LOCATION, location.getAddNewPropertyGeoLocation());
            if (location.getAddNewPropertyLat() == null)
                location.setAddNewPropertyLat("0.0");
            params.put(Constants.PARAM_LAT, location.getAddNewPropertyLat());
            if (location.getAddNewPropertyLng() == null)
                location.setAddNewPropertyLng("0.0");
            params.put(Constants.PARAM_LNG, location.getAddNewPropertyLng());
            params.put(Constants.PARAM_AUTOSHARE, String.valueOf(location.getAddNewPropertyAutoShare()));
            params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
            params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            StaticUtils.addCommonData(params);

            ApiInterface apiService = RetrofitProvider.createServiceString(ApiInterface.class);
            Call<String> call = apiService.requestToSaveNewProperty(params);
            hideSoftKeyboard(NewPropertyActivity.this);
            call.enqueue(new Callback<String>() {
                @Override
                public void onResponse(@NonNull Call<String> call, @NonNull Response<String> response) {
                    stopProgress();
                    if (response.code() == 200) {
                        saveDataInDataBase(response.body());
                    } else {
                        if (response.errorBody() != null) {
                            try {
                                JSONObject jsonObject = new JSONObject(response.errorBody().string());
                                showForeGroundToast(jsonObject.getString("message"));
                            } catch (Exception e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }
                    }
                }

                @Override
                public void onFailure(@NonNull Call<String> call, @NonNull Throwable t) {
                    stopProgress();
                }
            });
        } else {
            showForeGroundToast(getString(R.string.internet_not_available));
        }
    }

    private void saveDataInDataBase(String response) {
        if (response != null) {
            NewPropertyErrorResponse newPropertyErrorResponse = null;
            try {
                newPropertyErrorResponse = new Gson().fromJson(response, new TypeToken<NewPropertyErrorResponse>() {
                }.getType());
            } catch (JsonSyntaxException e) {
                FirebaseEventUtils.logException(e);
            }
            String responseMassage = "";
            if (newPropertyErrorResponse != null)
                responseMassage = newPropertyErrorResponse.getMessage();
            try {
                if (!TextUtils.isEmpty(responseMassage)) {
                    navigateToBackScreen(responseMassage, "");
                } else {
                    NewPropertyResponse newPropertyResponse = new Gson().fromJson(response, new TypeToken<NewPropertyResponse>() {
                    }.getType());
                    int buildingId = 0;
                    try {
                        buildingId = newPropertyResponse.getBuildingid();
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                    try {
                        if (buildingId > 0) {
                            PropertiesVo propertiesVo = new PropertiesVo();
                            propertiesVo.setPropertyId(buildingId);
                            propertiesVo.setPropertyName(edtNewPropertyActivityEnterPropertyName.getText().toString().trim());
                            propertiesVo.setPropertyAddress(edtNewPropertyActivityStreetAddress.getText().toString().trim());
                            propertiesVo.setPropertyCity(edtNewPropertyActivityCity.getText().toString().trim());
                            propertiesVo.setPropertyState(edtNewPropertyActivityState.getText().toString().trim());
                            propertiesVo.setPropertyZip(edtNewPropertyActivityZipCode.getText().toString().trim());
                            propertiesVo.setPropertyGeo("s:135:" + "\"" + location.getAddNewPropertyGeoLocation() + "\";");
                            TblProperties tblProperties = new TblProperties(NewPropertyActivity.this);
                            tblProperties.insertProperty(propertiesVo);
                            insertOrUpdateTempClusterData(buildingId);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                    String address = edtNewPropertyActivityEnterPropertyName.getText().toString().trim() + "\n" +
                            edtNewPropertyActivityCity.getText().toString().trim() + ", " + edtNewPropertyActivityState.getText().toString().trim() + ", " + edtNewPropertyActivityZipCode.getText().toString().trim();
                    navigateToBackScreen("YES", address);
                }
            } catch (JsonSyntaxException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    /**
     * Function to insert Temp cluster data
     *
     * @param buildingId
     */
    private void insertOrUpdateTempClusterData(int buildingId) {
        TblCluster tblCluster = new TblCluster(NewPropertyActivity.this);
        //add one new temp cluster until server prepare it
        List<Cluster> lstData = tblCluster.getAllClusterData();
        if (lstData.size() > 0) {
            Cluster cluster = updateTempCluster(lstData);
            if (cluster != null) {
                //Update temp cluster with new building Id
                String storedBuildings = cluster.getBuildings();
                cluster.setSgcBuildings(storedBuildings.concat(",").concat(String.valueOf(buildingId)));
                tblCluster.updateClusterById(cluster);
            } else {
                //insert new temp cluster
                Cluster newCluster = new Cluster();
                newCluster.setVendorId(lstData.get(0).getVendorId());
                newCluster.setSgcCluster("-1");
                newCluster.setCentroid("[" + location.getAddNewPropertyLat() + "," + location.getAddNewPropertyLng() + "]");
                newCluster.setSgcBuildings(String.valueOf(buildingId));
                tblCluster.insertClusterData(newCluster);
            }
        }
    }

    private Cluster updateTempCluster(List<Cluster> lstData) {
        for (Cluster cluster : lstData) {
            if (!TextUtils.isEmpty(cluster.getSgcCluster()) && cluster.getSgcCluster().equalsIgnoreCase("-1")) {
                return cluster;
            }
        }
        return null;
    }

    private void navigateToBackScreen(String message, String address) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constants.KEY_INTENT_RETURN_DATA, message);
        returnIntent.putExtra(Constants.KEY_INTENT_ADDRESS, address);
        if (isFormComponent) {
            returnIntent.putExtra(Constants.PARAM_STATE, edtNewPropertyActivityState.getText().toString().trim());
            returnIntent.putExtra(Constants.PARAM_LAT, location.getAddNewPropertyLat());
            returnIntent.putExtra(Constants.PARAM_LON, location.getAddNewPropertyLng());
            returnIntent.putExtra(Constants.PARAM_ZIP, edtNewPropertyActivityZipCode.getText().toString().trim());
            returnIntent.putExtra(Constants.PARAM_STREET_ADDRESS, edtNewPropertyActivityStreetAddress.getText().toString().trim());
            returnIntent.putExtra(Constants.PARAM_CITY, edtNewPropertyActivityCity.getText().toString().trim());
        }
        setResult(Activity.RESULT_OK, returnIntent);
        finish();
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            super.onBackPressed();
        } else if (viewId == R.id.tvSubmit) {
            if (validatePropertyAndOtherData()) {
                if (!isFormComponent) {
                    requestForSaveNewProperty();
                } else {
                    String address = edtNewPropertyActivityStreetAddress.getText().toString().trim() + "\n" +
                            edtNewPropertyActivityCity.getText().toString().trim() + ", " + edtNewPropertyActivityState.getText().toString().trim() + ", " + edtNewPropertyActivityZipCode.getText().toString().trim();
                    navigateToBackScreen("YES", address);
                }
            }
        }
    }


    public boolean validateZipCode(CharSequence s) {
        Pattern pattern = Pattern.compile("(?i)^[a-z0-9][a-z0-9\\- ]{0,10}[a-z0-9]$");
        return pattern.matcher(s).matches();
    }

    private boolean validatePropertyAndOtherData() {
        //Do not check validation of Property name if user came from form detail screen for address components.
        if (!isFormComponent) {
            if (edtNewPropertyActivityEnterPropertyName.getText().toString().trim().length() == 0) {
                edtNewPropertyActivityEnterPropertyName.setError(getString(R.string.propertyNameMustNotEmpty));
                return false;
            } else if (edtNewPropertyActivityEnterPropertyName.getText().toString().trim().equalsIgnoreCase(getString(R.string.anterPropertyName))) {
                edtNewPropertyActivityEnterPropertyName.setError(getString(R.string.propertyNameMustNotEmpty));
                return false;
            } else {
                return validateOtherData();
            }
        } else {
            return validateOtherData();
        }
    }


    private boolean validateOtherData() {
        if (edtNewPropertyActivityStreetAddress.getText().toString().trim().length() == 0) {
            edtNewPropertyActivityStreetAddress.setError(getString(R.string.propertyStreetAddressMustNotEmpty));
            return false;
        } else if (edtNewPropertyActivityCity.getText().toString().trim().length() == 0) {
            edtNewPropertyActivityCity.setError(getString(R.string.propertyCityMustNotEmpty));
            return false;
        } else if (edtNewPropertyActivityState.getText().toString().trim().length() == 0) {
            edtNewPropertyActivityState.setError(getString(R.string.propertyStateMustNotEmpty));
            return false;
        } else if (edtNewPropertyActivityZipCode.getText().toString().trim().length() == 0) {
            edtNewPropertyActivityZipCode.setError(getString(R.string.propertyZipCodeMustNotEmpty));
            return false;
        } else if (!validateZipCode(edtNewPropertyActivityZipCode.getText().toString().trim())) {
            edtNewPropertyActivityZipCode.setError(getString(R.string.property_zip_code_is_not_valid));
            return false;
        } else {
            return true;
        }
    }


    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.enter_from_left, R.anim.exit_to_right);
    }
}
