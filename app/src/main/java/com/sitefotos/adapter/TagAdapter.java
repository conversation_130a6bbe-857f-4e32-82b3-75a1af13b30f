package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.CheckBox;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemTagBinding;
import com.sitefotos.models.Tags;

import java.util.List;


public abstract class TagAdapter extends RecyclerView.Adapter<TagAdapter.ViewHolder> {
    private Context context;
    private List<Tags> lstTag;
    private ItemTagBinding  binding;

    public abstract void selectItem(int position, Tags tags, boolean isSelected);

    protected TagAdapter(Context context, List<Tags> lstItems) {
        this.context = context;
        this.lstTag = lstItems;
    }

    public void updateList(List<Tags> lstData) {
        lstTag = lstData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
       binding = ItemTagBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {

        Tags tag = lstTag.get(position);
        holder.cbName.setText(tag.getTagName());

        holder.cbName.setOnCheckedChangeListener(null);
        holder.cbName.setChecked(tag.isSelected());
        holder.cbName.setOnCheckedChangeListener((compoundButton, b) -> {
            selectItem(position, lstTag.get(position), b);

        });
    }

    @Override
    public int getItemCount() {
        return lstTag.size();
    }

    public List<Tags> getData() {
        return lstTag;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox cbName;

        public ViewHolder(ItemTagBinding binding) {
            super(binding.getRoot());

            cbName = binding.cbName;
        }
    }
}
