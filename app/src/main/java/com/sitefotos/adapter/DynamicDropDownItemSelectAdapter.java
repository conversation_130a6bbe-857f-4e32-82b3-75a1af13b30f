package com.sitefotos.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemVehicleEquipmentSelectionBinding;
import com.sitefotos.models.DynamicDropDownItem;
import com.sitefotos.models.ItemModel;

import java.util.ArrayList;
import java.util.List;


public abstract class DynamicDropDownItemSelectAdapter extends RecyclerView.Adapter<DynamicDropDownItemSelectAdapter.ViewHolder> {
    private Context context;
    private ArrayList<DynamicDropDownItem> lstItems;
    private ItemVehicleEquipmentSelectionBinding binding;
    private List<DynamicDropDownItem> lstSearchItems;
    private DynamicDropDownItem lastSelectedData = null;

    public abstract void selectItem(int position, boolean isSelected);

    protected DynamicDropDownItemSelectAdapter(Context context) {
        this.context = context;
        lastSelectedData = null;
    }


    public void updateList(ArrayList<DynamicDropDownItem> lstItems, boolean shouldNotify) {
        this.lstItems = lstItems;
        this.lstSearchItems = lstItems;
        if (shouldNotify)
            notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemVehicleEquipmentSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, int position) {
        DynamicDropDownItem data =lstSearchItems.get(position);
        holder.cbVESelection.setText(data.getValue());
        holder.cbVESelection.setOnCheckedChangeListener(null);
        if (data.isSelected()) {
            lastSelectedData = data;
        }
        holder.cbVESelection.setChecked(data.isSelected());
        holder.cbVESelection.setOnCheckedChangeListener((compoundButton, b) -> handleButtonClick(position));
    }

    private void handleButtonClick(int position) {
        if (lastSelectedData != null ) {
            lastSelectedData.setSelected(false);
            int lastSelectedIndex = lstSearchItems.indexOf(lastSelectedData);
            notifyItemChanged(lastSelectedIndex);
        }
        lstSearchItems.get(position).setSelected(true);
        notifyItemChanged(position);
    }

    @Override
    public int getItemCount() {
        return lstSearchItems.size();
    }


    public class ViewHolder extends RecyclerView.ViewHolder {
        RadioButton cbVESelection;

        public ViewHolder(ItemVehicleEquipmentSelectionBinding binding) {
            super(binding.getRoot());
            cbVESelection = binding.cbVESelection;
        }
    }


    public void filterData(CharSequence charSequence) {
        String charString = charSequence.toString();
        if (charString.isEmpty()) {
            lstSearchItems.clear();
            lstSearchItems.addAll(lstItems);
        } else {
            List<DynamicDropDownItem> filteredList = new ArrayList<>();
            for (DynamicDropDownItem data : lstItems) {
                String typedString = charString.trim().toLowerCase();
                String name = data.getValue();
                if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString)) {
                    filteredList.add(data);
                }
            }
            lstSearchItems = filteredList;
        }
        notifyDataSetChanged();
    }


    public void unCheckLastSelectedData() {
            if (lastSelectedData != null){
                lastSelectedData.setSelected(false);
            }
    }
}
