package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemMaterialSelectionBinding;
import com.sitefotos.models.Material;

import java.util.ArrayList;
import java.util.List;

public abstract class MaterialViewAdapter extends RecyclerView.Adapter<MaterialViewAdapter.ViewHolder> {
    private Context context;
    private List<Material> lstItems;
    private List<Material> mainList;
    private List<Material> tempArrayList;
    private ItemMaterialSelectionBinding binding;


    int selectedPosition;

    public boolean filterFlag;
    public String filterByText = "";

    public abstract void itemClick(int position);


    public MaterialViewAdapter(Context context, List<Material> lstItems, int selectedPosition) {
        this.context = context;
        this.lstItems = lstItems;
        this.mainList = lstItems;
        this.selectedPosition = selectedPosition;
    }


    public void setSelectedPosition(int selectedPosition) {
        this.selectedPosition = selectedPosition;
    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemMaterialSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        Material material = lstItems.get(position);
        holder.tvSelectedItem.setText(String.format("%s - %s", material.getMaterialName(), material.getMaterialUnit()));
        if (filterFlag) {
            if (selectedPosition != -1) {
                if (material.getMaterialID() == mainList.get(selectedPosition).getMaterialID()) {
                    holder.cbMaterial.setChecked(true);
                } else {
                    holder.cbMaterial.setChecked(false);
                }
            }
        } else {
            if (selectedPosition == position) {
                holder.cbMaterial.setChecked(true);
            } else {
                holder.cbMaterial.setChecked(false);
            }
        }

        holder.tvSelectedItem.setTextColor(ContextCompat.getColorStateList(context, R.color.black));
        holder.itemView.setOnClickListener(v -> itemClick(position));

        holder.itemView.setOnClickListener(v -> {
            if (filterFlag) {
                Material checkModel = new Material();
                checkModel.setMaterialID(lstItems.get(position).getMaterialID());
                if (mainList.contains(material)) {
                    selectedPosition = mainList.indexOf(material);
                    itemClick(selectedPosition);
                    notifyDataSetChanged();
                }
            } else {
                selectedPosition = position;
                itemClick(position);
                notifyDataSetChanged();
            }

        });

    }

    @Override
    public int getItemCount() {
        return lstItems.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvSelectedItem;
        AppCompatCheckBox cbMaterial;
        public ViewHolder(ItemMaterialSelectionBinding binding) {
            super(binding.getRoot());
            tvSelectedItem = binding.tvSelectedItem;
            cbMaterial = binding.cbMaterial;
        }
    }

    public void setData(List<Material> list) {
        this.lstItems = list;
        notifyDataSetChanged();
    }


    public void filter(String str) {
        if (!str.isEmpty()) {
            filterFlag = true;
            this.filterByText = str;
            ArrayList arrayList = new ArrayList();
            this.tempArrayList = arrayList;
            arrayList.clear();
            for (Material next : this.mainList) {
                if (filterByAll(next)) {
                    this.tempArrayList.add(next);
                }
            }
            setData(this.tempArrayList);
            return;
        }
        filterFlag = false;
        this.filterByText = "";
        setData(mainList);
    }

    private boolean filterByAll(Material material) {
        if (material.getMaterialName() != null && material.getMaterialUnit() != null)
            return material.getMaterialName().toLowerCase().contains(this.filterByText.toLowerCase()) || material.getMaterialUnit().toLowerCase().contains(this.filterByText.toLowerCase());

        if (material.getMaterialName() != null )
            return material.getMaterialName().toLowerCase().contains(this.filterByText.toLowerCase());

        if (material.getMaterialUnit() != null)
            return material.getMaterialUnit().toLowerCase().contains(this.filterByText.toLowerCase());

        return false;
    }


}
