package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.Constants;
import com.sitefotos.databinding.ItemImageComponentBinding;
import com.sitefotos.models.ImageComponent;

import org.json.JSONException;

import java.util.List;


public abstract class ImageComponentsAdapter extends RecyclerView.Adapter<ImageComponentsAdapter.ViewHolder> {
    private Context context;
    private List<ImageComponent> lstData;
    private ItemImageComponentBinding binding;

    public abstract void selectItem(ImageComponent imageComponent);

    protected ImageComponentsAdapter(Context context, List<ImageComponent> lstData) {
        this.context = context;
        this.lstData = lstData;
    }

    public void updateList(List<ImageComponent> lstData) {
        this.lstData = lstData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemImageComponentBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {

        ImageComponent imageComponents = lstData.get(position);
        try {
            holder.tvComponentName.setText(imageComponents.getImageObject().getString(Constants.TITLE));
            holder.tvComponentName.setOnClickListener(view -> {
                selectItem(imageComponents);
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getItemCount() {
        return lstData.size();
    }

    public List<ImageComponent> getData() {
        return lstData;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatTextView tvComponentName;

        public ViewHolder(ItemImageComponentBinding binding) {
            super(binding.getRoot());
            tvComponentName = binding.tvComponentName;
        }
    }
}
