package com.sitefotos.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemVehicleEquipmentSelectionBinding;
import com.sitefotos.models.ItemModel;

import java.util.ArrayList;
import java.util.List;


public abstract class VehicleEquipmentSelectAdapter extends RecyclerView.Adapter<VehicleEquipmentSelectAdapter.ViewHolder> {
    private Context context;
    private ArrayList<ItemModel> lstItems;
    private ItemVehicleEquipmentSelectionBinding binding;
    private List<ItemModel> lstSearchItems;
    private ItemModel lastSelectedData = null;

    public abstract void selectItem(int position, boolean isSelected);

    protected VehicleEquipmentSelectAdapter(Context context) {
        this.context = context;
        lastSelectedData = null;
    }


    public void updateList(ArrayList<ItemModel> lstItems, boolean shouldNotify) {
        this.lstItems = lstItems;
        this.lstSearchItems = lstItems;
        if (shouldNotify)
            notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemVehicleEquipmentSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, int position) {
        ItemModel data =lstSearchItems.get(position);
        holder.cbVESelection.setText(data.getName());
        holder.cbVESelection.setOnCheckedChangeListener(null);
        if (data.isSelected()) {
            lastSelectedData = data;
        }
        holder.cbVESelection.setChecked(data.isSelected());
        holder.cbVESelection.setOnCheckedChangeListener((compoundButton, b) -> handleButtonClick(position));
    }

    private void handleButtonClick(int position) {
        if (lastSelectedData != null ) {
            lastSelectedData.setSelected(false);
            int lastSelectedIndex = lstSearchItems.indexOf(lastSelectedData);
            notifyItemChanged(lastSelectedIndex);
        }
        lstSearchItems.get(position).setSelected(true);
        notifyItemChanged(position);
    }

    @Override
    public int getItemCount() {
        return lstSearchItems.size();
    }


    public class ViewHolder extends RecyclerView.ViewHolder {
        RadioButton cbVESelection;

        public ViewHolder(ItemVehicleEquipmentSelectionBinding binding) {
            super(binding.getRoot());
            cbVESelection = binding.cbVESelection;
        }
    }


    public void filterData(CharSequence charSequence) {
        String charString = charSequence.toString();
        if (charString.isEmpty()) {
            lstSearchItems.clear();
            lstSearchItems.addAll(lstItems);
        } else {
            List<ItemModel> filteredList = new ArrayList<>();
            for (ItemModel data : lstItems) {
                String typedString = charString.trim().toLowerCase();
                String name = data.getName();
                if (!TextUtils.isEmpty(name) && name.toLowerCase().contains(typedString)) {
                    filteredList.add(data);
                    if (data.isSelected()){
                    }
                }
            }
            lstSearchItems = filteredList;
        }
        notifyDataSetChanged();
    }


    public void unCheckLastSelectedData() {
            if (lastSelectedData != null){
                lastSelectedData.setSelected(false);
            }
    }
}
