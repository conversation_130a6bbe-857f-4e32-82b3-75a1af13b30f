package com.sitefotos.adapter;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemFormBinding;
import com.sitefotos.models.FormData;

import java.util.List;

public class FormAdapter extends RecyclerView.Adapter<FormAdapter.ViewHolder> {

    private List<FormData> lstForm;
    ViewClicked viewClicked;
    private final boolean isSiteForm;
    private List<Long> lstCheckedInForm;

    private ItemFormBinding binding;

    public interface ViewClicked {
        void onItemClicked(int position, View view, FormData dateItem);

        void onItemLongClicked(int position, View view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView tvFormName;
        AppCompatImageView ivFormCompleteStatus;
        public ViewHolder(ItemFormBinding binding) {
            super(binding.getRoot());
            tvFormName = binding.tvFormName;
            ivFormCompleteStatus = binding.ivFormCompleteStatus;
        }
    }

    public FormAdapter(List<FormData> lstForm, List<Long> lstCheckedInForm, Activity activity, boolean isSiteForm, ViewClicked viewClicked) {
        this.lstForm = lstForm;
        this.viewClicked = viewClicked;
        this.isSiteForm = isSiteForm;
        this.lstCheckedInForm = lstCheckedInForm;

    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemFormBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<FormData> lstForm, List<Long> lstCheckedInForm) {
        this.lstForm = lstForm;
        this.lstCheckedInForm = lstCheckedInForm;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        if (lstForm == null)
            return;
        FormData dateItem = lstForm.get(position);
        holder.tvFormName.setText(dateItem.getFormName());
        if (!isSiteForm) {
            holder.ivFormCompleteStatus.setVisibility(View.GONE);
        } else {
            if (dateItem.getIsCheckInOut() && dateItem.isCheckInOutComplete() && dateItem.isCheckInOutFormComplete()) {
                holder.ivFormCompleteStatus.setVisibility(View.VISIBLE);
            } else {
                holder.ivFormCompleteStatus.setVisibility(View.INVISIBLE);
            }
        }
        if (isSiteForm && lstCheckedInForm.contains(dateItem.getFormId())) {
            holder.tvFormName.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.colorPrimary));
        } else {
            holder.tvFormName.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.black));
        }
        holder.itemView.setOnClickListener(view -> viewClicked.onItemClicked(position, view, dateItem));
        holder.itemView.setOnLongClickListener(view -> {
            viewClicked.onItemLongClicked(position, view);
            return false;
        });

    }

    @Override
    public int getItemCount() {
        return lstForm.size();
    }

}
