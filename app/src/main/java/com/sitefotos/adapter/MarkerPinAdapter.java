package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatRadioButton;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemMarkerPinBinding;
import com.sitefotos.models.MapPinData;
import com.sitefotos.util.ImageUtil;

import java.io.File;
import java.util.List;


public abstract class MarkerPinAdapter extends RecyclerView.Adapter<MarkerPinAdapter.ViewHolder> {
    private Context context;
    private List<MapPinData> lstPins;
    private ItemMarkerPinBinding binding;

    public abstract void selectItem(int position, MapPinData mapPinData, boolean isSelected);

    protected MarkerPinAdapter(Context context, List<MapPinData> lstItems) {
        this.context = context;
        this.lstPins = lstItems;
    }

    public void updateList(List<MapPinData> lstData) {
        lstPins = lstData;
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemMarkerPinBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {

        MapPinData mapPinData = lstPins.get(position);
        holder.btnMarker.setText(mapPinData.getPinLabel());
        holder.btnMarker.setOnCheckedChangeListener(null);
        holder.btnMarker.setChecked(mapPinData.isSelected());
        File fileName = new File(mapPinData.getPngURL());
        ImageUtil.loadActualImageInGlide(holder.ivMarker, ImageUtil.getMapPinRootFolder(context).concat(fileName.getName()));
        holder.btnMarker.setOnCheckedChangeListener((compoundButton, b) -> {
            /*if (lastSelectedPosition > -1) {
                lstPins.get(lastSelectedPosition).setSelected(false);
                notifyItemChanged(lastSelectedPosition);
            }*/
            selectItem(position, lstPins.get(position), b);
            //lastSelectedPosition = position;
        });
    }

    @Override
    public int getItemCount() {
        return lstPins.size();
    }

    public List<MapPinData> getData() {
        return lstPins;
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatRadioButton btnMarker;
        AppCompatImageView ivMarker;

        public ViewHolder(ItemMarkerPinBinding binding) {
            super(binding.getRoot());
            btnMarker = binding.btnMarker;
            ivMarker = binding.ivMarker;
        }
    }
}
