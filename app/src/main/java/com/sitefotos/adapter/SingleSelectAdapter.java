package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemSingleSelectionBinding;
import com.sitefotos.models.ItemModel;

import java.util.ArrayList;


public abstract class SingleSelectAdapter extends RecyclerView.Adapter<SingleSelectAdapter.ViewHolder> {
    private Context context;
    private ArrayList<ItemModel> lstItems;
    private ItemSingleSelectionBinding binding;

    public abstract void selectItem(int position, boolean isSelected);

    protected SingleSelectAdapter(Context context, ArrayList<ItemModel> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemSingleSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        holder.cbSelection.setText(lstItems.get(position).getName());
        holder.cbSelection.setOnCheckedChangeListener(null);
        holder.cbSelection.setChecked(lstItems.get(position).isSelected());
        holder.cbSelection.setOnCheckedChangeListener((compoundButton, b) -> selectItem(position, b));
    }

    @Override
    public int getItemCount() {
        return lstItems.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        RadioButton cbSelection;

        public ViewHolder(ItemSingleSelectionBinding binding) {
            super(binding.getRoot());
            cbSelection = binding.cbSelection;
        }
    }
}
