package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.RadioButton;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemSingleNumberSelectionBinding;
import com.sitefotos.models.ItemModel;

import java.util.ArrayList;


public abstract class NumberPickerAdapter extends RecyclerView.Adapter<NumberPickerAdapter.ViewHolder> {
    private Context context;
    private ArrayList<ItemModel> lstItems;
    ItemSingleNumberSelectionBinding binding;

    public abstract void selectItem(int position, boolean isSelected);

    protected NumberPickerAdapter(Context context, ArrayList<ItemModel> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemSingleNumberSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        holder.cbSelection.setText(lstItems.get(position).getName());
        holder.cbSelection.setOnCheckedChangeListener(null);
        holder.cbSelection.setChecked(lstItems.get(position).isSelected());
        if (lstItems.get(position).isSelected()) {
            holder.cbSelection.setTextColor(ContextCompat.getColorStateList(context, R.color.colorPrimary));
        } else {
            holder.cbSelection.setTextColor(ContextCompat.getColorStateList(context, R.color.black));
        }
        holder.cbSelection.setOnCheckedChangeListener((compoundButton, b) -> {
            selectItem(position, b);
            notifyDataSetChanged();
        });
    }

    @Override
    public int getItemCount() {
        return lstItems.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        RadioButton cbSelection;

        public ViewHolder(ItemSingleNumberSelectionBinding binding) {
            super(binding.getRoot());
            cbSelection = binding.cbSelection;
        }
    }
}
