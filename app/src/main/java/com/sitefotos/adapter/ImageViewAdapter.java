package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemImagesBinding;
import com.sitefotos.models.ImageData;
import com.sitefotos.util.ImageUtil;

import java.util.ArrayList;

public abstract class ImageViewAdapter extends RecyclerView.Adapter<ImageViewAdapter.ViewHolder> {
    private Context context;
    private ArrayList<ImageData> lstItems;
    private ItemImagesBinding binding;
    boolean isSubmitted = false;

    public abstract void deleteItem(int position, int imageId);

    public abstract void itemClick(int position, ImageData imageData);

    public ImageViewAdapter(Context context, ArrayList<ImageData> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
    }

    public ImageViewAdapter(Context context, ArrayList<ImageData> lstItems, boolean isSubmitted) {
        this.context = context;
        this.lstItems = lstItems;
        this.isSubmitted = isSubmitted;
    }

    public void removeItem(int position) {
        if (lstItems != null && lstItems.size() > 0) {
            lstItems.remove(position);
            this.notifyDataSetChanged();
        }
    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemImagesBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        holder.ivDelete.setOnClickListener(view -> deleteItem(position, lstItems.get(position).getImageId()));
        holder.ivDelete.setVisibility(isSubmitted ? View.GONE : View.VISIBLE);
        ImageUtil.loadImageInGlide(holder.ivMainImage, lstItems.get(position).getImagePath());

        holder.itemView.setOnClickListener(v -> {
            if (!isSubmitted && lstItems != null && !lstItems.isEmpty())
                itemClick(position, lstItems.get(position));
        });
    }

    public ArrayList<ImageData> getImageList() {
        return lstItems;
    }

    public void updateList(ArrayList<ImageData> lstImage) {
        this.lstItems = lstImage;
        this.notifyDataSetChanged();
    }

    public void setSubmitted(boolean isSubmitted) {
        this.isSubmitted = isSubmitted;
    }

    @Override
    public int getItemCount() {
        return lstItems.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatImageView ivDelete,ivMainImage;
        public ViewHolder(ItemImagesBinding binding) {
            super(binding.getRoot());
            ivDelete = binding.ivDelete;
            ivMainImage = binding.ivMainImage;
        }
    }
}
