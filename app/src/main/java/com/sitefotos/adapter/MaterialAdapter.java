package com.sitefotos.adapter;

import android.app.Activity;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.LayoutMaterialBinding;
import com.sitefotos.models.Material;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.List;

/**
 * Created by dk on 21/8/17.
 */

public class MaterialAdapter extends RecyclerView.Adapter<MaterialAdapter.ViewHolder> {

    List<Material> lstMaterials;
    Activity activity;
    ViewClicked viewClicked;
    boolean isUserEnabled;
    private LayoutMaterialBinding binding;


    public interface ViewClicked {
        void setOnQtyClick(int position, View view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {

        TextView tvMaterialName;
        TextView tvUnit;
        AppCompatEditText edtQty;
        public ViewHolder(LayoutMaterialBinding binding) {
            super(binding.getRoot());
            tvMaterialName = binding.tvMaterialName;
            tvUnit = binding.tvUnit;
            edtQty = binding.edtQty;
        }
    }

    public MaterialAdapter(List<Material> lstMaterials, ViewClicked viewClicked) {
        this.lstMaterials = lstMaterials;
        this.viewClicked = viewClicked;

    }

    @Override
    public MaterialAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = LayoutMaterialBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new MaterialAdapter.ViewHolder(binding);
    }

    public void updateList(List<Material> latMaterials) {
        this.lstMaterials = latMaterials;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull final MaterialAdapter.ViewHolder holder, final int position) {
        Material model = lstMaterials.get(position);
        holder.tvMaterialName.setText(model.getMaterialName());
        holder.tvUnit.setText(model.getMaterialUnit());
        if (!TextUtils.isEmpty(String.valueOf(model.getUsage()))) {
            double qty = model.getUsage();
            if (qty == 0.0) {
                holder.edtQty.setText("");
            } else {
                holder.edtQty.setText(String.valueOf(model.getUsage()));
            }
        }
        holder.edtQty.setEnabled(isUserEnabled);
        holder.edtQty.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {

                double qty = 0;
                if (holder.edtQty.getText() != null && !TextUtils.isEmpty(holder.edtQty.getText().toString().trim())) {
                    try {
                        qty = Double.parseDouble(holder.edtQty.getText().toString().trim());
                    } catch (NumberFormatException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
                model.setUsage(qty);
            }
        });

        //holder.edtQty.setOnClickListener(view -> viewClicked.setOnQtyClick(position, view));
    }

    @Override
    public int getItemCount() {
        return lstMaterials.size();
    }

    public void updateData(Material material, int adapterPosition) {
        lstMaterials.set(adapterPosition, material);
        notifyDataSetChanged();
    }


    public void enableUserInput() {
        isUserEnabled = true;
    }

}
