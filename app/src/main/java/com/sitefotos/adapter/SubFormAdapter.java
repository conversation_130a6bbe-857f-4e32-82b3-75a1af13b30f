package com.sitefotos.adapter;

import android.app.Activity;
import android.os.SystemClock;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Chronometer;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.databinding.ItemFormBinding;
import com.sitefotos.databinding.ItemSubformBinding;
import com.sitefotos.models.FormData;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.StaticUtils;

import java.util.List;

public class SubFormAdapter extends RecyclerView.Adapter<SubFormAdapter.ViewHolder> {

    private List<FormData> lstForm;
    ViewClicked viewClicked;
    private ItemSubformBinding binding;

    public interface ViewClicked {
        void onItemClicked(int position, View view, FormData dataItem);

        void onItemLongClicked(int position, View view);
    }


    public static class ViewHolder extends RecyclerView.ViewHolder {

        AppCompatTextView tvServiceName, tvCrewName;
        Chronometer cmTime;

        public ViewHolder(ItemSubformBinding binding) {
            super(binding.getRoot());
            tvServiceName = binding.tvServiceName;
            tvCrewName = binding.tvCrewName;
            cmTime = binding.cmTime;
        }
    }

    public SubFormAdapter(List<FormData> lstForm, ViewClicked viewClicked) {
        this.lstForm = lstForm;
        this.viewClicked = viewClicked;

    }

    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        binding = ItemSubformBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new ViewHolder(binding);
    }

    public void updateList(List<FormData> lstForm, List<Long> lstCheckedInForm) {
        this.lstForm = lstForm;
        notifyDataSetChanged();
    }

    @Override
    public void onBindViewHolder(@NonNull final ViewHolder holder, final int position) {
        if (lstForm == null)
            return;
        FormData formData = lstForm.get(position);
        if (formData.getSubFormOtherData() != null) {
            holder.tvCrewName.setText(formData.getSubFormOtherData().getCrewName());
            holder.tvServiceName.setText(StaticUtils.getStringFromStringList(formData.getSubFormOtherData().getLstServiceName()));
        }
        if (formData.getCheckout_time() <=0) {
            holder.tvCrewName.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.green));
            holder.tvServiceName.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.green));
            holder.cmTime.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.green));
        } else {
            holder.tvCrewName.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.black));
            holder.tvServiceName.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.black));
            holder.cmTime.setTextColor(ContextCompat.getColor(holder.itemView.getContext(), R.color.black));
        }

        if(formData.getCheckin_time() >0 && formData.getCheckout_time() <=0){
            holder.cmTime.setVisibility(View.VISIBLE);
            long timeDiff  = System.currentTimeMillis() - formData.getCheckin_time();
            holder.cmTime.setBase(SystemClock.elapsedRealtime() - timeDiff);
            holder.cmTime.start();
            holder.cmTime.setOnChronometerTickListener(chronometer -> {
                long time = SystemClock.elapsedRealtime() - chronometer.getBase();
                chronometer.setText(DateUtil.getChronometerTimeWithSeconds(time));
            });
        }else if (formData.getCheckout_time() >0){
            holder.cmTime.setVisibility(View.VISIBLE);
            long timeDiff  = formData.getCheckout_time() - formData.getCheckin_time();
            holder.cmTime.setBase(SystemClock.elapsedRealtime() - timeDiff);
            holder.cmTime.stop();
            holder.cmTime.setOnChronometerTickListener(chronometer -> {
                long time = SystemClock.elapsedRealtime() - chronometer.getBase();
                chronometer.setText(DateUtil.getChronometerTimeWithSeconds(time));
            });
        }else{
            holder.cmTime.setVisibility(View.INVISIBLE);
        }
        holder.itemView.setOnClickListener(view -> viewClicked.onItemClicked(position, view, formData));
        holder.itemView.setOnLongClickListener(view -> {
            viewClicked.onItemLongClicked(position, view);
            return false;
        });
    }

    @Override
    public int getItemCount() {
        return lstForm.size();
    }

}
