package com.sitefotos.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.CheckBox;

import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.databinding.ItemMultiSelectionBinding;
import com.sitefotos.models.ItemModel;

import java.util.ArrayList;


public abstract class MultiSelectAdapter extends RecyclerView.Adapter<MultiSelectAdapter.ViewHolder> {
    Context context;
    ArrayList<ItemModel> lstItems;
    ItemMultiSelectionBinding binding;

    public abstract void selectItem(int position, boolean isSelected);

    public MultiSelectAdapter(Context context, ArrayList<ItemModel> lstItems) {
        this.context = context;
        this.lstItems = lstItems;
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        binding = ItemMultiSelectionBinding.inflate(LayoutInflater.from(context), parent, false);
        return new ViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(final ViewHolder holder, final int position) {
        holder.cbSelection.setText(lstItems.get(position).getName());
        holder.cbSelection.setOnCheckedChangeListener(null);
        holder.cbSelection.setChecked(lstItems.get(position).isSelected());
        holder.cbSelection.setOnCheckedChangeListener((compoundButton, b) -> selectItem(position, b));
    }

    @Override
    public int getItemCount() {
        return lstItems.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        CheckBox cbSelection;

        public ViewHolder(ItemMultiSelectionBinding binding) {
            super(binding.getRoot());
            cbSelection = binding.cbSelection;
        }
    }
}
