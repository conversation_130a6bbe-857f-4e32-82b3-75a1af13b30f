package com.sitefotos.util;

import android.content.Context;
import android.provider.Settings;

import com.sitefotos.util.logger.CustomLogKt;


public class NetworkUtil {


    public static boolean isMobileDataEnabled(Context context) {
        boolean isMobileDataAvailable = false;
        CustomLogKt.debug("CONNECTIVITY_17", "Mobile: " + (Settings.Global.getInt(context.getContentResolver(), "mobile_data", 1) == 1));
        isMobileDataAvailable = ((Settings.Global.getInt(context.getContentResolver(), "mobile_data", 1) == 1));

        CustomLogKt.debug("MOBILE_DATA", "" + isMobileDataAvailable);
        return isMobileDataAvailable;
    }

    public static boolean isWiFiDataEnabled(Context context) {
        boolean isWiFiDataAvailable = false;
        CustomLogKt.debug("WIFI_DATA_17", "Wifi: " + (Settings.Global.getInt(context.getContentResolver(), Settings.Global.WIFI_ON, 1) == 1));
        isWiFiDataAvailable = ((Settings.Global.getInt(context.getContentResolver(), Settings.Global.WIFI_ON, 1) == 1));

        CustomLogKt.debug("WIFI_DATA_AVAILABILITY", "" + isWiFiDataAvailable);
        return isWiFiDataAvailable;
    }
}
