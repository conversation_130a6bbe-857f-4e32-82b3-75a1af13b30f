package com.sitefotos.util;

import static com.sitefotos.util.PermissionUtils.CAMERA_PERMISSION;

import android.content.Context;
import android.graphics.PointF;
import android.graphics.drawable.AnimationDrawable;
import android.location.Location;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;

import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;

import com.otaliastudios.cameraview.CameraView;
import com.otaliastudios.cameraview.controls.Facing;
import com.otaliastudios.cameraview.controls.Flash;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.models.Tags;
import com.sitefotos.storage.AppPrefShared;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

public class MyCameraUtils {

    /**
     * Method to show Touch view on camera interface
     *
     * @param ivAutoFocusAnim drawable container view
     * @param point           Touch pints in x ,y
     */
    public static void showTouchView(AppCompatImageView ivAutoFocusAnim, PointF point) {

        if (ivAutoFocusAnim == null)
            return;
        ivAutoFocusAnim.setBackgroundResource(R.drawable.anim_auto_focus);
        AnimationDrawable animAutoFocus = (AnimationDrawable) ivAutoFocusAnim.getBackground();
        int paddingCount = StaticUtils.convertDpToPixels(46 / 2);
        ivAutoFocusAnim.setX(point.x - paddingCount);
        ivAutoFocusAnim.setY(point.y - paddingCount);
        if (animAutoFocus.isRunning()) {
            animAutoFocus.stop();
        }
        animAutoFocus.start();
    }

    /**
     * Method to check weather camera support flash or not.
     *
     * @param context       activity context
     * @param camera        CameraView
     * @param isPreviewMode preview mode
     * @return true if camera support flash else false
     */
    public static boolean hashCameraFlash(Context context, CameraView camera, boolean isPreviewMode) {
        if (!PermissionUtils.hasPermission(context, CAMERA_PERMISSION))
            return false;
        try {
            if (camera == null) {
                return false;
            }
            Collection<Flash> flashParams;
            try {
                if (camera.getCameraOptions() == null)
                    return false;
                flashParams = camera.getCameraOptions().getSupportedFlash();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
                return false;
            }
            if (flashParams.size() == 0) {
                return false;
            }
            return !camera.getFacing().equals(Facing.FRONT) && !isPreviewMode;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return false;
    }

    /**
     * Common method to set camera flash and also update image appropriately
     *
     * @param camera      instance on CameraView
     * @param imgBtnFlash Flash Image button
     */
    public static void updateFlashImage(CameraView camera, AppCompatImageButton imgBtnFlash) {
        if (AppPrefShared.getString(Constants.CAMERA_FLASH_MODE, Flash.ON.toString())
                .equalsIgnoreCase(Flash.ON.toString())) {
            imgBtnFlash.setImageResource(R.drawable.ic_flash_camera_activity);
            try {
                camera.setFlash(Flash.ON);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        } else if (AppPrefShared.getString(Constants.CAMERA_FLASH_MODE, Flash.ON.toString())
                .equalsIgnoreCase(Flash.OFF.toString())) {
            imgBtnFlash.setImageResource(R.drawable.ic_flash_mode_off);
            try {
                camera.setFlash(Flash.OFF);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        } else if (AppPrefShared.getString(Constants.CAMERA_FLASH_MODE, Flash.ON.toString())
                .equalsIgnoreCase(Flash.AUTO.toString())) {
            imgBtnFlash.setImageResource(R.drawable.ic_flash_auto);
            try {
                camera.setFlash(Flash.AUTO);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    /**
     * Common method to change flash settings.
     * This method being called from camera fragment and camera activity java class
     *
     * @param context     Context of Activity
     * @param camera      Instance of CameraView
     * @param imgBtnFlash Flash image button
     */
    public static void changeFlashSettings(Context context, CameraView camera, AppCompatImageButton imgBtnFlash) {
        try {
            if (PermissionUtils.hasPermission(context, CAMERA_PERMISSION)) {
                imgBtnFlash.setSelected(true);
                if (camera.getFlash().equals(Flash.OFF)) {
                    camera.setFlash(Flash.ON);
                    AppPrefShared.putValue(Constants.CAMERA_FLASH_MODE, Flash.ON.toString());
                } else if (camera.getFlash().equals(Flash.ON)) {
                    camera.setFlash(Flash.AUTO);
                    AppPrefShared.putValue(Constants.CAMERA_FLASH_MODE, Flash.AUTO.toString());
                } else if (camera.getFlash().equals(Flash.AUTO)) {
                    camera.setFlash(Flash.OFF);
                    AppPrefShared.putValue(Constants.CAMERA_FLASH_MODE, Flash.OFF.toString());
                }

                MyCameraUtils.updateFlashImage(camera, imgBtnFlash);

            } else {
                imgBtnFlash.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static List<Tags> getTagData() {
        ArrayList<Tags> lstTags = new ArrayList<>();
        try {
            String[] tags = StaticUtils.getStringArrayFromString(AppPrefShared.getString(Constants.LOGGED_IN_USER_TAGS, null));
            for (String tag : tags) {
                Tags item = new Tags();
                item.setTagName(tag.trim());
                lstTags.add(item);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return lstTags;
    }

    public static void resetTagData(List<Tags> lstTags, AppCompatTextView tvTagCount) {
        lstTags.clear();
        tvTagCount.setText("");
    }

    public static void updateTagCount(AppCompatTextView tvTagCount, boolean isSelected) {
        int count = 0;
        try {
            if (tvTagCount != null)
                count = Integer.parseInt(tvTagCount.getText().toString());
        } catch (NumberFormatException e) {
        }
        if (isSelected) {
            count++;
        } else {
            count--;
        }

        if (count > 0 && tvTagCount != null) {
            tvTagCount.setVisibility(View.VISIBLE);
            tvTagCount.setText(String.valueOf(count));
        } else {
            if (tvTagCount != null) {
                tvTagCount.setVisibility(View.GONE);
                tvTagCount.setText("");
            }
        }
    }


    public static void shareImage(AppCompatImageButton imgBtnShare) {
        if (imgBtnShare != null) {
            if (imgBtnShare.isSelected()) {
                imgBtnShare.setSelected(false);
            } else {
                imgBtnShare.setSelected(true);
            }
        }
    }

    public static List<AllImageModel> filterImageOnExifDate(List<AllImageModel> lstAllImage){
        for (Iterator<AllImageModel> it = lstAllImage.iterator(); it.hasNext(); ) {
            AllImageModel imageModel = it.next();
            if (imageModel.getImagePath() != null && !imageModel.getImagePath().isEmpty()) {
                String exifDate = DateUtil.getDateFromExifMetadata(imageModel.getImagePath());
                String exifDateWithFormat = DateUtil.convertExpectedDateFormat(exifDate);
                Location locationExif = ImageUtil.getLocationFromImage(imageModel.getImagePath());
                if (TextUtils.isEmpty(exifDateWithFormat)||  locationExif == null || locationExif.getLatitude() == 0 || locationExif.getLongitude() == 0){
                    it.remove();
                }
            }else{
                it.remove();
            }
        }

        return lstAllImage;
    }


    public static void startFadeInAnimation(Context context, View view) {
        if (context == null)
            return;
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.fade_in);
        view.startAnimation(animation);
    }

    public static void startScaleUpAnimation(Context context, View view) {
        if (context == null)
            return;
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.scale_up);
        view.startAnimation(animation);
    }

    public static void startFadeOutAnimation(Context context, View view) {
        if (context == null)
            return;
        Animation animation = AnimationUtils.loadAnimation(context, R.anim.fade_out);
        view.startAnimation(animation);
    }


    public static void startScaleDownAnimation(Context context, View view) {
        if (context == null)
            return;

        Animation animation = AnimationUtils.loadAnimation(context, R.anim.scale_down);
        view.startAnimation(animation);
    }
}
