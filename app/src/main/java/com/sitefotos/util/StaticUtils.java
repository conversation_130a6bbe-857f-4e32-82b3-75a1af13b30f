package com.sitefotos.util;

import static com.sitefotos.Constants.DEVICE_PLATFORM;
import static com.sitefotos.Constants.ELEMENTS;
import static com.sitefotos.Constants.FORM_SUBMISSION_ID;
import static com.sitefotos.Constants.HRIMAGE;
import static com.sitefotos.Constants.IMAGEID;
import static com.sitefotos.Constants.IMAGEPATHHIGH;
import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.IS_SIGNATURE;
import static com.sitefotos.Constants.LOGGED_IN_USER_PARAM_EMPLOYEE_ID;
import static com.sitefotos.Constants.LRIMAGE;
import static com.sitefotos.Constants.MANAGE_CREW;
import static com.sitefotos.Constants.PARAM_CHECK_IN_DATE;
import static com.sitefotos.Constants.PARAM_CHECK_OUT_DATE;
import static com.sitefotos.Constants.PARAM_PHOTOS;
import static com.sitefotos.Constants.PARAM_TITLE;
import static com.sitefotos.Constants.PARAM_UUID;
import static com.sitefotos.Constants.PREF_MD5_KEY_DATA;
import static com.sitefotos.Constants.PARAM_SUBMITTED_FOR;
import static com.sitefotos.Constants.SELECTED_EQUIPMENTS;
import static com.sitefotos.Constants.SELECTED_VEHICLES;
import static com.sitefotos.Constants.TITLE;
import static com.sitefotos.Constants.TYPE;
import static com.sitefotos.Constants.VALUE;
import static com.sitefotos.util.ImageUtil.uriFromFile;

import android.app.Activity;
import android.app.KeyguardManager;
import android.content.ActivityNotFoundException;
import android.content.ComponentName;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.os.PowerManager;
import android.os.SystemClock;
import android.provider.MediaStore;
import android.provider.Settings;
import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.TextUtils;
import android.text.style.RelativeSizeSpan;
import android.util.DisplayMetrics;
import android.util.Patterns;
import android.util.TypedValue;
import android.view.Display;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.inputmethod.InputMethodManager;
import android.widget.CheckBox;
import android.widget.Chronometer;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ViewFlipper;

import androidx.annotation.RequiresApi;
import androidx.appcompat.widget.AppCompatTextView;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.camera.CameraActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.CheckInDetails;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.models.CrewSelectionData;
import com.sitefotos.models.DynamicDropDownItem;
import com.sitefotos.models.Employees;
import com.sitefotos.models.FormData;
import com.sitefotos.models.ItemModel;
import com.sitefotos.models.LangData;
import com.sitefotos.models.ListParameterizedType;
import com.sitefotos.models.Material;
import com.sitefotos.models.Md5Keys;
import com.sitefotos.models.MeasureData;
import com.sitefotos.models.PanelData;
import com.sitefotos.models.Routes;
import com.sitefotos.models.SegmentData;
import com.sitefotos.models.ServiceTaskStatus;
import com.sitefotos.models.SiteData;
import com.sitefotos.models.TMService;
import com.sitefotos.models.Tags;
import com.sitefotos.models.TimeLog;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.models.ValidationData;
import com.sitefotos.models.WorkLogProfileData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblServices;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.logger.CustomLogKt;

import org.brotli.dec.BrotliInputStream;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Created by dk on 22/8/17.
 */

public class StaticUtils {
    public static String filterIssueJSON(String jsonData) {
        String sourceData = "";
        sourceData = sourceData.concat(jsonData);
        JSONObject mainObject = null;
        try {
            mainObject = new JSONObject(sourceData);
            JSONArray jsonArray = mainObject.getJSONArray("Issues");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                JSONArray imageChildValue = object.getJSONArray(PARAM_PHOTOS);
                removeExtraParamsFromFileAndIssue(imageChildValue);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        if (mainObject != null) {
            CustomLogKt.errorLog("Filter Issue Data", "" + mainObject);
            return mainObject.toString();
        }
        return null;
    }


    public static String filterJSON(String jsonData) {
        String sourceData = "";
        sourceData = sourceData.concat(jsonData);
        JSONObject mainObject = null;
        try {
            mainObject = new JSONObject(sourceData);
            JSONArray jsonArray = mainObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    try {
                        jsonArrayRead(object.getJSONArray(Constants.ELEMENTS));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        if (mainObject != null) {
            CustomLogKt.errorLog("Filter Data", "" + mainObject);
            return mainObject.toString();
        }
        return null;
    }


    public static JSONArray getCloneOfJSONArray(JSONArray arraySelectedServiceData) {
        JSONArray clonedJsonArray = new JSONArray();
        try {
            for (int i = 0; i < arraySelectedServiceData.length(); i++) {
                JSONObject object = arraySelectedServiceData.getJSONObject(i);
                JSONObject clonnedJsonObject = new JSONObject(object.toString());
                clonedJsonArray.put(clonnedJsonObject);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return clonedJsonArray;
    }

    public static String filterPreSelectServiceJSON(JSONArray arrayPreSelectServiceData) {
        try {
            for (int i = 0; i < arrayPreSelectServiceData.length(); i++) {
                JSONObject object = arrayPreSelectServiceData.getJSONObject(i);
                readAndRemoveJSONObject(object);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        CustomLogKt.errorLog("Filter Data", "" + arrayPreSelectServiceData);
        return arrayPreSelectServiceData.toString();
    }

    /**
     * jsonArrayRead method is use for read json array and get single object
     * this method is call when there is child view is there.
     *
     * @param jsonArray is json array is use for get single from array
     */
    private static void jsonArrayRead(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                readAndRemoveJSONObject(object);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    /**
     * objectRead is use for read single object from object array
     *
     * @param object is json object use for get data from object
     */
    private static void readAndRemoveJSONObject(JSONObject object) {
        if (object.has(Constants.ELEMENTS)) {
            try {
                JSONArray jsonArray = object.getJSONArray(Constants.ELEMENTS);
                if (object.has(TYPE) && object.getString(TYPE).equalsIgnoreCase(MANAGE_CREW) && object.has(VALUE) && object.get(VALUE) instanceof JSONArray) {
                    //Here we need to filter data from value Array
                    jsonArrayRead(object.getJSONArray(VALUE));
                } else {
                    for (int i = 0; i < jsonArray.length(); i++) {
                        try {
                            JSONObject childObject = jsonArray.getJSONObject(i);
                            readAndRemoveJSONObject(childObject);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                    if (jsonArray.length() == 0) {
                        removeUnNecessaryDataFromJson(object);
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
        if (object.has(Constants.CHOICES)) {
            try {
                JSONArray choiceJsonArray = object.getJSONArray(Constants.CHOICES);
                for (int i = 0; i < choiceJsonArray.length(); i++) {
                    try {
                        if (choiceJsonArray.get(i) instanceof JSONObject) {
                            JSONObject childObject = choiceJsonArray.getJSONObject(i);
                            readAndRemoveJSONObject(childObject);
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        } else {
            removeUnNecessaryDataFromJson(object);

        }
    }

    private static void removeUnNecessaryDataFromJson(JSONObject object) {
        try {
            if (!object.has(Constants.TYPE)) {
                return;
            }
            String type = (String) object.get(Constants.TYPE);
            if (object.has(Constants.ID))
                object.remove(Constants.ID);

            if (object.has(Constants.TEMP_VALUE))
                object.remove(Constants.TEMP_VALUE);

            switch (type) {
                case Constants.SERVICE:
                    removeServiceOtherData(object);
                    break;
                case Constants.MATERIAL:
                    removeMaterialOtherData(object);
                    break;
                case Constants.CREW:
                    removeCrewOtherData(object);
                    break;

                case Constants.DYNAMIC_DROPDOWN:
                    removeDynamicDropDownOtherData(object);
                    break;
                case Constants.PANEL:
                    if (object.has(Constants.IS_GROUP_CREATED)) {
                        object.remove(Constants.IS_GROUP_CREATED);
                    }
                    break;
                case Constants.ISSUES:
                    removeIssueOtherData(object);
                    break;

                case Constants.IMAGE_UPLOAD:
                    removePhotoOtherData(object);
                    break;
                case Constants.GEO:
                    removeGeoOtherData(object);
                    break;

                case Constants.ADDRESS:
                    removeAddressData(object);
                    break;

                case Constants.SIGNATUREPAD:
                case Constants.SKETCH:
                    removeSignatureSketchOtherData(object);
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private static void removeMaterialOtherData(JSONObject object) {
        if (object.has(Constants.MATERIAL_DATA)) {
            object.remove(Constants.MATERIAL_DATA);
        }
    }

    private static void removeGeoOtherData(JSONObject object) {
        if (object.has(Constants.PARAM_LAT)) {
            object.remove(Constants.PARAM_LAT);
        }
        if (object.has(Constants.PARAM_LNG)) {
            object.remove(Constants.PARAM_LNG);
        }
    }

    private static void removeAddressData(JSONObject object) {
        if (object.has(Constants.VALUE)) {
            object.remove(Constants.VALUE);
        }
    }


    private static void removeCrewOtherData(JSONObject object) {
        if (object.has(Constants.VALUE_NAME)) {
            object.remove(Constants.VALUE_NAME);
        }


    }

    private static void removeDynamicDropDownOtherData(JSONObject object) {
        if (object.has(Constants.VALUE_DISPLAY)) {
            object.remove(Constants.VALUE_DISPLAY);
        }
    }

    /**
     * Method to remove temp used parameters to manage file and issue data flow internally
     * This is common function to remove un necessary data from file and issue components
     *
     * @param imageChildValue JSONArray
     */
    private static void removeExtraParamsFromFileAndIssue(JSONArray imageChildValue) {
        for (int j = 0; j < imageChildValue.length(); j++) {
            try {
                JSONObject childImageValue = (JSONObject) imageChildValue.get(j);
                if (childImageValue.has(IMAGEPATHHIGH)) {
                    childImageValue.remove(IMAGEPATHHIGH);
                }
                if (childImageValue.has(IMAGEPATHLOW)) {
                    childImageValue.remove(IMAGEPATHLOW);
                }
                if (childImageValue.has(IMAGEID)) {
                    childImageValue.remove(IMAGEID);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }

        }
    }

    private static void removeIssueOtherData(JSONObject object) {
        try {
            if (object.has(Constants.ISSUE_VALUE)) {
                JSONArray issueArray = object.getJSONArray(Constants.ISSUE_VALUE);
                for (int i = 0; i < issueArray.length(); i++) {
                    JSONObject childIssue = (JSONObject) issueArray.get(i);
                    if (childIssue.has(Constants.ISSUES_ISSUBMITTED)) {
                        childIssue.remove(Constants.ISSUES_ISSUBMITTED);
                    }
                    if (object.has(Constants.ID))
                        object.remove(Constants.ID);

                    JSONArray imageChildValue = childIssue.getJSONArray(Constants.VALUE);
                    for (int j = 0; j < imageChildValue.length(); j++) {
                        JSONObject childImageValue = (JSONObject) imageChildValue.get(j);
                        if (childImageValue.has(IMAGEPATHHIGH)) {
                            childImageValue.remove(IMAGEPATHHIGH);
                        }
                        if (childImageValue.has(IMAGEPATHLOW)) {
                            childImageValue.remove(IMAGEPATHLOW);
                        }
                        if (childImageValue.has(IMAGEID)) {
                            childImageValue.remove(IMAGEID);
                        }

                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private static void removeSignatureSketchOtherData(JSONObject object) {
        try {
            if (object.has(Constants.VALUE)) {
                JSONArray childValueArray = object.getJSONArray(Constants.VALUE);
                for (int i = 0; i < childValueArray.length(); i++) {
                    JSONObject childValueObject = (JSONObject) childValueArray.get(i);
                    if (childValueObject.has(IMAGEPATHHIGH)) {
                        childValueObject.remove(IMAGEPATHHIGH);
                    }
                    if (childValueObject.has(IMAGEPATHLOW)) {
                        childValueObject.remove(IMAGEPATHLOW);
                    }
                    if (childValueObject.has(IMAGEID)) {
                        childValueObject.remove(IMAGEID);
                    }

                    if (childValueObject.has(IS_SIGNATURE)) {
                        childValueObject.remove(IS_SIGNATURE);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }


    private static void removePhotoOtherData(JSONObject object) {
        try {
            if (object.has(Constants.VALUE)) {
                JSONArray childValueArray = object.getJSONArray(Constants.VALUE);
                for (int i = 0; i < childValueArray.length(); i++) {
                    JSONObject childValueObject = (JSONObject) childValueArray.get(i);
                    if (childValueObject.has(IMAGEPATHHIGH)) {
                        childValueObject.remove(IMAGEPATHHIGH);
                    }
                    if (childValueObject.has(IMAGEPATHLOW)) {
                        childValueObject.remove(IMAGEPATHLOW);
                    }
                    if (childValueObject.has(IMAGEID)) {
                        childValueObject.remove(IMAGEID);
                    }

                    if (childValueObject.has(Constants.PIN_LABEL)) {
                        childValueObject.put(Constants.MAP_PIN_URL, addOrOverrideTitleQueryParam(childValueObject.getString(Constants.MAP_PIN_URL), childValueObject.getString(Constants.PIN_LABEL)));
                        childValueObject.remove(Constants.PIN_LABEL);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Method to build query parameters with url.
     *
     * @param urlString main Url
     * @param title     Query to apend with url
     * @return
     */
    public static String addOrOverrideTitleQueryParam(String urlString, String title) {
        Uri originalUri = Uri.parse(urlString);

        // Use Uri.Builder to build the modified URI
        Uri.Builder builder = originalUri.buildUpon();

        // If the Title parameter is already present, this will override it
        builder.clearQuery();

        // Add or override the "title" parameter
        builder.appendQueryParameter("title", title);

        return builder.toString();

    }

    private static void removeServiceOtherData(JSONObject object) {
        try {
            if (object.has(Constants.TnMService)) {
                object.remove(Constants.TnMService);
            }
            if (object.has(Constants.TEMP_TAG_ID)) {
                object.remove(Constants.TEMP_TAG_ID);
            }

            if (object.getString(Constants.SERVICETYPE).equals("AsDetail") || object.getString(Constants.SERVICETYPE).equals("AsTask")) {
                object.remove(Constants.TIMELOG);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /*public static String getValueJson(String jsonData, String imageData) {
        JSONArray formDataArray = new JSONArray();

        try {
            JSONObject jsonObject = new JSONObject(jsonData);

            JSONArray jsonArray = jsonObject.getJSONArray(Constants.PAGES);
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            jsonArrayRead(array, formDataArray, imageData);

                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            return formDataArray.toString();
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

            return null;
        }

    }*/

    public static void hideSoftKeyboard(Activity activity) {
        if (activity != null && activity.getCurrentFocus() != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
            if (inputMethodManager != null)
                inputMethodManager.hideSoftInputFromWindow(activity.getCurrentFocus().getWindowToken(), 0);
        }
    }

    public static void hideSoftKeyboard(Activity activity, View view) {
        if (activity != null && view != null) {
            try {
                InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null)
                    imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }


    public static JSONArray getNotSubmittedIssueDataJson(String modifiedData) {
        JSONArray formDataArray = new JSONArray();
        if (modifiedData == null)
            return formDataArray;
        try {
            JSONObject jsonObject = new JSONObject(modifiedData);
            JSONArray jsonArray = jsonObject.getJSONArray(Constants.PAGES);
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            jsonArrayForIssue(array, formDataArray);

                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            return formDataArray;
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

            return null;
        }

    }

    /*private static void jsonArrayRead(JSONArray jsonArray, JSONArray formDataArray, String imageData) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.PANEL)) {
                        JSONObject componentResult = new JSONObject();
                        componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                        componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                        componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                        formDataArray.put(componentResult);
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SERVICE)) {
                        if (object.has(Constants.TnMService)) {
                            JSONObject serviceObject = new JSONObject();
                            TMService tmService;
                            tmService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                            serviceObject.put(Constants.SERVICEID, String.valueOf(tmService.getServiceID()));
                            if (object.getString("serviceType").equalsIgnoreCase("AsTimer")) {
                                serviceObject.put(Constants.TIMELOG, getTimeLogsForServiceAsATimer(tmService));
                            }

                            JSONArray jsonOptionArray = new JSONArray();
                            if (tmService.getServiceOptions() != null && tmService.getServiceOptions().getOptions().size() > 0) {
                                if (tmService.getServiceOptions().getOptionsValue() == null) {
                                    tmService.getServiceOptions().setOptionsValue(new ArrayList<>());
                                }
                                for (String option : tmService.getServiceOptions().getOptionsValue()) {
                                    jsonOptionArray.put(option);
                                }

                            }
                            serviceObject.put(Constants.OPTIONS, jsonOptionArray);
                            if (object.getString("serviceType").equalsIgnoreCase("AsDetail")) {

                                serviceObject.put(Constants.PEOPLE, tmService.getPeople());
                                serviceObject.put(Constants.HOUR, tmService.getHours());
                            }

                            if (object.getString("serviceType").equalsIgnoreCase("AsTask")) {
                                serviceObject.put("Completed", tmService.isCompleted());
                            }
                            serviceObject.put(Constants.SF_ID, object.getString(Constants.SF_ID));
                            serviceObject.put(Constants.SITEID, object.getString(Constants.SITEID));
                            serviceObject.put(Constants.NAME, object.getString(Constants.NAME));
                            serviceObject.put(Constants.TYPE, object.getString(Constants.TYPE));
                            serviceObject.put(Constants.TITLE, tmService.getServiceName());
                            formDataArray.put(serviceObject);
                        }
                    }

                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayRead(array, formDataArray, imageData);
                } else if (object.has(Constants.CHOICES)) {
                    if (object.has(Constants.VALUE)) {
                        if (object.has(Constants.TITLE)) {
                            JSONObject componentResult = new JSONObject();
                            componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                            componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                            componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                            if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SPINNER) ||
                                    object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SEGMENT_INPUT)) {
                                componentResult.put(Constants.VALUE, object.get(Constants.VALUE));
                            } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.CHECKBOX)) {
                                if (object.has(Constants.VALUE)) {
                                    if (object.get(Constants.VALUE) instanceof String) {
                                        JSONArray valueJsonArray;
                                        valueJsonArray = StaticUtils.getArrayFromString(object.getString(Constants.VALUE));
                                        componentResult.put(Constants.VALUE, valueJsonArray);
                                    } else {
                                        componentResult.put(Constants.VALUE, object.getJSONArray(VALUE));
                                    }
                                } else {
                                    componentResult.put(Constants.VALUE, new JSONArray());
                                }
*//*
                                if (!TextUtils.isEmpty(object.getString(Constants.VALUE))) {
                                    try {
                                        JSONArray checkboxJsonArray = new JSONArray();
                                        String[] checkboxes = object.getString(Constants.VALUE).split(",");
                                        for (String checkbox : checkboxes) {
                                            checkboxJsonArray.put(checkbox);
                                        }
                                        componentResult.put(Constants.VALUE, checkboxJsonArray);
                                    } catch (JSONException e) {
                                        FirebaseEventUtils.logException(e);
                                    }
                                }*//*
                            } else {
                                componentResult.put(Constants.VALUE, object.get(Constants.VALUE));
                            }
                            formDataArray.put(componentResult);
                        }
                    }

                    String value = "";
                    if (object.has(Constants.VALUE)) {
                        value = object.getString(Constants.VALUE);
                    }
                    JSONArray array = object.getJSONArray(Constants.CHOICES);
                    for (int j = 0; j < array.length(); j++) {
                        JSONObject jsonObject = null;
                        try {
                            jsonObject = array.getJSONObject(j);
                        } catch (JSONException e) {
                            // FirebaseEventUtils.logException(e);
                        }
                        if (jsonObject != null) {
                            String choiceValue = jsonObject.getString(Constants.CHOICEVALUE);
                            if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SPINNER) ||
                                    object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SEGMENT_INPUT)) {
                                if (choiceValue.equals(value))
                                    if (jsonObject.has(Constants.ELEMENTS)) {
                                        try {
                                            JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                            if (jsonArray1 != null) {
                                                jsonArrayRead(jsonArray1, formDataArray, imageData);
                                            }
                                        } catch (JSONException e) {
                                            FirebaseEventUtils.logException(e);
                                        }
                                    }
                            } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.CHECKBOX)) {
                                if (value.contains(choiceValue))
                                    if (jsonObject.has(Constants.ELEMENTS)) {
                                        try {
                                            JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                            if (jsonArray1 != null) {
                                                jsonArrayRead(jsonArray1, formDataArray, imageData);
                                            }
                                        } catch (JSONException e) {
                                            FirebaseEventUtils.logException(e);

                                        }
                                    }
                            } else {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    try {
                                        JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                        if (jsonArray1 != null) {
                                            jsonArrayRead(jsonArray1, formDataArray, imageData);
                                        }
                                    } catch (JSONException e) {
                                        FirebaseEventUtils.logException(e);

                                    }
                                }
                            }
                        }
                    }
                } else {
                    if (object.has(Constants.VALUE)) {
                        if (object.has(Constants.TITLE)) {
                            if (object.getString(Constants.TYPE).equals(Constants.IMAGE_UPLOAD)) {

                                JSONObject componentResult = new JSONObject();
                                JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);

                                for (int k = 0; k < jsonArray1.length(); k++) {
                                    JSONObject object1 = jsonArray1.getJSONObject(k);
                                    if (object1.has(IMAGEID)) {
                                        object1.remove(IMAGEID);
                                    }
                                    if (object1.has(Constants.TAGID)) {
                                        object1.remove(Constants.TAGID);
                                    }
                                    if (object1.has(Constants.IMAGEPATHLOW)) {
                                        object1.remove(Constants.IMAGEPATHLOW);
                                    }
                                    if (object1.has(IMAGEPATHHIGH)) {
                                        object1.remove(IMAGEPATHHIGH);
                                    }
                                    if (object1.has(Constants.IS_SIGNATURE)) {
                                        object1.remove(Constants.IS_SIGNATURE);
                                    }
                                    if (object1.has(Constants.TAGID)) {
                                        object1.remove(Constants.TAGID);
                                    }

                                    if (object1.has(HRIMAGE)) {
                                        if (TextUtils.isEmpty(object1.getString(HRIMAGE))) {
                                            object1.remove(HRIMAGE);
                                        }
                                    }
                                }

                                componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                                componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                componentResult.put(Constants.VALUE, jsonArray1);
                                formDataArray.put(componentResult);


                            } else if (object.getString(Constants.TYPE).equals(Constants.SIGNATUREPAD) || object.getString(Constants.TYPE).equals(Constants.SKETCH)) {
                                JSONObject componentResult = new JSONObject();
                                try {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    String signPathHigh = "";
                                    String signPathLow = "";
                                    for (int k = 0; k < jsonArray1.length(); k++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(k);

                                        if (object1.has(HRIMAGE)) {
                                            if (TextUtils.isEmpty(object1.getString(HRIMAGE))) {
                                                object1.remove(HRIMAGE);
                                            } else {
                                                signPathHigh = object1.getString(HRIMAGE);
                                            }
                                        }

                                        if (object1.has(Constants.LRIMAGE)) {
                                            signPathLow = object1.getString(Constants.LRIMAGE);
                                        }
                                    }
                                    componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                    componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                                    componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                    JSONObject jsonObject = new JSONObject();
                                    if (!TextUtils.isEmpty(signPathHigh))
                                        jsonObject.put(HRIMAGE, signPathHigh);
                                    jsonObject.put(Constants.LRIMAGE, signPathLow);
                                    componentResult.put(Constants.VALUE, jsonObject);
                                    formDataArray.put(componentResult);
                                } catch (JSONException e) {
                                    componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                    componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                                    componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                    componentResult.put(Constants.VALUE, "");
                                    formDataArray.put(componentResult);
                                }
                            } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.MATERIAL)) {
                                if (object.has(Constants.MATERIAL)) {
                                    try {
                                        if (object.get(Constants.MATERIAL) instanceof JSONArray) {
                                            JSONArray materialObject = object.getJSONArray(Constants.MATERIAL);
                                            if (materialObject != null) {
                                                JSONObject componentResult = new JSONObject();
                                                componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                                componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                                if (object.has(Constants.UNIT)) {
                                                    componentResult.put(Constants.UNIT, object.getString(Constants.UNIT));
                                                }
                                                String title = "";
                                                if (object.has(Constants.TITLE)) {
                                                    title = object.getString(Constants.TITLE);
                                                }
                                                componentResult.put(Constants.TITLE, title);
                                                formDataArray.put(componentResult);
                                                addRepeatableInMaterialJson(object, formDataArray);
                                                continue;
                                            }
                                        }

                                    } catch (JSONException e) {
                                        FirebaseEventUtils.logException(e);

                                    }
                                }
                                JSONObject componentResult = new JSONObject();
                                componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                componentResult.put(Constants.QUANTITY, object.getString(Constants.VALUE));
                                componentResult.put(Constants.UNIT, object.getString(Constants.UNIT));
                                Material material = null;
                                try {
                                    material = new Gson().fromJson(object.getString(Constants.MATERIAL_DATA), Material.class);
                                } catch (JsonSyntaxException | JSONException e) {
                                    FirebaseEventUtils.logException(e);

                                }
                                if (material == null) {
                                    componentResult.put(Constants.TITLE, "");
                                    componentResult.put(Constants.UNIT, "");
                                } else {
                                    componentResult.put(Constants.TITLE, material.getMaterialName());
                                    componentResult.put(Constants.UNIT, material.getMaterialUnit());

                                }
                                componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                componentResult.put(Constants.QUANTITY, object.getString(Constants.VALUE));
                                componentResult.put(Constants.UNIT, object.getString(Constants.UNIT));

                                formDataArray.put(componentResult);

                            } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.CREW)) {
                                JSONObject componentResult = new JSONObject();
                                componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                                if (object.has(Constants.NAME))
                                    componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                if (object.has(Constants.SF_ID))
                                    componentResult.put(Constants.SF_ID, object.getString(Constants.SF_ID));
                                if (object.has(Constants.SITEID))
                                    componentResult.put(Constants.SITEID, object.getString(Constants.SITEID));
                                if (object.has(Constants.VALUE)) {
                                    componentResult.put(Constants.VALUE, object.getJSONArray(Constants.VALUE));
                                } else {
                                    componentResult.put(Constants.VALUE, new JSONArray());
                                }
                                formDataArray.put(componentResult);


                            } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.ISSUES)) {
                                if (object.has(Constants.ISSUE_VALUE)) {
                                    formDataArray.put(getIssueDataForShortJson(object, imageData));
                                }
                            } else {
                                JSONObject componentResult = new JSONObject();
                                componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                                componentResult.put(Constants.VALUE, object.getString(Constants.VALUE));
                                formDataArray.put(componentResult);
                            }
                        }
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.GEO)) {
                        JSONObject componentResult = new JSONObject();
                        componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                        componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                        componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                        componentResult.put(Constants.VALUE, object.getString(Constants.BUILDING_NAME));
                        componentResult.put(Constants.BUILDING_ID, object.getString(Constants.BUILDING_ID));
                        if (object.has(Constants.PARAM_LAT))
                            componentResult.put(Constants.PARAM_LAT, object.getString(Constants.PARAM_LAT));
                        if (object.has(Constants.PARAM_LNG))
                            componentResult.put(Constants.PARAM_LNG, object.getString(Constants.PARAM_LNG));
                        formDataArray.put(componentResult);
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.PANEL)) {
                        JSONObject componentResult = new JSONObject();
                        componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                        componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                        componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                        formDataArray.put(componentResult);
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SUB_HEADER)) {
                        JSONObject componentResult = new JSONObject();
                        componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                        componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                        componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                        formDataArray.put(componentResult);
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.CREW)) {
                        JSONObject componentResult = new JSONObject();
                        componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                        componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
                        if (object.has(Constants.NAME))
                            componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                        if (object.has(Constants.SF_ID))
                            componentResult.put(Constants.SF_ID, object.getString(Constants.SF_ID));
                        if (object.has(Constants.SITEID))
                            componentResult.put(Constants.SITEID, object.getString(Constants.SITEID));
                        if (object.has(Constants.VALUE)) {
                            componentResult.put(Constants.VALUE, object.getJSONArray(Constants.VALUE));
                        } else {
                            componentResult.put(Constants.VALUE, new JSONArray());
                        }
                        formDataArray.put(componentResult);
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.ISSUES)) {
                        if (object.has(Constants.ISSUE_VALUE)) {
                            formDataArray.put(getIssueDataForShortJson(object, imageData));
                        }
                    } else if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.MATERIAL)) {
                        if (object.has(Constants.MATERIAL)) {
                            try {
                                if (object.get(Constants.MATERIAL) instanceof JSONArray) {
                                    JSONArray materialObject = object.getJSONArray(Constants.MATERIAL);
                                    if (materialObject != null) {
                                        JSONObject componentResult = new JSONObject();
                                        componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                                        componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                                        componentResult.put(Constants.UNIT, object.getString(Constants.UNIT));
                                        String title = "";
                                        if (object.has(Constants.TITLE)) {
                                            title = object.getString(Constants.TITLE);
                                        }
                                        componentResult.put(Constants.TITLE, title);
                                        formDataArray.put(componentResult);
                                        addRepeatableInMaterialJson(object, formDataArray);
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        } else {
                            JSONObject componentResult = new JSONObject();
                            componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                            componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                            componentResult.put(Constants.QUANTITY, object.getString(Constants.VALUE));
                            componentResult.put(Constants.UNIT, object.getString(Constants.UNIT));
                            Material material = new Gson().fromJson(object.getString(Constants.MATERIAL_DATA), Material.class);
                            if (material == null) {
                                componentResult.put(Constants.TITLE, "");
                                componentResult.put(Constants.UNIT, "");
                            } else {
                                componentResult.put(Constants.TITLE, material.getMaterialName());
                                componentResult.put(Constants.UNIT, material.getMaterialUnit());
                            }
                            formDataArray.put(componentResult);
                        }
                    } else if (object.has(Constants.TnMService)) {
                        JSONObject serviceObject = new JSONObject();
                        TMService tmService;
                        tmService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                        serviceObject.put(Constants.SERVICEID, String.valueOf(tmService.getServiceID()));
                        if (object.getString("serviceType").equalsIgnoreCase("AsTimer")) {
                            if (tmService.getStartTime() > 0) {
                                serviceObject.put("StartTime", tmService.getStartTime());
                                serviceObject.put("StopTime", System.currentTimeMillis() / 1000);
                            } else {
                                serviceObject.put("StartTime", 0);
                                serviceObject.put("StopTime", 0);
                            }
                        }

                        JSONArray jsonOptionArray = new JSONArray();
                        if (tmService.getServiceOptions() != null && tmService.getServiceOptions().getOptions().size() > 0) {
                            if (tmService.getServiceOptions().getOptionsValue() == null) {
                                tmService.getServiceOptions().setOptionsValue(new ArrayList<>());
                            }
                            for (String option : tmService.getServiceOptions().getOptionsValue()) {
                                jsonOptionArray.put(option);
                            }

                        }
                        serviceObject.put(Constants.OPTIONS, jsonOptionArray);
                        if (object.getString("serviceType").equalsIgnoreCase("AsDetail")) {

                            serviceObject.put(Constants.PEOPLE, tmService.getPeople());
                            serviceObject.put(Constants.HOUR, tmService.getHours());
                        }

                        if (object.getString("serviceType").equalsIgnoreCase("AsTask")) {
                            serviceObject.put("Completed", tmService.isCompleted());
                        }
                        serviceObject.put(Constants.SF_ID, object.getString(Constants.SF_ID));
                        serviceObject.put(Constants.SITEID, object.getString(Constants.SITEID));
                        serviceObject.put(Constants.NAME, object.getString(Constants.NAME));
                        serviceObject.put(Constants.TYPE, object.getString(Constants.TYPE));
                        serviceObject.put(Constants.TITLE, tmService.getServiceName());
                        formDataArray.put(serviceObject);


                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        }
    }*/

    /**
     * Common method to get all time log data from List and set in JSONArray
     *
     * @param tmService
     * @return
     */
    public static JSONArray getTimeLogsForServiceAsATimer(TMService tmService) {

        JSONArray timeLogArray = new JSONArray();
        try {
            List<TimeLog> lstTimeLog = tmService.getLstTimeLog();
            if (lstTimeLog != null && !lstTimeLog.isEmpty()) {
                for (TimeLog timeLog : lstTimeLog) {
                    JSONObject timeLogDataObject = new JSONObject();
                    timeLogDataObject.put("StartTime", timeLog.getStartTime());
                    if (timeLog.getStopTime() > 0)
                        timeLogDataObject.put("StopTime", timeLog.getStopTime());
                    else
                        timeLogDataObject.put("StopTime", System.currentTimeMillis() / 1000);
                    timeLogArray.put(timeLogDataObject);
                }
            } else if (tmService.getStartTime() > 0) {
                //Manage Old app version(pre 3.0.0) data
                JSONObject timeLogDataObject = new JSONObject();
                timeLogDataObject.put("StartTime", tmService.getStartTime());
                timeLogDataObject.put("StopTime", System.currentTimeMillis() / 1000);
                timeLogArray.put(timeLogDataObject);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        return timeLogArray;

    }


    private static void addRepeatableInMaterialJson(JSONObject object, JSONArray formDataArray) {
        JSONArray jsonArray;
        if (object.has(Constants.VALUE)) {
            try {
                jsonArray = object.getJSONArray(Constants.VALUE);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    JSONObject componentResult = new JSONObject();
                    componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
                    componentResult.put(Constants.NAME, object.getString(Constants.NAME));
                    String title = "";
                    if (jsonObject.has(Constants.TITLE)) {
                        title = jsonObject.getString(Constants.TITLE);
                    }
                    componentResult.put(Constants.TITLE, title);
                    String unit = "";
                    if (jsonObject.has(Constants.UNIT)) {
                        unit = jsonObject.getString(Constants.UNIT);
                    }
                    componentResult.put(Constants.UNIT, unit);
                    String quantity = "0";
                    if (jsonObject.has(Constants.QUANTITY)) {
                        quantity = jsonObject.getString(Constants.QUANTITY);
                    }
                    componentResult.put(Constants.QUANTITY, quantity);
                    formDataArray.put(componentResult);
                }

            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    private static JSONObject getIssueDataForShortJson(JSONObject object, String imageData) {
        JSONObject componentResult = new JSONObject();
        try {
            if (object.has(Constants.TITLE))
                componentResult.put(Constants.TITLE, object.getString(Constants.TITLE));
            componentResult.put(Constants.TYPE, object.getString(Constants.TYPE));
            componentResult.put(Constants.NAME, object.getString(Constants.NAME));
            JSONArray jsonArray1 = new JSONArray();
            JSONArray jsonArray2 = object.getJSONArray(Constants.ISSUE_VALUE);
            for (int k = 0; k < jsonArray2.length(); k++) {
                JSONObject jsonObject = jsonArray2.getJSONObject(k);
                JSONObject jsonObject1 = new JSONObject();
                jsonObject1.put(Constants.TYPE, object.getString(Constants.TYPE));
                if (jsonObject.has(Constants.ISSUES_TEXE))
                    jsonObject1.put(Constants.TITLE, jsonObject.getString(Constants.ISSUES_TEXE));
                jsonObject1.put(Constants.NAME, object.getString(Constants.NAME));
                if (jsonObject.has(Constants.VALUE)) {
                    String issueValueString = jsonObject.getString(Constants.VALUE);
                    JSONArray issueArray = new JSONArray(issueValueString);
                    for (int l = 0; l < issueArray.length(); l++) {
                        JSONObject subData = issueArray.getJSONObject(l);
                        JSONObject lrhrData = StaticUtils.getHrAndLrFromImageData(imageData, subData.getString(IMAGEPATHLOW));
                        String lrImage = "";
                        String hrImage = "";
                        if (lrhrData != null) {
                            lrImage = lrhrData.getString(Constants.LRIMAGE);
                            hrImage = lrhrData.getString(HRIMAGE);
                            if (lrhrData.has(Constants.IMAGE_UUID)) {
                                subData.put(Constants.IMAGE_UUID, lrhrData.getString(Constants.IMAGE_UUID));
                            }
                        }
                        subData.put(LRIMAGE, lrImage);
                        subData.put(HRIMAGE, hrImage);
                    }
                    jsonObject1.put(Constants.VALUE, jsonObject.get(Constants.VALUE));
                }
                jsonArray1.put(jsonObject1);
            }
            componentResult.put(Constants.VALUE, jsonArray1);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        return componentResult;
    }


    public static String getWPDataFromForm(Context context, String modifiedData, FormData formData, SiteData siteData) {
        JSONObject mainObject = new JSONObject();
        try {
            if (siteData != null)
                mainObject.put("SiteID", siteData.getSiteId());
            else
                mainObject.put("SiteID", -2);
            mainObject.put("AppProfileID", formData.getFormId());


            JSONArray materialJasonArray = new JSONArray();
            JSONArray serviceArray = new JSONArray();
            JSONArray crewArray = new JSONArray();

            if (!TextUtils.isEmpty(modifiedData)) {
                JSONObject jsonObject = new JSONObject(modifiedData);
                JSONArray jsonArray = jsonObject.getJSONArray(Constants.PAGES);
                for (int i = 0; i < jsonArray.length(); i++) {
                    try {
                        JSONObject object = jsonArray.getJSONObject(i);
                        if (object.has(Constants.ELEMENTS)) {
                            try {
                                JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                                setStructureForWPComponent(context, array, mainObject, materialJasonArray, serviceArray, crewArray, formData, siteData);

                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
            mainObject.put("SubmittedTime", System.currentTimeMillis() / 1000);
            if (materialJasonArray.length() == 0 && serviceArray.length() == 0 && crewArray.length() == 0) {
                //Will return empty if there is no WP component in the form
                return "";
            }
            return mainObject.toString();
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

            return null;
        }
    }

    private static void setStructureForWPComponent(Context context, JSONArray jsonArray, JSONObject mainObject,
                                                   JSONArray materialJasonArray, JSONArray serviceArray,
                                                   JSONArray crewArray, FormData formData, SiteData siteData) {
        try {
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.TYPE)) {
                    if (object.getString(Constants.TYPE).equals(Constants.MATERIAL)) {
                        if (object.has(Constants.MATERIAL_DATA)) {
                            try {
                                Material material = new Gson().fromJson(object.getString(Constants.MATERIAL_DATA), Material.class);
                                JSONObject materialObject = new JSONObject();
                                if (material == null)
                                    continue;
                                if (material.getMaterialID() == 0)
                                    materialObject.put("MaterialID", -1);
                                else
                                    materialObject.put("MaterialID", material.getMaterialID());
                                materialObject.put("MaterialName", material.getMaterialName());
                                materialObject.put("MaterialUnit", material.getMaterialUnit());
                                String value;
                                if (object.has(Constants.VALUE)) {
                                    value = object.getString(Constants.VALUE);
                                } else {
                                    value = String.valueOf(material.getUsage());
                                }
                                materialObject.put("Usage", value);
                                if (!TextUtils.isEmpty(value)) {
                                    materialJasonArray.put(materialObject);
                                }

                            } catch (JsonSyntaxException | JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }
                    } else if (object.getString(Constants.TYPE).equals(Constants.CREW)) {

                        if (object.has(Constants.VALUE)) {
                            try {
                                JSONArray newCrewArray = object.getJSONArray(Constants.VALUE);
                                for (int j = 0; j < newCrewArray.length(); j++) {
                                    checkAndAddInArray(crewArray, (Integer) newCrewArray.get(j));
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        }
                    } else if (object.getString(Constants.TYPE).equals(Constants.SERVICE)) {
                        JSONObject serviceObject = new JSONObject();
                        TMService tMService = null;
                        try {
                            if (object.has(Constants.TnMService)) {
                                tMService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                            }
                        } catch (JsonSyntaxException | JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                        if (tMService == null)
                            continue;

                        serviceObject.put("sf_id", formData.getFormId());
                        if (siteData != null)
                            serviceObject.put("siteID", siteData.getSiteId());
                        else
                            serviceObject.put("siteID", -2);
                        serviceObject.put(Constants.SERVICEID, tMService.getServiceID());
                        serviceObject.put("ServiceName", tMService.getServiceName());
                        serviceObject.put("type", object.getString(Constants.TYPE));
                        if (object.getString("serviceType").equalsIgnoreCase("AsTimer")) {
                            if (tMService.getStartTime() > 0) {
                                try {

                                    serviceObject.put(Constants.TIMELOG, getTimeLogsForServiceAsATimer(tMService));

                                    if (siteData != null) {
                                        if (formData.isSubForm()) {
                                            setSiteAndFormBroadCrumbs(context, 11, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService, true, formData.getSubFormOtherData().getCrewId());
                                        } else {
                                            setSiteAndFormBroadCrumbs(context, 11, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService, false, 0);
                                        }
                                    } else {
                                        if (formData.isSubForm()) {
                                            setSiteAndFormBroadCrumbs(context, 11, -2, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService, true, formData.getSubFormOtherData().getCrewId());
                                        } else {
                                            setSiteAndFormBroadCrumbs(context, 11, -2, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService, false, 0);
                                        }
                                    }
                                } catch (JSONException e) {
                                    FirebaseEventUtils.logException(e);

                                }
                            } else {
                                continue;
                            }
                        }

                        if (object.getString("serviceType").equalsIgnoreCase("AsDetail")) {
                            if (!tMService.isCompleted())
                                continue;

                            serviceObject.put(Constants.PEOPLE, tMService.getPeople());
                            serviceObject.put(Constants.HOUR, tMService.getHours());
                        }

                        if (object.getString("serviceType").equalsIgnoreCase("AsTask")) {
                            if (!tMService.isCompleted())
                                continue;
                            serviceObject.put("Completed", tMService.isCompleted());
                        }
                        if (tMService.getServiceOptions() != null && tMService.getServiceOptions().getOptions().size() > 0) {
                            if (tMService.getServiceOptions().getOptionsValue() == null) {
                                tMService.getServiceOptions().setOptionsValue(new ArrayList<>());
                            }
                            JSONArray optionArray = new JSONArray();
                            try {
                                for (String option : tMService.getServiceOptions().getOptionsValue()) {
                                    optionArray.put(option);
                                }
                            } catch (Exception e) {
                                FirebaseEventUtils.logException(e);

                            }
                            serviceObject.put(Constants.OPTIONS, optionArray);
                        } else {

                            serviceObject.put(Constants.OPTIONS, new JSONArray());
                        }

                        serviceArray.put(serviceObject);

                    } else if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            setStructureForWPComponent(context, array, mainObject, materialJasonArray, serviceArray, crewArray, formData, siteData);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                }
            }
            mainObject.put("MaterialUsage", materialJasonArray);
            mainObject.put("ServicesProvided", serviceArray);
            mainObject.put("Crew", crewArray);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

    }


    public static ServiceTaskStatus checkServiceTypeAsTask(String modifiedData) {
        ServiceTaskStatus taskStatus = new ServiceTaskStatus();
        try {
            if (!TextUtils.isEmpty(modifiedData)) {
                JSONObject jsonObject = new JSONObject(modifiedData);
                JSONArray jsonArray = jsonObject.getJSONArray(Constants.PAGES);
                for (int i = 0; i < jsonArray.length(); i++) {
                    try {
                        JSONObject object = jsonArray.getJSONObject(i);
                        if (object.has(Constants.ELEMENTS)) {
                            try {
                                JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                                checkServiceTypeTaskCompleted(array, taskStatus);
                                if (taskStatus.isServiceTaskAvailable() && taskStatus.isCompleted()) {
                                    break;
                                }

                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
            return taskStatus;
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

            return taskStatus;
        }
    }


    private static ServiceTaskStatus checkServiceTypeTaskCompleted(JSONArray jsonArray, ServiceTaskStatus detailStatus) {
        try {
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.TYPE)) {
                    if (object.getString(Constants.TYPE).equals(Constants.SERVICE)) {
                        TMService tMService = null;
                        try {
                            tMService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                        } catch (JsonSyntaxException | JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                        if (tMService == null)
                            continue;

                        if (object.getString("serviceType").equalsIgnoreCase("AsTask")) {
                            detailStatus.setServiceTaskAvailable(true);
                            if (tMService.isCompleted()) {
                                detailStatus.setCompleted(true);
                            }
                        }

                    } else if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            checkServiceTypeTaskCompleted(array, detailStatus);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        return detailStatus;

    }

    private static void setSiteAndFormBroadCrumbs(Context context, int type, long siteId, long formId, String formName, String formSubmissionId, TMService service, boolean isSubForm, int empId) {
        ((BaseActivity) context).prepareDataForBreadCrumb(type, MainActivity.currentLatitude, MainActivity.currentLongitude, siteId, formId, formName, formSubmissionId, service, isSubForm, empId);
    }

    private static void jsonArrayForIssue(JSONArray jsonArray, JSONArray formDataArray) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.getString(Constants.TYPE).equals(Constants.ISSUES) && object.has(Constants.ISSUE_VALUE)) {
                    JSONArray jsonValueArray = object.getJSONArray(Constants.ISSUE_VALUE);
                    for (int j = 0; j < jsonValueArray.length(); j++) {
                        JSONObject object1 = jsonValueArray.getJSONObject(j);
                        if (object1.has(Constants.ISSUES_ISSUBMITTED)) {
                            if (!object1.getBoolean(Constants.ISSUES_ISSUBMITTED)) {
                                if (object1.has(Constants.ISSUES_TEXE)) {
                                    formDataArray.put(object1);
                                }
                            }
                        }
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        }
    }

    public static JSONObject getHrAndLrFromImageData(String imageData, String imagePathLow) {
        try {
            JSONObject jsonObject = new JSONObject(imageData);
            JSONArray array = jsonObject.getJSONArray(Constants.DATA);
            for (int k = 0; k < array.length(); k++) {
                JSONObject object2 = array.getJSONObject(k);
                if (object2.getString(Constants.IMAGEPATHLOW).trim().equals(imagePathLow.trim().replaceAll("\\'", "'"))) {
                    return object2;
                }

            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        return null;
    }

    /**
     * Method to set all components in one hierarchy. this method with get all childes from Element and set under one parent element.
     *
     * @param jsonData
     * @return
     */
    public static String getModifyJson(String jsonData) {
        //JSONObject resultJson = new JSONObject();
        try {
            JSONObject jsonObject = new JSONObject(jsonData);

            JSONArray jsonArray = jsonObject.getJSONArray(Constants.PAGES);
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                        jsonArrayReadForModifyJson(array);
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }

            return jsonObject.toString();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            return null;
        }

    }


    private static void jsonArrayReadForModifyJson(JSONArray jsonArray) {
        int i = 0;
        while (i < jsonArray.length()) {
            try {


                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    String type = (String) object.get(Constants.TYPE);
                    if (type.equals(Constants.PANEL)) {
                        JSONArray jsonArray1 = new JSONArray();

                        for (int j = 0; j < jsonArray.length(); j++) {
                            if (j > i) {
                                jsonArray1.put(jsonArray.get(j));
                            }
                        }

                        JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                        readAndAddChildTo(jsonArray, array, i);
                        object.remove(Constants.ELEMENTS);

                        try {
                            for (int k = 0; k < jsonArray1.length(); k++) {
                                jsonArray.put(i + 1 + array.length() + k, jsonArray1.get(k));
                            }
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                    //jsonArrayRead(array, formDataArray);
                }
                i++;
            } catch (JSONException e) {
                i++;
                FirebaseEventUtils.logException(e);
            }

        }
    }

    private static void readAndAddChildTo(JSONArray jsonObject, JSONArray jsonArray, int position) {
        int pos = position;
        for (int i = 0; i < jsonArray.length(); i++) {
            try {

                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    String type = (String) object.get(Constants.TYPE);
                    if (type.equals(Constants.PANEL)) {

                        JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                        readAndAddChildTo(jsonObject, array, pos + 1);
                    }
                } else {
                    pos = pos + 1;
                    jsonObject.put(pos, object);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        }

    }

    /**
     * This method is used set window dimensions
     *
     * @param context context of current screen.
     */

    public static void setWindowDimensions(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

        if (windowManager == null)
            return;
        Display display = windowManager.getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        Constants.SCREEN_WIDTH = size.x;
        Constants.SCREEN_HEIGHT = size.y;
    }


    public static void setWindowDimensionsForCamera(Context context) {
        WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);

        if (windowManager == null)
            return;
        Display display = windowManager.getDefaultDisplay();
        Point size = new Point();
        display.getSize(size);
        Constants.CAM_SCREEN_WIDTH = size.x;
        Constants.CAM_SCREEN_HEIGHT = size.y;
    }

    public static int convertDpToPixels(int dp) {
        return (int) (dp * Resources.getSystem().getDisplayMetrics().density);
    }

    public static int convertPixelsToDp(int px) {

        return (int) (px / Resources.getSystem().getDisplayMetrics().density);

    }

    public static void clearUploadObjectToList() {
        JSONArray jsonArrayUpload;
        try {
            jsonArrayUpload = new JSONArray();
            AppPrefShared.putValue(Constants.LOGGED_IN_USER_IMAGE_UPLOADS_QUEUE, jsonArrayUpload.toString().trim());
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public static String getRealPathFromURI(Context context, Uri contentUri) {
        Cursor cursor = null;
        try {
            String[] proj = {MediaStore.Images.Media.DATA};
            cursor = context.getContentResolver().query(contentUri, proj, null, null, null);

            if (cursor == null)
                return contentUri.getPath();
            int column_index = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA);
            cursor.moveToFirst();
            return cursor.getString(column_index);
        } catch (Exception e) {

            return contentUri.getPath();
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
    }

    /**
     * Method to navigate setting screen
     *
     * @param activity instance on Activity
     */
    public static void openSettingScreen(Activity activity, int requestCode) {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
        intent.setData(uri);
        if (requestCode > 0) {
            activity.startActivityForResult(intent, requestCode);
        } else {
            activity.startActivity(intent);
        }
    }

    /**
     * Method to rate application
     *
     * @param context context of activity
     */
    public static void navigateToPlayStore(Context context) {
        Uri uri = Uri.parse("market://details?id=" + context.getPackageName());
        Intent goToMarket = new Intent(Intent.ACTION_VIEW, uri);
        // To count with Play market backstack, After pressing back button,
        // to taken back to our application, we need to add following flags to intent.
        goToMarket.addFlags(Intent.FLAG_ACTIVITY_NO_HISTORY |
                Intent.FLAG_ACTIVITY_NEW_DOCUMENT | Intent.FLAG_ACTIVITY_NEW_TASK |
                Intent.FLAG_ACTIVITY_MULTIPLE_TASK);
        try {
            context.startActivity(goToMarket);
        } catch (ActivityNotFoundException e) {
            context.startActivity(new Intent(Intent.ACTION_VIEW,
                    Uri.parse("http://play.google.com/store/apps/details?id=" + context.getPackageName())));
        }
    }


    public static HashMap<String, Object> getHashMapFromStringForForm(Context context, UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = new HashMap<>();

        if (TextUtils.isEmpty(uploadOtherData.getRequestedData()))
            return params;
        if (!TextUtils.isEmpty(uploadOtherData.getFormSubmissionId())) {
            //Here we send formSubmissionId in uuid parameter only for form submit api
            params.put(Constants.PARAM_UUID, uploadOtherData.getFormSubmissionId());
        }
        try {
            JSONObject jsonData = new JSONObject(uploadOtherData.getRequestedData());

            if (jsonData.has(Constants.PARAM_FORM_ID)) {
                params.put(Constants.PARAM_FORM_ID, jsonData.getLong(Constants.PARAM_FORM_ID));
            }

            if (jsonData.has(Constants.PARAM_ACCESS_CODE)) {
                params.put(Constants.PARAM_ACCESS_CODE, jsonData.getString(Constants.PARAM_ACCESS_CODE));
            }
            if (jsonData.has(Constants.PARAM_BUIL)) {
                params.put(Constants.PARAM_BUIL, jsonData.getInt(Constants.PARAM_BUIL));
            }
            if (jsonData.has(PARAM_SUBMITTED_FOR)) {
                params.put(Constants.PARAM_SUBMITTED_FOR, jsonData.getInt(Constants.PARAM_SUBMITTED_FOR));
            }
            if (jsonData.has(Constants.PARAMS_FORM_DATA_APP)) {
                String data = jsonData.getString(Constants.PARAMS_FORM_DATA_APP);
                FormData formData = null;
                if (uploadOtherData.getTmFormPKId() > 0) {
                    formData = getFormDataFromPkId(context, uploadOtherData.getTmFormPKId(), true);
                } else if (uploadOtherData.getFormPKId() > 0) {
                    formData = getFormDataFromPkId(context, uploadOtherData.getFormPKId(), false);
                }
                if (formData != null && formData.getFormPKId() > 0) {
                    if (uploadOtherData.getTmFormPKId() > 0) {
                        data = getModifiedFormData(formData, context, true);
                    } else if (uploadOtherData.getFormPKId() > 0) {
                        data = getModifiedFormData(formData, context, false);
                    }
                    params.put(Constants.PARAMS_FORM_DATA_APP, StaticUtils.filterJSON(data));
                } else {
                    params.put(Constants.PARAMS_FORM_DATA_APP, StaticUtils.filterJSON(jsonData.getString(Constants.PARAMS_FORM_DATA_APP)));
                }
            }
            if (jsonData.has(Constants.PARAM_EMAIL)) {
                params.put(Constants.PARAM_EMAIL, jsonData.getString(Constants.PARAM_EMAIL));
            }
            StaticUtils.addCommonData(params);
            if (jsonData.has(Constants.PARAM_LAT)) {
                params.put(Constants.PARAM_LAT, String.valueOf(jsonData.getDouble(Constants.PARAM_LAT)));
            }
            if (jsonData.has(Constants.PARAM_LON)) {
                params.put(Constants.PARAM_LON, String.valueOf(jsonData.getDouble(Constants.PARAM_LON)));
            }
            if (jsonData.has(Constants.PARAM_SOURCE)) {
                params.put(Constants.PARAM_SOURCE, jsonData.getString(Constants.PARAM_SOURCE));
            }
            if (jsonData.has(Constants.PARAM_LANG)) {
                params.put(Constants.PARAM_LANG, jsonData.getString(Constants.PARAM_LANG));
            }
            if (jsonData.has(Constants.PARAM_DT)) {
                params.put(Constants.PARAM_DT, jsonData.getString(Constants.PARAM_DT));
            }

            if (jsonData.has(Constants.PARAM_FORM_NAME)) {
                params.put(Constants.PARAM_FORM_NAME, jsonData.getString(Constants.PARAM_FORM_NAME));
            }

            if (jsonData.has(Constants.PARAM_CHECK_IN_TIME) && jsonData.has(Constants.PARAM_CHECK_OUT_TIME)) {
                if (jsonData.getLong(Constants.PARAM_CHECK_IN_TIME) > 0 && jsonData.getLong(Constants.PARAM_CHECK_OUT_TIME) > 0) {
                    params.put(Constants.PARAM_CHECK_IN_TIME, String.valueOf(jsonData.getLong(Constants.PARAM_CHECK_IN_TIME) / 1000));
                    params.put(Constants.PARAM_CHECK_OUT_TIME, String.valueOf(jsonData.getLong(Constants.PARAM_CHECK_OUT_TIME) / 1000));
                }
            }

            if (jsonData.has(Constants.PARAM_APP_VERSION)) {
                params.put(Constants.PARAM_APP_VERSION, jsonData.get(Constants.PARAM_APP_VERSION));
            } else {
                params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            }

            params.put(Constants.PARAM_APP_UDID, checkAndGetDeviceId());
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        return params;
    }

    public static HashMap<String, Object> getHashMapFromStringForBreadCrumb(UploadOtherData uploadOtherData) {
        HashMap<String, Object> params = new HashMap<>();

        if (TextUtils.isEmpty(uploadOtherData.getRequestedData()))
            return params;

        if (!TextUtils.isEmpty(uploadOtherData.getFormSubmissionId())) {
            params.put(Constants.FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
        }
        try {
            JSONObject jsonData = new JSONObject(uploadOtherData.getRequestedData());

            if (jsonData.has(Constants.PARAM_LAT)) {
                params.put(Constants.PARAM_LAT, jsonData.getString(Constants.PARAM_LAT));
            }
            if (jsonData.has(Constants.PARAM_LON)) {
                params.put(Constants.PARAM_LON, jsonData.getString(Constants.PARAM_LON));
            }
            if (jsonData.has(Constants.PARAM_ACCESS_CODE)) {
                params.put(Constants.PARAM_ACCESS_CODE, jsonData.getString(Constants.PARAM_ACCESS_CODE));
            }
            if (jsonData.has(Constants.PARAM_EMAIL)) {
                params.put(Constants.PARAM_EMAIL, jsonData.getString(Constants.PARAM_EMAIL));
            }
            StaticUtils.addCommonData(params);

            if (jsonData.has(Constants.PARAM_DT)) {
                params.put(Constants.PARAM_DT, jsonData.getLong(Constants.PARAM_DT));

            }
            if (jsonData.has(Constants.PARAM_SITEID)) {
                params.put(Constants.PARAM_SITEID, jsonData.getString(Constants.PARAM_SITEID));
            }
            if (jsonData.has(Constants.PARAM_TYPE)) {
                params.put(Constants.PARAM_TYPE, jsonData.getInt(Constants.PARAM_TYPE));
            }
            if (jsonData.has(Constants.PARAM_TYPE)) {
                params.put(Constants.PARAM_TYPE, jsonData.getInt(Constants.PARAM_TYPE));
            }
            if (jsonData.has(Constants.PARAM_CANCEL_CHECK_IN)) {
                params.put(Constants.PARAM_CANCEL_CHECK_IN, jsonData.getBoolean(Constants.PARAM_CANCEL_CHECK_IN));
            }

            if (jsonData.has(PARAM_SUBMITTED_FOR)) {
                params.put(Constants.PARAM_SUBMITTED_FOR, jsonData.getInt(Constants.PARAM_SUBMITTED_FOR));
            }
            if (jsonData.has(Constants.PARAM_PROFILEID)) {
                params.put(Constants.PARAM_PROFILEID, jsonData.getString(Constants.PARAM_PROFILEID));
            }

            if (jsonData.has(Constants.PARAM_CREW)) {
                List<Integer> lstData = new Gson().fromJson(jsonData.get(Constants.PARAM_CREW).toString(), new TypeToken<List<Integer>>() {
                }.getType());
                for (int i = 0; i < lstData.size(); i++) {
                    params.put("crew[" + i + "]", lstData.get(i));
                }
                if (lstData.isEmpty()) {
                    params.put(Constants.PARAM_CREW, jsonData.get(Constants.PARAM_CREW));
                }
            }
            if (jsonData.has(Constants.PARAM_SERVICES)) {
                params.put(Constants.PARAM_SERVICES, jsonData.getString(Constants.PARAM_SERVICES));
            }

            if (jsonData.has(Constants.PARAM_SERVICEID)) {
                params.put(Constants.PARAM_SERVICEID, jsonData.getString(Constants.PARAM_SERVICEID));
            }

            if (jsonData.has(Constants.PARAM_SUBMITTED_TIME)) {
                params.put(Constants.PARAM_SUBMITTED_TIME, jsonData.get(Constants.PARAM_SUBMITTED_TIME));
            }

            if (jsonData.has(Constants.PARAM_APP_VERSION)) {
                params.put(Constants.PARAM_APP_VERSION, jsonData.get(Constants.PARAM_APP_VERSION));
            } else {
                params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            }

            if (!TextUtils.isEmpty(uploadOtherData.getUuid()))
                params.put(Constants.PARAM_UUID, uploadOtherData.getUuid());

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        return params;
    }


    public static HashMap<String, Object> getHashMapFromStringForWPData(UploadOtherData
                                                                                uploadOtherData) {

        HashMap<String, Object> params = new HashMap<>();
        if (TextUtils.isEmpty(uploadOtherData.getRequestedData()))
            return params;
        if (!TextUtils.isEmpty(uploadOtherData.getFormSubmissionId())) {
            params.put(Constants.FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
        }
        try {
            JSONObject jsonData = new JSONObject(uploadOtherData.getRequestedData());

            if (jsonData.has(Constants.PARAM_ACCESS_CODE)) {
                params.put(Constants.PARAM_ACCESS_CODE, jsonData.getString(Constants.PARAM_ACCESS_CODE));
            }
            if (jsonData.has(Constants.PARAMS_WP_DATA)) {
                params.put(Constants.PARAMS_WP_DATA, jsonData.getString(Constants.PARAMS_WP_DATA));
            }
            if (jsonData.has(Constants.PARAM_EMAIL)) {
                params.put(Constants.PARAM_EMAIL, jsonData.getString(Constants.PARAM_EMAIL));
            }
            StaticUtils.addCommonData(params);

            if (jsonData.has(Constants.PARAM_LAT)) {
                params.put(Constants.PARAM_LAT, String.valueOf(jsonData.getDouble(Constants.PARAM_LAT)));
            }
            if (jsonData.has(Constants.PARAM_LON)) {
                params.put(Constants.PARAM_LON, String.valueOf(jsonData.getDouble(Constants.PARAM_LON)));
            }

           /* if (jsonData.has(Constants.PARAM_SOURCE)) {
                params.put(Constants.PARAM_SOURCE, jsonData.getString(Constants.PARAM_SOURCE));
            }*/

            if (jsonData.has(Constants.PARAM_LANG)) {
                params.put(Constants.PARAM_LANG, jsonData.getString(Constants.PARAM_LANG));
            }
            if (jsonData.has(Constants.PARAM_WPSITEID)) {
                params.put(Constants.PARAM_WPSITEID, jsonData.get(Constants.PARAM_WPSITEID));
            }

            if (jsonData.has(Constants.PARAM_WPPROFILEID)) {
                params.put(Constants.PARAM_WPPROFILEID, jsonData.get(Constants.PARAM_WPPROFILEID));
            }
            if (jsonData.has(Constants.PARAM_SUBMITTED_TIME)) {
                params.put(Constants.PARAM_SUBMITTED_TIME, jsonData.getLong(Constants.PARAM_SUBMITTED_TIME));
            }

            if (jsonData.has(Constants.PARAM_DT)) {
                params.put(Constants.PARAM_DT, jsonData.getLong(Constants.PARAM_DT));
            }

            if (jsonData.has(Constants.PARAM_APP_VERSION)) {
                params.put(Constants.PARAM_APP_VERSION, jsonData.get(Constants.PARAM_APP_VERSION));
            } else {
                params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            }

            if (!TextUtils.isEmpty(uploadOtherData.getUuid()))
                params.put(Constants.PARAM_UUID, uploadOtherData.getUuid());
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        return params;
    }

    public static HashMap<String, Object> getHashMapFromStringForCrewData(UploadOtherData
                                                                                  uploadOtherData) {
        HashMap<String, Object> params = new HashMap<>();
        if (TextUtils.isEmpty(uploadOtherData.getRequestedData())) {
            return params;
        }
        if (!TextUtils.isEmpty(uploadOtherData.getFormSubmissionId())) {
            params.put(Constants.FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
        }
        params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
        try {
            JSONObject jsonData = new JSONObject(uploadOtherData.getRequestedData());
            params.put("crewData[email]", jsonData.getString(Constants.PARAM_EMAIL));
            params.put("crewData[dt]", jsonData.getString(Constants.PARAM_DT));
            params.put("crewData[lat]", jsonData.getString(Constants.PARAM_LAT));
            params.put("crewData[siteid]", jsonData.getString(Constants.PARAM_SITEID));
            params.put("crewData[lon]", jsonData.getString(Constants.PARAM_LON));
            params.put("crewData[profileid]", jsonData.getString(Constants.PARAM_PROFILEID));
            if (jsonData.has(Constants.PARAM_CREW)) {
                try {
                    List<Integer> lstSelectedData = new Gson().fromJson(jsonData.get(Constants.PARAM_CREW).toString(), new TypeToken<List<Integer>>() {
                    }.getType());
                    for (int i = 0; i < lstSelectedData.size(); i++) {
                        params.put("crewData[selectedCrew][" + i + "]", lstSelectedData.get(i));
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            if (jsonData.has(Constants.PARAM_DE_CREW)) {
                try {
                    List<Integer> lstDeSelectedData = new Gson().fromJson(jsonData.get(Constants.PARAM_DE_CREW).toString(), new TypeToken<List<Integer>>() {
                    }.getType());
                    for (int i = 0; i < lstDeSelectedData.size(); i++) {
                        params.put("crewData[deselectedCrew][" + i + "]", lstDeSelectedData.get(i));
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            if (jsonData.has(Constants.PARAM_APP_VERSION)) {
                params.put(Constants.PARAM_APP_VERSION, jsonData.get(Constants.PARAM_APP_VERSION));
            } else {
                params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            }

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        if (!TextUtils.isEmpty(uploadOtherData.getUuid()))
            params.put(Constants.PARAM_UUID, uploadOtherData.getUuid());

        addCommonData(params);
        return params;
    }

    public static HashMap<String, Object> getHashMapFromStringForIssueData(UploadOtherData
                                                                                   uploadOtherData) {
        HashMap<String, Object> params = new HashMap<>();

        if (TextUtils.isEmpty(uploadOtherData.getRequestedData()))
            return params;
        if (!TextUtils.isEmpty(uploadOtherData.getFormSubmissionId())) {
            params.put(Constants.FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
        }
        try {
            JSONObject jsonData = new JSONObject(uploadOtherData.getRequestedData());

            if (jsonData.has(Constants.PARAM_ACCESS_CODE)) {
                params.put(Constants.PARAM_ACCESS_CODE, jsonData.getString(Constants.PARAM_ACCESS_CODE));
            }
            params.put(Constants.PARAM_UUID, uploadOtherData.getUuid());
            if (jsonData.has(Constants.PARAM_EMAIL)) {
                params.put(Constants.PARAM_EMAIL, jsonData.getString(Constants.PARAM_EMAIL));
            }

            if (jsonData.has(Constants.PARAM_LAT)) {
                params.put(Constants.PARAM_LAT, jsonData.getString(Constants.PARAM_LAT));
            }

            if (jsonData.has(Constants.PARAM_LON)) {
                params.put(Constants.PARAM_LON, jsonData.getString(Constants.PARAM_LON));

            }

            if (jsonData.has(Constants.PARAM_ISSUE_DATA)) {
                params.put(Constants.PARAM_ISSUE_DATA, jsonData.getString(Constants.PARAM_ISSUE_DATA));
            }
            if (jsonData.has(Constants.PARAM_APP_VERSION)) {
                params.put(Constants.PARAM_APP_VERSION, jsonData.get(Constants.PARAM_APP_VERSION));
            } else {
                params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            }
            StaticUtils.addCommonData(params);


        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        return params;
    }


    public static void setPendingCount(int formPkId, int count) {
        AppPrefShared.putValue(String.valueOf(formPkId), count);
    }

    public static int getPendingCount(int formPkId) {
        if (!AppPrefShared.contains(String.valueOf(formPkId)))
            return -1;
        try {
            return AppPrefShared.getInt(String.valueOf(formPkId), 0);
        } catch (ClassCastException e) {
            FirebaseEventUtils.logException(e);
            try {
                return Integer.parseInt(AppPrefShared.getString(String.valueOf(formPkId), "0"));
            } catch (NumberFormatException numberFormatException) {
                numberFormatException.printStackTrace();
                return 0;
            }
        }
    }


    public static void setTMFormPendingCount(int formPkId, int count) {
        AppPrefShared.putValue("TM" + formPkId, count);
    }

    public static int getTMFormPendingCount(int formPkId) {
        if (!AppPrefShared.contains("TM" + formPkId))
            return -1;
        return AppPrefShared.getInt("TM" + formPkId, 0);
    }

    public static void setTMFormPendingIssueCount(int formPkId, int tagId, int count) {
        AppPrefShared.putValue("TM_" + formPkId + "###" + tagId, count);
    }

    public static int getTMFormPendingIssueCount(int formPkId, int tagId) {

        if (!AppPrefShared.contains("TM_" + formPkId + "###" + tagId))
            return -1;
        return AppPrefShared.getInt("TM_" + formPkId + "###" + tagId, 0);

    }

    public static void setFormPendingIssueCount(int formPkId, int tagId, int count) {
        try {
            AppPrefShared.putValue(formPkId + "###" + tagId, count);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public static int getFormPendingIssueCount(int formPkId, int tagId) {
        if (!AppPrefShared.contains(formPkId + "###" + tagId))
            return -1;
        return AppPrefShared.getInt(formPkId + "###" + tagId, 0);
    }

    public static void setWPPhotoPendingCount(int profilePkId, int count) {
        AppPrefShared.putValue("wp_" + profilePkId, count);
    }

    public static int getWPPhotoPendingCount(int profilePkId) {
        if (!AppPrefShared.contains("wp_" + profilePkId))
            return -1;
        return AppPrefShared.getInt("wp_" + profilePkId, 0);
    }

    public static <T> List<T> getData(Class<T> c, String response) {
        Type type = new ListParameterizedType<T>(c);
        return new Gson().fromJson(response, type);
    }

    /**
     * Method to store all md5 keys in preference
     *
     * @param md5Keys
     */
    public static void setMd5Data(Md5Keys md5Keys) {
        AppPrefShared.putValue(PREF_MD5_KEY_DATA, new Gson().toJson(md5Keys));
    }


    public static Md5Keys getMd5ClassData(String response) {
        Md5Keys md5Keys = new Gson().fromJson(response, new TypeToken<Md5Keys>() {
        }.getType());
        if (md5Keys == null) {
            return new Md5Keys();
        }
        return md5Keys;
    }

    public static long getCallTime() {
        return AppPrefShared.getLong(Constants.CALL_TIME, 0);

    }

    public static void setTime(long time) {
        AppPrefShared.putValue(Constants.CALL_TIME, time);

    }

    public static String getUuid() {
        return UUID.randomUUID().toString();
    }

    public static String getWPDataFromWorkLogProfile(WorkLogProfileData
                                                             workLogProfileData, TMService selectedService) {
        JSONObject mainObject = new JSONObject();
        JSONArray materialJasonArray = new JSONArray();
        JSONArray serviceArray = new JSONArray();
        JSONArray issuesArray = new JSONArray();
        JSONArray photosArray = new JSONArray();
        JSONArray crewArray = new JSONArray();

        try {
            mainObject.put("SiteID", workLogProfileData.getSiteId());
            mainObject.put("AppProfileID", workLogProfileData.getProfileID());

            mainObject.put("SubmittedTime", System.currentTimeMillis() / 1000);
            if (workLogProfileData.isCheckInOut()) {
                mainObject.put("CheckInTimeUNIX", workLogProfileData.getCheckinTime());
                mainObject.put("CheckOutTimeUNIX", workLogProfileData.getCheckoutTime());
                mainObject.put("TimeSpentInSeconds", (workLogProfileData.getCheckoutTime() - workLogProfileData.getCheckinTime()));
            }


            for (Material material : workLogProfileData.getMaterials()) {
                if (material != null && (material.getUsage() != 0 || material.getUsage() != 0.0)) {
                    JSONObject materialObject = new JSONObject();
                    materialObject.put("MaterialID", material.getMaterialID());
                    materialObject.put("MaterialName", material.getMaterialName());
                    materialObject.put("MaterialUnit", material.getMaterialUnit());
                    materialObject.put("Usage", material.getUsage());
                    materialJasonArray.put(materialObject);
                }
            }

            for (TMService service : workLogProfileData.getServices()) {
                JSONObject serviceObject = new JSONObject();

                if (workLogProfileData.getServicesType().equalsIgnoreCase("AsTimer")) {
                    serviceObject.put(Constants.TIMELOG, getTimeLogsForServiceAsATimer(service));
                    serviceObject.put(Constants.SERVICEID, service.getServiceID());
                    continue;
                } else if (workLogProfileData.getServicesType().equalsIgnoreCase("AsDetail")) {
                    if (TextUtils.isEmpty(service.getPeople()) && TextUtils.isEmpty(service.getPeople())) {
                        continue;
                    }
                    serviceObject.put(Constants.SERVICEID, service.getServiceID());
                    serviceObject.put(Constants.PEOPLE, service.getPeople());
                    serviceObject.put(Constants.HOUR, service.getHours());
                } else if (workLogProfileData.getServicesType().equalsIgnoreCase("AsTask")) {
                    if (!service.isCompleted())
                        continue;
                    serviceObject.put(Constants.SERVICEID, service.getServiceID());
                    serviceObject.put("Completed", service.isCompleted());
                }

                if (service.getServiceOptions() != null && service.getServiceOptions().getOptions().size() > 0) {
                    if (service.getServiceOptions().getOptionsValue() == null) {
                        service.getServiceOptions().setOptionsValue(new ArrayList<>());
                    }
                    serviceObject.put(Constants.OPTIONS, service.getServiceOptions().getOptionsValue());
                }

                if (serviceObject.length() > 0) {
                    serviceArray.put(serviceObject);
                }

            }

            String imageData = workLogProfileData.getImageData();
            if (!TextUtils.isEmpty(imageData)) {

                JSONObject mainImageObject = new JSONObject(imageData);
                JSONArray dataArray = mainImageObject.getJSONArray(Constants.DATA);
                if (dataArray.length() > 0) {
                    for (int i = 0; i < dataArray.length(); i++) {
                        JSONObject object = new JSONObject();
                        JSONObject dataObject = dataArray.getJSONObject(i);
                        object.put(HRIMAGE, dataObject.get(HRIMAGE));
                        object.put(Constants.LRIMAGE, dataObject.get(Constants.LRIMAGE));
                        if (dataObject.has(Constants.IMAGE_UUID)) {
                            object.put(Constants.IMAGE_UUID, dataObject.getString(Constants.IMAGE_UUID));
                        }
                        if (dataObject.has(Constants.MAP_PIN_URL)) {
                            if (dataObject.has(Constants.PIN_LABEL)) {
                                dataObject.put(Constants.MAP_PIN_URL, addOrOverrideTitleQueryParam(dataObject.getString(Constants.MAP_PIN_URL), dataObject.getString(Constants.PIN_LABEL)));
                                dataObject.remove(Constants.PIN_LABEL);
                            } else {
                                object.put(Constants.MAP_PIN_URL, dataObject.getString(Constants.MAP_PIN_URL));
                            }
                        }
                        if (dataObject.has(Constants.PARAM_LAT))
                            object.put(Constants.PARAM_LAT, dataObject.getDouble(Constants.PARAM_LAT));
                        if (dataObject.has(Constants.PARAM_LON))
                            object.put(Constants.PARAM_LON, dataObject.getDouble(Constants.PARAM_LON));
                        photosArray.put(object);
                    }
                }
            }
            mainObject.put("Notes", workLogProfileData.getUserNote());
            mainObject.put("MaterialUsage", materialJasonArray);
            mainObject.put("ServicesProvided", serviceArray);
            mainObject.put("Issues", issuesArray);
            mainObject.put("Photos", photosArray);
            if (workLogProfileData.isCrewTracking()) {

                for (Employees employees : workLogProfileData.getLstEmployees()) {
                    if (employees.isSelected()) {
                        crewArray.put(employees.getEmployeeID());
                    }
                }
                mainObject.put("Crew", crewArray);
            }
            //}
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }


        return mainObject.toString();
    }


    public static String getBreadcrumbTypeTitle(Context context, int type) {
        switch (type) {
            case 2:
                return context.getResources().getString(R.string.check_in);
            case 3:
                return context.getResources().getString(R.string.check_out);
            case 4:
                return context.getResources().getString(R.string.clock_in);
            case 5:
                return context.getResources().getString(R.string.clock_out);
            case 6:
                return context.getResources().getString(R.string.txt_opened);
            case 7:
                return context.getResources().getString(R.string.txt_closed);
            case 8:
                return context.getResources().getString(R.string.txt_submitted);
            case 10:
                return context.getResources().getString(R.string.txt_start_service);
            case 11:
                return context.getResources().getString(R.string.txt_stop_service);
            case 13:
                return context.getResources().getString(R.string.txt_crew_selection);
            case 15:
                return context.getResources().getString(R.string.txt_app_foreground);
            case 16:
                return context.getResources().getString(R.string.txt_app_backgroing);
            case 17:
                return context.getResources().getString(R.string.txt_user_login);
            case 18:
                return context.getResources().getString(R.string.txt_user_logout);
            case 30:
                return context.getResources().getString(R.string.txt_clock_break);
            case 31:
                return context.getResources().getString(R.string.txt_clock_resume);
            default:
                return "";
        }

    }

    public static void crewSelectionData(Context context, double currentLatitude,
                                         double currentLongitude, long siteId, long formId, String
                                                 formName, String formSubmissionId, List<Integer> lstCrewIds, List<Integer> lstDeCrewIds) {
        try {
            if (lstCrewIds.size() == 0 && lstDeCrewIds.size() == 0)
                return;
            TblUploadData tblUploadData = new TblUploadData(context);
            UploadOtherData uploadOtherData = new UploadOtherData();
            long currentTime = System.currentTimeMillis();
            uploadOtherData.setDataType(Constants.CREW_DATA);
            uploadOtherData.setCreatedAt(currentTime);
            uploadOtherData.setUpdatedAt(currentTime);
            uploadOtherData.setProcessStartTime(currentTime);
            uploadOtherData.setFormSubmissionId(formSubmissionId);

            JSONObject dataJson = new JSONObject();
            try {
                dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
                dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                dataJson.put(Constants.PARAM_DT, currentTime / 1000);
                dataJson.put(Constants.PARAM_LON, currentLongitude);
                dataJson.put(Constants.PARAM_LAT, currentLatitude);
                dataJson.put(Constants.PARAM_SITEID, siteId);
                dataJson.put(Constants.PARAM_PROFILEID, formId);
                dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
                dataJson.put(Constants.PARAM_SUBMITTED_TIME, System.currentTimeMillis() / 1000);
                if (!lstCrewIds.isEmpty()) {
                    dataJson.put(Constants.PARAM_CREW, lstCrewIds);
                }
                if (lstDeCrewIds != null && !lstDeCrewIds.isEmpty()) {
                    dataJson.put(Constants.PARAM_DE_CREW, lstDeCrewIds);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
            if (!TextUtils.isEmpty(formName)) {
                uploadOtherData.setTitle(context.getString(R.string.breadcrumb_, context.getString(R.string.txt_crew_selection), formName));
            } else {
                uploadOtherData.setTitle(context.getString(R.string.breadcrumb_single, context.getString(R.string.txt_crew_selection)));
            }
            uploadOtherData.setRequestedData(dataJson.toString());
            uploadOtherData.setImageUploaded(true);
            tblUploadData.insertData(uploadOtherData);

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public static JSONArray getSelectedCrewDataFromEmployee(List<Employees> lstEmployee) {
        JSONArray jsonArray = new JSONArray();
        for (Employees employees : lstEmployee) {
            if (employees.isSelected()) {
                jsonArray.put(String.valueOf(employees.getEmployeeID()));
            }
        }

        return jsonArray;
    }

    public static JSONArray getDeselectedCrewDataFromEmployee(List<Employees> lstEmployee) {
        JSONArray jsonArray = new JSONArray();
        for (Employees employees : lstEmployee) {
            if (!employees.isSelected()) {
                jsonArray.put(String.valueOf(employees.getEmployeeID()));
            }
        }

        return jsonArray;
    }


    public static JSONArray getSelectedCrewData(List<Integer> lstSelectedEmployee) {
        JSONArray jsonArray = new JSONArray();
        for (Integer id : lstSelectedEmployee) {
            jsonArray.put(id);
        }
        return jsonArray;
    }

    public static JSONArray getDeselectedCrewData(List<Integer> lstDeselectedEmployee) {
        JSONArray jsonArray = new JSONArray();
        for (Integer id : lstDeselectedEmployee) {
            jsonArray.put(String.valueOf(id));
        }
        return jsonArray;
    }


    public static void shareShareImage(Activity activity, boolean isEmail, String
            captionDetails, String strMediaPath, Bitmap bitmapCapturedImageWithDrawing) {

        // Get captured image bitmap here for sharing
        if (strMediaPath.trim().isEmpty()) {
            try {
                if (PermissionUtils.hasStoragePermissions(activity)) {
                    strMediaPath = ImageUtil.saveImage(activity, bitmapCapturedImageWithDrawing, false);
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
        }

        if (!strMediaPath.trim().isEmpty()) {
            Uri uriBitmapMedia;

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                uriBitmapMedia = getURiForOS11AndPost(activity, bitmapCapturedImageWithDrawing);
            } else {
                uriBitmapMedia = uriFromFile(activity, strMediaPath);
            }

            if (uriBitmapMedia == null)
                return;


            if (isEmail) {
                try {

                    Intent intent = new Intent(Intent.ACTION_SEND);
                    intent.setType("image/*");

                    // Set email details
                    intent.putExtra(Intent.EXTRA_EMAIL, new String[]{""});
                    intent.putExtra(Intent.EXTRA_SUBJECT, activity.getResources().getString(R.string.app_name));
                    intent.putExtra(Intent.EXTRA_TEXT, captionDetails.trim());

                    // Attach the image
                    intent.putExtra(Intent.EXTRA_STREAM, uriBitmapMedia);
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

                    // Check if Gmail is available
                    try {
                        String gmailPackage = "com.google.android.gm";

                        intent.setPackage(gmailPackage);
                        activity.startActivity(intent);
                    } catch (ActivityNotFoundException e) {
                        // Gmail is not installed, show a chooser dialog
                        intent.setPackage(null);
                        Intent chooser = Intent.createChooser(intent, activity.getString(R.string.share_using));

                        // Exclude broken share intents (For Android 7.0+)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            ArrayList<ComponentName> excludeIntents = new ArrayList<>();
                            excludeIntents.add(new ComponentName(BuildConfig.APPLICATION_ID, MainActivity.class.getName()));
                            excludeIntents.add(new ComponentName(BuildConfig.APPLICATION_ID, CameraActivity.class.getName()));
                            chooser.putExtra(Intent.EXTRA_EXCLUDE_COMPONENTS, excludeIntents.toArray(new ComponentName[0]));
                        }

                        activity.startActivity(chooser);
                    }
                } catch (Resources.NotFoundException e) {
                    FirebaseEventUtils.logException(e);
                }
            } else {
                try {
                    // Create share intent as described above
                    Intent intent = new Intent(Intent.ACTION_SEND);
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.setType("image/gif");
                    intent.putExtra(Intent.EXTRA_STREAM, uriBitmapMedia);
                    intent.putExtra(Intent.EXTRA_TEXT, captionDetails.trim());
                    activity.startActivity(Intent.createChooser(intent, activity.getString(R.string.share_using)));
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
    }


    /**
     * From android 11 WE have limitation to share images with provider.
     * We must need to store image first in public directory and then can attach it with gmail or any third party apps.
     *
     * @param context
     * @param bitmap
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.Q)
    private static Uri getURiForOS11AndPost(Context context, Bitmap bitmap) {
        try {
            final String relativeLocation = Environment.DIRECTORY_PICTURES + "/" + context.getString(R.string.app_name);

            final ContentValues contentValues = new ContentValues();
            contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, UUID.randomUUID().toString() + ".png");
            contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/png"); //Cannot be */*
            contentValues.put(MediaStore.Files.FileColumns.IS_PENDING, true);
            contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, relativeLocation);

            final ContentResolver resolver = context.getContentResolver();


            Uri uriResolve = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);

            if (uriResolve == null || uriResolve.getPath() == null) {
                return null;
            }

            try (OutputStream stream = resolver.openOutputStream(uriResolve)) {
                if (stream == null) {
                    return null;
                }

                if (!bitmap.compress(Bitmap.CompressFormat.PNG, 90, stream)) {
                    return null;
                }
                contentValues.put(MediaStore.Files.FileColumns.IS_PENDING, false);
                resolver.update(uriResolve, contentValues, null, null);
                return uriResolve;
            } catch (IOException e) {
                FirebaseEventUtils.logException(e);
                return null;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

            return null;
        }
    }

    public static Location getLocation(Location locationExif, double currentLatitude,
                                       double currentLongitude, boolean isMultipleImage) {
        Location location = new Location("A1");
        if (locationExif != null) {
            location.setLatitude(locationExif.getLatitude());
            location.setLongitude(locationExif.getLongitude());
        } else {
            if (!isMultipleImage) {
                location.setLatitude(currentLatitude);
                location.setLongitude(currentLongitude);
            } else {
                location.setLatitude(0.0);
                location.setLongitude(0.0);
            }
        }
        return location;
    }

    public static String getSelectedCrewMemberName(Employees employee) {
        String crewFullName = "";
        if (!TextUtils.isEmpty(employee.getEmployeeFirstName()) && !TextUtils.isEmpty(employee.getEmployeeLastName())) {
            crewFullName = crewFullName.concat(employee.getEmployeeFirstName().concat(" ").concat(employee.getEmployeeLastName()));
        } else if (!TextUtils.isEmpty(employee.getEmployeeFirstName()) && TextUtils.isEmpty(employee.getEmployeeLastName())) {
            crewFullName = employee.getEmployeeFirstName();
        } else if (TextUtils.isEmpty(employee.getEmployeeFirstName()) && !TextUtils.isEmpty(employee.getEmployeeLastName())) {
            crewFullName = employee.getEmployeeLastName();

        }
        return crewFullName;
    }


    public static String getEmployeesNameFromList(List<Employees> lstEmployee) {
        String selectedItem = "";
        for (int i = 0; i < lstEmployee.size(); i++) {
            Employees itemModel = lstEmployee.get(i);
            if (itemModel.isSelected()) {
                if (!TextUtils.isEmpty(itemModel.getEmployeeFirstName()) && !TextUtils.isEmpty(itemModel.getEmployeeLastName())) {
                    if (i == lstEmployee.size() - 1) {
                        selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName().concat(" ").concat(itemModel.getEmployeeLastName()));
                    } else {
                        selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName().concat(" ").concat(itemModel.getEmployeeLastName())).concat(",");
                    }
                } else if (TextUtils.isEmpty(itemModel.getEmployeeFirstName()) && !TextUtils.isEmpty(itemModel.getEmployeeLastName())) {
                    if (i == lstEmployee.size() - 1) {
                        selectedItem = selectedItem.concat(itemModel.getEmployeeLastName());
                    } else {
                        selectedItem = selectedItem.concat(itemModel.getEmployeeLastName()).concat(",");
                    }
                } else {
                    if (i == lstEmployee.size() - 1) {
                        selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName());
                    } else {
                        selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName().concat(","));
                    }


                }
            }
        }
        return selectedItem;
    }

    public static String getEmployeesNames(List<Employees> lstEmployee) {
        String selectedItem = "";
        for (int i = 0; i < lstEmployee.size(); i++) {
            Employees itemModel = lstEmployee.get(i);
            if (!TextUtils.isEmpty(itemModel.getEmployeeFirstName()) && !TextUtils.isEmpty(itemModel.getEmployeeLastName())) {
                if (i == lstEmployee.size() - 1) {
                    selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName().concat(" ").concat(itemModel.getEmployeeLastName()));
                } else {
                    selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName().concat(" ").concat(itemModel.getEmployeeLastName())).concat(",");
                }
            } else if (TextUtils.isEmpty(itemModel.getEmployeeFirstName()) && !TextUtils.isEmpty(itemModel.getEmployeeLastName())) {
                if (i == lstEmployee.size() - 1) {
                    selectedItem = selectedItem.concat(itemModel.getEmployeeLastName());
                } else {
                    selectedItem = selectedItem.concat(itemModel.getEmployeeLastName()).concat(",");
                }
            } else {
                if (i == lstEmployee.size() - 1) {
                    selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName());
                } else {
                    selectedItem = selectedItem.concat(itemModel.getEmployeeFirstName().concat(","));
                }
            }
        }
        return selectedItem;
    }

    public static List<Employees> getEmployeeDataFromCrewList(List<CrewSelectionData> lstCrewData) {
        List<Employees> lstEmployee = new ArrayList<>();
        for (CrewSelectionData data : lstCrewData) {
            Employees employees = new Employees();
            employees.setEmployeeID(data.getCrewId());
            employees.setEmployeeSearchID(data.getEmployeeSearchID());
            employees.setCrew(data.isCrew());
            employees.setSelected(data.isSelected());
            employees.setEmployeeFirstName(data.getEmployeeFirstName());
            employees.setEmployeeLastName(data.getEmployeeLastName());
            lstEmployee.add(employees);
        }
        return lstEmployee;
    }

    public static List<Object> getCrewDataFromEmployee(List<Employees> lstEmployee, boolean isSelected, String userType) {

        List<Object> lstCrew = new ArrayList<>();
        for (Employees employees : lstEmployee) {
            CrewSelectionData selectionData = new CrewSelectionData();
            selectionData.setCrew(employees.isCrew());
            selectionData.setSelected(isSelected);
            selectionData.setCrewId(employees.getEmployeeID());
            selectionData.setUsertype(userType);
            selectionData.setCrewName(StaticUtils.getSingleStringFromTwoInput(employees.getEmployeeFirstName(), employees.getEmployeeLastName()));
            selectionData.setEmployeeSearchID(employees.getEmployeeSearchID());
            selectionData.setEmployeeFirstName(employees.getEmployeeFirstName());
            selectionData.setEmployeeLastName(employees.getEmployeeLastName());
            lstCrew.add(selectionData);
        }
        return lstCrew;
    }


    public static SpannableStringBuilder getClockTimeUsingSpannable(String hours, String
            txtHrs, String mins, String txtMins) {
        SpannableStringBuilder spannableStringBuilder = new SpannableStringBuilder();
        try {
            SpannableString spannableString1 = new SpannableString(txtHrs);
            spannableString1.setSpan(new RelativeSizeSpan(0.7f), 0, hours.length(), 0);

            SpannableString spannableString2 = new SpannableString(txtMins);
            spannableString2.setSpan(new RelativeSizeSpan(0.7f), 0, mins.length(), 0);


            return spannableStringBuilder.append(hours).append(spannableString1).append(" ").append(mins).append(spannableString2);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        return spannableStringBuilder.append(hours).append(txtHrs).append(" ").append(mins).append(txtMins);
    }

    public static int getEmployeeIdInInt() {
        int userId = 0;
        try {
            userId = Integer.parseInt(AppPrefShared.getString(LOGGED_IN_USER_PARAM_EMPLOYEE_ID, "0"));
        } catch (NumberFormatException e) {
            FirebaseEventUtils.logException(e);
        }
        return userId;
    }

    public static long getEmployeeIdInLong() {
        long userId = 0;
        try {
            userId = Long.parseLong(AppPrefShared.getString(LOGGED_IN_USER_PARAM_EMPLOYEE_ID, "0"));
        } catch (NumberFormatException e) {
            FirebaseEventUtils.logException(e);
        }
        return userId;
    }

    public static CheckInDetails getCheckInDetailData() {
        CheckInDetails checkInDetails = new CheckInDetails();
        try {
            String valueJson = AppPrefShared.getString(Constants.PREF_FORM_CHECKIN_DETAIL, "");
            if (!TextUtils.isEmpty(valueJson))
                checkInDetails = new Gson().fromJson(valueJson, CheckInDetails.class);
        } catch (JsonSyntaxException e) {
            FirebaseEventUtils.logException(e);

        }
        return checkInDetails;
    }

    public static void resetCheckInDetailData() {
        AppPrefShared.putValue(Constants.PREF_FORM_CHECKIN_DETAIL, "");

    }

    public static String getDataFromArray(JSONArray dataArray) {
        String data = "";
        for (int i = 0; i < dataArray.length(); i++) {
            try {
                String itemData = dataArray.getString(i);
                if (TextUtils.isEmpty(data)) {
                    data = itemData;
                } else {
                    data = data.concat(Constants.SEPARATOR).concat(itemData);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
        return data;
    }


    public static JSONArray getArrayFromString(String commaSepData) {
        JSONArray dataArray = new JSONArray();
        if (TextUtils.isEmpty(commaSepData)) {
            return dataArray;
        }
        String[] data = commaSepData.split(Constants.SEPARATOR);
        for (String datum : data) {
            dataArray.put(datum);
        }
        return dataArray;
    }

    public static void removeSelectedDataFromArray(JSONArray dataArray, String selectedData) {
        int length = dataArray.length();
        List<Integer> removePosition = new ArrayList<>();
        for (int i = 0; i < length; i++) {
            try {
                if (dataArray.get(i).equals(selectedData)) {
                    removePosition.add(i);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        //Remove Selected positions from JSONArray(selected user values)
        for (int i = 0; i < removePosition.size(); i++) {
            dataArray.remove(i);
        }
    }

    public static void checkAndAddInArray(JSONArray dataArray, String selectedData) {
        if (dataArray.toString().contains("'\'"))
            selectedData = selectedData.concat("'\'");

        if (!dataArray.toString().contains(selectedData)) {
            dataArray.put(selectedData);
        }

    }

    public static void checkAndAddInArray(JSONArray dataArray, int selectedData) {
        if (!dataArray.toString().contains(String.valueOf(selectedData))) {
            dataArray.put(selectedData);
        }
    }

    public static HashMap<String, Object> addCommonData(HashMap<String, Object> params) {
        try {
            params.put("deviceType", DEVICE_PLATFORM.concat("-").concat(String.valueOf(android.os.Build.VERSION.SDK_INT)));
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        if (!params.containsKey(Constants.PARAM_UUID)) {
            params.put(Constants.PARAM_UUID, StaticUtils.getUuid());
        }
        if (!params.containsKey(Constants.PARAM_EMAIL)) {
            params.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
        }
        try {
            params.put("deviceModel", Build.MANUFACTURER.concat("-").concat(Build.MODEL).concat("-").concat(Build.DEVICE));
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

            params.put("deviceModel", "");
        }
        return params;
    }

    public static List<Employees> changeCrewSelectionStatus(boolean shouldSelect, List<
            Employees> lstCrew) {

        for (Employees employees : lstCrew) {
            employees.setSelected(shouldSelect);
        }
        return lstCrew;
    }


    public static List<LangData> getLanguageData(Activity activity) {

        List<LangData> lstLang = new ArrayList<>();

        LangData langData = new LangData();
        langData.setTitle(activity.getString(R.string.txt_english));
        langData.setLang(activity.getString(R.string.txt_english_title));
        langData.setLangCode("en");
        if (AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en").equals("en")) {
            langData.setSelected(true);
        }

        lstLang.add(langData);

        LangData langData1 = new LangData();
        langData1.setTitle(activity.getString(R.string.txt_spanish));
        langData1.setLang(activity.getString(R.string.txt_spanish_title));
        langData1.setLangCode("es");
        if (AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en").equals("es")) {
            langData1.setSelected(true);
        }

        lstLang.add(langData1);

        return lstLang;
    }


    public static List<MeasureData> getMeasurementData(Activity activity) {

        List<MeasureData> lstMeasurement = new ArrayList<>();

        // Check if the unit is in miles or in km
        boolean isUnitInMiles = AppPrefShared.getBoolean(Constants.IS_UNIT_IN_MILES, false);

        MeasureData measureData = new MeasureData();
        measureData.setTitle(activity.getString(R.string.imperial));
        measureData.setSubtitle(activity.getString(R.string.txt_imperial));
        if (isUnitInMiles) {
            measureData.setSelected(true);
        }
        lstMeasurement.add(measureData);

        MeasureData measureData1 = new MeasureData();
        measureData1.setTitle(activity.getString(R.string.metric));
        measureData1.setSubtitle(activity.getString(R.string.txt_metric));
        if (!isUnitInMiles) {
            measureData1.setSelected(true);
        }
        lstMeasurement.add(measureData1);

        return lstMeasurement;
    }


    public static String getStringFromList(List<Long> siteList) {
        String siteIds = "";
        for (Long id : siteList) {
            if (TextUtils.isEmpty(siteIds)) {
                siteIds = String.valueOf(id);
            } else {
                siteIds = siteIds.concat(Constants.SEPARATOR).concat(String.valueOf(id));
            }
        }
        return siteIds;
    }


    public static String getStringFromStringList(List<String> lstString) {
        String result = "";
        if (lstString == null) {
            return result;
        }
        for (String stringData : lstString) {
            if (TextUtils.isEmpty(result)) {
                result = String.valueOf(stringData);
            } else {
                result = result.concat(Constants.SEPARATOR).concat(String.valueOf(stringData));
            }
        }
        return result;
    }

    public static String getStringFromDynamicDropDownSelectedData(ArrayList<DynamicDropDownItem> lstData) {
        String dynamicData = "";
        for (DynamicDropDownItem itemData : lstData) {
            if (TextUtils.isEmpty(dynamicData)) {
                dynamicData = itemData.getValue();
            } else {
                dynamicData = dynamicData.concat(Constants.SEPARATOR).concat(itemData.getValue());
            }
        }
        return dynamicData;
    }

    public static String getStringFromDynamicDropDownSelectedDataID(ArrayList<DynamicDropDownItem> lstData) {
        String dynamicData = "";
        for (DynamicDropDownItem itemData : lstData) {
            if (TextUtils.isEmpty(dynamicData)) {
                dynamicData = String.valueOf(itemData.getId());
            } else {
                dynamicData = dynamicData.concat(Constants.SEPARATOR).concat(String.valueOf(itemData.getId()));
            }
        }
        return dynamicData;
    }

    public static List<Long> getListFromString(String siteIds) {
        List<Long> lstSites = new ArrayList<>();
        String[] data = siteIds.split(Constants.SEPARATOR);
        for (String datum : data) {
            lstSites.add(Long.valueOf(datum.trim()));
        }
        return lstSites;
    }

    public static String getStringFromIntegerList(List<Integer> lstData) {
        String ids = "";

        if (lstData == null)
            return ids;
        for (Integer id : lstData) {
            if (TextUtils.isEmpty(ids)) {
                ids = String.valueOf(id);
            } else {
                ids = ids.concat(Constants.SEPARATOR).concat(String.valueOf(id));
            }
        }
        return ids;
    }

    public static String getSingleStringFromTwoInput(String input1, String input2) {

        String result = "";
        if (!TextUtils.isEmpty(input1) && !TextUtils.isEmpty(input2)) {
            result = input1.concat(" ").concat(input2);
        } else if (!TextUtils.isEmpty(input1) && TextUtils.isEmpty(input2)) {
            result = input1;
        } else if (TextUtils.isEmpty(input1) && !TextUtils.isEmpty(input2)) {
            result = input2;

        }
        return result;
    }


    /**
     * Enables/Disables all child views in a view group.
     *
     * @param view    main component view
     * @param enabled <code>true</code> to enable, <code>false</code> to disable
     *                the views.
     */
    public static void setViewAndChildrenEnabled(View view, boolean enabled) {
        view.setEnabled(enabled);
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                View child = viewGroup.getChildAt(i);
                setViewAndChildrenEnabled(child, enabled);
            }
        }
    }

    public static String getStringFromStringArray(String[] tags) {
        String tagString = null;
        for (String tag : tags) {
            if (TextUtils.isEmpty(tagString)) {
                tagString = tag;
            } else {
                tagString = tagString.concat(Constants.SEPARATOR).concat(tag);
            }
        }
        return tagString;
    }

    /**
     * Get result in a string with comma separated.
     *
     * @param array
     * @param lstData
     * @return
     */
    public static String getStringFromJSONArrayForAutoFill(JSONArray array, ArrayList<String> lstData) {
        String valueString = null;
        if (array != null && array.length() > 0) {
            for (int i = 0; i < array.length(); i++) {
                try {
                    String itemData = array.getString(i);
                    //Consider only values that are in choices.
                    for (int j = 0; j < lstData.size(); j++) {
                        JSONObject dataObject;
                        try {
                            dataObject = new JSONObject(lstData.get(j));
                            //JSONObject
                            if (dataObject.has(Constants.CHOICEVALUE)) {
                                String choiceValue = dataObject.getString(Constants.CHOICEVALUE);
                                if (itemData.equalsIgnoreCase(choiceValue)) {
                                    valueString = getValueString(valueString, itemData);
                                }
                            }
                        } catch (JSONException e) {
                            //possible value in string
                            //String value
                            valueString = getValueString(valueString, itemData);
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        return valueString;
    }

    private static String getValueString(String valueString, String itemData) {
        if (TextUtils.isEmpty(valueString)) {
            valueString = itemData;
        } else {
            valueString = valueString.concat(Constants.SEPARATOR).concat(itemData);
        }
        return valueString;
    }

    public static ArrayList<String> getArrayFromJSonArrayForAutoFill(JSONArray array) {
        ArrayList<String> lstData = new ArrayList<>();
        if (array != null && array.length() > 0) {
            for (int i = 0; i < array.length(); i++) {
                try {
                    lstData.add(array.getString(i));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        return lstData;
    }

    public static String[] getStringArrayFromString(String tagString) {
        return tagString.split(Constants.SEPARATOR);
    }


    /**
     * Method to get data in string format from list of tags
     *
     * @param lstTag
     * @return
     */
    public static String getStringFromTagList(List<Tags> lstTag) {
        String tagString = "";

        if (lstTag == null)
            return tagString;
        for (Tags tag : lstTag) {
            if (tag.isSelected()) {
                if (TextUtils.isEmpty(tagString)) {
                    tagString = tag.getTagName();
                } else {
                    if (tagString != null) {
                        tagString = tagString.concat(Constants.SEPARATOR).concat(" ").concat(tag.getTagName());
                    }
                }
            }
        }
        return tagString;
    }


    /**
     * Method to get data in string format from list of tags
     *
     * @param lstTag
     * @return
     */
    public static String getStringWithHashTagFromTagList(List<Tags> lstTag) {
        String tagString = "";

        if (lstTag == null)
            return tagString;
        for (Tags tag : lstTag) {
            if (tag.isSelected()) {
                if (TextUtils.isEmpty(tagString)) {
                    tagString = tag.getTagName();
                    tagString = "#".concat(tagString);
                } else {
                    String tagData = tag.getTagName();
                    tagData = "#".concat(tagData);
                    tagString = tagString.concat(Constants.SEPARATOR).concat(" ").concat(tagData);
                }
            }
        }
        return tagString.trim();
    }


    /**
     * Method to get data in string format from list of tags
     *
     * @param lstCheckMap
     * @return
     */
    public static String getStringFromCheckMapList(List<CheckInMap> lstCheckMap) {
        String result = "";
        if (lstCheckMap == null)
            return result;
        for (CheckInMap data : lstCheckMap) {
            if (TextUtils.isEmpty(result)) {
                result = data.getFormName();
            } else {
                String formName = data.getFormName();
                result = result.concat(Constants.SEPARATOR).concat(" ").concat(formName);
            }
        }
        return result.trim();
    }


    /**
     * Get List of Employee id from received employee data from server
     *
     * @param lstEmployee list of Employee
     * @return list of all employee ids
     */
    public static List<Integer> getEmployeeIds(List<Employees> lstEmployee) {
        List<Integer> lstEmpIds = new ArrayList<>();
        if (lstEmployee != null) {
            for (Employees employees : lstEmployee) {
                lstEmpIds.add(employees.getEmployeeID());
            }
        }
        return lstEmpIds;
    }


    /**
     * Get List of service id from received TMService data from server
     *
     * @param lstService list of TMService
     * @return list of all TMService ids
     */
    public static List<Integer> getServiceIds(List<TMService> lstService) {
        List<Integer> lstServiceIds = new ArrayList<>();
        if (lstService != null) {
            for (TMService tmService : lstService) {
                lstServiceIds.add(tmService.getServiceID());
            }
        }
        return lstServiceIds;
    }


    /**
     * Get List of material id from received TMService data from server
     *
     * @param lstMaterial list of Material
     * @return list of all Material ids
     */
    public static List<Integer> getMaterialIds(List<Material> lstMaterial) {
        List<Integer> lstMaterialIds = new ArrayList<>();
        if (lstMaterial != null) {
            for (Material material : lstMaterial) {
                lstMaterialIds.add(material.getMaterialID());
            }
        }
        return lstMaterialIds;
    }


    /**
     * Get List of SiteData id from received SiteData data from server
     *
     * @param lstSiteData list of SiteData
     * @return list of all SiteData ids
     */
    public static List<Integer> getSiteIds(List<SiteData> lstSiteData) {
        List<Integer> lstSiteIds = new ArrayList<>();
        if (lstSiteData != null) {
            for (SiteData siteData : lstSiteData) {
                lstSiteIds.add((int) siteData.getSiteId());
            }
        }
        return lstSiteIds;
    }

    /**
     * Get List of Routes id from received Routes data from server
     *
     * @param lstMaterial list of Routes
     * @return list of all Routes ids
     */
    public static List<String> getRouteIds(List<Routes> lstMaterial) {
        List<String> lstRouteIds = new ArrayList<>();
        for (Routes routes : lstMaterial) {
            lstRouteIds.add(routes.getRouteId());
        }
        return lstRouteIds;
    }


    public static String getMD5String(final String input) {

        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString();
        } catch (java.security.NoSuchAlgorithmException ignored) {
        }
        return "";


    }

    public static String concatString(String data1, String data2, String data3) {
        String result = "";

        if (!TextUtils.isEmpty(data1)) {
            result = data1;
        }
        if (!TextUtils.isEmpty(data2)) {
            if (TextUtils.isEmpty(result)) {
                result = data2;
            } else {
                result = result.concat(", ").concat(data2);
            }
        }

        if (!TextUtils.isEmpty(data3)) {
            if (TextUtils.isEmpty(result)) {
                result = data3;
            } else {
                result = result.concat(", ").concat(data3);
            }
        }
        return result;
    }

    public static CheckInMap getCheckInMapData(Context context) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
        List<CheckInMap> lstCheckInMap = tblCheckInMap.getAllData();
        if (lstCheckInMap.isEmpty()) {
            return null;
        } else {
            return lstCheckInMap.get(0);
        }
    }


    public static HashMap<String, Object> getParamsForStandAlonBreadCrumbRequest(int type) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
        params.put(Constants.PARAM_LON, MainActivity.currentLatitude);
        params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
        params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        StaticUtils.addCommonData(params);
        params.put(Constants.PARAM_DT, System.currentTimeMillis() / 1000);
        params.put(Constants.PARAM_TYPE, type);
        params.put(Constants.PARAM_SUBMITTED_TIME, System.currentTimeMillis() / 1000);
        params.put(Constants.PARAM_UUID, StaticUtils.getUuid());
        params.put(Constants.PARAM_APP_UDID, checkAndGetDeviceId());
        return params;
    }

    public static void prepareBreadCrumbDataToAddInUploadQueue(Context
                                                                       activity, HashMap<String, Object> breadCrumbData) {
        TblUploadData tblUploadData = new TblUploadData(activity);
        UploadOtherData uploadOtherData = new UploadOtherData();

        long currentTime = System.currentTimeMillis();
        uploadOtherData.setDataType(Constants.BREADCRUMBS_DATA);
        uploadOtherData.setCreatedAt(currentTime);
        uploadOtherData.setUpdatedAt(currentTime);
        uploadOtherData.setProcessStartTime(currentTime);

        JSONObject dataJson = new JSONObject();
        String title = "";
        try {
            dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
            if (breadCrumbData.containsKey(Constants.PARAM_DT)) {
                dataJson.put(Constants.PARAM_DT, breadCrumbData.get(Constants.PARAM_DT));
            } else {
                dataJson.put(Constants.PARAM_DT, currentTime / 1000);
            }
            if (breadCrumbData.containsKey(Constants.PARAM_LON)) {
                dataJson.put(Constants.PARAM_LON, breadCrumbData.get(Constants.PARAM_LON));
            }
            if (breadCrumbData.containsKey(Constants.PARAM_LAT)) {
                dataJson.put(Constants.PARAM_LAT, breadCrumbData.get(Constants.PARAM_LAT));
            }
            if (breadCrumbData.containsKey(Constants.PARAM_TYPE)) {
                dataJson.put(Constants.PARAM_TYPE, breadCrumbData.get(Constants.PARAM_TYPE));
            }
            if (breadCrumbData.containsKey(Constants.PARAM_SITEID)) {
                dataJson.put(Constants.PARAM_SITEID, breadCrumbData.get(Constants.PARAM_SITEID));
            }
            if (breadCrumbData.containsKey(Constants.PARAM_PROFILEID)) {
                dataJson.put(Constants.PARAM_PROFILEID, breadCrumbData.get(Constants.PARAM_PROFILEID));
            }

            if (breadCrumbData.containsKey(Constants.PARAM_SERVICEID)) {
                dataJson.put(Constants.PARAM_SERVICEID, breadCrumbData.get(Constants.PARAM_SERVICEID));
            }

            if (breadCrumbData.containsKey(Constants.PARAM_CANCEL_CHECK_IN)) {
                dataJson.put(Constants.PARAM_CANCEL_CHECK_IN, breadCrumbData.get(Constants.PARAM_CANCEL_CHECK_IN));
            }
            if (breadCrumbData.containsKey(PARAM_SUBMITTED_FOR)) {
                dataJson.put(Constants.PARAM_SUBMITTED_FOR, breadCrumbData.get(Constants.PARAM_SUBMITTED_FOR));
            }

            if (breadCrumbData.containsKey(PARAM_TITLE)) {
                title = (String) breadCrumbData.get(PARAM_TITLE);
            }

            if (breadCrumbData.containsKey(Constants.PARAM_SUBMITTED_TIME)) {
                dataJson.put(Constants.PARAM_SUBMITTED_TIME, breadCrumbData.get(Constants.PARAM_SUBMITTED_TIME));
            }

            if (breadCrumbData.containsKey(Constants.PARAM_CREW)) {
                dataJson.put(Constants.PARAM_CREW, breadCrumbData.get(Constants.PARAM_CREW));
            }
            if (breadCrumbData.containsKey(Constants.PARAM_SERVICES)) {
                dataJson.put(Constants.PARAM_SERVICES, breadCrumbData.get(Constants.PARAM_SERVICES));
            }
            dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        if (breadCrumbData.containsKey(Constants.FORM_SUBMISSION_ID)) {
            if (!TextUtils.isEmpty((String) breadCrumbData.get(Constants.FORM_SUBMISSION_ID))) {
                uploadOtherData.setFormSubmissionId((String) breadCrumbData.get(Constants.FORM_SUBMISSION_ID));
            }
        }
        uploadOtherData.setTitle(title);
        uploadOtherData.setRequestedData(dataJson.toString());
        uploadOtherData.setImageUploaded(true);
        uploadOtherData.setUuid(StaticUtils.getUuid());
        tblUploadData.insertData(uploadOtherData);
        AsyncTask.execute(() -> BaseApplication.getInstance().startOtherUploads());
    }


    public static boolean isValidEmailId(String email) {
        return Pattern.compile("^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$").matcher(email).matches();
    }

    public static boolean isValidCompanyId(String companyId) {
        return Pattern.compile("^(?:(?=.*[#\\-_.,/&:+’@($`é])[a-zA-Z0-9#\\-_.,/&:+’@($`é]{8,32}|[a-zA-Z0-9]{8,32})$").matcher(companyId).matches();
    }

    public static boolean isValidUrl(String url) {
        //String regex = "^(http|https)://.*$";
        //String regex = "^(?:(?:(?:https?|ftp):)?\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z0-9\\u00a1-\\uffff][a-z0-9\\u00a1-\\uffff_-]{0,62})?[a-z0-9\\u00a1-\\uffff]\\.)(?:[a-z\\u00a1-\\uffff]{2,}\\.?))(?::\\d{2,5})?(?:[/?#]\\S*)?$";
        // Pattern pattern = Pattern.compile(regex);
        //Matcher matcher = pattern.matcher(url);
        return Patterns.WEB_URL.matcher(url).matches();
    }

    public static void submitIssueData(Context context, boolean isTMForm, SiteData siteData, FormData formData, String issueDetail, JSONObject issueObject, int tagId) {
        try {
            TblUploadData tblUploadData = new TblUploadData(context);
            UploadOtherData uploadOtherData = new UploadOtherData();

            long currentTime = System.currentTimeMillis();
            uploadOtherData.setDataType(Constants.ISSUE_DATA);
            uploadOtherData.setCreatedAt(currentTime);
            uploadOtherData.setUpdatedAt(currentTime);
            uploadOtherData.setProcessStartTime(currentTime);
            uploadOtherData.setIssueTagId(tagId);
            uploadOtherData.setFormSubmissionId(formData.getFormSubmissionId());
            if (isTMForm) {
                uploadOtherData.setTmFormPKId(formData.getFormPKId());
            } else {
                uploadOtherData.setFormPKId(formData.getFormPKId());
            }

            JSONObject mainDataJson = new JSONObject();

            int pendingCount;
            if (isTMForm) {
                pendingCount = Math.max(StaticUtils.getTMFormPendingIssueCount(formData.getFormPKId(), tagId), 0);
            } else {
                pendingCount = Math.max(StaticUtils.getFormPendingIssueCount(formData.getFormPKId(), tagId), 0);
            }
            try {
                mainDataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
                mainDataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                mainDataJson.put(Constants.PARAM_LON, MainActivity.currentLongitude);
                mainDataJson.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
                mainDataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
                uploadOtherData.setTitle(context.getString(R.string.submit_issue));
                JSONObject issueDataJson = new JSONObject();
                issueDataJson.put(Constants.PARAM_ISSUE_TIME_UNIX, currentTime / 1000);
                if (isTMForm) {
                    issueDataJson.put(Constants.PARAM_SITE_ID, siteData.getSiteId());
                } else {
                    issueDataJson.put(Constants.PARAM_SITE_ID, -2);
                }
                issueDataJson.put(Constants.PARAM_APP_PROFILE_ID, formData.getFormId());

                JSONArray issueArray = new JSONArray();
                JSONObject photoJsonObject = new JSONObject();

                photoJsonObject.put(PARAM_TITLE, issueDetail);
                JSONArray photoArray = new JSONArray();
                String imageData = "";
                if (isTMForm) {
                    TblTMForms tblTNMForms = new TblTMForms(context);
                    FormData formData1 = tblTNMForms.getFormDataByPKId(formData.getFormPKId());
                    imageData = formData1.getImageData();
                } else {
                    TblForms tblForms = new TblForms(context);
                    FormData formData1 = tblForms.getFormDataByPKId(formData.getFormPKId());
                    imageData = formData1.getImageData();
                }
                JSONArray issueImageData = issueObject.getJSONArray(Constants.VALUE);

                for (int i = 0; i < issueImageData.length(); i++) {
                    JSONObject photoObject = new JSONObject();
                    JSONObject imageJsonObject = issueImageData.getJSONObject(i);
                    JSONObject lrhrData = StaticUtils.getHrAndLrFromImageData(imageData, imageJsonObject.getString(IMAGEPATHLOW));
                    String lrImage = "";
                    String hrImage = "";
                    if (lrhrData != null) {
                        lrImage = lrhrData.getString(LRIMAGE);
                        hrImage = lrhrData.getString(HRIMAGE);
                    }
                    photoObject.put(LRIMAGE, lrImage);
                    photoObject.put(HRIMAGE, hrImage);
                    if (pendingCount > 0) {
                        photoObject.put(IMAGEPATHLOW, imageJsonObject.getString(IMAGEPATHLOW));
                        photoObject.put(IMAGEPATHHIGH, imageJsonObject.getString(IMAGEPATHHIGH));
                    }
                    photoArray.put(photoObject);
                }
                photoJsonObject.put(PARAM_PHOTOS, photoArray);
                issueArray.put(photoJsonObject);
                issueDataJson.put(Constants.PARAM_ISSUE, issueArray);
                mainDataJson.put(Constants.PARAM_ISSUE_DATA, issueDataJson);
                uploadOtherData.setRequestedData(mainDataJson.toString());
                uploadOtherData.setImageUploaded(pendingCount == 0);
                uploadOtherData.setUuid(StaticUtils.getUuid());
                issueObject.put(PARAM_UUID, uploadOtherData.getUuid());
                tblUploadData.insertData(uploadOtherData);
                sendBroadCastForStartOtherDataUpload();
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public static void sendBroadCastForStartOtherDataUpload() {
        AsyncTask.execute(() -> BaseApplication.getInstance().startOtherUploads());
    }


    public static String scanJsonForPanelValidation(String jsonData) {
        String sourceData = "";
        sourceData = sourceData.concat(jsonData);
        JSONObject mainObject = null;
        try {
            mainObject = new JSONObject(sourceData);
            JSONArray jsonArray = mainObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    try {
                        jsonArrayRead(object.getJSONArray(Constants.ELEMENTS));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        if (mainObject != null) {
            return mainObject.toString();
        }
        return null;
    }

    public static String scanPanelDataInJSON(String jsonData, HashMap<Integer, PanelData> panelValidationData) {
        String sourceData = "";
        sourceData = sourceData.concat(jsonData);
        JSONObject mainObject = null;
        try {
            mainObject = new JSONObject(sourceData);
            JSONArray jsonArray = mainObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    try {
                        jsonArrayReadForPanel(object.getJSONArray(Constants.ELEMENTS), panelValidationData);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        if (mainObject != null) {
            return mainObject.toString();
        }
        return null;
    }

    /**
     * jsonArrayRead method is use for read json array and get panel object
     * this method is call when there is child view is there.
     *
     * @param jsonArray is json array is use for get single from array
     */
    public static void jsonArrayReadForPanel(JSONArray jsonArray, HashMap<Integer, PanelData> panelValidationData) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.getString(Constants.TYPE).equals(Constants.PANEL)) {
                    readAndGetPanelJSONObject(object, panelValidationData);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }


    /**
     * objectRead is use for read single object from object array
     *
     * @param object is json object use for get data from object
     */
    private static void readAndGetPanelJSONObject(JSONObject object, HashMap<Integer, PanelData> panelValidationData) {
        if (object.has(Constants.ELEMENTS) && object.has(Constants.PARENT_TAG)) {
            try {
                JSONArray jsonArray = object.getJSONArray(Constants.ELEMENTS);
                int parentTagId = object.getInt(Constants.PARENT_TAG);
                PanelData panelData = panelValidationData.get(parentTagId);
                if (panelData != null) {
                    panelData.setPanelData(object.toString());
                    for (int i = 0; i < jsonArray.length(); i++) {
                        try {
                            if (!panelData.isValidated()) {
                                JSONObject childObject = jsonArray.getJSONObject(i);
                                readPanelRequiredData(childObject, panelData);
                            } else {
                                break;
                            }
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                }

            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    private static void readPanelRequiredData(JSONObject object, PanelData panelData) {
        boolean anyConditionTrue = false;
        try {
            String type = object.getString(Constants.TYPE);
            switch (type) {
                case Constants.TEXT:
                case Constants.MATERIAL:
                case Constants.GEO:
                case Constants.COMMENT:
                case Constants.TEXTDISPLAY:
                case Constants.SPINNER:
                case Constants.RADIOGROUP:
                case Constants.SEGMENT_INPUT:
                case Constants.ADDRESS:
                    if (!object.has(Constants.VALUE) || !TextUtils.isEmpty(object.getString(Constants.VALUE))) {
                        anyConditionTrue = true;
                    }
                    break;

                case Constants.DROPDOWNMULTIPLE:
                case Constants.SKETCH:
                case Constants.SIGNATUREPAD:
                case Constants.IMAGE_UPLOAD:
                case Constants.ISSUES:
                case Constants.CREW:
                    JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                    if (jsonArray.length() > 0) {
                        anyConditionTrue = true;
                    }
                    break;
                case Constants.CHECKBOX:
                    if (object.get(Constants.VALUE) instanceof String) {
                        if (!TextUtils.isEmpty(object.getString(Constants.VALUE))) {
                            anyConditionTrue = true;
                        }
                    } else {
                        if (object.getJSONArray(VALUE).length() > 0) {
                            anyConditionTrue = true;
                        }
                    }
                    break;
                case Constants.SERVICE:
                    String serviceType = object.getString("serviceType");
                    TMService tmService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                    if (serviceType.equalsIgnoreCase("AsDetail")) {
                        if (!TextUtils.isEmpty(object.getString(Constants.PEOPLE)) && !TextUtils.isEmpty(object.getString(Constants.HOUR))) {
                            //Here if service has option then it must be attended
                            if (tmService.getServiceOptions() != null && tmService.getServiceOptions().getOptionsValue() != null && tmService.getServiceOptions().getOptionsValue().size() > 0) {
                                anyConditionTrue = true;
                            } else if (tmService.getServiceOptions() == null) {
                                anyConditionTrue = true;
                            }
                        }

                    } else if (serviceType.equalsIgnoreCase("AsTask")) {
                        // For service type task, at least any one option must be selected along with completed task check
                        if (tmService.isCompleted() && tmService.getServiceOptions() != null && tmService.getServiceOptions().getOptionsValue() != null && tmService.getServiceOptions().getOptionsValue().size() > 0) {
                            anyConditionTrue = true;
                        } else if (tmService.isCompleted() && tmService.getServiceOptions() == null) {
                            anyConditionTrue = true;
                        }
                    } else if (serviceType.equalsIgnoreCase("AsTimer")) {
                        boolean optionSelected;
                        List<TimeLog> lstTimeLog = tmService.getLstTimeLog();
                        // For service type timer, at least any one option must be selected along with timer
                        if (tmService.getServiceOptions() != null && tmService.getServiceOptions().getOptions() != null &&
                                tmService.getServiceOptions().getOptions().size() > 0) {
                            //Here if service has option then it must be attended
                            optionSelected = tmService.getServiceOptions().getOptionsValue() != null && !tmService.getServiceOptions().getOptionsValue().isEmpty();
                            if (optionSelected && lstTimeLog != null && lstTimeLog.size() >= 1) {
                                anyConditionTrue = true;
                            }
                        } else {
                            // For service type timer, if options is not available then just check timer count.
                            if (lstTimeLog != null && lstTimeLog.size() >= 1) {
                                anyConditionTrue = true;
                            }
                        }
                    }
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        panelData.setValidated(anyConditionTrue);
    }


    public static void readElementsOfPanel(JSONArray jsonArray, PanelData panelData) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);


            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    public static float convertDpToPixel(float dp, Context context) {
        return dp * ((float) context.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }


    public static void addCompoundDrawableWithView(TextView tvValue, String data) {
        if (!TextUtils.isEmpty(data)) {
            tvValue.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
        } else {
            tvValue.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }

    }


    public static void setVisibilityOfServiceSubView(TMService service, View view) {
        if (view != null) {
            if (service.isCompleted()) {
                view.setVisibility(View.VISIBLE);
            } else {
                view.setVisibility(View.GONE);
            }
        }
    }


    /**
     * Method to set text and compulsory text with *.
     *
     * @param tvTitle    instance of component in title text
     * @param jsonObject JSONObject instance of component
     */
    public static void setTitleOfComponent(TextView tvTitle, JSONObject jsonObject) {
        setTitleOfComponent(tvTitle, jsonObject, "");
    }

    /**
     * Method to set text and compulsory text with *.
     *
     * @param tvTitle    instance of component in title text
     * @param jsonObject JSONObject instance of component
     */
    public static void setTitleOfComponent(TextView tvTitle, JSONObject jsonObject, String title) {
        try {
            if (jsonObject.has(Constants.TITLE)) {
                if (jsonObject.has(Constants.ISREQUIRED)) {
                    if (jsonObject.getBoolean(Constants.ISREQUIRED)) {
                        if (TextUtils.isEmpty(title)) {
                            tvTitle.setText("* ".concat(jsonObject.getString(Constants.TITLE)));
                        } else {
                            tvTitle.setText("* ".concat(title));
                        }
                    } else {
                        if (TextUtils.isEmpty(title)) {
                            tvTitle.setText(jsonObject.getString(Constants.TITLE));
                        } else {
                            tvTitle.setText(title);
                        }
                    }
                } else if (!TextUtils.isEmpty(title)) {
                    tvTitle.setText(title);
                } else if (!TextUtils.isEmpty(jsonObject.getString(Constants.TITLE))) {
                    tvTitle.setText(jsonObject.getString(Constants.TITLE));
                } else {
                    tvTitle.setVisibility(View.GONE);
                }
            } else {
                //In some cases we don't receive title of component  from server and to manage and avoid null pointer exception, we set empty title as a default value
                if (!jsonObject.has(Constants.TITLE)) {
                    jsonObject.put(Constants.TITLE, "");
                }
            }
        } catch (JSONException e) {

            FirebaseEventUtils.logException(e);
        }
    }

    public static ValidationData setValidationForServiceInMap(HashMap<View, Object> validationMap, int pageNumber, JSONObject object, boolean setFalse, View keyView, TMService tMService) {
        ValidationData validationData = null;
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (setFalse) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(true);
                    validationData.setTitle(tMService.getServiceName());
                } else if (object.getBoolean(Constants.ISREQUIRED)) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(false);
                    validationData.setTitle(tMService.getServiceName());
                }
                validationMap.put(keyView, validationData);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return validationData;
    }

    public static ValidationData setValidationForMaterial(Activity activity, HashMap<View, Object> validationMap, int pageNumber, JSONObject object, boolean setFalse, View keyView) {
        ValidationData validationData = null;
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (setFalse) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(true);
                    validationData.setTitle(activity.getString(R.string.material));
                } else if (object.getBoolean(Constants.ISREQUIRED)) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(false);
                    validationData.setTitle(activity.getString(R.string.material));
                }
                validationMap.put(keyView, validationData);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return validationData;
    }


    public static void updateServiceValidationInMap(HashMap<View, Object> validationMap, JSONObject object, View view, TMService service) {
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (object.getBoolean(Constants.ISREQUIRED)) {
                    ValidationData validationData = (ValidationData) validationMap.get(view);
                    if (validationData != null) {
                        if (!service.isCompleted()) {
                            validationData.setValidate(false);
                        } else {
                            try {
                                if (object.getString("serviceType").equalsIgnoreCase("AsDetail")) {
                                    if (!TextUtils.isEmpty(service.getHours()) && !TextUtils.isEmpty(service.getPeople())
                                            && service.getServiceOptions() != null && service.getServiceOptions().getOptionsValue() != null
                                            && !service.getServiceOptions().getOptionsValue().isEmpty()) {
                                        validationData.setValidate(true);
                                    } else if (!TextUtils.isEmpty(service.getHours()) && !TextUtils.isEmpty(service.getPeople())
                                            && service.getServiceOptions() == null) {
                                        validationData.setValidate(true);
                                    } else {
                                        validationData.setValidate(false);
                                    }
                                } else if (object.getString("serviceType").equalsIgnoreCase("AsTask")) {
                                    if (service.getServiceOptions() != null
                                            && service.getServiceOptions().getOptionsValue() != null
                                            && !service.getServiceOptions().getOptionsValue().isEmpty()) {
                                        validationData.setValidate(true);
                                    } else if (service.isCompleted() && service.getServiceOptions() == null) {
                                        validationData.setValidate(true);
                                    } else {
                                        validationData.setValidate(false);
                                    }
                                } else if (object.getString("serviceType").equalsIgnoreCase("AsTimer")) {
                                    if (service.getServiceOptions() != null
                                            && service.getServiceOptions().getOptionsValue() != null
                                            && !service.getServiceOptions().getOptionsValue().isEmpty()) {
                                        validationData.setValidate(true);
                                    } else {
                                        validationData.setValidate(false);
                                    }
                                }
                            } catch (Exception e) {
                                validationData.setValidate(true);
                                FirebaseEventUtils.logException(e);
                            }
                        }
                        validationMap.put(view, validationData);
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    /**
     * inputTypeDate is use for add Date in parent view
     * this method is call when type is text and inputType is date
     *
     * @param adsLayout is view group and work as parent layout
     */
    public static void addSeparatorView(Activity activity, ViewGroup adsLayout) {
        View separator = activity.getLayoutInflater().inflate(R.layout.layout_saparator, adsLayout, false);
        adsLayout.addView(separator);
    }


    public static void changeVisibilityOfTotalTime(TMService tMService, Chronometer cmTotalTime, boolean stopTimer) {
        List<TimeLog> lstTimeLog = tMService.getLstTimeLog();
        if (lstTimeLog.size() > 0) {
            long timeDifference = 0;
            if (lstTimeLog.size() == 1) {
                if (lstTimeLog.get(0).getStopTime() == 0) {
                    cmTotalTime.setVisibility(View.GONE);
                } else {
                    cmTotalTime.setVisibility(View.VISIBLE);
                }
            } else {
                cmTotalTime.setVisibility(View.VISIBLE);
            }

            for (TimeLog timeLog : lstTimeLog) {
                if (timeLog.getStopTime() > 0) {
                    timeDifference = timeDifference + (timeLog.getStopTime() - timeLog.getStartTime());
                } else {
                    timeDifference = timeDifference + (System.currentTimeMillis() / 1000 - timeLog.getStartTime());
                }
            }
            timeDifference = timeDifference * 1000;
            if (stopTimer) {
                cmTotalTime.setBase(SystemClock.elapsedRealtime() - timeDifference);
                cmTotalTime.setText(DateUtil.getChronometerTime(SystemClock.elapsedRealtime() - cmTotalTime.getBase()));
                cmTotalTime.stop();

            } else {
                cmTotalTime.setBase(SystemClock.elapsedRealtime() - timeDifference);
                cmTotalTime.start();
            }
        } else {
            cmTotalTime.setVisibility(View.GONE);
        }
        cmTotalTime.setOnChronometerTickListener(chronometer -> {
            long time = SystemClock.elapsedRealtime() - chronometer.getBase();
            chronometer.setText(DateUtil.getChronometerTime(time));
        });


    }

    public static void changeVisibilityOfCurrentTime(LinearLayout llCurrentTime, Chronometer cmCurrentTime, boolean stopTimer) {
        if (stopTimer) {
            llCurrentTime.setVisibility(View.GONE);
            cmCurrentTime.setBase(SystemClock.elapsedRealtime());
            cmCurrentTime.stop();
            return;
        }
        llCurrentTime.setVisibility(View.VISIBLE);
        cmCurrentTime.setOnChronometerTickListener(new Chronometer.OnChronometerTickListener() {
            @Override
            public void onChronometerTick(Chronometer chronometer) {
                onChronometerTickerHandler(chronometer);
            }
        });
        cmCurrentTime.start();
    }

    private static void onChronometerTickerHandler(Chronometer chronometer) {
        long delta = SystemClock.elapsedRealtime() - chronometer.getBase();
        int h = (int) ((delta / 1000) / 3600);
        int m = (int) (((delta / 1000) / 60) % 60);
        int s = (int) ((delta / 1000) % 60);
        String customText = h + ":" + m + ":" + s;
        chronometer.setText(customText);
    }

    /**
     * Method to Clear People data and remove drawable from text view.
     *
     * @param tMService TMService service object
     * @param tvPeople  AppCompatTextView
     * @param tvTitle   TextView
     * @param object    JSONObject
     */
    public static void clearPeopleData(HashMap<View, Object> validationMap, TMService tMService, AppCompatTextView tvPeople, TextView tvTitle, JSONObject object) {
        tvPeople.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        tvTitle.setError(null);

        try {
            tMService.setPeople("");
            tvPeople.setText("");
            object.put(Constants.TnMService, new Gson().toJson(tMService));
            object.put(Constants.PEOPLE, tMService.getPeople());
            updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Method to Clear Hour data and remove drawable from text view.
     *
     * @param tMService TMService service object
     * @param tvHour    AppCompatTextView
     * @param tvTitle   TextView
     * @param object    JSONObject
     */
    public static void clearHourData(HashMap<View, Object> validationMap, TMService tMService, AppCompatTextView tvHour, TextView tvTitle, JSONObject object) {
        tvHour.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        tvTitle.setError(null);
        try {
            tMService.setHours("");
            tvHour.setText("");
            object.put(Constants.TnMService, new Gson().toJson(tMService));
            object.put(Constants.HOUR, tMService.getHours());
            updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * jsonArrayRead method is use for read json array and get single object
     * this method is call when there is child view is there.
     *
     * @param jsonArray    is json array is use for get single from array
     * @param parentLayout is view group and work as parent layout
     * @param pageNumber   int current Page number
     */
    public static void jsonArrayRead(Activity activity, boolean isTmForm, SiteData siteData, FormData formData, int tag, HashMap<View, Object> validationMap, JSONArray jsonArray, ViewGroup parentLayout, int pageNumber, int parentPanelTag) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                objectRead(activity, isTmForm, siteData, formData, tag, validationMap, object, parentLayout, jsonArray, i, pageNumber, parentPanelTag, true);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    public static void jsonArrayRead(JSONArray jsonArray, HashMap<View, Object> validationMap, ViewGroup adsLayout) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                if (jsonArray.get(i) instanceof JSONObject) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    JSONArray array = null;
                    if (object.has(Constants.ELEMENTS)) {
                        array = object.getJSONArray(Constants.ELEMENTS);
                    } else if (object.has(Constants.CHOICES)) {
                        array = object.getJSONArray(Constants.CHOICES);
                    }
                    if (array != null && array.length() > 0) {
                        jsonArrayRead(array, validationMap, adsLayout);
                    }
                    readViewAndJson(validationMap, adsLayout, object, false);
                }
            } catch (JSONException e) {

                FirebaseEventUtils.logException(e);
            }
        }
    }


    public static void inputTypeService(Activity activity, boolean isTmForm, SiteData siteData, FormData formData, int tag, HashMap<View, Object> validationMap, JSONObject object, ViewGroup parentLayout) {
        View serviceView = activity.getLayoutInflater().inflate(R.layout.layout_service, parentLayout, false);
        serviceView.setTag(tag);
        LinearLayout llServiceRoot = serviceView.findViewById(R.id.llServiceRoot);

        try {
            object.put(Constants.TEMP_TAG_ID, tag);

            if (isTmForm) {
                object.put(Constants.SITEID, String.valueOf(siteData.getSiteId()));
            } else {
                object.put(Constants.SITEID, "-2");
            }
            object.put(Constants.SF_ID, String.valueOf(formData.getFormId()));

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        String serviceType;
        parentLayout.addView(serviceView);
        if (object.has("serviceType")) {
            TMService tMService;
            try {
                int serviceId = -1;
                if (object.has("service")) {
                    serviceId = object.getInt("service");
                }
                if (!object.has(Constants.TnMService)) {
                    TblServices tblServices = new TblServices(activity);
                    tMService = tblServices.getAllServices(serviceId);
                    object.put(Constants.TnMService, new Gson().toJson(tMService));
                } else {
                    tMService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                }
                object.put(Constants.SERVICEID, tMService.getServiceID());
                object.put(Constants.TIMELOG, StaticUtils.getTimeLogsForServiceAsATimer(tMService));
                object.put(Constants.SERVICE_NAME, tMService.getServiceName());

                serviceType = object.getString("serviceType");
                if (serviceType.equalsIgnoreCase("AsDetail")) {
                    addServiceAsDetailView(activity, isTmForm, siteData, formData, validationMap, parentLayout, object, llServiceRoot, tMService);
                } else if (serviceType.equalsIgnoreCase("AsTask")) {
                    addServiceAsTaskView(activity, isTmForm, siteData, formData, validationMap, parentLayout, object, llServiceRoot, tMService);
                }
                if (object.has(Constants.ELEMENTS)) {
                    View linear = activity.getLayoutInflater().inflate(R.layout.layout_linear, parentLayout, false);
                    LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                    JSONArray arrayElement = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayRead(activity, isTmForm, siteData, formData, tag, validationMap, arrayElement, llSubview, 0, -1);
                    jsonArrayRead(arrayElement, validationMap, llSubview);
                    llServiceRoot.addView(llSubview);
                    addSeparatorView(activity, llSubview);
                    setVisibilityOfServiceSubView(tMService, llSubview);
                }
                if (tMService.isCompleted()) {
                    setValidationMapForChild(validationMap, true, object, llServiceRoot);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    private static void addServiceAsDetailView(Activity activity, boolean isTmForm, SiteData siteData, FormData formData, HashMap<View, Object> validationMap, ViewGroup parentLayout, JSONObject object, LinearLayout llServiceRoot, TMService tMService) {
        LinearLayout llSubview;
        View childView = null;
        LinearLayout viewPeople = (LinearLayout) activity.getLayoutInflater().inflate(R.layout.layout_service_people, parentLayout, false);
        TextView tvTitle = viewPeople.findViewById(R.id.tvTitle);
        LinearLayout llPeopleView = viewPeople.findViewById(R.id.llPeopleHours);
        LinearLayout llSeparator = viewPeople.findViewById(R.id.llSeparator);
        if (!TextUtils.isEmpty(tMService.getServiceName()))
            tvTitle.setText(tMService.getServiceName());
        CheckBox cbItem = viewPeople.findViewById(R.id.cbItem);
        try {
            if (!TextUtils.isEmpty(tMService.getServiceName())) {
                setTitleOfComponent(tvTitle, object, tMService.getServiceName());
                setValidationForServiceInMap(validationMap, 1, object, !tMService.isCompleted(), tvTitle, tMService);
                object.put(Constants.TITLE, tMService.getServiceName());
            }
            cbItem.setChecked(tMService.isCompleted());
            object.put(Constants.PEOPLE, tMService.getPeople());
            object.put(Constants.HOUR, tMService.getHours());
            llSubview = llServiceRoot.findViewById(R.id.llSubView);
            setVisibilityOfServiceSubView(tMService, llSubview);
            setVisibilityOfServiceSubView(tMService, llPeopleView);
            if (llSubview != null)
                manageValidationForService(validationMap, tMService, llSubview, object);
            if (!object.has(Constants.OPTIONS)) {
                object.put(Constants.OPTIONS, new JSONArray());
            }
            if (tMService.getServiceOptions() != null) {
                childView = setSegmentOrMultipleSelectionView(activity, validationMap, object, tMService, viewPeople, tvTitle);
                setVisibilityOfServiceSubView(tMService, childView);
                llSubview = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSubview);
                manageValidationForService(validationMap, tMService, llSubview, object);

                //Show separator if view is visible else false
                if (cbItem.isChecked()) {
                    llSeparator.setVisibility(View.VISIBLE);
                } else {
                    llSeparator.setVisibility(View.GONE);
                }
            }

            View finalChildView = childView;
            updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
            cbItem.setOnCheckedChangeListener((compoundButton, b) -> {
                tMService.setCompleted(b);
                setVisibilityOfServiceSubView(tMService, llPeopleView);
                if (finalChildView != null) {
                    setVisibilityOfServiceSubView(tMService, finalChildView);

                }

                //Show separator if view is visible else false
                if (b) {
                    llSeparator.setVisibility(View.VISIBLE);
                } else {
                    llSeparator.setVisibility(View.GONE);
                }
                LinearLayout llSub = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSub);
                try {
                    object.put(Constants.TnMService, new Gson().toJson(tMService));
                    tvTitle.setError(null);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }

            });

            AppCompatTextView tvPeople = viewPeople.findViewById(R.id.tvPeople);
            AppCompatTextView tvHour = viewPeople.findViewById(R.id.tvHour);

            tvPeople.setText(tMService.getPeople());
            addCompoundDrawableWithView(tvPeople, tMService.getPeople());
            tvPeople.setOnTouchListener((view, motionEvent) -> {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    Drawable drawableRight = tvPeople.getCompoundDrawables()[2];
                    if (drawableRight != null) {
                        if (motionEvent.getRawX() >= tvPeople.getRight() - drawableRight.getBounds().width()) {
                            clearPeopleData(validationMap, tMService, tvPeople, tvTitle, object);
                            return true;
                        }
                    }
                }
                return false;
            });

            addCompoundDrawableWithView(tvHour, tMService.getHours());

            tvHour.setOnTouchListener((view, motionEvent) -> {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    Drawable drawableRight = tvHour.getCompoundDrawables()[2];
                    if (drawableRight != null) {
                        if ((motionEvent.getRawX() / 2 - StaticUtils.convertDpToPixels(20)) >= tvHour.getRight() - drawableRight.getBounds().width()) {
                            clearHourData(validationMap, tMService, tvHour, tvTitle, object);
                            return true;
                        }
                    }
                }
                return false;
            });


            tvHour.setText(String.valueOf(tMService.getHours()));

            tvPeople.setOnClickListener(v -> {
                ArrayList<ItemModel> lstData = new ArrayList<>();
                int selectedPosition = -1;
                for (int i = 0; i <= 50; i++) {
                    ItemModel itemModel = new ItemModel();
                    itemModel.setName(String.valueOf(i));
                    if (!TextUtils.isEmpty(tMService.getPeople()) && Integer.parseInt(tMService.getPeople()) == i) {
                        itemModel.setSelected(true);
                        selectedPosition = i;
                    }
                    lstData.add(itemModel);
                }
                PopUtils.showBottomViewForNumberSelection(activity, lstData, selectedPosition, (s, itemModel) -> {
                    tvPeople.setText(s);
                    tvTitle.setError(null);
                    try {
                        addCompoundDrawableWithView(tvPeople, s);
                        tMService.setPeople(itemModel.getName());
                        object.put(Constants.TnMService, new Gson().toJson(tMService));
                        object.put(Constants.PEOPLE, tMService.getPeople());
                        updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
            });

            tvHour.setOnClickListener(v -> {
                ArrayList<ItemModel> lstData = new ArrayList<>();
                int selectedPosition = -1;
                for (int i = 0; i <= 96; i++) {
                    double d = 0.25 * i;
                    ItemModel itemModel = new ItemModel();
                    itemModel.setName(String.valueOf(d));
                    if (!TextUtils.isEmpty(tMService.getHours()) && Double.parseDouble(tMService.getHours()) == d) {
                        itemModel.setSelected(true);
                        selectedPosition = i;
                    }
                    lstData.add(itemModel);
                }
                PopUtils.showBottomViewForNumberSelection(activity, lstData, selectedPosition, (s, itemModel) -> {
                    tvHour.setText(s);
                    tvTitle.setError(null);
                    try {
                        addCompoundDrawableWithView(tvHour, s);
                        tMService.setHours(itemModel.getName());
                        object.put(Constants.TnMService, new Gson().toJson(tMService));
                        object.put(Constants.HOUR, tMService.getHours());
                        updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
            });
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        llServiceRoot.addView(viewPeople);
        addSeparatorView(activity, viewPeople);

    }


    private static void manageValidationForService(HashMap<View, Object> validationMap, TMService tMService, LinearLayout llSubview, JSONObject object) {
        if (tMService == null || llSubview == null)
            return;
        if (tMService.isCompleted()) {
            readAndAddValidation(validationMap, llSubview, object);
        } else {
            try {
                if (object.has(Constants.ELEMENTS)) {
                    JSONArray arrayElement = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayRead(arrayElement, validationMap, llSubview);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private static void addServiceAsTaskView(Activity activity, boolean isTmForm, SiteData siteData, FormData formData, HashMap<View, Object> validationMap, ViewGroup adsLayout, JSONObject object, LinearLayout llServiceRoot, TMService tMService) {
        View childView = null;
        LinearLayout viewSingleCheckbox = (LinearLayout) activity.getLayoutInflater().inflate(R.layout.layout_service_checkbox, adsLayout, false);
        TextView tvCheckboxItem = viewSingleCheckbox.findViewById(R.id.tvCheckboxItem);
        CheckBox cbItem = viewSingleCheckbox.findViewById(R.id.cbItem);
        cbItem.setChecked(tMService.isCompleted());
        if (tMService.getServiceOptions() != null) {
            childView = setSegmentOrMultipleSelectionView(activity, validationMap, object, tMService, viewSingleCheckbox, tvCheckboxItem);
        }
        try {
            if (!object.has(Constants.OPTIONS)) {
                object.put(Constants.OPTIONS, new JSONArray());
            }
            object.put(Constants.COMPLETED, tMService.isCompleted());
            object.put(Constants.SERVICEID, tMService.getServiceID());
            object.put(Constants.SERVICE_NAME, tMService.getServiceName());
            setVisibilityOfServiceSubView(tMService, childView);
            setValidationForServiceInMap(validationMap, 1, object, !tMService.isCompleted(), tvCheckboxItem, tMService);

            updateServiceValidationInMap(validationMap, object, tvCheckboxItem, tMService);
            manageValidationForService(validationMap, tMService, viewSingleCheckbox, object);

            View finalChildView = childView;
            cbItem.setOnCheckedChangeListener((compoundButton, b) -> {
                tMService.setCompleted(b);
                try {
                    object.put(Constants.TnMService, new Gson().toJson(tMService));
                    object.put(Constants.COMPLETED, b);
                    tvCheckboxItem.setError(null);
                    updateServiceValidationInMap(validationMap, object, tvCheckboxItem, tMService);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
                setValidationMapForChild(validationMap, b, object, llServiceRoot);
                setVisibilityOfServiceSubView(tMService, finalChildView);
                LinearLayout llSubview = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSubview);
                manageValidationForService(validationMap, tMService, llSubview, object);
            });
            if (!TextUtils.isEmpty(tMService.getServiceName())) {
                tvCheckboxItem.setText(tMService.getServiceName());
                setTitleOfComponent(tvCheckboxItem, object, tMService.getServiceName());

                if (tMService.getServiceOptions() == null)
                    setValidationForServiceInMap(validationMap, 1, object, tMService.isCompleted(), tvCheckboxItem, tMService);
                else if (tMService.getServiceOptions().getOptionsValue() != null && tMService.getServiceOptions().getOptionsValue().size() > 0) {
                    setValidationForServiceInMap(validationMap, 1, object, tMService.isCompleted(), tvCheckboxItem, tMService);
                }
                object.put(Constants.TITLE, tMService.getServiceName());
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        llServiceRoot.addView(viewSingleCheckbox);
        addSeparatorView(activity, viewSingleCheckbox);

    }

    private static void setValidationMapForChild(HashMap<View, Object> validationMap, boolean isValidation, JSONObject object, LinearLayout llServiceRoot) {
        if (object.has(Constants.ELEMENTS)) {
            LinearLayout llSubview = llServiceRoot.findViewById(R.id.llSubView);
            JSONArray arrayElement;
            try {
                arrayElement = object.getJSONArray(Constants.ELEMENTS);
                jsonArrayReadService(validationMap, arrayElement, llSubview, isValidation);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }

    }

    public static void jsonArrayReadService(HashMap<View, Object> validationMap, JSONArray jsonArray, ViewGroup adsLayout, boolean isValidation) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                if (jsonArray.get(i) instanceof JSONObject) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    JSONArray array = null;
                    if (object.has(Constants.ELEMENTS)) {
                        array = object.getJSONArray(Constants.ELEMENTS);
                    } else if (object.has(Constants.CHOICES)) {
                        array = object.getJSONArray(Constants.CHOICES);
                    }
                    if (array != null && array.length() > 0) {
                        jsonArrayReadService(validationMap, array, adsLayout, isValidation);
                    }
                    readViewAndJson(validationMap, adsLayout, object, isValidation);
                }
            } catch (JSONException e) {

                FirebaseEventUtils.logException(e);
            }
        }
    }


    public static void resetSegmentOrMultipleSelectionView(Activity activity, TMService service, LinearLayout parentView) {
        if (service.getServiceOptions().getMultiple().equalsIgnoreCase("true")) {
            LinearLayout llCheckbox = parentView.findViewById(R.id.llCheckbox);
            for (int i = 0; i < llCheckbox.getChildCount(); i++) {
                if ((llCheckbox.getChildAt(i)) instanceof CheckBox) {
                    ((CheckBox) llCheckbox.getChildAt(i)).setChecked(false);
                }
            }
            llCheckbox.invalidate();
        } else if (service.getServiceOptions().getMultiple().equalsIgnoreCase("false")) {
            LinearLayout llSegmentView = parentView.findViewById(R.id.llSegmentView);
            for (int i = 0; i < llSegmentView.getChildCount(); i++) {
                if ((llSegmentView.getChildAt(i)) instanceof AppCompatTextView) {
                    AppCompatTextView tvSelection = (AppCompatTextView) llSegmentView.getChildAt(i);
                    tvSelection.setBackgroundColor(activity.getResources().getColor(R.color.transparent));
                    tvSelection.setTextColor(activity.getResources().getColor(R.color.black_font));
                }
            }
            llSegmentView.invalidate();
        }
    }


    public static View setSegmentOrMultipleSelectionView(Activity activity, HashMap<View, Object> validationMap, JSONObject object, TMService
            service, LinearLayout parentView, TextView tvTitle) {

        View returnView = null;
        if (service.getServiceOptions().getMultiple().equalsIgnoreCase("true")) {
            View viewMultipleCheckbox = activity.getLayoutInflater().inflate(R.layout.layout_service_multiple_checkbox, parentView, false);
            LinearLayout llCheckbox = viewMultipleCheckbox.findViewById(R.id.llCheckbox);
            List<String> lstValue = service.getServiceOptions().getOptionsValue();
            if (lstValue == null) {
                lstValue = new ArrayList<>();
            }
            JSONArray optionArray = new JSONArray();
            try {
                object.put(Constants.OPTIONS, optionArray);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            for (String option : service.getServiceOptions().getOptions()) {
                CheckBox checkBox = new CheckBox(activity);
                checkBox.setTextSize(TypedValue.COMPLEX_UNIT_PX, activity.getResources().getDimension(R.dimen.text_size_xlarge));
                checkBox.setButtonTintList(ColorStateList.valueOf(activity.getResources().getColor(R.color.colorPrimary)));

                if (lstValue.contains(option)) {
                    checkBox.setChecked(true);
                    optionArray.put(option);
                }
                checkBox.setText(option);
                List<String> finalLstValue = lstValue;
                checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                    if (isChecked) {
                        finalLstValue.add(checkBox.getText().toString());
                        optionArray.put(option);

                    } else {
                        finalLstValue.remove(checkBox.getText().toString());
                        int length = optionArray.length();
                        for (int i = 0; i < length; i++) {
                            try {
                                if (optionArray.get(i).equals(checkBox.getText().toString())) {
                                    optionArray.remove(i);
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }
                    }
                    service.getServiceOptions().setOptionsValue(finalLstValue);
                    try {
                        updateServiceValidationInMap(validationMap, object, tvTitle, service);
                        tvTitle.setError(null);
                        object.put(Constants.TnMService, new Gson().toJson(service));
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }

                });
                llCheckbox.addView(checkBox);
            }
            parentView.addView(viewMultipleCheckbox);
            returnView = viewMultipleCheckbox;
        } else if (service.getServiceOptions().getMultiple().equalsIgnoreCase("false")) {
            View viewRadioGroup = activity.getLayoutInflater().inflate(R.layout.layout_service_segment, parentView, false);

            LinearLayout llSegmentView = viewRadioGroup.findViewById(R.id.llSegmentView);

            List<String> lstValue;

            lstValue = service.getServiceOptions().getOptionsValue();

            if (lstValue == null) {
                lstValue = new ArrayList<>();
            }

            JSONArray optionArray = new JSONArray();
            try {
                object.put(Constants.OPTIONS, optionArray);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            ArrayList<SegmentData> lstSegmentData = new ArrayList<>();
            for (String option : service.getServiceOptions().getOptions()) {
                View textView = activity.getLayoutInflater().inflate(R.layout.item_radiogroup_segment, llSegmentView, false);
                View view = activity.getLayoutInflater().inflate(R.layout.item_saprator_segment, llSegmentView, false);
                View linear = activity.getLayoutInflater().inflate(R.layout.layout_linear, llSegmentView, false);
                LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                TextView tvSelection = textView.findViewById(R.id.cbSelection);
                tvSelection.setText(option);
                SegmentData segmentData = new SegmentData();
                segmentData.setLinearLayout(llSubview);
                segmentData.setTextView(tvSelection);
                segmentData.setTvTag(option);
                tvSelection.setTag(option);
                lstSegmentData.add(segmentData);
                for (SegmentData segmentData1 : lstSegmentData) {
                    if (lstValue.contains(option)) {
                        StaticUtils.checkAndAddInArray(optionArray, option);
                        setVisibilityOfSegmentView(activity, object, option, lstValue, tvSelection, segmentData1, optionArray, false);

                    }
                }

                List<String> finalLstValue = lstValue;
                tvSelection.setOnClickListener(v -> {
                    for (SegmentData segmentData1 : lstSegmentData) {
                        setVisibilityOfSegmentView(activity, object, option, finalLstValue, tvSelection, segmentData1, optionArray, true);
                    }
                    service.getServiceOptions().setOptionsValue(finalLstValue);
                    try {
                        updateServiceValidationInMap(validationMap, object, tvTitle, service);
                        tvTitle.setError(null);
                        object.put(Constants.TnMService, new Gson().toJson(service));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
                llSegmentView.addView(tvSelection);
                llSegmentView.addView(view);
            }

            parentView.addView(viewRadioGroup);
            returnView = viewRadioGroup;
        }
        return returnView;
    }


    public static void setVisibilityOfSegmentView(Activity activity, JSONObject object, String option, List<String> lstValue,
                                                  TextView tvSelection, SegmentData segmentData1, JSONArray optionArray, boolean isSelect) {
        if (isSelect) {
            if (tvSelection == segmentData1.getTextView()) {
                tvSelection.setBackgroundColor(activity.getResources().getColor(R.color.colorPrimary));
                tvSelection.setTextColor(activity.getResources().getColor(R.color.white));
                if (!lstValue.contains(tvSelection.getText().toString().trim())) {
                    lstValue.add(tvSelection.getText().toString().trim());
                    try {
                        StaticUtils.checkAndAddInArray(optionArray, tvSelection.getText().toString().trim());
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            } else {
                segmentData1.getTextView().setBackgroundColor(activity.getResources().getColor(R.color.transparent));
                segmentData1.getTextView().setTextColor(activity.getResources().getColor(R.color.black_font));
                segmentData1.getLinearLayout().setVisibility(View.GONE);
                lstValue.remove(segmentData1.getTvTag());
                try {
                    StaticUtils.removeSelectedDataFromArray(optionArray, segmentData1.getTvTag());
                    object.put(Constants.OPTIONS, optionArray);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } else {
            if (tvSelection.getTag() == option) {
                tvSelection.setBackgroundColor(activity.getResources().getColor(R.color.colorPrimary));
                tvSelection.setTextColor(activity.getResources().getColor(R.color.white));
                if (!lstValue.contains(option)) {
                    lstValue.add(option);
                    try {
                        StaticUtils.checkAndAddInArray(optionArray, option);
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            } else {
                segmentData1.getTextView().setBackgroundColor(activity.getResources().getColor(R.color.transparent));
                segmentData1.getTextView().setTextColor(activity.getResources().getColor(R.color.black_font));
                segmentData1.getLinearLayout().setVisibility(View.GONE);
                lstValue.remove(option);
                if (lstValue.size() == 0) {
                    try {
                        StaticUtils.removeSelectedDataFromArray(optionArray, option);
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            }
        }
    }


    public static void readAndAddValidation(HashMap<View, Object> validationMap, LinearLayout llSubview, JSONObject object) {
        try {
            // Check if object has elements key then only get array data from it.
            if (object.has(Constants.ELEMENTS)) {
                JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                jsonArrayReadAndAdd(validationMap, array, llSubview);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private static void jsonArrayReadAndAdd(HashMap<View, Object> validationMap, JSONArray jsonArray, LinearLayout llSubview) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    //ViewGroup view = (ViewGroup) adsLayout.getChildAt(i);
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayReadAndAdd(validationMap, array, llSubview);
                    readViewAndJson(validationMap, llSubview, object, true);
                } else if (object.has(Constants.CHOICES)) {
                    JSONArray array = object.getJSONArray(Constants.CHOICES);
                    if (object.has(Constants.VALUE) && !TextUtils.isEmpty(object.getString(Constants.VALUE).trim()) && !object.getString(Constants.VALUE).equals("[]")) {
                        for (int j = 0; j < array.length(); j++) {
                            if (array.get(j) instanceof JSONObject) {
                                JSONObject jsonObject = array.getJSONObject(j);
                                if (jsonObject.has(Constants.CHOICEVALUE)
                                        && jsonObject.getString(Constants.CHOICEVALUE).trim().equalsIgnoreCase(object.getString(Constants.VALUE))
                                        && jsonObject.has(Constants.ELEMENTS)) {
                                    JSONArray array1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                    if (array1.length() > 0) {
                                        jsonArrayReadAndAdd(validationMap, array1, llSubview);
                                    }
                                }
                            }
                        }
                    } else {
                        readViewAndJson(validationMap, llSubview, object, true);
                    }
                } else {
                    readViewAndJson(validationMap, llSubview, object, true);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }

        }

    }


    public static void readViewAndJson(HashMap<View, Object> validationMap, View view, JSONObject object, boolean isAdd) {
        String type;
        try {
            if (!object.has(Constants.TYPE))
                return;
            type = (String) object.get(Constants.TYPE);
            switch (type) {
                case Constants.CHECKBOX:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvCheckboxTitle, object, isAdd);
                    break;
                case Constants.DROPDOWNMULTIPLE:
                case Constants.SPINNER:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvMultiSelected, object, isAdd);
                    break;
                case Constants.IMAGE_UPLOAD:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvUploadPhoto, object, isAdd);
                    break;
                case Constants.TEXT:
                    textViewCheckTextType(validationMap, view, object, isAdd);
                    break;
                case Constants.INPUTTYPE_URL:
                    //editTextUpdateValidationMap(view,R.id.edtUrl,object,isAdd);
                    break;

                case Constants.SIGNATUREPAD:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvSignatureTitle, object, isAdd);
                    break;
                case Constants.SEGMENT_INPUT:
                case Constants.RADIOGROUP:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvRadioGroupTitle, object, isAdd);

                    break;
                case Constants.INPUTTYPE_COMMENT:
                    textViewUpdateValidationMap(validationMap, view, R.id.edtComment, object, isAdd);

                    break;
                case Constants.GEO:
                    break;
                case Constants.MATERIAL:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvMaterialName, object, isAdd);
                    break;
                case Constants.SERVICE:
                    String serviceType = object.getString("serviceType");
                    if (serviceType.equalsIgnoreCase("AsDetail")) {
                        textViewUpdateValidationMap(validationMap, view, R.id.tvTitle, object, isAdd);
                    } else if (serviceType.equalsIgnoreCase("AsTask")) {
                        textViewUpdateValidationMap(validationMap, view, R.id.tvCheckboxItem, object, isAdd);
                    } else if (serviceType.equalsIgnoreCase("AsTimer")) {
                        textViewUpdateValidationMap(validationMap, view, R.id.tvServiceName, object, isAdd);
                    }

                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private static void textViewUpdateValidationMap(HashMap<View, Object> validationMap, View view, int textViewId, JSONObject object, boolean isAdd) {
        try {
            TextView tvKey = null;
            if (object.has(Constants.TEMP_TAG_ID) && object.has(Constants.ISREQUIRED) && object.getBoolean(Constants.ISREQUIRED)) {
                if (view.findViewWithTag(object.getInt(Constants.TEMP_TAG_ID)) instanceof LinearLayout) {
                    LinearLayout linearLayout = view.findViewWithTag(object.getInt(Constants.TEMP_TAG_ID));
                    tvKey = linearLayout.findViewById(textViewId);
                } else if (view.findViewWithTag(object.getInt(Constants.TEMP_TAG_ID)) instanceof ScrollView) {
                    ScrollView linearLayout = view.findViewWithTag(object.getInt(Constants.TEMP_TAG_ID));
                    tvKey = linearLayout.findViewById(textViewId);
                }
                if (tvKey != null) {
                    if (!isAdd) {
                        validationMap.remove(tvKey);
                    } else {
                        setValidationInMap(validationMap, object, tvKey);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private static void textViewCheckTextType(HashMap<View, Object> validationMap, View view, JSONObject object, boolean isAdd) {
        try {
            String inoutType = (String) object.get(Constants.INPUTTYPE);
            switch (inoutType) {
                case Constants.INPUTTYPE_COMMENT:
                    editTextUpdateValidationMap(validationMap, view, R.id.edtComment, object, isAdd);
                    break;
                case Constants.INPUTTYPE_DATE:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvDateDisplay, object, isAdd);
                    break;
                case Constants.INPUTTYPE_DATE_TIME:
                    textViewUpdateValidationMap(validationMap, view, R.id.tvDateAndTime, object, isAdd);
                    break;
                case Constants.INPUTTYPE_EMAIL:
                    editTextUpdateValidationMap(validationMap, view, R.id.edtEmail, object, isAdd);
                    break;
                case Constants.INPUTTYPE_NUMBER:
                    editTextUpdateValidationMap(validationMap, view, R.id.edtNumber, object, isAdd);
                    break;
                case Constants.INPUTTYPE_PASSWORD:
                    editTextUpdateValidationMap(validationMap, view, R.id.edtPassword, object, isAdd);
                    break;
                case Constants.INPUTTYPE_TEL:
                    editTextUpdateValidationMap(validationMap, view, R.id.edtPhone, object, isAdd);
                    break;
                case Constants.INPUTTYPE_URL:
                    //editTextUpdateValidationMap(view,R.id.edtUrl,object,isAdd);
                    break;
                case Constants.TEXT:
                    editTextUpdateValidationMap(validationMap, view, R.id.edtText, object, isAdd);
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private static void editTextUpdateValidationMap(HashMap<View, Object> validationMap, View componentView, int edtTextId, JSONObject object, boolean isAdd) {
        try {
            if (object.has(Constants.ISREQUIRED) && object.getBoolean(Constants.ISREQUIRED)) {
                LinearLayout layout = null;
                EditText edtTextKey;
                if (object.has(Constants.TEMP_TAG_ID)) {
                    layout = componentView.findViewWithTag(object.get(Constants.TEMP_TAG_ID));
                }
                if (layout != null)
                    edtTextKey = layout.findViewById(edtTextId);
                else
                    edtTextKey = componentView.findViewById(edtTextId);
                if (edtTextKey != null) {
                    if (!isAdd) {
                        validationMap.remove(edtTextKey);
                    } else {
                        setValidationInMap(validationMap, object, edtTextKey);
                    }
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private static ValidationData setValidationInMap(HashMap<View, Object> validationMap, JSONObject object, View keyView) {
        ValidationData validationData = null;
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (object.getBoolean(Constants.ISREQUIRED) && object.has("auto") && object.getBoolean("auto")) {
                    validationData = new ValidationData();
                    if (object.has(Constants.VALUE)) {
                        validationData.setValidate(true);
                    }
                } else if (object.getBoolean(Constants.ISREQUIRED)) {
                    validationData = new ValidationData();
                    if (object.has(Constants.VALUE)) {
                        if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.IMAGE_UPLOAD) || object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SIGNATUREPAD) || object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SKETCH)) {
                            JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                            validationData.setValidate(jsonArray.length() > 0);
                        } else {
                            validationData.setValidate(!TextUtils.isEmpty(object.getString(Constants.VALUE)) && !object.getString(Constants.VALUE).equals("[]"));
                        }
                    }
                    validationData.setEmail(false);
                    validationData.setTitle(object.getString(Constants.TITLE));
                }
                validationMap.put(keyView, validationData);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return validationData;
    }


    private static void objectRead(Activity activity, boolean isTmForm, SiteData siteData, FormData formData, int tag, HashMap<View, Object> validationMap, JSONObject object, ViewGroup parentLayout, JSONArray
            jsonArray, int objectPosition, int pageNumber, int parentPanelTag, boolean shouldAdd) {
        try {
            String type = (String) object.get(Constants.TYPE);

            switch (type) {
                case Constants.CHECKBOX:
                    //inputTypeCheckBox(object, parentLayout, parentPanelTag);
                    break;
                case Constants.DROPDOWNMULTIPLE:
                    // inputTypeDropdownMultiple(object, parentLayout);
                    break;
                case Constants.SEGMENT_INPUT:
                case Constants.RADIOGROUP:
                    //inputTypeRadioGroup(object, parentLayout);
                    break;
                case Constants.SERVICE:
                    inputTypeService(activity, isTmForm, siteData, formData, tag, validationMap, object, parentLayout);
                    break;
                case Constants.MATERIAL:
                    //inputTypeMaterial(object, parentLayout);
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static boolean isFormValidate(Activity activity, HashMap<View, Object> validationMap) {
        boolean isValidData = true;
        int counter = 0;

        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    if (view instanceof EditText) {
                        isValidData = false;
                        if (validationData.isEmail()) {
                            ((EditText) view).setError(activity.getString(R.string.msg_valid_email_address));
                            ((EditText) view).setSelection(((EditText) view).getText().toString().trim().length());
                        } else {
                            ((EditText) view).setError(activity.getString(R.string.msg_field_can_not_be_an_empty));
                        }
                        counter++;
                        if (counter == 1) {
                            showForeGroundToast(activity, activity.getString(R.string.required, validationData.getTitle()), false);

                        }
                    } else if (view instanceof TextView) {
                        isValidData = false;
                        ((TextView) view).setError(activity.getString(R.string.msg_field_can_not_be_an_empty));
                        counter++;
                        if (counter == 1) {
                            showForeGroundToast(activity, activity.getString(R.string.required, validationData.getTitle()), false);
                        }
                    }
                }
            }
        }

        return isValidData;
    }


    /**
     * ShowToast Message Notification for short time when app is on foreground
     *
     * @param msg Toast Title Message
     */
    public static void showForeGroundToast(Context context, String msg, boolean isLongToast) {
        try {
            if (context == null || context.getApplicationContext() == null)
                return;
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                @Override
                public void run() {
                    Toast toast = new Toast(context.getApplicationContext());
                    View toastView = LayoutInflater.from(context.getApplicationContext()).inflate(R.layout.layout_custom_toast, null);
                    if (toastView == null)
                        return;
                    TextView tvTitle = toastView.findViewById(R.id.tvToast);
                    tvTitle.setText(msg);
                    toast.setView(toastView);
                    toast.setGravity(Gravity.CENTER, 0, (int) (Constants.SCREEN_HEIGHT - (Constants.SCREEN_HEIGHT / 1.4)));
                    if (isLongToast)
                        toast.setDuration(Toast.LENGTH_SHORT);
                    else
                        toast.setDuration(Toast.LENGTH_LONG);
                    toast.show();
                }
            });

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static void setFlipAnimation(ViewFlipper vfCrewService, Animation leftOutAnimation, Animation leftInAnimation
            , Animation rightInAnimation, Animation rightOutAnimation, boolean isNext) {
        if (isNext) {
            vfCrewService.setInAnimation(rightInAnimation);
            vfCrewService.setOutAnimation(leftOutAnimation);
        } else {
            vfCrewService.setInAnimation(leftInAnimation);
            vfCrewService.setOutAnimation(rightOutAnimation);
        }
    }


    public static boolean checkIfUploadIsPending(Activity activity) {
        TblUploadImage tblUploadImage = new TblUploadImage(activity);
        TblUploadData tblUploadData = new TblUploadData(activity);
        return (tblUploadImage.getDataCount() + tblUploadData.getDataCount()) > 0;
    }

    public static void updateMarginOfTitleView(Activity activity, LinearLayout llTitle) {
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) llTitle.getLayoutParams();
        if (checkIfUploadIsPending(activity)) {
            layoutParams.rightMargin = StaticUtils.convertDpToPixels((int) activity.getResources().getDimension(R.dimen.padding_tiny));
        } else {
            layoutParams.rightMargin = StaticUtils.convertDpToPixels((int) activity.getResources().getDimension(R.dimen.padding_large));
        }
        llTitle.setLayoutParams(layoutParams);
    }

    /**
     * Returns true if the device is locked or screen turned off (in case password not set)
     */
    public static boolean isDeviceLocked(Context context) {
        boolean isLocked = false;
        // First we check the locked state
        KeyguardManager keyguardManager = (KeyguardManager) context.getSystemService(Context.KEYGUARD_SERVICE);
        boolean inKeyguardRestrictedInputMode = keyguardManager.inKeyguardRestrictedInputMode();
        if (inKeyguardRestrictedInputMode) {
            isLocked = true;
        } else {
            // If password is not set in the settings, the inKeyguardRestrictedInputMode() returns false,
            // so we need to check if screen on for this case
            PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            isLocked = !powerManager.isInteractive();
        }
        return isLocked;
    }


    /**
     * return true if typeValue is in JSONArray
     *
     * @param dataArray JSONArray Long
     * @param typeValue String
     * @return
     */
    public static boolean checkDataInJsonArray(JSONArray dataArray, String typeValue) {
        if (dataArray != null && dataArray.length() > 0) {
            for (int i = 0; i < dataArray.length(); i++) {
                try {
                    Object dataElement = dataArray.get(i);
                    // We may have either string or Object
                    if (dataElement instanceof JSONObject) {
                        JSONObject subDataObject = dataArray.getJSONObject(i);
                        //Get value for key name "choiceValue"
                        String valueString = subDataObject.getString(Constants.CHOICEVALUE);
                        if (valueString.equalsIgnoreCase(typeValue)) {
                            return true;
                        }

                    } else if (dataElement instanceof String) {
                        // get value from string key
                        String valueString = dataArray.getString(i);
                        if (valueString.equalsIgnoreCase(typeValue)) {
                            return true;
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        return false;
    }


    /**
     * Function to decode encoded data and decompress using brotli's decompress method.
     *
     * @param data encoded data
     * @return decompressed original data
     */
    public static String checkAndDecompressAutoFillData(String data) {
        if (TextUtils.isEmpty(data))
            return data;
        //check data with normal and valid json. if true return input data else decompress data
        if (isValidJson(data))
            return data;
        try {
            byte[] compressedBytes;
            String validData = data.replaceAll(" ", "+"); // Add + at space (This is default behaviour of Uri it removes + and add space for uri.getQueryParameter("autofill"))
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                compressedBytes = Base64.getDecoder().decode(validData);
            } else {
                compressedBytes = android.util.Base64.decode(validData, android.util.Base64.DEFAULT);
            }
            BrotliInputStream brotliInputStream = new BrotliInputStream(new ByteArrayInputStream(compressedBytes));
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = brotliInputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            return outputStream.toString(StandardCharsets.UTF_8.name());
        } catch (IOException | IllegalArgumentException e) {
            FirebaseEventUtils.logException(e);
            return "";
        }
    }


    /**
     * function to check given input is valid json or not.
     *
     * @param data
     * @return
     */
    public static boolean isValidJson(String data) {
        try {
            //Check for valid json object
            new JSONObject(data);
        } catch (JSONException e) {
            try {
                //Check for valid json array
                new JSONArray(data);
            } catch (JSONException ex1) {
                return false;
            }
        }
        return true;
    }

    public static FormData getFormDataFromPkId(Context context, int formPKId, boolean isTmForm) {
        if (isTmForm) {
            TblTMForms tblTNMForms = new TblTMForms(context);
            return tblTNMForms.getFormDataByPKId(formPKId);
        } else {
            TblForms tblForms = new TblForms(context);
            return tblForms.getFormDataByPKId(formPKId);
        }
    }

    public static String getModifiedFormData(FormData formData, Context context, boolean isTmForm) {
        //merge subform data if main form hase completed sub forms
        String data = formData.getModifiedFormData();
        if (formData.getFormPKId() > 0 && formData.hasSubForm()) {
            List<FormData> lstSubForms = getSubFormData(context, isTmForm, formData);
            //concat sub form data with main form data
            concatSubFormsInMainFormResponse(isTmForm, context, formData, lstSubForms);
            data = formData.getModifiedFormData();
        }
        if (formData.getFormPKId() > 0 && formData.isSubForm()) {
            JSONObject combinedModifiedDataJson = concatImageDataWithTMFormData(isTmForm, context, formData.getFormPKId(), formData.getModifiedFormData());
            data = combinedModifiedDataJson.toString();
        }
        return data;
    }


    /*
     * @param jsonArray is json array is use for get single from array
     * @param tmForm    isTMForm
     */
    public static void jsonArrayReadForConcatData(Context context, JSONArray jsonArray, int formPKId, boolean tmForm) {

        FormData formData;
        if (tmForm) {
            TblTMForms tblForms = new TblTMForms(context);
            formData = tblForms.getFormDataByPKId(formPKId);

        } else {
            TblForms tblForms = new TblForms(context);
            formData = tblForms.getFormDataByPKId(formPKId);
        }
        String imagePathString = formData.getImageData();
        for (int i = 0; i < jsonArray.length(); i++) {
            try {

                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayReadForConcatData(context, array, formPKId, tmForm);
                } else if (object.has(Constants.CHOICES)) {
                    try {
                        JSONArray array = object.getJSONArray(Constants.CHOICES);
                        for (int j = 0; j < array.length(); j++) {
                            JSONObject jsonObject = null;
                            try {
                                jsonObject = array.getJSONObject(j);
                            } catch (JSONException e) {
                                // FirebaseEventUtils.logException(e);
                            }
                            if (jsonObject != null) {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    try {
                                        JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                        jsonArrayReadForConcatData(context, jsonArray1, formPKId, tmForm);
                                    } catch (JSONException e) {
                                        FirebaseEventUtils.logException(e);
                                    }
                                }
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                } else {
                    if (TextUtils.isEmpty(imagePathString))
                        return;
                    JSONObject jsonObject = new JSONObject(imagePathString);
                    JSONArray imageArray = jsonObject.getJSONArray(Constants.DATA);
                    switch (object.getString(Constants.TYPE)) {
                        case Constants.IMAGE_UPLOAD:
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    if (jsonArray1.length() > 0) {
                                        for (int j = 0; j < jsonArray1.length(); j++) {
                                            JSONObject object1 = jsonArray1.getJSONObject(j);
                                            for (int k = 0; k < imageArray.length(); k++) {
                                                JSONObject object2 = imageArray.getJSONObject(k);
                                                if (object2.getString(IMAGEPATHLOW).replaceAll("\\'", "'").equals(object1.getString(IMAGEPATHLOW).replaceAll("\\'", "'"))
                                                        || (!TextUtils.isEmpty(object2.getString(IMAGEPATHHIGH)) && !TextUtils.isEmpty(object1.getString(IMAGEPATHHIGH)) &&
                                                        object2.getString(IMAGEPATHHIGH).replaceAll("\\'", "'").equals(object1.getString(IMAGEPATHHIGH).replaceAll("\\'", "'")))) {
                                                    object1.put(Constants.LRIMAGE, object2.getString(Constants.LRIMAGE));
                                                    object1.put(Constants.HRIMAGE, object2.getString(Constants.HRIMAGE));
                                                    if (object2.has(Constants.IMAGE_UUID)) {
                                                        object1.put(Constants.IMAGE_UUID, object2.getString(Constants.IMAGE_UUID));
                                                    }

                                                    object1.put(IMAGEPATHHIGH, object2.getString(IMAGEPATHHIGH));
                                                    object1.put(IMAGEPATHLOW, object2.getString(IMAGEPATHLOW));
                                                    addPinData(object2, object1);
                                                }
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                            break;
                        case Constants.SIGNATUREPAD:
                        case Constants.SKETCH:
                            if (object.has(Constants.VALUE)) {
                                try {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);
                                        for (int k = 0; k < imageArray.length(); k++) {
                                            JSONObject object2 = imageArray.getJSONObject(k);
                                            if (object1.has(Constants.IMAGEID) && object2.has(Constants.IMAGEID) && object2.getInt(Constants.IMAGEID) == object1.getInt(Constants.IMAGEID)) {
                                                object1.put(Constants.LRIMAGE, object2.getString(Constants.LRIMAGE));
                                                object1.put(Constants.HRIMAGE, object2.getString(Constants.HRIMAGE));
                                                object1.put(IMAGEPATHHIGH, object2.getString(IMAGEPATHHIGH));
                                                object1.put(IMAGEPATHLOW, object2.getString(IMAGEPATHLOW));
                                                if (object2.has(Constants.IMAGE_UUID)) {
                                                    object1.put(Constants.IMAGE_UUID, object2.getString(Constants.IMAGE_UUID));
                                                }
                                            }
                                        }
                                    }
                                } catch (JSONException e) {
                                    FirebaseEventUtils.logException(e);
                                }
                            }
                            break;
                        case Constants.ISSUES:
                            if (object.has(Constants.ISSUE_VALUE))
                                try {

                                    JSONArray jsonArrayIssue = object.getJSONArray(Constants.ISSUE_VALUE);
                                    for (int m = 0; m < jsonArrayIssue.length(); m++) {
                                        JSONObject object3 = jsonArrayIssue.getJSONObject(m);
                                        if (object3.has(Constants.VALUE)) {
                                            JSONArray jsonArray1 = object3.getJSONArray(Constants.VALUE);
                                            for (int j = 0; j < jsonArray1.length(); j++) {
                                                JSONObject object1 = jsonArray1.getJSONObject(j);
                                                for (int k = 0; k < imageArray.length(); k++) {
                                                    JSONObject object2 = imageArray.getJSONObject(k);
                                                    if (object1.has(Constants.IMAGEID) && object2.has(Constants.IMAGEID) && object2.getInt(Constants.IMAGEID) == object1.getInt(Constants.IMAGEID)) {
                                                        object1.put(Constants.LRIMAGE, object2.getString(Constants.LRIMAGE));
                                                        object1.put(Constants.HRIMAGE, object2.getString(Constants.HRIMAGE));
                                                        object1.put(IMAGEPATHHIGH, object2.getString(IMAGEPATHHIGH));
                                                        object1.put(IMAGEPATHLOW, object2.getString(IMAGEPATHLOW));
                                                        if (object2.has(Constants.IMAGE_UUID)) {
                                                            object1.put(Constants.IMAGE_UUID, object2.getString(Constants.IMAGE_UUID));
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                } catch (JSONException e) {
                                    FirebaseEventUtils.logException(e);
                                }
                            break;
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private static void addPinData(JSONObject fromJson, JSONObject toJson) {
        try {
            if (fromJson.has(Constants.MAP_PIN_URL))
                toJson.put(Constants.MAP_PIN_URL, fromJson.getString(Constants.MAP_PIN_URL));
            if (fromJson.has(Constants.PARAM_LAT))
                toJson.put(Constants.PARAM_LAT, fromJson.getDouble(Constants.PARAM_LAT));
            if (fromJson.has(Constants.PARAM_LON))
                toJson.put(Constants.PARAM_LON, fromJson.getDouble(Constants.PARAM_LON));
            if (fromJson.has(Constants.PIN_LABEL)) {
                toJson.put(Constants.PIN_LABEL, fromJson.getString(Constants.PIN_LABEL));
            }

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static List<FormData> getSubFormData(Context context, boolean isTMForm, FormData tempFormData) {
        List<FormData> lstSubForms;
        if (isTMForm) {
            TblTMForms tblTNMForms = new TblTMForms(context);
            lstSubForms = tblTNMForms.getAllSubmittedSubFormsByMainFormPKId(tempFormData.getFormPKId());
        } else {
            TblForms tblForms = new TblForms(context);
            lstSubForms = tblForms.getAllSubmittedSubFormsByMainFormPKId(tempFormData.getFormPKId());
        }
        return lstSubForms;
    }

    public static void concatSubFormsInMainFormResponse(boolean isTmForm, Context context, FormData mainForm, List<FormData> lstSubForms) {
        try {
            JSONObject combinedModifiedMainJsonData = null;
            String mainJsonDataObjectString;
            try {
                combinedModifiedMainJsonData = concatImageDataWithTMFormData(isTmForm, context, mainForm.getFormPKId(), mainForm.getModifiedFormData());
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
            if (combinedModifiedMainJsonData != null) {
                mainJsonDataObjectString = combinedModifiedMainJsonData.toString();
            } else {
                mainJsonDataObjectString = mainForm.getModifiedFormData();
            }
            JSONObject mainObject = new JSONObject(mainJsonDataObjectString);
            JSONArray jsonArray = mainObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            jsonArrayReadForManagedCrew(isTmForm, context, mainForm, mainObject, array, lstSubForms);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private static void jsonArrayReadForManagedCrew(boolean isTmForm, Context context, FormData mainForm, JSONObject mainObject, JSONArray jsonArray, List<FormData> lstSubForms) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.getString(Constants.TYPE).equals(Constants.MANAGE_CREW) && object.has(Constants.ELEMENTS)) {
                    JSONArray valueArray = new JSONArray();
                    for (FormData subFromData : lstSubForms) {
                        JSONObject subFormObject = new JSONObject();
                        subFormObject.put(PARAM_CHECK_IN_DATE, subFromData.getCheckin_time() / 1000);
                        subFormObject.put(PARAM_CHECK_OUT_DATE, subFromData.getCheckout_time() / 1000);
                        subFormObject.put(TYPE, object.getString(TYPE));
                        subFormObject.put(FORM_SUBMISSION_ID, subFromData.getFormSubmissionId());

                        if (subFromData.getSubFormOtherData() != null) {
                            subFormObject.put(VALUE, subFromData.getSubFormOtherData().getCrewId());
                            subFormObject.put(TITLE, subFromData.getSubFormOtherData().getCrewName());
                            if (subFromData.isCanceledForm()) {
                                subFormObject.put(PARAM_SUBMITTED_FOR, subFromData.getSubFormOtherData().getCrewId());
                            }
                        }
                        JSONObject combinedModifiedDataJson = concatImageDataWithTMFormData(isTmForm, context, subFromData.getFormPKId(), subFromData.getModifiedFormData());
                        if (combinedModifiedDataJson != null) {
                            JSONArray elementArray = getElementOfSubForm(combinedModifiedDataJson);
                            if (elementArray != null) {
                                subFormObject.put(ELEMENTS, elementArray);
                            }
                        }
                        valueArray.put(subFormObject);
                    }
                    object.put(VALUE, valueArray);
                    mainForm.setModifiedFormData(mainObject.toString());
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private static JSONArray getElementOfSubForm(JSONObject subFormObject) {
        JSONArray elementArray = null;
        try {
            JSONArray jsonArray = subFormObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    elementArray = object.getJSONArray(Constants.ELEMENTS);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return elementArray;
    }

    public static JSONObject concatImageDataWithTMFormData(boolean isTmForm, Context context, int formPKId, String modifiedDataString) {
        JSONObject jsonObject = null;
        try {
            if (!TextUtils.isEmpty(modifiedDataString)) {
                jsonObject = new JSONObject(modifiedDataString);
                JSONArray jsonArray = jsonObject.getJSONArray(Constants.PAGES);
                for (int i = 0; i < jsonArray.length(); i++) {
                    try {
                        JSONObject object = jsonArray.getJSONObject(i);
                        if (object.has(Constants.ELEMENTS)) {
                            try {
                                JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                                jsonArrayReadForConcatData(context, array, formPKId, isTmForm);
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            }
        } catch (JSONException | NullPointerException e) {
            FirebaseEventUtils.logException(e);
        }
        return jsonObject;
    }

    /**
     * Method to check device id and if not stored in preference then generate new one and store in preference
     *
     * @return
     */
    public static String checkAndGetDeviceId() {
        if (TextUtils.isEmpty(AppPrefShared.getString(Constants.DEVICE_ID, ""))) {
            AppPrefShared.putValue(Constants.DEVICE_ID, StaticUtils.getUuid());
        }

        return AppPrefShared.getString(Constants.DEVICE_ID, "");
    }

    /**
     * Method to get message to clear form.
     *
     * @param context
     * @param formData selected form data
     * @return checked in form cleare message if form is checkedInOut and already checked in
     */
    public static String getClearFormMessage(Context context, FormData formData) {
        return StaticUtils.isFormCheckedIn(formData) ? context.getString(R.string.msg_clear_checkin_site_form_message) : context.getString(R.string.msg_clear_form_data);
    }

    /**
     * Check if form is checked in or not
     *
     * @param formData
     * @return true if form is checkinout and checked in by user
     */
    public static boolean isFormCheckedIn(FormData formData) {
        return formData.getIsCheckInOut() && formData.getCheckin_time() > 0;
    }

    /**
     * Method to set default value for selectedVehicles and selectedEquipments
     *
     * @param object
     */
    public static void setDefaultVehicleAndEquipmentValue(JSONObject object) {
        try {
            if (object.has(Constants.IS_FLEET_VEHICLELIST) && object.getBoolean(Constants.IS_FLEET_VEHICLELIST)) {
                object.put(SELECTED_VEHICLES, "");
            }
            if (object.has(Constants.IS_FLEET_EQUIPMENTLIST) && object.getBoolean(Constants.IS_FLEET_EQUIPMENTLIST)) {
                object.put(SELECTED_EQUIPMENTS, "");
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static boolean isSubFormUploadingPending(Context context, int mainFormPKId, boolean isTmForm) {
        TblUploadData tblUploadData = new TblUploadData(context);
        // return true if main form has submitted sub forms in upload table
        if (isTmForm) {
            TblTMForms tblTMForms = new TblTMForms(context);
            List<Integer> lstSubForm = tblTMForms.getPkIdsOfSubmittedSubForm(TblTMForms.TABLE_NAME, mainFormPKId);
            return tblUploadData.isSubFormDataExistForTmForm(lstSubForm);
        } else {
            TblForms tblForms = new TblForms(context);
            List<Integer> lstSubForm = tblForms.getPkIdsOfSubmittedSubForm(TblForms.TABLE_NAME, mainFormPKId);
            return tblUploadData.isSubFormDataExistForNormalForm(lstSubForm);
        }
    }


    public static void setPreferredDistanceUnit(Context context) {
        boolean isInitialized = AppPrefShared.contains(Constants.IS_UNIT_IN_MILES);
        if (isInitialized) {
            return; // Already initialized, no need to set again
        }

        String countryCode = context.getApplicationContext().getResources().getConfiguration().locale.getCountry();
        // These are common countries that primarily use miles for road distances.
        switch (countryCode) {
            case "US": // United States
            case "LR": // Liberia
            case "MM": // Myanmar
            case "GB": // United Kingdom (for road distances)
            case "": // Default case for empty country code, we can assume miles
                AppPrefShared.putValue(Constants.IS_UNIT_IN_MILES, true); // miles
                return;
            default:
                AppPrefShared.putValue(Constants.IS_UNIT_IN_MILES, false); // Most other countries use kilometers
        }
    }

    /// parameter "distance" is in miles
    public static String getDistanceByUnit(Context context, double distance) {
        boolean isUnitInMiles = AppPrefShared.getBoolean(Constants.IS_UNIT_IN_MILES, false);
        if (isUnitInMiles) {
            // 1 miles = 5280 feet
            double feet = distance * 5280;
            if (feet < 1000.0) {
                return String.format(Locale.US, "%.0f ", feet) + context.getString(R.string.feet);
            } else {
                String formatted = (distance < 10)
                        ? String.format(Locale.US, "%.1f ", distance)
                        : String.format(Locale.US, "%,.0f ", distance);
                return formatted + context.getString(R.string.miles);
            }
        } else {
            // 1 miles = 1.60934 km
            double kilometers = distance * 1.60934;
            if (kilometers < 1.0) {
                double meters = kilometers * 1000;
                return String.format(Locale.US, "%.0f ", meters) + "m";
            } else {
                String formatted = (kilometers < 10)
                        ? String.format(Locale.US, "%.1f ", kilometers)
                        : String.format(Locale.US, "%,.0f ", kilometers);
                return formatted + "km";
            }
        }
    }

}

