package com.sitefotos.util;

import android.content.Context;
import android.os.Build;
import android.os.Bundle;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.logger.CustomLogKt;


public class FirebaseEventUtils {


    public static void setUserData(Context context) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {
                FirebaseAnalytics firebaseAnalytics = FirebaseAnalytics.getInstance(context);
                firebaseAnalytics.setUserId(AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                firebaseAnalytics.setUserProperty("CompanyId", AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                firebaseAnalytics.setUserProperty("CompanyName", AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_NAME, ""));
            } catch (Exception e) {
                logException(e);
            }
        }
    }

    public static void InsertImageDataEvent(Context context, String uuId) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {

                Bundle bundle = getCommonData();
                bundle.putString("ImageUUID", uuId);
                FirebaseAnalytics.getInstance(context).logEvent("InsertImageData", bundle);
            } catch (Exception e) {
                logException(e);
            }
        }
    }

    public static void InsertImageDataExceptionEvent(Context context, String uuId) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {
                Bundle bundle = getCommonData();
                bundle.putString("ImageUUID", uuId);
                FirebaseAnalytics.getInstance(context).logEvent("InsertImageDataException", bundle);

            } catch (Exception e) {
                logException(e);
            }
        }
    }



    public static void PendingImageFilesEvent(Context context, int count) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {

                Bundle bundle = getCommonData();
                bundle.putString("count", String.valueOf(count));
                FirebaseAnalytics.getInstance(context).logEvent("PendingFiles", bundle);
            } catch (Exception e) {
                logException(e);
            }
        }
    }

    public static void PendingOtherDataEvent(Context context, int count) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {
                Bundle bundle = getCommonData();
                bundle.putString("count", String.valueOf(count));
                FirebaseAnalytics.getInstance(context).logEvent("PendingOtherData", bundle);

            } catch (Exception e) {
                logException(e);
            }
        }
    }

    public static void uploadCompletedEvent(Context context, int remainingCount) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {
                Bundle bundle = getCommonData();
                bundle.putString("Email", AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                bundle.putString("RemainingCount", String.valueOf(remainingCount));
                FirebaseAnalytics.getInstance(context).logEvent("UploadCompleted", bundle);
            } catch (Exception e) {
                logException(e);
            }
        }
    }


    public static void NotFoundFilesEvent(Context context, String uuId) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {
                Bundle bundle = getCommonData();
                bundle.putString("Email", AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                bundle.putString("uuId", uuId);
                FirebaseAnalytics.getInstance(context).logEvent("NotFoundFiles", bundle);
            } catch (Exception e) {
                logException(e);
            }
        }
    }


    public static void EmptyDataSubmissionEvent(Context context) {
        if (context == null)
            return;
        if (BuildConfig.shouldUseFirebase) {
            try {
                Bundle bundle = getCommonData();
                bundle.putString("Email", AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                FirebaseAnalytics.getInstance(context).logEvent("EmptyFormData", bundle);
            } catch (Exception e) {
                logException(e);
            }
        }
    }



    private static Bundle getCommonData() {
        Bundle bundle = null;
        try {
            bundle = new Bundle();
            bundle.putString("AndroidVersion", Build.VERSION.RELEASE);
            bundle.putString("AppVersion", String.valueOf(BuildConfig.VERSION_CODE));
            bundle.putString("Device", Build.MANUFACTURER.concat("-").concat(Build.MODEL));
        } catch (Exception e) {
            logException(e);
        }

        return bundle;
    }


    /**
     * Method to record exception on firebase
     * @param exception
     */
    public static void logException(Exception exception) {
        try {
            if (BuildConfig.DEBUG){
                exception.printStackTrace();
            }
            // Print the stack trace to the console
           // exception.printStackTrace();

            // Record the exception with Firebase Crashlytics
            FirebaseCrashlytics.getInstance().recordException(exception);
        } catch (Exception e) {
            // Handle any exceptions that occurred during logging
            e.printStackTrace();
        }
    }

}
