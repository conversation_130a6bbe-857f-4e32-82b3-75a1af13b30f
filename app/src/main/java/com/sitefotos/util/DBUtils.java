package com.sitefotos.util;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.location.Location;
import android.text.TextUtils;

import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.models.FormData;
import com.sitefotos.models.Tags;
import com.sitefotos.models.UploadImageData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblUploadImage;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DBUtils {

    /**
     * Common method to save captured, single and multiple selected photos in TblImageUpload table
     */
    public static void insertImageUploadData(Context context, String imagePathLow, String imagePathHigh,
                                             String strDescription, String date, String buildingId,
                                             Location location, double extraLatitude, double extraLongitude,
                                             FormData formData, boolean isTMForm, int tagId, boolean isIssue, List<Tags> lstTag, boolean isFromMultipleImage, boolean isGalleryImage) {

        UploadImageData uploadImageData = new UploadImageData();
        uploadImageData.setImagePathLow(imagePathLow);
        uploadImageData.setImagePathHigh(imagePathHigh);
        String tags = StaticUtils.getStringWithHashTagFromTagList(lstTag);
        if (TextUtils.isEmpty(strDescription)){
            strDescription = tags;
        }else {
            strDescription = strDescription.concat(" ").concat(tags);
        }
        uploadImageData.setDescription(strDescription);
        uploadImageData.setLatitude(location.getLatitude());
        uploadImageData.setLongitude(location.getLongitude());
        uploadImageData.setExtraLatitude(extraLatitude);
        uploadImageData.setExtraLongitude(extraLongitude);
        uploadImageData.setBuilding(buildingId);
        uploadImageData.setAccessCode(AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, ""));
        uploadImageData.setEmail(AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
        uploadImageData.setUploadOriginalSize(AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false));
        uploadImageData.setRetryCount(0);
        uploadImageData.setValid(true);
        uploadImageData.setUuid(StaticUtils.getUuid());
        uploadImageData.setHrImage("");
        uploadImageData.setLrImage("");
        uploadImageData.setUploadUrlHigh("");
        uploadImageData.setUploadUrlHigh("");
        // 1 if captured from app camera, 2 if picked from gallery, 0 if old or signature or sketch. default 0
        if (isGalleryImage) {
            uploadImageData.setCameraImage(2);
        }else{
            uploadImageData.setCameraImage(1);
        }

        uploadImageData.setTags(StaticUtils.getStringFromTagList(lstTag));
        uploadImageData.setTagId(tagId);
        uploadImageData.setAppVersion(BuildConfig.VERSION_NAME);

        if (formData != null) {
            uploadImageData.setFormImage(true);
            uploadImageData.setFormSign(false);
            uploadImageData.setFormId(formData.getFormId());
            if (isTMForm) {
                uploadImageData.setTmFormPkId(formData.getFormPKId());
                uploadImageData.setWpId(formData.getSiteId());
            } else {
                uploadImageData.setFormPkId(formData.getFormPKId());
                uploadImageData.setWpId(-2);
            }
            uploadImageData.setFormId(formData.getFormId());
        } else {
            uploadImageData.setFormImage(false);
            uploadImageData.setFormPkId(0);
            uploadImageData.setFormSign(false);
            uploadImageData.setFormId(0);
            uploadImageData.setWpId(-2);
        }

        try {
            if (!TextUtils.isEmpty(date)) {
                uploadImageData.setDate(date);
            } else {
                if (isFromMultipleImage) {
                    uploadImageData.setDate("");
                } else {
                    uploadImageData.setDate(DateUtil.fullDateTimeT.format(new Date()));
                }
            }
            if (!TextUtils.isEmpty(uploadImageData.getDate())){
                uploadImageData.setTimeInUnix(DateUtil.getDateInLongFromImageDateFormat(uploadImageData.getDate()));
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            TblUploadImage tblUploadImage = new TblUploadImage(context);
            boolean isDataInserted = tblUploadImage.insertData(uploadImageData);
            if (!isDataInserted) {
                FirebaseEventUtils.InsertImageDataExceptionEvent(context, uploadImageData.getUuid());
            } else {
                FirebaseEventUtils.InsertImageDataEvent(context, uploadImageData.getUuid());
                if (formData != null && isIssue) {
                    if (isTMForm) {
                        int issueCount = Math.max(StaticUtils.getTMFormPendingIssueCount(formData.getFormPKId(), tagId), 0);
                        StaticUtils.setTMFormPendingIssueCount(formData.getFormPKId(), tagId, issueCount + 1);
                    } else {
                        int issueCount = Math.max(StaticUtils.getFormPendingIssueCount(formData.getFormPKId(), tagId), 0);
                        StaticUtils.setFormPendingIssueCount(formData.getFormPKId(), tagId, issueCount + 1);
                    }
                }
            }
        }
    }

    public static String toCommaSeparatedString(List<Integer> list) {
        if (list == null || list.isEmpty())
            return "";

        StringBuilder nameBuilder = new StringBuilder();
        for (Integer item : list) {
            nameBuilder.append(item).append(", ");
        }
        nameBuilder.deleteCharAt(nameBuilder.length() - 1);
        nameBuilder.deleteCharAt(nameBuilder.length() - 1);
        return nameBuilder.toString();


    }

    public static String toCommaSeparatedStringFromLong(List<Long> list) {
        if (list == null || list.isEmpty())
            return "";

        StringBuilder nameBuilder = new StringBuilder();
        for (Long item : list) {
            nameBuilder.append(item).append(", ");
        }
        nameBuilder.deleteCharAt(nameBuilder.length() - 1);
        nameBuilder.deleteCharAt(nameBuilder.length() - 1);
        return nameBuilder.toString();


    }

    public static String toCommaSeparated(List<String> list) {
        if (list != null && !list.isEmpty()) {
            StringBuilder nameBuilder = new StringBuilder();
            for (String item : list) {
                nameBuilder.append(item).append(", ");
            }
            nameBuilder.deleteCharAt(nameBuilder.length() - 1);
            nameBuilder.deleteCharAt(nameBuilder.length() - 1);
            return nameBuilder.toString();
        } else {
            return "";
        }

    }

    public static String toCommaSeparatedStringFromList(List<Long> list) {
        if (!list.isEmpty()) {
            StringBuilder nameBuilder = new StringBuilder();
            for (Long item : list) {
                nameBuilder.append(item).append(", ");
            }
            nameBuilder.deleteCharAt(nameBuilder.length() - 1);
            nameBuilder.deleteCharAt(nameBuilder.length() - 1);
            return nameBuilder.toString();
        } else {
            return "";
        }
    }

    public static boolean dataExist(SQLiteDatabase writableDb, String query) {
        try {
            Cursor cursor = writableDb.rawQuery(query, null);
            cursor.moveToFirst();
            if (cursor.getInt(0) == 1) {
                cursor.close();
                return true;
            } else {
                cursor.close();
                return false;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return false;
    }


    public static List<Integer> getIdFromString(String string) {
        List<Integer> lstIds = new ArrayList<>();
        if (!TextUtils.isEmpty(string)) {
            for (String id : string.split(",")) {
                lstIds.add(Integer.parseInt(id.trim()));
            }
        }
        return lstIds;
    }
}
