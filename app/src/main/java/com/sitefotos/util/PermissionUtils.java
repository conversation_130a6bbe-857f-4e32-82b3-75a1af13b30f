package com.sitefotos.util;

import static androidx.core.content.ContextCompat.checkSelfPermission;

import static com.sitefotos.Constants.LOGGED_IN_PARAM_IS_LOCATION_REQUIRED;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.core.app.ActivityCompat;
import androidx.fragment.app.Fragment;

import com.sitefotos.Constants;
import com.sitefotos.storage.AppPrefShared;

public class PermissionUtils {
    public static String[] STORAGE_PERMISSIONS = {Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};
    public static String STORAGE_PERMISSIONS_11 = Manifest.permission.READ_EXTERNAL_STORAGE;
    public static String STORAGE_PERMISSIONS_13 = Manifest.permission.READ_MEDIA_IMAGES;
    public static final String CAMERA_PERMISSION = Manifest.permission.CAMERA;
    public static final String[] LOCATION_PERMISSION = {Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION};
    public static final String[] FINE_LOCATION_PERMISSION = {Manifest.permission.ACCESS_FINE_LOCATION};
    public static final String[] APPROX_LOCATION_PERMISSION = {Manifest.permission.ACCESS_COARSE_LOCATION};

    public static String[] PERMISSIONS = {Manifest.permission.CAMERA, Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION
            , Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE};

    public static String[] PERMISSIONS_29 = {Manifest.permission.CAMERA, Manifest.permission.ACCESS_FINE_LOCATION, Manifest.permission.ACCESS_COARSE_LOCATION
            , Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.ACCESS_MEDIA_LOCATION};

    public static String[] PERMISSIONS_33 = {Manifest.permission.CAMERA, Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION, Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.POST_NOTIFICATIONS, Manifest.permission.ACCESS_MEDIA_LOCATION,
            Manifest.permission.READ_MEDIA_IMAGES};

    public static boolean checkAllPermission(Context context) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU)
                return hasPermissions(context, PERMISSIONS_33);
            else
                return hasPermissions(context, PERMISSIONS_29);
        }
        return hasPermissions(context, PERMISSIONS);
    }

    public static boolean hasPermissions(Context context, String[] permissions) {
        if (context == null) {
            return false;
        }
        for (String permission : permissions) {
            if (checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    public static String[] getLocationPermissions() {
        if (TextUtils.isEmpty(AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "))){
            return LOCATION_PERMISSION;
        }

        if(AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 1) {
            return APPROX_LOCATION_PERMISSION;
        }

        if(AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 2) {
            return FINE_LOCATION_PERMISSION;
        }
        return LOCATION_PERMISSION;
    }

    public static boolean hasStoragePermissions(Context context) {
        if (context == null) {
            return false;
        }
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return checkSelfPermission(context, STORAGE_PERMISSIONS_13) == PackageManager.PERMISSION_GRANTED;
        }
        if (android.os.Build.VERSION.SDK_INT <= android.os.Build.VERSION_CODES.R) {
            return checkSelfPermission(context, STORAGE_PERMISSIONS_11) == PackageManager.PERMISSION_GRANTED;
        }
        for (String permission : STORAGE_PERMISSIONS) {
            if (checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    public static boolean hasPermission(Context context, String permission) {
        if (context == null) {
            return false;
        }
        return checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Gets whether you should show UI with rationale for requesting permissions.
     *
     * @param permissions The permissions your app wants to request.
     * @return Whether you can show permission rationale UI.
     */
    public static boolean shouldShowRequestPermissions(Fragment fragment, String[] permissions) {
        for (String permission : permissions) {
            if (fragment.shouldShowRequestPermissionRationale(permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Gets whether you should show UI with rationale for requesting permissions.
     *
     * @param permissions The permissions your app wants to request.
     * @return Whether you can show permission rationale UI.
     */
    public static boolean shouldShowRequestPermissions(Activity activity, String[] permissions) {
        for (String permission : permissions) {
            if (ActivityCompat.shouldShowRequestPermissionRationale(activity, permission)) {
                return true;
            }
        }
        return false;
    }


    public static boolean shouldShowRequestPermission(Activity activity, String permission) {
        return ActivityCompat.shouldShowRequestPermissionRationale(activity, permission);
    }

    public static boolean shouldShowRequestPermission(Fragment fragment, String permission) {
        return fragment.shouldShowRequestPermissionRationale(permission);
    }

    public static void requestPermission(Activity activity, String[] permissions, int reqId) {
        ActivityCompat.requestPermissions(activity, permissions, reqId);
    }

    public static void requestPermission(Activity activity, String permission, int reqId) {
        String[] permissions = {permission};
        ActivityCompat.requestPermissions(activity, permissions, reqId);
    }

    public static void requestPermission(Fragment fragment, String[] permissions, int reqId) {
        fragment.requestPermissions(permissions, reqId);
    }


    public static void requestStoragePermission(Activity activity, int reqId) {
        String[] permissions;

        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions = new String[]{STORAGE_PERMISSIONS_13};
        } else if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            permissions = new String[]{STORAGE_PERMISSIONS_11};
        } else {
            permissions = STORAGE_PERMISSIONS;
        }
        ActivityCompat.requestPermissions(activity, permissions, reqId);
    }


    /**
     * Method to get required permission string from permission string array
     *
     * @param context     context of Activity/Fragment
     * @param permissions String array of required permission
     * @return returns empty if not required permission else required permission string.
     */
    private static String getRequiredPermissionString(Context context, String[] permissions) {
        for (String permission : permissions) {
            if (checkSelfPermission(context, permission) != PackageManager.PERMISSION_GRANTED) {
                return permission;
            }
        }
        return "";
    }


    /**
     * Method to check user checked "Never asked" option or not.
     *
     * @param activity    context of Activity.
     * @param permissions String array of required permission
     * @return returns true if neverAsked selected else returns false.
     */
    public static boolean hasPermissionDenied(Activity activity, String[] permissions) {
        String permission = getRequiredPermissionString(activity, permissions);
        return !TextUtils.isEmpty(permission) && activity.shouldShowRequestPermissionRationale(permission);
    }


    /**
     * Method to check user checked "Never asked" option or not.
     *
     * @param activity context of Activity.
     * @return returns true if neverAsked selected else returns false.
     */
    /*public static boolean storagePermissionDenied(Activity activity) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.R) {
            return checkSelfPermission(activity, STORAGE_PERMISSIONS_11) == PackageManager.PERMISSION_GRANTED;
        }

        String permission = getRequiredPermissionString(activity, STORAGE_PERMISSIONS);
        return !TextUtils.isEmpty(permission) && activity.shouldShowRequestPermissionRationale(permission);

    }*/

    public static void navigateUserToPermissionScreen(Activity activity) {
        Intent intent = new Intent();
        intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
        Uri uri = Uri.fromParts("package", activity.getPackageName(), null);
        intent.setData(uri);
        activity.startActivity(intent);
    }

}
