package com.sitefotos.util;

import androidx.exifinterface.media.ExifInterface;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.sitefotos.util.logger.CustomLogKt;

import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class DateUtil {

    public static final SimpleDateFormat fullDateTimeZome = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss Z", Locale.US);
    public static final SimpleDateFormat fullDateTimeT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US);
    public static final SimpleDateFormat uploadDataDateFormat = new SimpleDateFormat("yyyy:MM:dd HH:mm:ss", Locale.US);
    public static final SimpleDateFormat dateFormatYYYYMMDD = new SimpleDateFormat("yyyy-MM-dd", Locale.US);
    public static final SimpleDateFormat timeFormatHHMM = new SimpleDateFormat("HH:mm", Locale.US);

    public static final SimpleDateFormat dateFormatDDMMMYYY = new SimpleDateFormat("dd-MMM-yyyy", Locale.US);

    public static final SimpleDateFormat dateFormatMMDDYYYMMhhmm = new SimpleDateFormat("MM/dd/yy hh:mm a", Locale.US);

    public static String getCurrentDateAndTime() {

        String date = "";
        try {
            date = fullDateTimeT.format(System.currentTimeMillis());
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    public static String getCurrentTimeInTimeDateFormat() {
        String date = "";
        try {
            date = fullDateTimeZome.format(System.currentTimeMillis());
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    /**
     * dd MM yyyy
     *
     * @param updateDate date in string format
     * @return date format as a string
     */
    public static long getDateInLongFromImageDateFormat(String updateDate) {
        Date date;
        try {
            date = fullDateTimeT.parse(updateDate);
            return date.getTime()/1000;
        } catch (Exception ex) {
            FirebaseEventUtils.logException(ex);
            return 0;
        }


    }

    public static long getTimeAfterAdditionOfSomeTime(long currentTime, int additionHours, int addintionMins) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(currentTime);
        calendar.add(Calendar.HOUR, additionHours);
        calendar.add(Calendar.MINUTE, addintionMins);

        return calendar.getTimeInMillis();
    }


    public static String getDateAndTime(long time) {
        String date = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy hh:mm a", Locale.getDefault());
            date = sdf.format(new Date(time));
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    public static String getDate(long time) {
        String date = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy", Locale.getDefault());
            date = sdf.format(new Date(time));
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    public static String getDatInDDMMMYYYY(long time) {
        String date = "";
        try {
            date = dateFormatDDMMMYYY.format(new Date(time));
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    public static String getDateInDayFormat(long time) {
        String date = "";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd MMMM yyyy", Locale.getDefault());
            date = sdf.format(new Date(time));
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    public static String getDateInYYYYMMDD(long time) {
        String date = "";
        try {
            date = dateFormatYYYYMMDD.format(new Date(time));
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }

    public static String getTimeInHHMM(long timeMillis) {
        String time = "";
        try {
            time = timeFormatHHMM.format(new Date(timeMillis));
            return time;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return time;
    }



    /**
     * dd MM yyyy
     *
     * @param updateDate date in string format
     * @return date format as a string
     */
    public static long getDateInLong(String updateDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd MMMM yyyy", Locale.getDefault());
        Date date;
        try {
            date = sdf.parse(updateDate);
            return date.getTime();
        } catch (Exception ex) {
            FirebaseEventUtils.logException(ex);
            return 0;
        }


    }

    public static String getDateFromExifMetadata(String path) {

        String dateTime = null;

        if (path.isEmpty()) {
            return null;
        }
        try {
            Metadata metadata = ImageMetadataReader.readMetadata(new File(path));
            ExifInterface exif = null;
            try {
                exif = new ExifInterface(path);
            } catch (IOException e) {
                FirebaseEventUtils.logException(e);
            }
            if (exif != null) {
                try {
                    for (Directory directory : metadata.getDirectories()) {
                        for (Tag tag : directory.getTags()) {
                            if (tag.getTagName().equalsIgnoreCase("Date/Time Original")) {
                                dateTime = tag.getDescription();
                            } else if (tag.getTagName().equalsIgnoreCase("Date/Time")) {
                                dateTime = tag.getDescription();
                            } else if (tag.getTagName().equalsIgnoreCase("Date/Time Digitized")) {
                                dateTime = tag.getDescription();
                            }else if(tag.getTagName().equalsIgnoreCase("File Modified Date")){
                                dateTime = tag.getDescription();
                            }
                        }
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }

                if (dateTime == null) {
                    dateTime = exif.getAttribute(ExifInterface.TAG_DATETIME);
                }
            }
        } catch (ImageProcessingException | IOException e) {
            FirebaseEventUtils.logException(e);
        }
        return dateTime;
    }

    /**
     * Method to get date format from date string
     */
    private static final Map<String, String> DATE_FORMAT_REGEXPS = new HashMap<String, String>() {{
        put("^\\d{8}$", "yyyyMMdd");
        put("^\\d{1,2}-\\d{1,2}-\\d{4}$", "dd-MM-yyyy");
        put("^\\d{1,2}.\\d{1,2}.\\d{4}$", "dd-MM-yyyy");//21.01.2017
        put("^\\d{4}-\\d{1,2}-\\d{1,2}$", "yyyy-MM-dd");
        put("^\\d{1,2}/\\d{1,2}/\\d{4}$", "MM/dd/yyyy");
        put("^\\d{4}/\\d{1,2}/\\d{1,2}$", "yyyy/MM/dd");
        put("^\\d{1,2}\\s[a-z]{3}\\s\\d{4}$", "dd MMM yyyy");
        put("^\\d{1,2}\\s[a-z]{4,}\\s\\d{4}$", "dd MMMM yyyy");
        put("^\\d{12}$", "yyyyMMddHHmm");
        put("^\\d{8}\\s\\d{4}$", "yyyyMMdd HHmm");
        put("^\\d{1,2}-\\d{1,2}-\\d{4}\\s\\d{1,2}:\\d{2}$", "dd-MM-yyyy HH:mm");
        put("^\\d{4}-\\d{1,2}-\\d{1,2}\\s\\d{1,2}:\\d{2}$", "yyyy-MM-dd HH:mm");
        put("^\\d{1,2}/\\d{1,2}/\\d{4}\\s\\d{1,2}:\\d{2}$", "MM/dd/yyyy HH:mm");
        put("^\\d{4}/\\d{1,2}/\\d{1,2}\\s\\d{1,2}:\\d{2}$", "yyyy/MM/dd HH:mm");
        put("^\\d{1,2}\\s[a-z]{3}\\s\\d{4}\\s\\d{1,2}:\\d{2}$", "dd MMM yyyy HH:mm");
        put("^\\d{1,2}\\s[a-z]{4,}\\s\\d{4}\\s\\d{1,2}:\\d{2}$", "dd MMMM yyyy HH:mm");
        put("^\\d{14}$", "yyyyMMddHHmmss");
        put("^\\d{8}\\s\\d{6}$", "yyyyMMdd HHmmss");
        put("^\\d{1,2}-\\d{1,2}-\\d{4}\\s\\d{1,2}:\\d{2}:\\d{2}$", "dd-MM-yyyy HH:mm:ss");
        put("^\\d{1,2}.\\d{1,2}.\\d{4}\\s\\d{1,2}:\\d{2}:\\d{2}$", "dd.MM.yyyy HH:mm:ss");//21.01.2017
        put("^\\d{4}-\\d{1,2}-\\d{1,2}\\s\\d{1,2}:\\d{2}:\\d{2}$", "yyyy-MM-dd HH:mm:ss");
        put("^\\d{1,2}/\\d{1,2}/\\d{4}\\s\\d{1,2}:\\d{2}:\\d{2}$", "MM/dd/yyyy HH:mm:ss");
        put("^\\d{4}/\\d{1,2}/\\d{1,2}\\s\\d{1,2}:\\d{2}:\\d{2}$", "yyyy/MM/dd HH:mm:ss");
        put("^\\d{4}:\\d{1,2}:\\d{1,2}\\s\\d{1,2}:\\d{2}:\\d{2}$", "yyyy:MM:dd HH:mm:ss");//2017:01:03 14:49:15
        put("^\\d{1,2}\\s[a-z]{3}\\s\\d{4}\\s\\d{1,2}:\\d{2}:\\d{2}$", "dd MMM yyyy HH:mm:ss");
        put("^\\d{1,2}\\s[a-z]{4,}\\s\\d{4}\\s\\d{1,2}:\\d{2}:\\d{2}$", "dd MMMM yyyy HH:mm:ss");
        put("^(Mon|Tue|Wed|Thu|Fri|Sat|Sun)\\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s\\d{2}\\s\\d{1,2}:\\d{1,2}:\\d{1,2}\\s[+-]\\d{2}:\\d{2}\\s\\d{4}$","EEE MMM dd HH:mm:ss Z yyyy");//Mon Nov 11 12:07:57 +05:30 2024
    }};

    /**
     * Determine SimpleDateFormat pattern matching with the given date string. Returns null if
     * format is unknown. You can simply extend DateUtil with more formats if needed.
     *
     * @param dateString The date string to determine the SimpleDateFormat pattern for.
     * @return The matching SimpleDateFormat pattern, or null if format is unknown.
     * @see SimpleDateFormat
     */
    private static String determineDateFormat(String dateString) {
        for (String regexp : DATE_FORMAT_REGEXPS.keySet()) {
            if (dateString.toLowerCase().matches(regexp)|| dateString.matches(regexp)) {
                return DATE_FORMAT_REGEXPS.get(regexp);
            }
        }
        return null; // Unknown format.
    }

    public static String convertExpectedDateFormat(String dateTimeInput) {
        String expectedDateFormat = null;
        if (dateTimeInput != null) {
            String dateFormat = determineDateFormat(dateTimeInput);
            try {
                if (dateFormat != null) {
                    SimpleDateFormat inputDateFormat = new SimpleDateFormat(dateFormat, Locale.US);
                    Date date = inputDateFormat.parse(dateTimeInput);
                    expectedDateFormat = fullDateTimeT.format(date);
                }
            } catch (ParseException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return expectedDateFormat;
    }

    public static String getTimeFromIntValue(long time) {
        long times = time / 1000;
        int hours = (int) (times / (3600));
        times = times - (hours * 3600);
        int minutes = (int) (times / 60);
        return String.format(Locale.US, "%2d", hours) + ":" + String.format(Locale.US, "%02d", minutes);
    }

    public static String getTimeHoursFromIntValue(long time) {
        int hours = (int) ((time / 1000) / (3600));
        return String.format(Locale.US, "%2d", hours);
    }

    public static String getTimeMinutesFromIntValue(long time) {
        long times = time / 1000;
        int hours = (int) (times / (3600));
        times = times - (hours * 3600);
        int minutes = (int) (times / 60);
        return String.format(Locale.US, "%02d", minutes);
    }

    public static String getTimeSecondsFromIntValue(long time) {

        long times = time / 1000;
        int hours = (int) (times / (3600));
        times = times - (hours * 3600);
        int seconds = (int) ((time / 1000) % 60);

        return String.format(Locale.US, "%02d", seconds);
    }

    /**
     * Method to get time from current time.
     * This method will check Months, Weeks, days, Weeks, Days,Hours, Minutes, Sec different
     *
     * @param timeStamp end date
     * @return time different in string
     */
    public static String getDateTimeDifference(long timeStamp) {


        Date currentDate = new Date();
        long different = currentDate.getTime() - timeStamp;

        long secondsInMilli = 1000;
        long minutesInMilli = secondsInMilli * 60;
        long hoursInMilli = minutesInMilli * 60;
        long daysInMilli = hoursInMilli * 24;
        long weeksInMilli = daysInMilli * 7;
        long monthsInMilli = weeksInMilli * 4;

        /*long elapsedMonths = different / monthsInMilli;
        different = different % monthsInMilli;

        long elapsedWeeks = different / weeksInMilli;
        different = different % weeksInMilli;

        long elapsedDays = different / daysInMilli;
        different = different % daysInMilli;
*/
        long elapsedHours = different / hoursInMilli;
        different = different % hoursInMilli;

        long elapsedMinutes = different / minutesInMilli;

        String time = null;

        if (elapsedHours > 0) {
            time = formatTime(elapsedHours, elapsedMinutes, " hrs", " min");
        } else {
            if (elapsedMinutes > 0) {
                if (elapsedMinutes == 1) {
                    time = elapsedMinutes + " min";
                } else {
                    time = elapsedMinutes + " mins";
                }
            }

        }
        if (time == null) {
            time = "0";
        }
        return time;
    }

    /**
     * Common method for checking single or multiple values in string and concat string
     *
     * @param checkVar   Main params
     * @param CompareVar sub params
     * @param mainString Main string
     * @param subString  Sub String
     * @return String with whole text which will be displayed.
     */

    private static String formatTime(long checkVar, long CompareVar, String mainString, String subString) {

        String time;

        if (checkVar == 1) {
            if (CompareVar > 0) {
                time = checkVar + mainString + CompareVar + subString;
            } else {
                time = checkVar + subString + "s ";
            }
        } else {
            if (CompareVar > 0) {
                time = checkVar + mainString + "s " + CompareVar + subString + "s ";
            } else {
                time = checkVar + subString + "s ";
            }
        }
        return time;
    }

    public static String getChronometerTime(long time) {
        int h = (int) (time / 3600000);
        int m = (int) (time - h * 3600000) / 60000;
        int s = (int) (time - h * 3600000 - m * 60000) / 1000;
        return h + "h:" + m + "m";
    }

    public static String getChronometerTimeWithSeconds(long time) {
        int h = (int) (time / 3600000);
        int m = (int) (time - h * 3600000) / 60000;
        int s = (int) (time - h * 3600000 - m * 60000) / 1000;
        return h + ":" + m + ":"+ s ;
    }

    public static String getCheckInAndCheckOutDateWithDifference(long checkInTime, long checkoutTime) {
       String startTime = getDateMMDDYYYMMhhmm(checkInTime);
       String endTime = getDateMMDDYYYMMhhmm(checkoutTime);
       String timeDifference = getChronometerTimeWithSeconds(checkoutTime-checkInTime);

       return startTime.concat(" - ").concat(endTime).concat(" = ").concat(timeDifference);
    }

    public static String getDateMMDDYYYMMhhmm(long time) {
        String date = "";
        try {
            date = dateFormatMMDDYYYMMhhmm.format(new Date(time));
            return date;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return date;
    }
}
