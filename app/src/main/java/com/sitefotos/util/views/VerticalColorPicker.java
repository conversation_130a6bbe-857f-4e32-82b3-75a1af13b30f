package com.sitefotos.util.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Shader;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import com.sitefotos.R;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.FirebaseEventUtils;


public class VerticalColorPicker extends View {
    Canvas canvasView;
    private Paint paint;
    private Paint strokePaint;
    private Path path;
    private Bitmap bitmap;
    private int viewWidth;
    private int viewHeight;
    private int centerX;
    private float colorPickerRadius;
    private OnColorChangeListener onColorChangeListener;
    private RectF colorPickerBody;
    private float selectorYPos;
    private int borderColor;
    private float borderWidth;
    private int[] colors;
    private boolean cacheBitmap = true;
    private boolean isSet = false;
    int count = 1;
    String key;

    public VerticalColorPicker(Context context) {
        super(context);
        init();
    }



    public void setKey(String key) {
        this.key = key;
    }

    public VerticalColorPicker(Context context, AttributeSet attrs) {
        super(context, attrs);
        TypedArray a = context.getTheme().obtainStyledAttributes(
                attrs,
                R.styleable.VerticalSlideColorPicker,
                0, 0);

        try {
            borderColor = a.getColor(R.styleable.VerticalSlideColorPicker_borderColor, Color.WHITE);
            borderWidth = a.getDimension(R.styleable.VerticalSlideColorPicker_borderWidth, 10f);
            int colorsResourceId = a.getResourceId(R.styleable.VerticalSlideColorPicker_colors, R.array.sketch_colors);
            colors = a.getResources().getIntArray(colorsResourceId);
        } finally {
            a.recycle();
        }
        init();
    }

    public VerticalColorPicker(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

	/*public VerticalColorPicker(Context context, AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
		init();
	}*/

    private void init() {
        setWillNotDraw(false);
        paint = new Paint();
        paint.setStyle(Paint.Style.FILL);
        paint.setAntiAlias(true);

        path = new Path();

        strokePaint = new Paint();
        strokePaint.setStyle(Paint.Style.STROKE);
        strokePaint.setColor(borderColor);
        strokePaint.setAntiAlias(true);
        strokePaint.setStrokeWidth(borderWidth);

        setDrawingCacheEnabled(true);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        path.addCircle(centerX, borderWidth + colorPickerRadius, colorPickerRadius, Path.Direction.CW);
        path.addRect(colorPickerBody, Path.Direction.CW);
        path.addCircle(centerX, viewHeight - (borderWidth + colorPickerRadius), colorPickerRadius, Path.Direction.CW);
        canvasView = canvas;
        canvas.drawPath(path, strokePaint);
        canvas.drawPath(path, paint);

        if (cacheBitmap) {
            bitmap = getDrawingCache();
            cacheBitmap = false;
            invalidate();
        } else {
            canvas.drawLine(colorPickerBody.left, selectorYPos, colorPickerBody.right, selectorYPos, strokePaint);
        }
        if (!isSet) {
            if (count == 3) {
                setProgress();
            } else {
                count++;
            }
        }
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {

        float yPos = Math.min(event.getY(), colorPickerBody.bottom);
        yPos = Math.max(colorPickerBody.top, yPos);

        selectorYPos = yPos;
        if (bitmap != null && bitmap.isRecycled()) {
            bitmap = getDrawingCache();
        }
        if (bitmap != null && !bitmap.isRecycled()) {
            int selectedColor = bitmap.getPixel(viewWidth / 2, (int) selectorYPos);

            if (onColorChangeListener != null) {
                onColorChangeListener.onColorChange(selectedColor);
            }
            if (key != null)
                AppPrefShared.putValue(key, selectorYPos);
        }

        invalidate();

        return true;
    }

    public void setProgress() {
        float yPos = 52.00f;
        if (key != null)
            yPos = AppPrefShared.getFloat(key, (float) 52.00);
        selectorYPos = yPos;
        if (canvasView != null) {
            isSet = true;
            canvasView.drawLine(colorPickerBody.left, selectorYPos, colorPickerBody.right, selectorYPos, strokePaint);
            try {
                int selectedColor = bitmap.getPixel(viewWidth / 2, (int) selectorYPos);
                if (onColorChangeListener != null) {
                    onColorChangeListener.onColorChange(selectedColor);
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
            invalidate();
        }
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);

        viewWidth = w;
        viewHeight = h;

        centerX = viewWidth / 2;
        colorPickerRadius = (viewWidth / 2) - borderWidth;

        colorPickerBody = new RectF(centerX - colorPickerRadius, borderWidth + colorPickerRadius, centerX + colorPickerRadius, viewHeight - (borderWidth + colorPickerRadius));

        LinearGradient gradient = new LinearGradient(0, colorPickerBody.top, 0, colorPickerBody.bottom, colors, null, Shader.TileMode.CLAMP);
        paint.setShader(gradient);

        resetToDefault();
    }

    public void setBorderColor(int borderColor) {
        this.borderColor = borderColor;
        invalidate();
    }

    public void setBorderWidth(float borderWidth) {
        this.borderWidth = borderWidth;
        invalidate();
    }

    public void setColors(int[] colors) {
        this.colors = colors;
        cacheBitmap = true;
        invalidate();
    }

    public void resetToDefault() {
        selectorYPos = borderWidth + colorPickerRadius;

        if (onColorChangeListener != null) {
            onColorChangeListener.onColorChange(Color.TRANSPARENT);
        }

        invalidate();
    }

    public void setOnColorChangeListener(OnColorChangeListener onColorChangeListener) {
        this.onColorChangeListener = onColorChangeListener;
    }

    public interface OnColorChangeListener {

        void onColorChange(int selectedColor);
    }
}
