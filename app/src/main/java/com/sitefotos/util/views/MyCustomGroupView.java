package com.sitefotos.util.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

import androidx.annotation.Nullable;

/**
 * Created by dk on 23/2/18.
 */

public class MyCustomGroupView extends LinearLayout {
    public MyCustomGroupView(Context context) {
        super(context);
    }

    public MyCustomGroupView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public MyCustomGroupView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        return super.onInterceptTouchEvent(ev);
    }
}
