/*
package com.sitefotos.util.views;

import android.content.Context;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import androidx.annotation.Nullable;

import com.sitefotos.R;

public class BaseDrawingView extends View implements View.OnTouchListener {

    Context context;
    private Paint circlePaint;
    private Path circlePath;
    private float mX, mY;
    private static final float TOUCH_TOLERANCE = 4;

    public BaseDrawingView(Context context) {
        super(context);
    }

    public BaseDrawingView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public BaseDrawingView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    public BaseDrawingView(Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    private void setupDrawing() {
        // get drawing area setup for interaction

        setFocusable(true);
        setFocusableInTouchMode(true);
        setOnTouchListener(this);
        setupPaint();
        mPath = new Path();
        setupPaintForCircle();


    }

    private Paint setupPaint() {
       */
/* if(mPaint != null)
            return mPaint;*//*

        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(getResources().getColor(R.color.blue_ink_color));
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeJoin(Paint.Join.ROUND);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStrokeWidth(14);

        return mPaint;
    }

    private void setupPaintForCircle(){
        circlePaint = new Paint();
        circlePath = new Path();
        circlePaint.setAntiAlias(true);
        circlePaint.setColor(Color.BLUE);
        circlePaint.setStyle(Paint.Style.STROKE);
        circlePaint.setStrokeJoin(Paint.Join.MITER);
        circlePaint.setStrokeWidth(4f);

    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        return false;
    }
}
*/
