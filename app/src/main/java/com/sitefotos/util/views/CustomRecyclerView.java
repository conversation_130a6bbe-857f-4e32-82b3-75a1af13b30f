package com.sitefotos.util.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.TextView;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.OrientationHelper;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

import com.sitefotos.R;

import javax.annotation.Nullable;


public class CustomRecyclerView extends RecyclerView {

    private View emptyView;
    private String emptyTitle;
    private View refreshLayoutView;

    /**
     * Constructor with 1 parameter context and attrs
     *
     * @param context context
     */
    public CustomRecyclerView(Context context) {
        super(context);
    }

    /**
     * Constructor with 2 parameters context and attrs
     *
     * @param context context
     * @param attrs   context
     */
    public CustomRecyclerView(Context context, AttributeSet attrs) {
        super(context, attrs);
        initCustomText(context, attrs);
    }

    /**
     * Initializes all the attributes and respective methods are called based on the attributes
     *
     * @param context context of activity
     * @param attrs   attrs
     */
    private void initCustomText(Context context, AttributeSet attrs) {
        TypedArray ta = context.obtainStyledAttributes(attrs, R.styleable.CustomRecyclerView);

        int listOrientation = ta.getInt(R.styleable.CustomRecyclerView_list_orientation, 0);
        int listType = ta.getInt(R.styleable.CustomRecyclerView_list_type, 0);
        int grid_span = ta.getInt(R.styleable.CustomRecyclerView_gird_span, 3);

        /*
         * A custom getView uses isInEditMode() to determine whether or not it is being rendered inside the editor
         * and if so then loads test data instead of real data.
         */
        LayoutManager layoutManager;
        if (!isInEditMode()) {
            int layoutOrientation;
            if (listOrientation == 1) {
                layoutOrientation = OrientationHelper.HORIZONTAL;
            } else {
                layoutOrientation = OrientationHelper.VERTICAL;
            }

            switch (listType) {
                case 1:
                    layoutManager = new GridLayoutManager(context, grid_span, layoutOrientation, false);
                    break;
                case 2:
                    layoutManager = new StaggeredGridLayoutManager(grid_span, layoutOrientation);
                    break;
                default:
                    layoutManager = new LinearLayoutManager(context, layoutOrientation, false);
                    break;
            }

            setLayoutManager(layoutManager);

            ta.recycle();
        }
    }


    // This observer  observes made changes in adapter
    private AdapterDataObserver observer = new AdapterDataObserver() {
        @Override
        public void onChanged() {
            checkIfEmpty();
        }

        @Override
        public void onItemRangeInserted(int positionStart, int itemCount) {
            checkIfEmpty();
        }

        @Override
        public void onItemRangeRemoved(int positionStart, int itemCount) {
            checkIfEmpty();
        }
    };

    @Override
    public void setAdapter(Adapter adapter) {
        final Adapter oldAdapter = getAdapter();
        if (oldAdapter != null) {
            oldAdapter.unregisterAdapterDataObserver(observer);
        }
        super.setAdapter(adapter);
        if (adapter != null) {
            adapter.registerAdapterDataObserver(observer);
        }
        checkIfEmpty();
    }

    public void setEmptyView(View emptyView, View refreshLayoutView) {
        this.refreshLayoutView = refreshLayoutView;
        setEmptyView(emptyView);
    }

    public void setEmptyView(View emptyView) {
        this.emptyView = emptyView;
        checkIfEmpty();
    }

    public Boolean isEmptyViewVisible() {
        if (emptyView == null) return null;
        return emptyView.getVisibility() == VISIBLE;
    }

    /**
     * This function checks data empty in recycler-view.
     */
    void checkIfEmpty() {
        if (emptyView != null && getAdapter() != null) {
            final boolean emptyViewVisible = getAdapter().getItemCount() == 0;
            emptyView.setVisibility(emptyViewVisible ? VISIBLE : GONE);
            if (refreshLayoutView != null) {
                refreshLayoutView.setVisibility(emptyViewVisible ? GONE : VISIBLE);
            }
            setVisibility(emptyViewVisible ? GONE : VISIBLE);
            setDataInEmptyView();
        }
    }

    public void setEmptyData(String emptyTitle) {
        this.emptyTitle = emptyTitle;
        if (emptyView != null && emptyView.getVisibility() == VISIBLE) {
            setDataInEmptyView();
        }
    }

    private void setDataInEmptyView() {
        TextView tvTitle = emptyView.findViewById(R.id.tvEmptyTitle);
        tvTitle.setVisibility(TextUtils.isEmpty(emptyTitle) ? GONE : VISIBLE);
        tvTitle.setText(emptyTitle);
    }
}
