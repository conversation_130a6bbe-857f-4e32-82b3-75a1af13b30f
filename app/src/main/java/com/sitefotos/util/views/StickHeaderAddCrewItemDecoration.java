package com.sitefotos.util.views;

import android.graphics.Canvas;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.appinterface.OnStickyHeaderCrewListener;
import com.sitefotos.models.CrewSelectionHeader;

public class StickHeaderAddCrewItemDecoration extends RecyclerView.ItemDecoration {

    private StickyHeaderInterface mListener;
    private int mStickyHeaderHeight;
    private OnStickyHeaderCrewListener mOnStickyHeaderListener;
    private int currentHeaderPos;
    private boolean isHeaderClicked = false;
    private float lastX = 0.0f;
    private ImageView ivArrow;
    private RelativeLayout rlRootLayout;
    private AppCompatTextView tvHeaderTitle;

    public StickHeaderAddCrewItemDecoration(RecyclerView recyclerView, @NonNull StickyHeaderInterface listener, OnStickyHeaderCrewListener onStickyHeaderListener) {
        mListener = listener;
        mOnStickyHeaderListener = onStickyHeaderListener;
        // On Sticky Header Click
        recyclerView.addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            public boolean onInterceptTouchEvent(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent) {
                if (motionEvent.getY() <= mStickyHeaderHeight && motionEvent.getAction() == MotionEvent.ACTION_UP && motionEvent.getX() >= recyclerView.getWidth() - (float) recyclerView.getWidth() / 4) {
                    if (lastX != motionEvent.getX()) {
                        isHeaderClicked = false;
                    }
                    lastX = motionEvent.getX();
                    if (!isHeaderClicked) {
                        isHeaderClicked = true;

                    }
                    return true;
                }

                if (motionEvent.getY() <= mStickyHeaderHeight && motionEvent.getAction() == MotionEvent.ACTION_UP) {
                    if (lastX != motionEvent.getX()) {
                        isHeaderClicked = false;
                    }
                    lastX = motionEvent.getX();
                    if (!isHeaderClicked) {
                        isHeaderClicked = true;
                        if (rlRootLayout.getTag() != null) {
                            CrewSelectionHeader data = (CrewSelectionHeader) rlRootLayout.getTag();
                            if (data.getHeaderId() == 1) {
                                data.setSelected(!data.isSelected());
                                rlRootLayout.setTag(data);
                                mOnStickyHeaderListener.OnStickyHeaderArrowClicked(currentHeaderPos,ivArrow, data.isSelected());
                            }
                        }
                    }
                    return true;
                }
                isHeaderClicked = false;
                return false;
            }

            public void onTouchEvent(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent) {
            }

            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
    }

    @Override
    public void onDrawOver(@NonNull Canvas canvas, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDrawOver(canvas, parent, state);
        if (parent.getLayoutManager() == null)
            return;
        View topChild = parent.getLayoutManager().getChildAt(0);
        if (topChild == null) {
            return;
        }
        int topChildPosition = parent.getChildAdapterPosition(topChild);
        if (topChildPosition == RecyclerView.NO_POSITION) {
            return;
        }
        // int headerPos = mListener.getHeaderPositionForItem(topChildPosition);

        // If no header found then return.
        // For that headerPos will be -1.
        //if (headerPos < 0)
        //    return;

        if (parent.getAdapter() != null && parent.getAdapter().getItemCount() == 0)
            return;
        View currentHeader = getHeaderViewForItem(topChildPosition, parent);

        fixLayoutSize(parent, currentHeader);
        int contactPoint = currentHeader.getBottom();
        View childInContact = getChildInContact(parent, contactPoint, mListener.getHeaderPositionForItem(topChildPosition));

        if (childInContact != null) {
            if (mListener.isHeader(parent.getChildAdapterPosition(childInContact))) {
                moveHeader(canvas, currentHeader, childInContact);
                return;
            }
            drawHeader(canvas, currentHeader);
        }
    }

    private View getHeaderViewForItem(int itemPosition, RecyclerView parent) {
        int headerPosition = mListener.getHeaderPositionForItem(itemPosition);
        int layoutResId = mListener.getHeaderLayout(headerPosition);
        View header = LayoutInflater.from(parent.getContext()).inflate(layoutResId, parent, false);
        mListener.bindHeaderData(header, headerPosition);
        return header;
    }


    private void drawHeader(Canvas canvas, View currentHeader) {
        canvas.save();
        canvas.translate(0, 0);
        currentHeader.draw(canvas);
        canvas.restore();
    }

    private void moveHeader(Canvas canvas, View currentHeader, View nextHeader) {
        canvas.save();
        canvas.translate(0, nextHeader.getTop() - currentHeader.getHeight());
        currentHeader.draw(canvas);
        canvas.restore();

    }

    private View getChildInContact(RecyclerView parent, int contactPoint, int currentHeaderPos) {
        this.currentHeaderPos = currentHeaderPos;
        View childInContact = null;
        for (int i = 0; i < parent.getChildCount(); i++) {
            int heightTolerance = 0;
            View child = parent.getChildAt(i);

            //measure height tolerance with child if child is another header
            if (currentHeaderPos != i) {
                boolean isChildHeader = mListener.isHeader(parent.getChildAdapterPosition(child));
                if (isChildHeader) {
                    heightTolerance = mStickyHeaderHeight - child.getHeight();
                }
            }

            //add heightTolerance if child top be in display area
            int childBottomPosition;
            if (child.getTop() > 0) {
                childBottomPosition = child.getBottom() + heightTolerance;
            } else {
                childBottomPosition = child.getBottom();
            }

            if (childBottomPosition > contactPoint) {
                if (child.getTop() <= contactPoint) {
                    // This child overlaps the contactPoint
                    childInContact = child;
                    break;
                }
            }
        }
        return childInContact;
    }


    /**
     * Properly measures and layouts the top sticky header.
     *
     * @param parent ViewGroup: RecyclerView in this case.
     */
    private void fixLayoutSize(ViewGroup parent, View view) {

        // Specs for parent (RecyclerView)
        int widthSpec = View.MeasureSpec.makeMeasureSpec(parent.getWidth(), View.MeasureSpec.EXACTLY);
        int heightSpec = View.MeasureSpec.makeMeasureSpec(parent.getHeight(), View.MeasureSpec.UNSPECIFIED);

        // Specs for children (headers)
        int childWidthSpec = ViewGroup.getChildMeasureSpec(widthSpec, parent.getPaddingLeft() + parent.getPaddingRight(), view.getLayoutParams().width);
        int childHeightSpec = ViewGroup.getChildMeasureSpec(heightSpec, parent.getPaddingTop() + parent.getPaddingBottom(), view.getLayoutParams().height);

        view.measure(childWidthSpec, childHeightSpec);
        rlRootLayout = view.findViewById(R.id.rlMainLayout);
        ivArrow = view.findViewById(R.id.ivArrow);
        tvHeaderTitle = view.findViewById(R.id.tvHeaderTitle);
        view.layout(0, 0, view.getMeasuredWidth(), mStickyHeaderHeight = view.getMeasuredHeight());
    }

    public interface StickyHeaderInterface {

        /**
         * This method gets called by {@link StickHeaderAddCrewItemDecoration} to fetch the position of the header item in the adapter
         * that is used for (represents) item at specified position.
         *
         * @param itemPosition int. Adapter's position of the item for which to do the search of the position of the header item.
         * @return int. Position of the header item in the adapter.
         */
        int getHeaderPositionForItem(int itemPosition);

        /**
         * This method gets called by {@link StickHeaderAddCrewItemDecoration} to get layout resource id for the header item at specified adapter's position.
         *
         * @param headerPosition int. Position of the header item in the adapter.
         * @return int. Layout resource id.
         */
        int getHeaderLayout(int headerPosition);

        /**
         * This method gets called by {@link StickHeaderAddCrewItemDecoration} to setup the header View.
         *
         * @param header         View. Header to set the data on.
         * @param headerPosition int. Position of the header item in the adapter.
         */
        void bindHeaderData(View header, int headerPosition);

        /**
         * This method gets called by {@link StickHeaderAddCrewItemDecoration} to verify whether the item represents a header.
         *
         * @param itemPosition int.
         * @return true, if item at the specified adapter's position represents a header.
         */
        boolean isHeader(int itemPosition);
    }

}