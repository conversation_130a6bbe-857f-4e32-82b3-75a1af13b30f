package com.sitefotos.util.views;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.View;

import com.google.gson.Gson;
import com.sitefotos.R;
import com.sitefotos.form.SignatureActivity;
import com.sitefotos.form.SketchActivity;
import com.sitefotos.models.DrawingData;
import com.sitefotos.models.DrawingPoints;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;

public class DrawingView extends View implements View.OnTouchListener {
    private Path mPath;
    private Paint mPaint;

    private boolean isDrawn;
    private boolean isSketch;
    private float mX, mY;
    private static final float TOUCH_TOLERANCE = 4;
    private Context context;
    private Paint circlePaint;
    private Path circlePath;
    public ArrayList<Pair<Path, Paint>> paths = new ArrayList<>();
    private ArrayList<Path> undonePaths = new ArrayList<>();
    private List<DrawingData> lstDrawingData = new ArrayList<>();
    private List<DrawingPoints> points = new ArrayList<>();


    public DrawingView(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.context = context;
        setupDrawing();
    }

    private void setupDrawing() {
        // get drawing area setup for interaction

        setFocusable(true);
        setFocusableInTouchMode(true);
        setOnTouchListener(this);
        setupPaint();
        mPath = new Path();
        setupPaintForCircle();


    }

    private Paint setupPaint() {
       /* if(mPaint != null)
            return mPaint;*/
        mPaint = new Paint();
        mPaint.setAntiAlias(true);
        mPaint.setDither(true);
        mPaint.setColor(getResources().getColor(R.color.blue_ink_color));
        mPaint.setStyle(Paint.Style.STROKE);
        mPaint.setStrokeJoin(Paint.Join.ROUND);
        mPaint.setStrokeCap(Paint.Cap.ROUND);
        mPaint.setStrokeWidth(14);

        return mPaint;
    }

    private void setupPaintForCircle() {
        circlePaint = new Paint();
        circlePath = new Path();
        circlePaint.setAntiAlias(true);
        circlePaint.setColor(Color.BLUE);
        circlePaint.setStyle(Paint.Style.STROKE);
        circlePaint.setStrokeJoin(Paint.Join.MITER);
        circlePaint.setStrokeWidth(4f);

    }

    @Override
    protected void onDraw(Canvas canvas) {
        //super.onDraw(canvas);
        for (Pair<Path, Paint> p : paths) {
            canvas.drawPath(p.first, p.second);
        }
        canvas.drawPath(mPath, mPaint);
        canvas.drawPath(circlePath, circlePaint);
    }

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        float x = event.getX();
        float y = event.getY();

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                touch_start(x, y);
                invalidate();
                break;
            case MotionEvent.ACTION_MOVE:
                touch_move(x, y);
                invalidate();
                break;
            case MotionEvent.ACTION_UP:
                touch_up();
                invalidate();
                break;
        }
        return true;
    }

    private void touch_start(float x, float y) {
        undonePaths.clear();
        mPath.reset();
        mPath.moveTo(x, y);
        mX = x;
        mY = y;
    }

    public void setPencilColor(int color) {
        mPaint.setColor(color);
    }

    private void touch_move(float x, float y) {
        float dx = Math.abs(x - mX);
        float dy = Math.abs(y - mY);
        if (dx >= TOUCH_TOLERANCE || dy >= TOUCH_TOLERANCE) {
            mPath.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2);

            points.add(new DrawingPoints(mX, mY, (x + mX) / 2, (y + mY) / 2));
            mX = x;
            mY = y;
            circlePath.reset();
            circlePath.addCircle(mX, mY, 30, Path.Direction.CW);
        }
    }

    private void touch_up() {
        mPath.lineTo(mX, mY);
        circlePath.reset();
        // commit the path to our offscreen
        // kill this so we don't double draw
        Paint newPaint = new Paint(mPaint); // Clones the mPaint object
        paths.add(new Pair<>(mPath, newPaint));
        DrawingData drawingData = new DrawingData();
        List<DrawingPoints> lstDrawingPoints = new ArrayList<>();
        lstDrawingPoints.addAll(points);
        drawingData.setDrawingPoints(lstDrawingPoints);
        drawingData.setColor(mPaint.getColor());
        lstDrawingData.add(drawingData);
        points.clear();
        if (isSketch) {
            ((SketchActivity) context).binding.ivUndo.setBackgroundColor(mPaint.getColor());
        }

        if (paths.size() > 0) {
            if (!isSketch) {
                ((SignatureActivity) context).binding.ivUndo.setVisibility(VISIBLE);
            } else {
                ((SketchActivity) context).binding.ivUndo.setVisibility(VISIBLE);

            }
            isDrawn = true;
        }
        mPath = new Path();

    }


    public void setSketch(boolean isSketch) {
        this.isSketch = isSketch;
    }

    public void undo() {

        if (paths.size() > 0) {
            paths.remove(paths.size() - 1);
            lstDrawingData.remove(lstDrawingData.size() - 1);
            if (isSketch) {
                if (paths.size() > 0)
                    ((SketchActivity) context).binding.ivUndo.setBackgroundColor(paths.get(paths.size() - 1).second.getColor());
                else {
                    ((SketchActivity) context).binding.ivUndo.setBackgroundColor(Color.TRANSPARENT);
                }
            }
            invalidate();
            isDrawn = paths.size() > 0;
        } else {
            if (!isSketch) {
                ((SignatureActivity) context).binding.ivUndo.setVisibility(GONE);
            } else {
                ((SketchActivity) context).binding.ivUndo.setVisibility(GONE);
            }
            isDrawn = false;
        }
    }

/*
    public void redo() {
        if (undonePaths.size() > 0) {
            paths.add(undonePaths.remove(undonePaths.size() - 1));
            // paths.add(undonePaths.remove(undonePaths.size() - 1));
            invalidate();
        }
    }*/

    public boolean isDrawnAnything() {
        return isDrawn;
    }

    public void SetIsDrawnAnything(boolean drawn) {
        isDrawn = drawn;
    }

    public ArrayList<Pair<Path, Paint>> getPaths() {
        return paths;
    }

    /*
    Method to get all list data in string format to store it in sharePreference
     */
    public String getDrawingData() {

        return new Gson().toJson(lstDrawingData);
    }

    public interface DrawComplete {
        void onDrawComplete();
    }

    DrawComplete drawComplete;

    public void setDrawView(DrawComplete drawComplete) {
        this.drawComplete = drawComplete;
    }

    /**
     * Method to get all data from string to list and make it in path and paint format.
     */
    public void setAllDataInAList(String rawData) {

        if (!TextUtils.isEmpty(rawData)) {

            lstDrawingData = StaticUtils.getData(DrawingData.class, rawData);
            for (DrawingData drawingData : lstDrawingData) {
                Path mPath = new Path();
                for (int i = 0; i < drawingData.getLstDrawingPoints().size(); i++) {
                    DrawingPoints drawingPoints = drawingData.getLstDrawingPoints().get(i);
                    if (i == 0) {
                        mPath.moveTo(drawingPoints.getX(), drawingPoints.getY());
                    }
                    mPath.quadTo(drawingPoints.getX(), drawingPoints.getY(), drawingPoints.getX1(), drawingPoints.getY1());
                    if (i == drawingData.getLstDrawingPoints().size() - 1) {
                        mPath.lineTo(drawingPoints.getX(), drawingPoints.getY());
                    }
                }
                Paint paint = setupPaint();
                paint.setColor(drawingData.getColor());
                Paint newPaint = new Paint(paint);
                paths.add(new Pair<>(mPath, newPaint));
                isDrawn = true;
            }
        }
    }

    public void clearDrawingData(){
        try {
            paths.clear();
            lstDrawingData.clear();
            invalidate();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }

    }

}
