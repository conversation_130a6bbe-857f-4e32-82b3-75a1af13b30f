package com.sitefotos.util.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.widget.LinearLayout;

public class CustomLinearLayout extends LinearLayout {

    private boolean isTouchDisable = false;

    public CustomLinearLayout(Context context) {
        super(context);
    }

    public CustomLinearLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public CustomLinearLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (isTouchDisable) {
            return true;
        }
        return super.onInterceptTouchEvent(ev);
    }

    public void setTouchDisable(boolean isTouchDisable) {
        this.isTouchDisable = isTouchDisable;
    }

}

