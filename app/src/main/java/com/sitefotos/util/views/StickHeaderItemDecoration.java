package com.sitefotos.util.views;

import android.graphics.Canvas;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.appinterface.OnStickyHeaderListener;
import com.sitefotos.models.ClockHeader;

public class StickHeaderItemDecoration extends RecyclerView.ItemDecoration {

    private StickyHeaderInterface mListener;
    private int mStickyHeaderHeight;
    private OnStickyHeaderListener mOnStickyHeaderListener;
    private int currentHeaderPos;
    private boolean isHeaderClicked = false;
    private float lastX = 0.0f;
    private String headerButtonName = "";
    private AppCompatTextView btnBreakResume;
    private ImageView ivHeaderCrewSelect;

    public StickHeaderItemDecoration(RecyclerView recyclerView, @NonNull StickyHeaderInterface listener, OnStickyHeaderListener onStickyHeaderListener) {
        mListener = listener;
        mOnStickyHeaderListener = onStickyHeaderListener;
        // On Sticky Header Click
        recyclerView.addOnItemTouchListener(new RecyclerView.OnItemTouchListener() {
            public boolean onInterceptTouchEvent(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent) {
                if (motionEvent.getY() <= mStickyHeaderHeight && motionEvent.getAction() == MotionEvent.ACTION_UP && motionEvent.getX() >= recyclerView.getWidth() - (float) recyclerView.getWidth() / 4) {
                    if (lastX != motionEvent.getX()) {
                        isHeaderClicked = false;
                    }
                    lastX = motionEvent.getX();
                    if (!isHeaderClicked) {
                        isHeaderClicked = true;
                        mOnStickyHeaderListener.OnStickyHeaderClicked(btnBreakResume, headerButtonName);
                    }
                    return true;
                }

                if (motionEvent.getY() <= mStickyHeaderHeight && motionEvent.getAction() == MotionEvent.ACTION_UP && motionEvent.getX() <= ivHeaderCrewSelect.getX() + ivHeaderCrewSelect.getWidth()) {
                    if (lastX != motionEvent.getX()) {
                        isHeaderClicked = false;
                    }
                    lastX = motionEvent.getX();
                    if (!isHeaderClicked) {
                        isHeaderClicked = true;
                        if (ivHeaderCrewSelect.getTag() != null) {
                            ClockHeader data = (ClockHeader) ivHeaderCrewSelect.getTag();
                            data.setAllSelect(!data.isAllSelect());
                            ivHeaderCrewSelect.setTag(data);
                            if (data.isAllSelect()) {
                                ivHeaderCrewSelect.setImageResource(R.drawable.ic_checkbox_selected);
                            } else {
                                ivHeaderCrewSelect.setImageResource(R.drawable.ic_checkbox_unselected);
                            }
                            mOnStickyHeaderListener.OnStickyHeaderCheckBoxClicked(currentHeaderPos, headerButtonName, data.isAllSelect());
                        }
                    }
                    return true;
                }
                isHeaderClicked = false;
                return false;
            }

            public void onTouchEvent(@NonNull RecyclerView recyclerView, @NonNull MotionEvent motionEvent) {
            }

            public void onRequestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            }
        });
    }

    @Override
    public void onDrawOver(@NonNull Canvas canvas, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDrawOver(canvas, parent, state);
        if (parent.getLayoutManager() == null)
            return;
        View topChild = parent.getLayoutManager().getChildAt(0);
        if (topChild == null) {
            return;
        }
        int topChildPosition = parent.getChildAdapterPosition(topChild);
        if (topChildPosition == RecyclerView.NO_POSITION) {
            return;
        }
        // int headerPos = mListener.getHeaderPositionForItem(topChildPosition);

        // If no header found then return.
        // For that headerPos will be -1.
        //if (headerPos < 0)
        //    return;

        if (parent.getAdapter() != null && parent.getAdapter().getItemCount() == 0)
            return;
        View currentHeader = getHeaderViewForItem(topChildPosition, parent);

        fixLayoutSize(parent, currentHeader);
        int contactPoint = currentHeader.getBottom();
        View childInContact = getChildInContact(parent, contactPoint, mListener.getHeaderPositionForItem(topChildPosition));

        if (childInContact != null) {
            if (mListener.isHeader(parent.getChildAdapterPosition(childInContact))) {
                moveHeader(canvas, currentHeader, childInContact);
                return;
            }
            drawHeader(canvas, currentHeader);
        }
    }

    private View getHeaderViewForItem(int itemPosition, RecyclerView parent) {
        int headerPosition = mListener.getHeaderPositionForItem(itemPosition);
        int layoutResId = mListener.getHeaderLayout(headerPosition);
        View header = LayoutInflater.from(parent.getContext()).inflate(layoutResId, parent, false);
        mListener.bindHeaderData(header, headerPosition);
        return header;
    }


    private void drawHeader(Canvas canvas, View currentHeader) {
        canvas.save();
        canvas.translate(0, 0);
        currentHeader.draw(canvas);
        canvas.restore();
    }

    private void moveHeader(Canvas canvas, View currentHeader, View nextHeader) {
        canvas.save();
        canvas.translate(0, nextHeader.getTop() - currentHeader.getHeight());
        currentHeader.draw(canvas);
        canvas.restore();

    }

    private View getChildInContact(RecyclerView parent, int contactPoint, int currentHeaderPos) {
        this.currentHeaderPos = currentHeaderPos;
        View childInContact = null;
        for (int i = 0; i < parent.getChildCount(); i++) {
            int heightTolerance = 0;
            View child = parent.getChildAt(i);

            //measure height tolerance with child if child is another header
            if (currentHeaderPos != i) {
                boolean isChildHeader = mListener.isHeader(parent.getChildAdapterPosition(child));
                if (isChildHeader) {
                    heightTolerance = mStickyHeaderHeight - child.getHeight();
                }
            }

            //add heightTolerance if child top be in display area
            int childBottomPosition;
            if (child.getTop() > 0) {
                childBottomPosition = child.getBottom() + heightTolerance;
            } else {
                childBottomPosition = child.getBottom();
            }

            if (childBottomPosition > contactPoint) {
                if (child.getTop() <= contactPoint) {
                    // This child overlaps the contactPoint
                    childInContact = child;
                    break;
                }
            }
        }
        return childInContact;
    }


    /**
     * Properly measures and layouts the top sticky header.
     *
     * @param parent ViewGroup: RecyclerView in this case.
     */
    private void fixLayoutSize(ViewGroup parent, View view) {

        // Specs for parent (RecyclerView)
        int widthSpec = View.MeasureSpec.makeMeasureSpec(parent.getWidth(), View.MeasureSpec.EXACTLY);
        int heightSpec = View.MeasureSpec.makeMeasureSpec(parent.getHeight(), View.MeasureSpec.UNSPECIFIED);

        // Specs for children (headers)
        int childWidthSpec = ViewGroup.getChildMeasureSpec(widthSpec, parent.getPaddingLeft() + parent.getPaddingRight(), view.getLayoutParams().width);
        int childHeightSpec = ViewGroup.getChildMeasureSpec(heightSpec, parent.getPaddingTop() + parent.getPaddingBottom(), view.getLayoutParams().height);

        view.measure(childWidthSpec, childHeightSpec);
        btnBreakResume = view.findViewById(R.id.btnBreakResume);
        ivHeaderCrewSelect = view.findViewById(R.id.ivHeaderCrewSelect);

        if (btnBreakResume != null) {
            headerButtonName = btnBreakResume.getText().toString();
        }
        view.layout(0, 0, view.getMeasuredWidth(), mStickyHeaderHeight = view.getMeasuredHeight());
    }

    public interface StickyHeaderInterface {

        /**
         * This method gets called by {@link StickHeaderItemDecoration} to fetch the position of the header item in the adapter
         * that is used for (represents) item at specified position.
         *
         * @param itemPosition int. Adapter's position of the item for which to do the search of the position of the header item.
         * @return int. Position of the header item in the adapter.
         */
        int getHeaderPositionForItem(int itemPosition);

        /**
         * This method gets called by {@link StickHeaderItemDecoration} to get layout resource id for the header item at specified adapter's position.
         *
         * @param headerPosition int. Position of the header item in the adapter.
         * @return int. Layout resource id.
         */
        int getHeaderLayout(int headerPosition);

        /**
         * This method gets called by {@link StickHeaderItemDecoration} to setup the header View.
         *
         * @param header         View. Header to set the data on.
         * @param headerPosition int. Position of the header item in the adapter.
         */
        void bindHeaderData(View header, int headerPosition);

        /**
         * This method gets called by {@link StickHeaderItemDecoration} to verify whether the item represents a header.
         *
         * @param itemPosition int.
         * @return true, if item at the specified adapter's position represents a header.
         */
        boolean isHeader(int itemPosition);
    }

}