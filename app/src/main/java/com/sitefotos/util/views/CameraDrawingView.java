package com.sitefotos.util.views;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.Pair;
import android.view.MotionEvent;
import android.view.View;

import com.sitefotos.Constants;
import com.sitefotos.camera.CameraActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;

public class CameraDrawingView extends View implements View.OnTouchListener {
    public int width;
    public int height;
    private Bitmap mBitmap;
    private Canvas mCanvas;
    Context context;
    private Paint circlePaint;
    private Path circlePath;
    private float mX, mY;
    private static final float TOUCH_TOLERANCE = 4;
    public Paint mPaintEditDrawing;
    private Path mPathEditDrawing;
    public int strokePaintDrawing = 6;
    public ArrayList<Pair<Path, Paint>> pathsStackDrawing = new ArrayList<>();

    // Drawing bitmap used for upload
    public Bitmap bitmapDrawing;

    public CameraDrawingView(Context c) {
        super(c);
        context = c;
        setOnTouchListener(this);
        setPaintDrawingView();
        mPathEditDrawing = new Path();
        setupPaintForCircle();

    }


    public void setPaintDrawingView() {
        mPaintEditDrawing = new Paint();
        mPaintEditDrawing.setAntiAlias(true);
        mPaintEditDrawing.setDither(true);
        mPaintEditDrawing.setColor(AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR));
        mPaintEditDrawing.setStyle(Paint.Style.STROKE);
        mPaintEditDrawing.setStrokeJoin(Paint.Join.ROUND);
        mPaintEditDrawing.setStrokeCap(Paint.Cap.ROUND);
        mPaintEditDrawing.setStrokeWidth(StaticUtils.convertDpToPixels(strokePaintDrawing));
    }

    private Paint setupPaintForCircle() {
        circlePaint = new Paint();
        circlePath = new Path();
        circlePaint.setAntiAlias(true);
        circlePaint.setColor(Color.BLUE);
        circlePaint.setStyle(Paint.Style.STROKE);
        circlePaint.setStrokeJoin(Paint.Join.MITER);
        circlePaint.setStrokeWidth(4f);

        return circlePaint;
    }

    public Bitmap getDrawingBitmap() {
        return bitmapDrawing;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);

        width = w;
        height = h;

        mBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        bitmapDrawing = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
        mCanvas = new Canvas(mBitmap);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        for (Pair<Path, Paint> p : pathsStackDrawing) {
            canvas.drawPath(p.first, p.second);
        }

        canvas.drawPath(mPathEditDrawing, mPaintEditDrawing);
        canvas.drawPath(circlePath, circlePaint);
    }


    private void touch_start(float x, float y) {
        mPathEditDrawing.reset();
        mPathEditDrawing.moveTo(x, y);
        mX = x;
        mY = y;
    }

    private void touch_move(float x, float y) {
        float dx = Math.abs(x - mX);
        float dy = Math.abs(y - mY);
        if (dx >= TOUCH_TOLERANCE || dy >= TOUCH_TOLERANCE) {
            mPathEditDrawing.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2);
            mX = x;
            mY = y;

            circlePath.reset();
            circlePath.addCircle(mX, mY, 30, Path.Direction.CW);
        }
    }

    private void touch_up() {
        mPathEditDrawing.lineTo(mX, mY);
        circlePath.reset();
        // commit the path to our offscreen
        mCanvas.drawPath(mPathEditDrawing, mPaintEditDrawing);
        Paint newPaint = new Paint(mPaintEditDrawing); // Clones the mPaint object
        pathsStackDrawing.add(new Pair<>(mPathEditDrawing, newPaint));
        if (context instanceof MainActivity) {
            ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnDrawingUndo.setBackgroundColor(newPaint.getColor());
        } else {
            ((CameraActivity) context).binding.cameraDraw.imgBtnDrawingUndo.setBackgroundColor(newPaint.getColor());
        }
        mPathEditDrawing = new Path();

        if (context instanceof MainActivity) {
            if (pathsStackDrawing.size() > 0 && ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnDrawingUndo.getVisibility() != VISIBLE) {
                ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnDrawingUndo.setVisibility(VISIBLE);
            }
        } else {
            if (pathsStackDrawing.size() > 0 && ((CameraActivity) context).binding.cameraDraw.imgBtnDrawingUndo.getVisibility() != VISIBLE) {
                ((CameraActivity) context).binding.cameraDraw.imgBtnDrawingUndo.setVisibility(VISIBLE);
            }
        }
    }

    public void undo() {

        if (pathsStackDrawing.size() > 0) {
            pathsStackDrawing.remove(pathsStackDrawing.size() - 1);
            invalidate();

            bitmapDrawing = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas c = new Canvas(bitmapDrawing);
            layout(getLeft(), getTop(), getRight(), getBottom());
            draw(c);

            invalidate();

            if (pathsStackDrawing.size() > 0) {
                int colorPreviousPaint = pathsStackDrawing.get(pathsStackDrawing.size() - 1).second.getColor();
                AppPrefShared.putValue(Constants.DRAWING_PAINT_STROKE_COLOR, colorPreviousPaint);

                if (context instanceof MainActivity) {
                    ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(colorPreviousPaint);
                    ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnDrawingUndo.setBackgroundColor(colorPreviousPaint);
                } else {
                    ((CameraActivity) context).binding.cameraDraw.imgBtnEditDrawingToggle.setBackgroundColor(colorPreviousPaint);
                    ((CameraActivity) context).binding.cameraDraw.imgBtnDrawingUndo.setBackgroundColor(colorPreviousPaint);
                }

                mPaintEditDrawing.setColor(colorPreviousPaint);
            } else {
                if (context instanceof MainActivity)
                    ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.GONE);
                else
                    ((CameraActivity) context).binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.GONE);
            }
        } else {
            invalidate();

            bitmapDrawing = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            Canvas c = new Canvas(bitmapDrawing);
            layout(getLeft(), getTop(), getRight(), getBottom());
            draw(c);

            invalidate();

            if (context instanceof MainActivity)
                ((MainActivity) context).cameraFragment.binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.GONE);
            else
                ((CameraActivity) context).binding.cameraDraw.imgBtnDrawingUndo.setVisibility(View.GONE);
        }


    }


    @Override
    public boolean onTouch(View v, MotionEvent event) {

        boolean isDrawingMode;
        if (context instanceof MainActivity)
            isDrawingMode = ((MainActivity) context).cameraFragment.isEditModeDrawing;
        else
            isDrawingMode = ((CameraActivity) context).isEditModeDrawing;
        if (isDrawingMode) {
            float x = event.getX();
            float y = event.getY();

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    touch_start(x, y);
                    invalidate();
                    break;
                case MotionEvent.ACTION_MOVE:
                    touch_move(x, y);
                    invalidate();
                    break;
                case MotionEvent.ACTION_UP:
                    touch_up();
                    invalidate();

                    bitmapDrawing = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
                    Canvas c = new Canvas(bitmapDrawing);
                    layout(getLeft(), getTop(), getRight(), getBottom());
                    draw(c);
                    invalidate();
                    break;
            }
        }
        return true;
    }
}
