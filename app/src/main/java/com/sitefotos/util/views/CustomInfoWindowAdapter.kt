package com.sitefotos.util.views

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import com.google.android.gms.maps.GoogleMap
import com.google.android.gms.maps.model.Marker
import com.sitefotos.R
import com.sitefotos.util.ImageUtil

class CustomInfoWindowAdapter(private val context: Context) :
    GoogleMap.InfoWindowAdapter {

    var mWindow: View =
        LayoutInflater.from(context).inflate(R.layout.item_custom_info_window_plottomap, null)

    override fun getInfoContents(marker: Marker): View {
        setInfoWindowText(marker)
        return mWindow
    }

    override fun getInfoWindow(marker: Marker): View {
        setInfoWindowText(marker)
        return mWindow
    }

    private fun setInfoWindowText(marker: Marker) {
        val ivMarkerDetails = mWindow.findViewById<ImageView>(R.id.ivMarkerDetails)
        ImageUtil.getBitmapFromPath(marker.title)?.let {
            ivMarkerDetails.setImageBitmap(it)
        }
    }
}