package com.sitefotos.util;

import android.content.Context;
import android.location.Location;
import android.text.TextUtils;

import com.sitefotos.Constants;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.ComparatorSiteData;
import com.sitefotos.models.Routes;
import com.sitefotos.models.SiteData;
import com.sitefotos.polygon.Point;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblProperties;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;

public class PropertyUtils {

    public static Location getPropertyLocation(PropertiesVo propertiesVo) {

        String property_geo = propertiesVo.getPropertyGeo();
        String[] separated = property_geo.split(":");
        if (separated.length <= 2)
            return null;
        String values = separated[2].replaceAll(";", "").trim();
        String[] separatedArray = values.split("\\),\\(");
        ArrayList<Point> arrayLatLng = getItemFromGeoProperty(separatedArray);
        double latMax, latMin, longMax, longMin;
        if (!arrayLatLng.isEmpty()) {
            latMax = arrayLatLng.get(0).mLatitude;
            latMin = arrayLatLng.get(0).mLatitude;
            longMax = arrayLatLng.get(0).mLongitude;
            longMin = arrayLatLng.get(0).mLongitude;
            for (Point point : arrayLatLng) {
                latMax = Math.max(point.mLatitude, latMax);
                latMin = Math.min(point.mLatitude, latMin);
                longMax = Math.max(point.mLongitude, longMax);
                longMin = Math.min(point.mLongitude, longMin);
            }
        } else {
            return null;
        }

        Location propertyLocation = new Location("Sitefotos1");

        propertyLocation.setLatitude((latMax + latMin) / 2);
        propertyLocation.setLongitude((longMax + longMin) / 2);

        return propertyLocation;
    }

    /**
     * Function to get lat and long pair with comma separated from geo property as a string.
     *
     * @param separatedArray string array of property geo array
     * @return return list of point of lat and long
     */
    public static ArrayList<Point> getItemFromGeoProperty(String[] separatedArray) {
        ArrayList<Point> arrayLatLng = new ArrayList<>();
        for (String item : separatedArray) {
            String values1 = item.replaceAll("\\(", "").replaceAll("\\)", "").replaceAll("\"", "").trim();
            String[] latLongSpilt = values1.split(",");
            try {
                if (latLongSpilt.length > 1) {
                    if (!TextUtils.isEmpty(latLongSpilt[0]) && !TextUtils.isEmpty(latLongSpilt[1])) {
                        try {
                            arrayLatLng.add(new Point(Double.parseDouble(latLongSpilt[0]), Double.parseDouble(latLongSpilt[1])));
                        } catch (NumberFormatException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                }
            } catch (ArrayIndexOutOfBoundsException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return arrayLatLng;
    }


    public static List<SiteData> getClosestDistanceListFromRoute(Context context, List<SiteData> lstUnsortedSites, Routes routesData) {

        List<SiteData> lstSites = new ArrayList<>();
        double distanceLimit = 30.00;
        if (AppPrefShared.getFloat(Constants.PARAM_SITE_FILTER_DISTANCE, -1.0f) > -1.0f) {
            distanceLimit = AppPrefShared.getFloat(Constants.PARAM_SITE_FILTER_DISTANCE, -1.0f);
        }
        List<Long> lstCheckedSite = (context != null) ? getCheckInSitesData(context) : new ArrayList<>();

        for (int i = 0; i < lstUnsortedSites.size(); i++) {
            SiteData siteData = lstUnsortedSites.get(i);
            siteData.setDistance(PropertyUtils.getDistance(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(), MainActivity.currentLatitude, MainActivity.currentLongitude));
            //If shouldFilter is true then site should be skipped.
            boolean shouldFilter = routesData.isFilter() && siteData.getDistance() > distanceLimit;
            // Add in list if site is checked in and out side of distance limit.
            if (!shouldFilter || lstCheckedSite.contains(siteData.getSiteId())) {
                lstSites.add(siteData);
            }
        }
        //This is our current default value. just return data with distance. if isOrdered is true then we do not need to check Filter option
        if (routesData.isOrdered()) {
            return lstUnsortedSites;
        } else {
            Collections.sort(lstSites, new ComparatorSiteData());
        }
        return lstSites;
    }


    public static List<SiteData> getClosestDistanceList(Context context, List<SiteData> lstUnsortedSites) {
        List<SiteData> lstSites = new ArrayList<>();
        if (!AppPrefShared.getBoolean(Constants.PARAM_SITE_ORDERED, false)) {
            for (int i = 0; i < lstUnsortedSites.size(); i++) {
                SiteData siteData = lstUnsortedSites.get(i);
                siteData.setDistance(PropertyUtils.getDistance(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(),
                        MainActivity.currentLatitude, MainActivity.currentLongitude));
            }
            Collections.sort(lstUnsortedSites, new ComparatorSiteData());
        }

        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_SKIP_GEO, 0) > 0) {
            return lstUnsortedSites;
        }
        if (AppPrefShared.getBoolean(Constants.PARAM_ALL_SITES, false)) {
            return lstUnsortedSites;
        }

        double distanceLimit = 30.00;

        if (AppPrefShared.getFloat(Constants.PARAM_SITE_FILTER_DISTANCE, -1.0f) > -1.0f) {
            distanceLimit = AppPrefShared.getFloat(Constants.PARAM_SITE_FILTER_DISTANCE, -1.0f);
        }
        List<Long> lstCheckedSite = (context != null) ? getCheckInSitesData(context) : new ArrayList<>();

        for (int i = 0; i < lstUnsortedSites.size(); i++) {
            SiteData siteData = lstUnsortedSites.get(i);
            siteData.setDistance(PropertyUtils.getDistance(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(), MainActivity.currentLatitude, MainActivity.currentLongitude));
            // Add in list if site is checked in and out side of distance limit.
            if (siteData.getDistance() <= distanceLimit || lstCheckedSite.contains(siteData.getSiteId())) {
                lstSites.add(siteData);
            }
        }
        Collections.sort(lstSites, new ComparatorSiteData());
        return lstSites;
    }

    public static List<Long> getCheckInSitesData(Context context) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
        return tblCheckInMap.getAllSiteIds();
    }

    public static PropertiesVo checkValidDistanceForProperty(Context context, int propertyId, double currentLatitude, double currentLongitude) {

        PropertiesVo propertiesVo;
        TblProperties tblProperties = new TblProperties(context);
        propertiesVo = tblProperties.getPropertyDataFromId(propertyId);

        if (propertiesVo.getPropertyId() == 0) {
            return null;
        }
        Location location = getPropertyLocation(propertiesVo);
        if (location != null) {
            propertiesVo.setLat(location.getLatitude());
            propertiesVo.setLon(location.getLongitude());
        }

        float distance = getDistance(propertiesVo.getLat(), propertiesVo.getLon(), currentLatitude, currentLongitude);

        if (distance <= 0.9941939) {  // 0.9941939 = 1600 miters
            return propertiesVo;
        } else {
            return null;
        }

    }

    public static float getDistance(double propertyLatitude, double propertyLongitude, double currentLatitude, double currentLongitude) {
        float[] result = new float[1];
        Location.distanceBetween(propertyLatitude, propertyLongitude, currentLatitude, currentLongitude, result);
        return (float) (result[0] / 1609.3440057765);
    }

    public static String getDistanceInString(double propertyLatitude, double propertyLongitude, double currentLatitude, double currentLongitude) {
        float[] result = new float[1];
        Location.distanceBetween(propertyLatitude, propertyLongitude, currentLatitude, currentLongitude, result);
        return String.format(Locale.US, "%.1f", (result[0]));
    }

    public static float distanceToCurrentLocation(double oldLatitude, double oldLongitude) {
        //return true if location distance greater than distance.
        float[] result = new float[1];
        Location.distanceBetween(oldLatitude, oldLongitude, MainActivity.currentLatitude, MainActivity.currentLongitude, result);
        return result[0];
    }

    /**
     * function to check siteDistance from the user is equal or less site distance
     *
     * @param isCheckout         true for checkout form or false for check in
     * @param siteRadiusString   site radius in string
     * @param siteDistanceString user current distance from site location
     * @return true if user is inside site radius.
     */
    public static boolean userInsideSiteRadius(boolean isCheckout, String siteRadiusString, String siteDistanceString, boolean forceRadiusOnCheckout) {

        // possible if no any value set for the site.
        try {
            if (TextUtils.isEmpty(siteRadiusString))
                return true;

            //possible empty or null at any exception in calculation.
            if (TextUtils.isEmpty(siteDistanceString))
                return true;
            double siteRadius = 0;
            try {
                siteRadius = Double.parseDouble(siteRadiusString);
            } catch (NumberFormatException e) {
                FirebaseEventUtils.logException(e);
                return true;
            }
            double siteDistance = Double.parseDouble(siteDistanceString);
            //Allow to check in if value is 0.0 or 0
            if (siteRadius == 0.0) {
                return true;
            }
            // Allow user to checkout the form if shouldForceRadiusOnCheckout is false and site radius is greater than 0.
            if (siteRadius > 0 && isCheckout && !forceRadiusOnCheckout) {
                return true;
            }
            return siteDistance <= siteRadius;
        } catch (NumberFormatException e) {
            FirebaseEventUtils.logException(e);
            return true;
        }
    }
}
