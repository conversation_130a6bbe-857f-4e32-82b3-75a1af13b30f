package com.sitefotos.util;

import static com.sitefotos.util.PropertyUtils.getItemFromGeoProperty;

import android.content.Context;
import android.location.Location;
import android.text.TextUtils;
import android.widget.Toast;

import com.sitefotos.Constants;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.ComparatorClusterListVO;
import com.sitefotos.models.ComparatorPropertyListVO;
import com.sitefotos.polygon.Point;
import com.sitefotos.polygon.Polygon;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.util.logger.CustomLogKt;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.ConcurrentModificationException;
import java.util.List;


public class PolygonCalculation {

    private static PolygonCalculation polygonCalculation;
    public String buildingName = "";
    public String propertyName = "";
    public String buildingId = "0";
    private int loopCounter = 0;
    private ArrayList<PropertiesVo> lstCloseProperty = new ArrayList<>();

    public static PolygonCalculation getPolygonInstance() {

        if (polygonCalculation == null) {
            polygonCalculation = new PolygonCalculation();
        }
        return polygonCalculation;

    }

    public ArrayList<PropertiesVo> getFilteredPropertyListFromPolygon(Context context, boolean isFromGallery, Location locationExif,
                                                                      double latitude, double longitude, long distanceAccuracy,
                                                                      List<String> lstIgnoreBuildings, List<Cluster> lstClusters,
                                                                      List<PropertiesVo> lstProperties) {
        loopCounter = 0;
        boolean isTagFound = false;
        if (!lstCloseProperty.isEmpty()) {
            lstCloseProperty.clear();
            lstCloseProperty = new ArrayList<>();
            loopCounter = 0;
            isTagFound = false;
        }
        long accuracy = distanceAccuracy + 20;
        if (isFromGallery) {
            if (locationExif == null) {
                return lstCloseProperty;
            } else {
                latitude = locationExif.getLatitude();
                longitude = locationExif.getLongitude();
            }
        }
        double distInMeters;
        TblProperties tblProperties = new TblProperties(context);
        if (!lstClusters.isEmpty()) {
            getAndSetClusterDistance(lstClusters, latitude, longitude);
            Collections.sort(lstClusters, new ComparatorClusterListVO());
            int exitCounter = 500;
            for (int i = 0; i < lstClusters.size(); i++) {
                List<String> lstPropertyIds = lstClusters.get(i).getLstPropertyIds();
                //ArrayList<PropertiesVo> lstProductVO = tblProperties.getPropertyDataByIds(lstPropertyIds);
                if (isTagFound)
                    break;
                for (int j = 0; j < lstPropertyIds.size(); j++) {
                    if (exitCounter < 0) {
                        return lstCloseProperty;
                    }
                    exitCounter--;
                    String propertyId = lstPropertyIds.get(j);
                    if (!TextUtils.isEmpty(propertyId) && longitude != 0.0 && latitude != 0.0 && !lstIgnoreBuildings.contains(propertyId)) {
                        //PropertiesVo propertyObject = getPropertyDataById(lstProductVO,propertyId);
                        PropertiesVo propertyObject = tblProperties.getPropertyDataFromId(Integer.parseInt(propertyId));
                        boolean isLocationInside;
                        if (propertyObject.getPropertyId() > 0) {
                            isLocationInside = isLocationInside(propertyObject, latitude, longitude);
                            if (isLocationInside) {
                                buildingName = propertyObject.getPropertyName().trim();
                                propertyName = propertyObject.getPropertyName();
                                buildingId = String.valueOf(propertyObject.getPropertyId());
                                isTagFound = true;
                                break;
                            }
                            //Getting Distance from Center
                            Location location = getPropertyLocation(propertyObject);
                            if (location != null) {
                                double property_latitude = location.getLatitude();
                                double property_longitude = location.getLongitude();
                                distInMeters = Distance_from_center(latitude, longitude, property_latitude, property_longitude);
                                lstCloseProperty = checkClosestDistance(propertyObject, distInMeters, accuracy);
                            }
                            try {
                                Collections.sort(lstCloseProperty, new ComparatorPropertyListVO());
                            } catch (ConcurrentModificationException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }
                    }
                }
            }
        } else {
            for (int i = 0; i < lstProperties.size(); i++) {
                PropertiesVo propertiesVo = lstProperties.get(i);
                boolean isLocationInside = isLocationInside(propertiesVo, latitude, longitude);
                if (isLocationInside) {
                    buildingName = propertiesVo.getPropertyName().trim();
                    propertyName = propertiesVo.getPropertyName();
                    buildingId = String.valueOf(propertiesVo.getPropertyId());
                }
                try {
                    Location location = getPropertyLocation(propertiesVo);
                    if (location != null) {
                        double property_latitude = location.getLatitude();
                        double property_longitude = location.getLongitude();
                        distInMeters = Distance_from_center(latitude, longitude, property_latitude, property_longitude);
                        lstCloseProperty = checkClosestDistance(propertiesVo, distInMeters, accuracy);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                    buildingId = "0";
                    buildingName = "";
                    propertyName = "";
                }
            }
            // Sort the list of properties by distance in ASC Order.
            Collections.sort(lstCloseProperty, new ComparatorPropertyListVO());
        }
        return lstCloseProperty;

    }

    private static PropertiesVo getPropertyDataById(List<PropertiesVo> lstProperty, String propertyId){
        for ( PropertiesVo propertiesVo : lstProperty){
            if (propertiesVo.getPropertyId() == Long.parseLong(propertyId)){
                return propertiesVo;
            }
        }
        return null;
    }


    private static void getAndSetClusterDistance(List<Cluster> lstClusters, double latitude, double longitude) {
        for (int i = 0; i < lstClusters.size(); i++) {
            try {
                Cluster cluster = lstClusters.get(i);
                String centerLocation = cluster.getCentroid();
                double centerLat, centerLong, clusterDistance;
                cluster.setLstPropertyIds(Arrays.asList(cluster.getBuildings().split("\\s*,\\s*")));
                if (!TextUtils.isEmpty(centerLocation)) {
                    String[] centerLocationArray = centerLocation.split(",");
                    centerLat = Double.parseDouble(centerLocationArray[0].replace("[", "").replaceAll("\"", "").replaceAll("°", "").replaceAll("'", ""));
                    centerLong = Double.parseDouble(centerLocationArray[1].replace("]", "").replaceAll("\"", "").replaceAll("°", "").replaceAll("'", ""));
                    clusterDistance = Distance_from_center(latitude, longitude, centerLat, centerLong);
                    cluster.setSgcDistance(clusterDistance);
                }
            } catch (NumberFormatException e) {
                FirebaseEventUtils.logException(e);
            }
        }

    }

    private static double Distance_from_center(double lat1, double lon1, double lat2,
                                               double lon2) {
        int redius = 6371000;
        double radlat1 = (Math.PI * lat1) / 180;
        double radlat2 = (Math.PI * lat2) / 180;
        double theta = lat2 - lat1;
        double radtheta = (Math.PI * theta) / 180;
        double radGama = (Math.PI * (lon2 - lon1)) / 180;
        double dist = Math.sin(radtheta / 2) * Math.sin(radtheta / 2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.sin(radGama / 2) * Math.sin(radGama / 2);
        double dist1 = 2 * Math.atan2(Math.sqrt(dist), Math.sqrt(1 - dist));
        return redius * dist1;
    }

    private boolean isLocationInside(PropertiesVo propertiesVo, double latitude, double longitude) {

        String property_geo = propertiesVo.getPropertyGeo();
        String[] separated = property_geo.split(":");
        if (separated.length <= 2)
            return false;

        String values = separated[2].replaceAll(";", "").trim();

        String[] separatedArray = values.split("\\),\\(");

        ArrayList<Point> arrayLatLng = getItemFromGeoProperty(separatedArray);

        try {
            if (arrayLatLng.size() > 3) {
                // Prepare simple polygon with the use of property lat long here.
                Polygon.Builder polygonBuilder = Polygon.Builder();
                for (int p = 0; p < arrayLatLng.size(); p++) {
                    polygonBuilder.addVertex(arrayLatLng.get(p));
                }

                Polygon polygon = polygonBuilder.build(); // Build polygon here.
                // Take current latitude & longitude into account & check whether its inside a property polygon.
                Point currentLatLngPoint = new Point(latitude, longitude);
                // Tag the photo with the property and exit the loop, if pic lat & long are inside the polygon
                return polygon.contains(currentLatLngPoint);
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            return false;
        }
        return false;
    }

    private Location getPropertyLocation(PropertiesVo propertiesVo) {

        String property_geo = propertiesVo.getPropertyGeo();
        String[] separated = property_geo.split(":");
        if (separated.length <= 2)
            return null;
        String values = separated[2].replaceAll(";", "").trim();
        String[] separatedArray = values.split("\\),\\(");
        ArrayList<Point> arrayLatLng = getItemFromGeoProperty(separatedArray);;
        double latMax, latMin, longMax, longMin;
        if (!arrayLatLng.isEmpty()) {
            latMax = arrayLatLng.get(0).mLatitude;
            latMin = arrayLatLng.get(0).mLatitude;
            longMax = arrayLatLng.get(0).mLongitude;
            longMin = arrayLatLng.get(0).mLongitude;
            for (Point point : arrayLatLng) {
                latMax = Math.max(point.mLatitude, latMax);
                latMin = Math.min(point.mLatitude, latMin);
                longMax = Math.max(point.mLongitude, longMax);
                longMin = Math.min(point.mLongitude, longMin);
            }
        } else {
            return null;
        }

        Location propertyLocation = new Location("Sitefotos1");

        propertyLocation.setLatitude((latMax + latMin) / 2);
        propertyLocation.setLongitude((longMax + longMin) / 2);

        return propertyLocation;
    }

    private ArrayList<PropertiesVo> checkClosestDistance(PropertiesVo propertyObject, double distInMeters, long distanceAccuracy) {

        //Checking if the distance of the pic closest to the current Lat Long
        long distanceDelta = AppPrefShared.getLong(Constants.POLYGONE_DISTANCE_DELTA, 800);
        if (distInMeters < (distanceDelta + distanceAccuracy)) {
            //add values in array of popup Dialogue
            PropertiesVo obj = new PropertiesVo();

            String property_name = propertyObject.getPropertyName().trim();
            long property_id = propertyObject.getPropertyId();
            obj.setPropertyName(property_name);
            obj.setPropertyId(property_id);
            obj.setDistanceInMeters(distInMeters);
            lstCloseProperty.add(loopCounter, obj);

            // Increment Loop counter for next position
            loopCounter++;
        } else {
            // Nothing happens here. Set building Id to 0.
            buildingId = "0";
            buildingName = "";
            propertyName = "";
        }
        return lstCloseProperty;
    }
}
