package com.sitefotos.util;

import static com.sitefotos.util.StaticUtils.inputTypeService;
import static com.sitefotos.util.StaticUtils.isFormValidate;
import static com.sitefotos.util.StaticUtils.openSettingScreen;
import static com.sitefotos.util.StaticUtils.setFlipAnimation;
import static com.sitefotos.util.StaticUtils.showForeGroundToast;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.IntentSender;
import android.content.res.Resources;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.MotionEvent;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.ViewFlipper;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.LinearSmoothScroller;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.common.api.GoogleApiClient;
import com.google.android.gms.common.api.ResolvableApiException;
import com.google.android.gms.location.LocationRequest;
import com.google.android.gms.location.LocationServices;
import com.google.android.gms.location.LocationSettingsRequest;
import com.google.android.gms.location.LocationSettingsResponse;
import com.google.android.gms.location.LocationSettingsStatusCodes;
import com.google.android.gms.location.Priority;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.Task;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.gson.Gson;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.DynamicDropDownItemSelectAdapter;
import com.sitefotos.adapter.ImageComponentsAdapter;
import com.sitefotos.adapter.MarkerPinAdapter;
import com.sitefotos.adapter.MaterialViewAdapter;
import com.sitefotos.adapter.MultiSelectAdapter;
import com.sitefotos.adapter.NumberPickerAdapter;
import com.sitefotos.adapter.SingleSelectAdapter;
import com.sitefotos.adapter.TagAdapter;
import com.sitefotos.adapter.VehicleEquipmentSelectAdapter;
import com.sitefotos.appinterface.OnClockScreenMultiCrewSelected;
import com.sitefotos.appinterface.OnStickyHeaderCrewListener;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.camera.PropertyListAdapter;
import com.sitefotos.form.OnCrewAndServiceActionTapped;
import com.sitefotos.form.OnDynamicItemSelected;
import com.sitefotos.form.OnItemSelected;
import com.sitefotos.form.OnPreSelectServiceActionTapped;
import com.sitefotos.interfaces.OnImageComponentSelected;
import com.sitefotos.interfaces.OnMarkerPinSelected;
import com.sitefotos.interfaces.OnMaterialSelect;
import com.sitefotos.interfaces.OnTagSelected;
import com.sitefotos.models.CrewSelectionData;
import com.sitefotos.models.CrewSelectionHeader;
import com.sitefotos.models.DynamicDropDownItem;
import com.sitefotos.models.Employees;
import com.sitefotos.models.FormData;
import com.sitefotos.models.ImageComponent;
import com.sitefotos.models.ItemModel;
import com.sitefotos.models.MapPinData;
import com.sitefotos.models.Material;
import com.sitefotos.models.SiteData;
import com.sitefotos.models.TMService;
import com.sitefotos.models.Tags;
import com.sitefotos.site.detail.OnMultiCrewSelected;
import com.sitefotos.site.detail.adapter.CrewSelectionAdapter;
import com.sitefotos.site.detail.adapter.ExtendedCrewSelectionAdapter;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.views.StickHeaderAddCrewItemDecoration;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

/**
 * Created by dk on 22/8/17.
 */

public class PopUtils {

    /**
     * This methods shows application update view.
     *
     * @param context Instance on singleSelectAdapter
     */
    private static SingleSelectAdapter singleSelectAdapter;
    public static boolean isHide;
    static AlertDialog alertDialog;

    /**
     * Method to show alert dialog with two/single button
     *
     * @param context               context
     * @param message               Message of dialog
     * @param posButtonName         Name of positive button
     * @param nagButtonName         Name of negative button
     * @param onPositiveButtonClick call back method of positive button
     * @param onNegativeButtonClick call back method of negative butoon
     */
    public static void showCustomTwoButtonAlertDialog(final Context context, String title, String message, String posButtonName,
                                                      String nagButtonName, boolean isOutSideCancelable,
                                                      DialogInterface.OnClickListener onPositiveButtonClick,
                                                      DialogInterface.OnClickListener onNegativeButtonClick) {
        showCustomTwoButtonAlertDialog(context, title, message, posButtonName, nagButtonName, false, isOutSideCancelable, onPositiveButtonClick, onNegativeButtonClick);

    }

    /**
     * Method to show alert dialog with single  positive  button
     *
     * @param context               context
     * @param message               Message of dialog
     * @param posButtonName         Name of positive button
     * @param onPositiveButtonClick call back method of positive button
     */
    public static void showCustomTwoButtonAlertDialog(final Context context, String title, String message, String posButtonName,
                                                      boolean isOutSideCancelable, DialogInterface.OnClickListener onPositiveButtonClick) {
        showCustomTwoButtonAlertDialog(context, title, message, posButtonName, "", false, isOutSideCancelable, onPositiveButtonClick, null);

    }

    /**
     * Method to show alert dialog with two/single button
     *
     * @param context               context
     * @param message               Message of dialog
     * @param posButtonName         Name of positive button
     * @param nagButtonName         Name of negative button
     * @param onPositiveButtonClick call back method of positive button
     * @param onNegativeButtonClick call back method of negative butoon
     */
    public static void showCustomTwoButtonAlertDialog(final Context context, String title, String message, String posButtonName,
                                                      String nagButtonName, boolean changeButtonColor, boolean isOutSideCancelable,
                                                      DialogInterface.OnClickListener onPositiveButtonClick,
                                                      DialogInterface.OnClickListener onNegativeButtonClick) {
        try {
            if (!checkActivityStatusFromContext(context))
                return;

            AlertDialog.Builder builder;
            builder = new AlertDialog.Builder(context, R.style.MaterialDialogTheme);
            builder.setMessage(message);
            if (TextUtils.isEmpty(title)) {
                builder.setTitle(context.getResources().getString(R.string.app_name));
            } else {
                builder.setTitle(title);
            }

            if (onPositiveButtonClick != null) {
                builder.setPositiveButton(posButtonName, onPositiveButtonClick);
            }
            if (onNegativeButtonClick != null) {
                builder.setNegativeButton(nagButtonName, onNegativeButtonClick);
            }
            alertDialog = builder.create();
            alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
            if (changeButtonColor) {
                try {
                    alertDialog.setOnShowListener(arg0 -> alertDialog.getButton(AlertDialog.BUTTON_NEGATIVE).setTextColor(Color.RED));
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
            alertDialog.setCanceledOnTouchOutside(isOutSideCancelable);
            if (!((Activity) context).isFinishing())
                alertDialog.show();

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Method to dismissed previously visible dialog before showing new dialog.
     */
    public static void dismissAlertDialog() {
        try {
            if (alertDialog != null && alertDialog.isShowing())
                alertDialog.dismiss();
            alertDialog = null;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Method to show alertDialog with items array
     *
     * @param context             context
     * @param items               Array of data which are display in dialog
     * @param title               title of the alert dialog
     * @param onItemClickListener call back method of selected item
     */
    public static void showCustomAlertDialogWithItems(Context context, CharSequence[] items,
                                                      final String title,
                                                      final DialogInterface.OnClickListener onItemClickListener) {
        try {
            if (context != null) {
                AlertDialog.Builder builder;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    builder = new AlertDialog.Builder(context, android.R.style.Theme_Material_Light_Dialog_Alert);
                } else {
                    builder = new AlertDialog.Builder(context);
                }

                builder.setTitle(title);
                builder.setItems(items, onItemClickListener);
                AlertDialog alertDialog = builder.create();

/*
                try {
                    Window window = alertDialog.getWindow();
                    WindowManager.LayoutParams wlp = window.getAttributes();

                    wlp.gravity = Gravity.BOTTOM;
                    wlp.flags &= ~WindowManager.LayoutParams.FLAG_DIM_BEHIND;
                    window.setAttributes(wlp);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }*/
                alertDialog.requestWindowFeature(Window.FEATURE_NO_TITLE);
                alertDialog.show();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * This method opens system's GPS location dialog to TURN GPS ON
     *
     * @param activity is Context
     */
    public static void displayLocationSettingsRequest(Activity activity, int permissionReq) {

        if (!checkActivityStatus(activity))
            return;
        try {
            LocationRequest locationRequest = new LocationRequest.Builder(1000)
                    .setPriority(Priority.PRIORITY_HIGH_ACCURACY).build();
            LocationSettingsRequest.Builder builder = new LocationSettingsRequest.Builder().addLocationRequest(locationRequest);
            builder.setAlwaysShow(true);
            Task<LocationSettingsResponse> result = LocationServices.getSettingsClient(activity).checkLocationSettings(builder.build());
            result.addOnCompleteListener(task -> {
                try {
                    task.getResult(ApiException.class);
                } catch (ApiException exception) {
                    switch (exception.getStatusCode()) {
                        case LocationSettingsStatusCodes.RESOLUTION_REQUIRED:
                            // Location settings are not satisfied. But could be fixed by showing the
                            // user a dialog.
                            try {
                                // Cast to a resolvable exception.
                                ResolvableApiException resolvable = (ResolvableApiException) exception;
                                // Show the dialog by calling startResolutionForResult(),
                                // and check the result in onActivityResult().
                                resolvable.startResolutionForResult(activity, permissionReq);
                            } catch (IntentSender.SendIntentException e) {
                                // Ignore the error.
                            } catch (ClassCastException e) {
                                // Ignore, should be an impossible error.
                            }
                            break;
                        case LocationSettingsStatusCodes.SETTINGS_CHANGE_UNAVAILABLE:
                            // Location settings are not satisfied. However, we have no way to fix the
                            // settings so we won't show the dialog.
                            break;
                    }
                }
            });

            result.addOnFailureListener(new OnFailureListener() {

                @Override
                public void onFailure(@NonNull Exception e) {

                }
            });
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Function to show settings alert dialog
     * On pressing Settings button will lauch Settings Options
     */
    public static void showGPSSettingsAlert(Activity activity, String massage, final DialogInterface.OnClickListener positiveClickListener, final DialogInterface.OnClickListener negativeClickListener) {

        if (!checkActivityStatus(activity))
            return;
        android.app.AlertDialog.Builder alertDialog = new android.app.AlertDialog.Builder(activity);
        alertDialog.setTitle(activity.getResources().getString(R.string.app_name));

        alertDialog.setMessage(massage);
        alertDialog.setCancelable(false);

        alertDialog.setPositiveButton(activity.getResources().getString(R.string.enable_gps), (dialog, which) -> {
            dialog.cancel();
            positiveClickListener.onClick(dialog, which);

        });

        alertDialog.setNegativeButton(activity.getResources().getString(R.string.txt_cancel), (dialog, which) -> {
            dialog.dismiss();
            negativeClickListener.onClick(dialog, which);

        });

        if (!activity.isFinishing()) {
            try {
                alertDialog.show();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    /**
     * function to check activity object and its current status.
     * if instance is null or activity is not present then we wont show dialog.
     *
     * @param activity Activity
     * @return
     */
    private static boolean checkActivityStatus(Activity activity) {
        if (activity == null)
            return false;
        return !activity.isFinishing();
    }

    /**
     * function to check activity object and its current status.
     * if instance is null or activity is not present then we wont show dialog.
     *
     * @param context Context
     * @return
     */
    private static boolean checkActivityStatusFromContext(Context context) {
        if (context == null)
            return false;
        return !((Activity) context).isFinishing();
    }

    public static void showAlertDialogPositiveButtonOnlyEnableLocation(Context context, final DialogInterface.OnClickListener positiveClickListner, final DialogInterface.OnClickListener negativeClickListner) {

        if (!checkActivityStatusFromContext(context))
            return;
        android.app.AlertDialog.Builder alertDialog = new android.app.AlertDialog.Builder(context);

        alertDialog.setTitle(context.getResources().getString(R.string.app_name));
        alertDialog.setMessage(context.getResources().getString(R.string.to_add_new_property_sitefotos_requires_access_to_your_location));
        alertDialog.setPositiveButton(context.getResources().getString(R.string.enable_gps), (dialog, which) -> {
            dialog.cancel();
            positiveClickListner.onClick(dialog, which);

        });

        // on pressing cancel button
        alertDialog.setNegativeButton(context.getResources().getString(R.string.txt_cancel), (dialog, which) -> {
            dialog.dismiss();
            negativeClickListner.onClick(dialog, which);
//                isGpsEnableCalled = true;
        });

        // Showing Alert Message
//        if (!isDialogueToHide) {
        if (!((Activity) context).isFinishing()) {
            //show dialog
            alertDialog.show();
        }
    }

    public static void showConformAlertDialogForProperty(Context context, String property,
                                                         final DialogInterface.OnClickListener positiveClickListener, final DialogInterface.OnClickListener negativeClickListner) {

        if (!checkActivityStatusFromContext(context))
            return;
        android.app.AlertDialog.Builder alertDialog = new android.app.AlertDialog.Builder(context);

        alertDialog.setTitle(context.getResources().getString(R.string.app_name));
        alertDialog.setMessage(String.format("%s %s ?", context.getString(R.string.txt_location_message), property));

        alertDialog.setPositiveButton(context.getString(R.string.txt_change), (dialog, which) -> {
            dialog.cancel();
            positiveClickListener.onClick(dialog, which);

        });

        // on pressing cancel button
        alertDialog.setNegativeButton(context.getString(R.string.txt_confirm), (dialog, which) -> {
            dialog.dismiss();
            negativeClickListner.onClick(dialog, which);
//                isGpsEnableCalled = true;
        });

        // Showing Alert Message
//        if (!isDialogueToHide) {
        if (!((Activity) context).isFinishing()) {
            //show dialog
            alertDialog.show();
        }
    }

    public static void showPropertyDialog(final Activity context, final List<PropertiesVo> propertiesVoArrayList,
                                          boolean canGetLocation, final PropertyListAdapter.OnItemClickedListener onItemClickedListener,
                                          final View.OnClickListener newPropertyClicked, final View.OnClickListener nonOfAboveClicked) {
        try {
            if (!checkActivityStatus(context))
                return;
            final Dialog dialog = new Dialog(context);
            dialog.setContentView(R.layout.layout_property_list_dialogue);
            dialog.setTitle(context.getString(R.string.whichProperty));
            dialog.setCanceledOnTouchOutside(false);
            setWindowManager(dialog);

            boolean isPrivilegedUser = AppPrefShared.getString(Constants.LOGGED_IN_USER_TYPE, Constants.LOGGED_IN_USER_TYPE).equalsIgnoreCase(Constants.LOGGED_IN_USER_TYPE_PRIVILEGED_USER);
            ListView propertyList = dialog.findViewById(R.id.listCustomDialoguePropertyList);
            Button btnNewProperty = dialog.findViewById(R.id.btnCustomDialogueAddNewProperty);
            Button btnNonOfAbove = dialog.findViewById(R.id.btnCustomDialogueNoneOfAbove);

            PropertyListAdapter adapter = new PropertyListAdapter(context, propertiesVoArrayList, position -> {
                btnNonOfAbove.setTag(2);
                dialog.dismiss();
                onItemClickedListener.setOnItemClicked(position);
            });
            propertyList.setAdapter(adapter);

            if (isPrivilegedUser && canGetLocation) {
                btnNewProperty.setVisibility(View.VISIBLE);
            } else {
                btnNewProperty.setVisibility(View.GONE);
                if (propertiesVoArrayList.isEmpty()) {
                    return;
                }
            }

            btnNewProperty.setOnClickListener(v -> {
                // Close dialog
                btnNewProperty.setTag(1);

                dialog.dismiss();
                newPropertyClicked.onClick(v);

            });
            btnNonOfAbove.setOnClickListener(v -> {
                btnNonOfAbove.setTag(2);
                dialog.dismiss();
                nonOfAboveClicked.onClick(v);
            });
            if (!context.isFinishing()) {
                dialog.show();
            }

            dialog.setOnDismissListener(dialogInterface -> {
                if (btnNonOfAbove.getTag() == null) {
                    nonOfAboveClicked.onClick(null);
                }
            });
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }

    }

    /**
     * This methods shows application update view.
     *
     * @param context Instance on Activity
     */
    public static void showDialogForLongPress(Context context, View.OnClickListener clearOnClick, View.OnClickListener deleteOnClick) {
        if (context == null)
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_long_press);
        setWindowManager(dialog);
        TextView btnClear = dialog.findViewById(R.id.btnClear);
        TextView btnDelete = dialog.findViewById(R.id.btnDelete);

        btnClear.setOnClickListener(view -> {
            clearOnClick.onClick(view);
            dialog.dismiss();
        });
        btnDelete.setOnClickListener(view -> {
            deleteOnClick.onClick(view);
            dialog.dismiss();
        });
        dialog.show();
    }

    /**
     * This methods shows application update view.
     *
     * @param context Instance on Activity
     */
    public static void showDialogForClearFormData(Context context, String title, View.OnClickListener clearOnClick) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_long_press_cleare_form);
        setWindowManager(dialog);
        TextView tvTitle = dialog.findViewById(R.id.tvTitle);
        TextView tvCancel = dialog.findViewById(R.id.tvCancel);
        TextView tvClear = dialog.findViewById(R.id.tvClear);

        tvTitle.setText(title);
        tvClear.setOnClickListener(view -> {
            clearOnClick.onClick(view);
            dialog.dismiss();
        });

        tvCancel.setOnClickListener(view -> {
            dialog.dismiss();
        });

        if (!((Activity) context).isFinishing()) {
            dialog.show();
        }
    }

    public static void showDialogForRemoveIssue(Context context, View.OnClickListener checkINClick) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_remove_issue);
        AppCompatTextView tvCancel = dialog.findViewById(R.id.tvCancel);
        AppCompatTextView tvYes = dialog.findViewById(R.id.tvYes);
        tvYes.setOnClickListener(view -> {
            dialog.dismiss();
            checkINClick.onClick(view);
        });
        tvCancel.setOnClickListener(view -> dialog.dismiss());
        if (!((Activity) context).isFinishing())
            dialog.show();
    }

    public static void showDialogForRemoveMaterial(Context context, View.OnClickListener checkINClick) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_remove_issue);
        AppCompatTextView tvCancel = dialog.findViewById(R.id.tvCancel);
        AppCompatTextView tvTitle = dialog.findViewById(R.id.tvTitle);
        tvTitle.setText(context.getString(R.string.you_want_to_remove_material));
        AppCompatTextView tvYes = dialog.findViewById(R.id.tvYes);
        tvYes.setOnClickListener(view -> {
            dialog.dismiss();
            checkINClick.onClick(view);
        });
        tvCancel.setOnClickListener(view -> dialog.dismiss());
        if (!((Activity) context).isFinishing())
            dialog.show();
    }

    public static void showDialogForCheckout(Context context, View.OnClickListener yesClick, View.OnClickListener Click) {
        if (context == null)
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_check_in);
        TextView tvTitle = dialog.findViewById(R.id.tvTitle);
        tvTitle.setText(context.getString(R.string.would_you_like_to_check_out));
        setWindowManager(dialog);
        AppCompatTextView tvNo = dialog.findViewById(R.id.tvNo);
        AppCompatTextView tvYes = dialog.findViewById(R.id.tvYes);
        tvYes.setOnClickListener(view -> {
            dialog.dismiss();
            yesClick.onClick(view);
        });
        tvNo.setOnClickListener(view -> {
            dialog.dismiss();
            Click.onClick(view);
        });
        dialog.show();
    }

    /**
     * This methods shows application update view.
     *
     * @param context Instance on Activity
     */
    public static void showDialogForMultiSelectView(Context context, final ArrayList<ItemModel> lstItems, final OnItemSelected onItemSelected) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_multi_select);
        setWindowManager(dialog);
        RecyclerView rvImageMain = dialog.findViewById(R.id.rvCrewMain);
        rvImageMain.setLayoutManager(new LinearLayoutManager(context));

        final MultiSelectAdapter multiSelectAdapter = new MultiSelectAdapter(context, lstItems) {
            @Override
            public void selectItem(int position, boolean isSelected) {
                lstItems.get(position).setSelected(isSelected);
                // multiSelectAdapter.notifyDataSetChanged();
            }
        };
        rvImageMain.setAdapter(multiSelectAdapter);

        TextView tvOk = dialog.findViewById(R.id.btnSubmit);
        TextView tvCancel = dialog.findViewById(R.id.btnCancel);
        tvOk.setOnClickListener(view -> {
            String selectedItem = "";
            for (ItemModel itemModel : lstItems) {
                if (itemModel.isSelected()) {
                    selectedItem = selectedItem.concat(itemModel.getName()).concat(",");
                }
            }
            if (!TextUtils.isEmpty(selectedItem)) {
                onItemSelected.onSelectedItems(selectedItem.substring(0, selectedItem.length() - 1), new ItemModel());
            } else {
                onItemSelected.onSelectedItems("", new ItemModel());
            }
            dialog.dismiss();
        });
        tvCancel.setOnClickListener(view -> dialog.dismiss());
        dialog.setOnDismissListener(dialogInterface -> {
            for (ItemModel itemModel : lstItems) {
                itemModel.setSelected(false);
            }
        });
        if (!((Activity) context).isFinishing())
            dialog.show();
    }

    public static void showDialogToSelectVehicle(Activity context, boolean isVehicle, final ArrayList<ItemModel> lstItems, final OnItemSelected onItemSelected) {
        BottomSheetDialog bsdVehicleEquipment = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_vehicle_equipment, null);
        AppCompatTextView tvTitle = sheetView.findViewById(R.id.tvTitle);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnDone);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        AppCompatEditText edtSearch = sheetView.findViewById(R.id.edtSearch);
        RecyclerView rvData = sheetView.findViewById(R.id.rvVehicleEquipment);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        rvData.setLayoutManager(new LinearLayoutManager(context));
        if (isVehicle) {
            tvTitle.setText(R.string.txt_select_vehicle);
        } else {
            tvTitle.setText(R.string.txt_select_equipment);
        }

        VehicleEquipmentSelectAdapter vehicleEquipmentSelectAdapter = new VehicleEquipmentSelectAdapter(context) {
            @Override
            public void selectItem(int position, boolean isSelected) {
              /*  for (ItemModel itemModel : lstItems) {
                    itemModel.setSelected(false);
                }
                lstItems.get(position).setSelected(isSelected);*/

            }
        };
        rvData.setAdapter(vehicleEquipmentSelectAdapter);
        bsdVehicleEquipment.setContentView(sheetView);
        bsdVehicleEquipment.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        vehicleEquipmentSelectAdapter.updateList(lstItems, true);
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(rvData.getContext(), 1);
        rvData.addItemDecoration(dividerItemDecoration);

        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String text = charSequence.toString();
                if (text.length() > 0) {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                } else {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                }
                vehicleEquipmentSelectAdapter.filterData(charSequence);
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        btnDone.setOnClickListener(view -> {
            ItemModel selectedItem = null;
            for (ItemModel lstModel : lstItems) {
                if (lstModel.isSelected()) {
                    selectedItem = lstModel;
                }
            }
            if (selectedItem != null) {
                onItemSelected.onSelectedItems(selectedItem.getName(), selectedItem);
                bsdVehicleEquipment.dismiss();
            }

        });
        btnCancel.setOnClickListener(view -> {
            vehicleEquipmentSelectAdapter.unCheckLastSelectedData();
            bsdVehicleEquipment.dismiss();
        });

        bsdVehicleEquipment.setOnDismissListener(dialogInterface -> {
            vehicleEquipmentSelectAdapter.unCheckLastSelectedData();
        });
        if (!context.isFinishing()) {
            bsdVehicleEquipment.show();
            bsdVehicleEquipment.setCanceledOnTouchOutside(true);
            bsdVehicleEquipment.setCancelable(true);
        }
    }


    public static void showDialogToSelectDynamicDropDown(Activity context, final ArrayList<DynamicDropDownItem> lstItems, final OnDynamicItemSelected onItemSelected) {
        BottomSheetDialog bsdVehicleEquipment = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_dynamic_dropdown, null);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnDone);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        AppCompatEditText edtSearch = sheetView.findViewById(R.id.edtSearch);
        RecyclerView rvData = sheetView.findViewById(R.id.rvDynamicDropDown);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        rvData.setLayoutManager(new LinearLayoutManager(context));

        DynamicDropDownItemSelectAdapter selectAdapter = new DynamicDropDownItemSelectAdapter(context) {
            @Override
            public void selectItem(int position, boolean isSelected) {
              /*  for (ItemModel itemModel : lstItems) {
                    itemModel.setSelected(false);
                }
                lstItems.get(position).setSelected(isSelected);*/

            }
        };
        rvData.setAdapter(selectAdapter);
        bsdVehicleEquipment.setContentView(sheetView);
        bsdVehicleEquipment.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        selectAdapter.updateList(lstItems, true);
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(rvData.getContext(), 1);
        rvData.addItemDecoration(dividerItemDecoration);

        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String text = charSequence.toString();
                if (text.length() > 0) {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                } else {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                }
                selectAdapter.filterData(charSequence);
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        btnDone.setOnClickListener(view -> {
            DynamicDropDownItem selectedItem = null;
            for (DynamicDropDownItem lstModel : lstItems) {
                if (lstModel.isSelected()) {
                    selectedItem = lstModel;
                }
            }
            if (selectedItem != null) {
                onItemSelected.onSelectedItems(selectedItem.getValue(), selectedItem);
                bsdVehicleEquipment.dismiss();
            }

        });
        btnCancel.setOnClickListener(view -> {
            selectAdapter.unCheckLastSelectedData();
            bsdVehicleEquipment.dismiss();
        });

        bsdVehicleEquipment.setOnDismissListener(dialogInterface -> {
            selectAdapter.unCheckLastSelectedData();
        });
        if (!context.isFinishing()) {
            bsdVehicleEquipment.show();
            bsdVehicleEquipment.setCanceledOnTouchOutside(true);
            bsdVehicleEquipment.setCancelable(true);
        }
    }

    public static void showDialogFoSingleSelectView(Context context, final ArrayList<ItemModel> lstItems, final OnItemSelected onItemSelected) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_multi_select);
        setWindowManager(dialog);
        RecyclerView rvImageMain = dialog.findViewById(R.id.rvCrewMain);
        rvImageMain.setLayoutManager(new LinearLayoutManager(context));
        singleSelectAdapter = new SingleSelectAdapter(context, lstItems) {
            @Override
            public void selectItem(int position, boolean isSelected) {
                for (ItemModel itemModel : lstItems) {
                    itemModel.setSelected(false);
                }
                lstItems.get(position).setSelected(isSelected);
                singleSelectAdapter.notifyDataSetChanged();
            }
        };
        rvImageMain.setAdapter(singleSelectAdapter);

        TextView tvOk = dialog.findViewById(R.id.btnSubmit);
        TextView tvCancel = dialog.findViewById(R.id.btnCancel);
        tvOk.setOnClickListener(view -> {
            dialog.dismiss();
            String selectedItem = "";
            ItemModel itemModel1 = null;
            for (ItemModel itemModel : lstItems) {
                if (itemModel.getLinearLayout() != null) {
                    itemModel.getLinearLayout().setVisibility(View.GONE);
                }
            }
            for (ItemModel itemModel : lstItems) {
                if (itemModel.isSelected()) {
                    selectedItem = itemModel.getName();
                    itemModel1 = itemModel;
                    if (itemModel.getLinearLayout() != null)
                        itemModel.getLinearLayout().setVisibility(View.VISIBLE);

                }
            }
            if (!TextUtils.isEmpty(selectedItem)) {
                onItemSelected.onSelectedItems(selectedItem, itemModel1);
            }
        });
        tvCancel.setOnClickListener(view -> {
           /* for (ItemModel itemModel : lstItems) {
                itemModel.setSelected(false);
            }*/
            dialog.dismiss();
        });
        dialog.setOnDismissListener(dialogInterface -> {
            for (ItemModel itemModel : lstItems) {
                itemModel.setSelected(false);
            }
        });
        if (!((Activity) context).isFinishing())
            dialog.show();
    }

    private static void setWindowManager(Dialog dialog) {
        WindowManager.LayoutParams windowManagerLayoutParams = new WindowManager.LayoutParams();
        Window window = dialog.getWindow();
        if (window != null) {
            window.setBackgroundDrawableResource(R.color.transparent);
            windowManagerLayoutParams.copyFrom(window.getAttributes());
            //This makes the dialog take up the full width
            windowManagerLayoutParams.width = WindowManager.LayoutParams.MATCH_PARENT;
            windowManagerLayoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;
            if (Constants.SCREEN_WIDTH > 0 && Constants.SCREEN_HEIGHT > 0) {
                windowManagerLayoutParams.width = Constants.SCREEN_WIDTH - ((Constants.SCREEN_WIDTH) / 10);
            }
            window.setAttributes(windowManagerLayoutParams);
        }
    }

    /**
     * This methods shows application update view.
     *
     * @param context                Instance on Activity
     * @param newAppVersion          Available application version
     * @param updateNowClickListener call back of update click listener.
     */
    public static void showDialogForUpdateApplication(Context context, boolean forceUpdate, String newAppVersion, final View.OnClickListener updateNowClickListener) {
        try {
            if (!checkActivityStatusFromContext(context))
                return;
            final Dialog dialog = new Dialog(context);
            dialog.setContentView(R.layout.dialog_app_update_available);
            setWindowManager(dialog);
            dialog.setCanceledOnTouchOutside(false);
            TextView tvAppVersion = dialog.findViewById(R.id.tvAppVersion);

            TextView tvUpdateNow = dialog.findViewById(R.id.tvUpdateNow);
            TextView tvLater = dialog.findViewById(R.id.tvLater);
            if (forceUpdate) {
                tvLater.setVisibility(View.GONE);
                dialog.setCancelable(false);
            }

            if (TextUtils.isEmpty(newAppVersion)) {
                tvAppVersion.setText("");
            } else {
                tvAppVersion.setText(context.getResources().getString(R.string.new_version).concat(newAppVersion));
            }

            tvUpdateNow.setOnClickListener(v -> updateNowClickListener.onClick(v));

            tvLater.setOnClickListener(v -> dialog.cancel());
            if (!((Activity) context).isFinishing())
                dialog.show();
        } catch (Resources.NotFoundException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * This methods shows application update view.
     *
     * @param context             Instance on Activity
     * @param message             dialog message
     * @param deleteClickListener call back of update click listener.
     */
    public static void showDialogForDeleteView(Context context, String message, final View.OnClickListener deleteClickListener) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_delete_view);
        setWindowManager(dialog);
        TextView tvText = dialog.findViewById(R.id.tvText);
        tvText.setText(message);
        TextView tvDelete = dialog.findViewById(R.id.tvDelete);
        TextView tvCancel = dialog.findViewById(R.id.btnCancel);

        tvDelete.setOnClickListener(v -> {
            deleteClickListener.onClick(v);
            dialog.dismiss();
        });

        tvCancel.setOnClickListener(v -> dialog.cancel());
        if (!((Activity) context).isFinishing())
            dialog.show();
    }

    /**
     * This methods shows image in dialog view.
     *
     * @param context  Instance on Activity
     * @param imageUrl url of image
     */
    public static void showDialogImageView(Context context, String imageUrl) {
        if (!checkActivityStatusFromContext(context))
            return;
        final Dialog dialog = new Dialog(context);
        dialog.setContentView(R.layout.dialog_image_fullscreen);
        setWindowManager(dialog);
        ImageView ivCancel = dialog.findViewById(R.id.ivCancel);
        ImageView ivView = dialog.findViewById(R.id.ivView);

        ImageUtil.loadImageInGlide(ivView, imageUrl);

        ivCancel.setOnClickListener(v -> dialog.cancel());
        if (!((Activity) context).isFinishing())
            dialog.show();
    }

    public static void showAlertDialogPositiveButtonOnly(Activity activity, String strTitle, String strMessage) {

        if (!checkActivityStatus(activity))
            return;
        new android.app.AlertDialog.Builder(activity).setTitle(strTitle.trim()).setMessage(strMessage.trim())
                .setPositiveButton(android.R.string.ok, (dialog, which) -> {
                    // continue with delete
                    dialog.dismiss();
                })
                .show();

    }


    public static void showDialogSMSOrEmail(final Activity activity, AppCompatImageButton imgBtnShare, View.OnClickListener smsClickListener, View.OnClickListener emailClickListener, View.OnClickListener cancelClickListener) {

        if (!checkActivityStatus(activity))
            return;

        BottomSheetDialog shareSheetDialog = new BottomSheetDialog(activity);
        View sheetView = activity.getLayoutInflater().inflate(R.layout.layout_dialog_sms_email, null);

        AppPrefShared.putValue(Constants.IS_SHARE_CAPTURED_IMAGE, false);
        imgBtnShare.setSelected(false);
        Button btnSMS = sheetView.findViewById(R.id.btnSMS);
        Button btnEmail = sheetView.findViewById(R.id.btnEmail);
        Button btnCancel = sheetView.findViewById(R.id.btnCancel);
        shareSheetDialog.setContentView(sheetView);
        //shareSheetDialog.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        btnSMS.setOnClickListener(v -> {
            shareSheetDialog.dismiss();
            smsClickListener.onClick(v);

        });

        btnEmail.setOnClickListener(v -> {
            shareSheetDialog.dismiss();
            emailClickListener.onClick(v);

        });

        btnCancel.setOnClickListener(v -> {
            shareSheetDialog.dismiss();
            cancelClickListener.onClick(v);

        });
        if (!activity.isFinishing()) {
            shareSheetDialog.show();
            shareSheetDialog.setCanceledOnTouchOutside(false);
        }
      /*  try {
            Dialog dialogShare = new Dialog(activity, R.style.AppCompatAlertDialogStyle);
            dialogShare.setContentView(R.layout.layout_dialog_sms_email);
            dialogShare.setTitle(activity.getString(R.string.share_via));
            setWindowManager(dialogShare);
            if (!activity.isFinishing()) {
                //show dialog
                dialogShare.show();
                Button btnSMS = dialogShare.findViewById(R.id.btnSMS);
                Button btnEmail = dialogShare.findViewById(R.id.btnEmail);
                // if decline button is clicked, close the custom dialog


            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }*/
    }

    public static void showBottomViewForNumberSelection(Activity context, final ArrayList<ItemModel> lstItems, int selectedPosition, final OnItemSelected onItemSelected) {

        if (!checkActivityStatus(context))
            return;
        final BottomSheetDialog[] mBottomSheetDialog = {new BottomSheetDialog(context)};
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_number_selecttion, null);

        RecyclerView rvNumber = sheetView.findViewById(R.id.rvNumber);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        rvNumber.setLayoutManager(new LinearLayoutManager(context));
        NumberPickerAdapter numberPickerAdapter = new NumberPickerAdapter(context, lstItems) {
            @Override
            public void selectItem(int position, boolean isSelected) {
                for (ItemModel itemModel : lstItems) {
                    itemModel.setSelected(false);
                }
                lstItems.get(position).setSelected(isSelected);
            }
        };

        rvNumber.setAdapter(numberPickerAdapter);
        if (selectedPosition > 0) {
            try {
                RecyclerView.SmoothScroller smoothScroller = new
                        LinearSmoothScroller(context) {
                            @Override
                            protected int getVerticalSnapPreference() {
                                return LinearSmoothScroller.SNAP_TO_START;
                            }
                        };
                smoothScroller.setTargetPosition(selectedPosition);
                if (rvNumber.getLayoutManager() != null)
                    rvNumber.getLayoutManager().startSmoothScroll(smoothScroller);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
        mBottomSheetDialog[0].setContentView(sheetView);


        BottomSheetBehavior mBehavior = BottomSheetBehavior.from((View) sheetView.getParent());
        mBehavior.setPeekHeight(Constants.SCREEN_HEIGHT / 2 - Constants.SCREEN_HEIGHT / 100);

        btnDone.setOnClickListener(view -> {
            String selectedItem = "";
            ItemModel itemModel1 = null;
            for (ItemModel itemModel : lstItems) {
                if (itemModel.isSelected()) {
                    itemModel1 = itemModel;
                    selectedItem = itemModel1.getName();
                    break;
                }
            }

            onItemSelected.onSelectedItems(selectedItem, itemModel1);
            mBottomSheetDialog[0].dismiss();
        });
        btnCancel.setOnClickListener(view -> mBottomSheetDialog[0].dismiss());
        mBottomSheetDialog[0].setOnDismissListener(dialogInterface -> {
            for (ItemModel itemModel : lstItems) {
                itemModel.setSelected(false);
            }
            mBottomSheetDialog[0] = null;
        });
        if (!context.isFinishing()) {
            mBottomSheetDialog[0].show();
            mBottomSheetDialog[0].setCanceledOnTouchOutside(false);
        }
    }


    /**
     * Method to set login user at first position and other data are in alphabetical order
     *
     * @param lstItems        list of alphabetical order
     * @param setSelfSelected true/False
     * @return
     */
    public static List<Employees> setLoggedInDataAtFirst(List<Employees> lstItems, boolean setSelfSelected) {

        int userId = StaticUtils.getEmployeeIdInInt();
        Employees selfData = null;
        for (Iterator<Employees> lstItemIterator = lstItems.iterator(); lstItemIterator.hasNext(); ) {
            Employees employees = lstItemIterator.next();
            try {
                if (employees.getEmployeeID() == userId) {
                    if (setSelfSelected)
                        employees.setSelected(true);
                    selfData = employees;
                    lstItemIterator.remove();

                }
            } catch (NumberFormatException e) {
                FirebaseEventUtils.logException(e);
            }
        }

        if (selfData != null)
            lstItems.add(0, selfData);

        return lstItems;
    }

    public static void manageSearchViewCloseTouchEvent(AppCompatEditText edtSearch) {
        edtSearch.setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                Drawable drawableRight = edtSearch.getCompoundDrawables()[2];
                if (drawableRight != null) {
                    if (motionEvent.getRawX() >= edtSearch.getRight() - edtSearch.getPaddingEnd() - drawableRight.getBounds().width() - 30) {
                        edtSearch.setText("");
                        return true;
                    }
                }
            }
            return false;
        });
    }


    public static void showNewCrewSelectionView(Activity context, List<Employees> lstPreSelectedItems,
                                                List<Employees> lstEmployee, LinearLayout llMultiSelect, final OnMultiCrewSelected onMultiSelected) {
        if (!checkActivityStatus(context))
            return;

        List<Object> lstAllData = new ArrayList<>();

        List<Employees> lstPreSelectedData = new ArrayList<>(lstPreSelectedItems);
        List<CrewSelectionData> lstPreSelectedCrewData = new ArrayList<>();
        BottomSheetDialog bsdCrewSelection = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_extended_crew, null);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        AppCompatEditText edtSearch = sheetView.findViewById(R.id.edtSearch);
        RecyclerView rvCrew = sheetView.findViewById(R.id.rvCrew);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        rvCrew.setLayoutManager(new LinearLayoutManager(context));
        disableEnableColor(btnDone, context, false);
        addCrewInCrewSelectionData(lstAllData, lstPreSelectedData, lstPreSelectedCrewData, lstEmployee);
        bsdCrewSelection.setContentView(sheetView);
        bsdCrewSelection.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        CrewSelectionAdapter crewSelectionAdapter = new CrewSelectionAdapter(context) {
            @Override
            public void onItemClicked(List<Object> lstAllData, int position, CrewSelectionData crewSelectionData, boolean isSelected) {
                manageCrewAdapterItemClicked(context, lstAllData, crewSelectionData, isSelected, btnDone);
                List<Object> lstData = getDataWithHeader(context, lstAllData, isVisibleSelectedCrews());
                if (edtSearch.getText() != null && !TextUtils.isEmpty(edtSearch.getText().toString())) {
                    updateList(lstData, false);
                    edtSearch.setSelection(edtSearch.getText().length());
                    filterData(edtSearch.getText().toString().trim());
                } else {
                    updateList(lstData, true);
                }
            }

            @Override
            public void onHeaderImageClicked(int position, View view) {
                showSelectedCrews(true);
            }
        };

        rvCrew.setAdapter(crewSelectionAdapter);
        manageCrewRecycleItemDecoration(rvCrew, crewSelectionAdapter);
        //Add Headers in Data
        List<Object> lstData = getDataWithHeader(context, lstAllData, lstPreSelectedCrewData.size() > 0);
        crewSelectionAdapter.updateList(lstData, true);

        crewSelectionAdapter.filterData("");
        crewSelectionAdapter.showSelectedCrews(lstPreSelectedData.size() > 0);
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
        manageCrewSearchInCrewListingDialog(edtSearch, crewSelectionAdapter);
        btnDone.setOnClickListener(view -> {
            llMultiSelect.setEnabled(true);
            List<CrewSelectionData> lstSelectedData = getFinalCrewSelection(lstData);
            if (lstSelectedData.size() > 0) {
                List<CrewSelectionData> lstFinalSelected = new ArrayList<>(lstSelectedData);
                List<Integer> lstUnselected = getUnselectedCrewIds(false, lstFinalSelected, lstPreSelectedCrewData, false);
                List<Integer> lstNewSelectedIds = getNewSelectedCrewData(lstFinalSelected);
                Collections.sort(lstSelectedData, (employees1, employees2) -> employees1.getCrewName().compareTo(employees2.getCrewName()));
                onMultiSelected.onSelected(lstSelectedData, lstSelectedData, lstUnselected, lstNewSelectedIds);
                bsdCrewSelection.dismiss();
            }

        });
        btnCancel.setOnClickListener(view -> {
            bsdCrewSelection.dismiss();
            onMultiSelected.onCancel();
        });

        bsdCrewSelection.setOnDismissListener(dialogInterface -> {
            onMultiSelected.onCancel();
        });
        bsdCrewSelection.setOnShowListener(dialog -> llMultiSelect.setEnabled(true));
        if (!context.isFinishing()) {
            bsdCrewSelection.show();
            bsdCrewSelection.setCanceledOnTouchOutside(false);
            bsdCrewSelection.setCancelable(false);
        }
    }

    private static void manageCrewRecycleItemDecoration(RecyclerView rvCrew, CrewSelectionAdapter crewSelectionAdapter) {
        rvCrew.addItemDecoration(new StickHeaderAddCrewItemDecoration(rvCrew, crewSelectionAdapter, new OnStickyHeaderCrewListener() {
            @Override
            public void OnStickyHeaderClicked(AppCompatTextView view, ImageView ivArrow, boolean isOpened) {
                crewSelectionAdapter.showSelectedCrews(isOpened);
            }

            @Override
            public void OnStickyHeaderArrowClicked(int headerPosition, ImageView ivArrow, boolean isSelected) {
                crewSelectionAdapter.showSelectedCrews(isSelected);
            }
        }));

    }

    public static void addCrewInCrewSelectionData(List<Object> lstAllData, List<Employees> lstPreSelectedData,
                                                  List<CrewSelectionData> lstPreSelectedCrewData, List<Employees> lstEmployee) {

        for (Employees employees : lstPreSelectedData) {
            CrewSelectionData selectionData = new CrewSelectionData();
            selectionData.setCrew(employees.isCrew());
            selectionData.setSelected(true);
            selectionData.setCrewId(employees.getEmployeeID());
            selectionData.setUsertype(Constants.CrewSelectionStatus.SelectedCrew.toString());
            selectionData.setCrewName(StaticUtils.getSingleStringFromTwoInput(employees.getEmployeeFirstName(), employees.getEmployeeLastName()));
            selectionData.setEmployeeSearchID(employees.getEmployeeSearchID());
            selectionData.setEmployeeFirstName(employees.getEmployeeFirstName());
            selectionData.setEmployeeLastName(employees.getEmployeeLastName());
            lstAllData.add(selectionData);
            lstPreSelectedCrewData.add(selectionData);
        }
        for (Employees employees : lstEmployee) {
            CrewSelectionData selectionData = new CrewSelectionData();
            selectionData.setCrew(employees.isCrew());
            selectionData.setSelected(false);
            selectionData.setCrewId(employees.getEmployeeID());
            selectionData.setUsertype(Constants.CrewSelectionStatus.NormalCrew.toString());
            selectionData.setCrewName(StaticUtils.getSingleStringFromTwoInput(employees.getEmployeeFirstName(), employees.getEmployeeLastName()));
            selectionData.setEmployeeSearchID(employees.getEmployeeSearchID());
            selectionData.setEmployeeFirstName(employees.getEmployeeFirstName());
            selectionData.setEmployeeLastName(employees.getEmployeeLastName());
            lstAllData.add(selectionData);
        }

    }

    public static void showCrewSelectionView(Activity context, String formName, LinearLayout llMultiSelect, List<Employees> lstPreSelectedItems, List<Employees> lstItems, boolean isCrewNotSelected, OnMultiCrewSelected onMultiSelected) {
        if (!checkActivityStatus(context))
            return;
        isHide = false;

        List<Object> lstAllData = new ArrayList<>();
        List<Employees> lstPreSelectedData = new ArrayList<>(lstPreSelectedItems);
        List<CrewSelectionData> lstPreSelectedCrewData = new ArrayList<>();
        BottomSheetDialog bsdCrewSelection = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_extended_crew, null);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        AppCompatEditText edtSearch = sheetView.findViewById(R.id.edtSearch);
        RecyclerView rvCrew = sheetView.findViewById(R.id.rvCrew);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        rvCrew.setLayoutManager(new LinearLayoutManager(context));
        // btnDone.setText(context.getString(R.string.txt_add));
        disableEnableColor(btnDone, context, false);
        addCrewInCrewSelectionData(lstAllData, lstPreSelectedData, lstPreSelectedCrewData, lstItems);
        CrewSelectionAdapter crewSelectionAdapter = new CrewSelectionAdapter(context) {
            @Override
            public void onItemClicked(List<Object> lstAllData, int position, CrewSelectionData crewSelectionData, boolean isSelected) {
                manageCrewAdapterItemClicked(context, lstAllData, crewSelectionData, isSelected, btnDone);
                List<Object> lstData = getDataWithHeader(context, lstAllData, isVisibleSelectedCrews());
                if (edtSearch.getText() != null && !TextUtils.isEmpty(edtSearch.getText().toString())) {
                    updateList(lstData, false);
                    filterData(edtSearch.getText().toString());
                } else {
                    updateList(lstData, true);
                }
            }

            @Override
            public void onHeaderImageClicked(int position, View view) {
                showSelectedCrews(true);
            }
        };

        rvCrew.setAdapter(crewSelectionAdapter);
        manageCrewRecycleItemDecoration(rvCrew, crewSelectionAdapter);
        //Add Headers in Data
        List<Object> lstData = getDataWithHeader(context, lstAllData, lstPreSelectedCrewData.size() > 0);
        crewSelectionAdapter.updateList(lstData, true);
        bsdCrewSelection.setContentView(sheetView);
        bsdCrewSelection.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        crewSelectionAdapter.filterData("");
        crewSelectionAdapter.showSelectedCrews(lstPreSelectedData.size() > 0);
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
        manageCrewSearchInCrewListingDialog(edtSearch, crewSelectionAdapter);
        disableEnableColor(btnDone, context, lstPreSelectedData.size() > 0);
        btnDone.setOnClickListener(view -> {
            llMultiSelect.setEnabled(true);
            List<CrewSelectionData> lstSelectedData = getFinalCrewSelection(lstData);
            if (lstSelectedData.size() > 0) {
                List<CrewSelectionData> lstFinalSelected = new ArrayList<>(lstSelectedData);
                List<Integer> lstUnselected = getUnselectedCrewIds(true, lstFinalSelected, lstPreSelectedCrewData, isCrewNotSelected);
                List<Integer> lstNewSelectedIds = getNewSelectedCrewData(lstFinalSelected);
                Collections.sort(lstSelectedData, (employees1, employees2) -> employees1.getCrewName().compareTo(employees2.getCrewName()));
                onMultiSelected.onSelected(lstSelectedData, lstSelectedData, lstUnselected, lstNewSelectedIds);
                bsdCrewSelection.dismiss();
            }
        });
        btnCancel.setOnClickListener(view -> bsdCrewSelection.dismiss());
        bsdCrewSelection.setOnDismissListener(dialogInterface -> {
            onMultiSelected.onCancel();
        });
        bsdCrewSelection.setOnShowListener(dialog -> llMultiSelect.setEnabled(true));
        if (!context.isFinishing()) {
            bsdCrewSelection.show();
            bsdCrewSelection.setCanceledOnTouchOutside(false);
        }
    }

    private static List<Integer> getUnselectedCrewIds(boolean considerSelectedCrew, List<CrewSelectionData> lstFinalSelected, List<CrewSelectionData> lstPreSelectedCrewData, boolean isCrewNotSelected) {
        List<Integer> lstUnselected = new ArrayList<>();
        if (considerSelectedCrew) {
            if (!isCrewNotSelected) {
                for (CrewSelectionData data : lstPreSelectedCrewData) {
                    if (lstFinalSelected.contains(data)) {
                        lstFinalSelected.remove(data);
                    } else {
                        lstUnselected.add(data.getCrewId());
                    }
                }
            }
        } else {
            for (CrewSelectionData data : lstPreSelectedCrewData) {
                if (lstFinalSelected.contains(data)) {
                    lstFinalSelected.remove(data);
                } else {
                    lstUnselected.add(data.getCrewId());
                }
            }
        }

        return lstUnselected;
    }

    private static List<CrewSelectionData> getFinalCrewSelection(List<Object> lstData) {
        List<CrewSelectionData> lstSelectedData = new ArrayList<>();
        for (Object data : lstData) {
            if (data instanceof CrewSelectionData) {
                CrewSelectionData selectionData = (CrewSelectionData) data;
                if (selectionData.isSelected()) {
                    lstSelectedData.add(selectionData);
                }
            }
        }

        return lstSelectedData;
    }

    private static List<Integer> getNewSelectedCrewData(List<CrewSelectionData> lstFinalSelected) {
        List<Integer> lstNewSelectedIds = new ArrayList<>();
        for (CrewSelectionData data : lstFinalSelected) {
            lstNewSelectedIds.add(data.getCrewId());
        }
        return lstNewSelectedIds;
    }

    public static void showClockExtendedCrewSelectionView(Activity context, List<Employees> lstExtendedCrew, List<Integer> lstSelectedCrew, final OnClockScreenMultiCrewSelected onMultiSelected) {
        if (!checkActivityStatus(context))
            return;
        isHide = false;
        List<Object> lstPreSelectedCrew = new ArrayList<>();
        BottomSheetDialog bsdClockCrew = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_extended_crew, null);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        AppCompatEditText edtSearch = sheetView.findViewById(R.id.edtSearch);
        RecyclerView rvCrew = sheetView.findViewById(R.id.rvCrew);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        rvCrew.setLayoutManager(new LinearLayoutManager(context));
        PopUtils.setLoggedInDataAtFirst(lstExtendedCrew, false);
        btnDone.setText(context.getString(R.string.txt_add));
        disableEnableColor(btnDone, context, false);

        // Remove already added crews from list those are added in clock screen.
        for (Iterator<Employees> it = lstExtendedCrew.iterator(); it.hasNext(); ) {
            Employees employees = it.next();
            for (Integer crewIds : lstSelectedCrew) {
                if (employees.getEmployeeID() == crewIds) {
                    it.remove();
                    break;
                }
            }
        }
        lstSelectedCrew.clear();

        for (Employees employees : lstExtendedCrew) {
            CrewSelectionData selectionData = new CrewSelectionData();
            selectionData.setCrew(employees.isCrew());
            selectionData.setSelected(false);
            selectionData.setCrewId(employees.getEmployeeID());
            selectionData.setUsertype(Constants.CrewSelectionStatus.NormalCrew.toString());
            selectionData.setCrewName(StaticUtils.getSingleStringFromTwoInput(employees.getEmployeeFirstName(), employees.getEmployeeLastName()));
            selectionData.setEmployeeSearchID(employees.getEmployeeSearchID());
            selectionData.setEmployeeFirstName(employees.getEmployeeFirstName());
            selectionData.setEmployeeLastName(employees.getEmployeeLastName());
            lstPreSelectedCrew.add(selectionData);
        }

        ExtendedCrewSelectionAdapter multiSelectAdapter = new ExtendedCrewSelectionAdapter(context) {
            @Override
            public void onItemClicked(List<Object> lstAllData, int position, View view, CrewSelectionData crewSelectionData, boolean isSelected) {
                int pos = lstAllData.indexOf(crewSelectionData);
                crewSelectionData.setSelected(isSelected);
                lstAllData.set(pos, crewSelectionData);
                disableEnableColor(btnDone, context, isAnyCrewSelected(lstAllData));
                List<Object> lstData = getDataWithHeader(context, lstAllData, isVisibleSelectedCrews());
                if (edtSearch.getText() != null && !TextUtils.isEmpty(edtSearch.getText().toString())) {
                    updateList(lstData, false);
                    filterData(edtSearch.getText().toString());
                } else {
                    updateList(lstData, true);
                }
                disableEnableColor(btnDone, context, lstPreSelectedCrew.size() > 0);
            }

            @Override
            public void onHeaderImageClicked(int position, View view) {
                showSelectedCrews(true);
            }
        };

        rvCrew.setAdapter(multiSelectAdapter);
        rvCrew.addItemDecoration(new StickHeaderAddCrewItemDecoration(rvCrew, multiSelectAdapter, new OnStickyHeaderCrewListener() {
            @Override
            public void OnStickyHeaderClicked(AppCompatTextView view, ImageView ivArrow, boolean isOpened) {
                multiSelectAdapter.showSelectedCrews(isOpened);
            }

            @Override
            public void OnStickyHeaderArrowClicked(int headerPosition, ImageView ivArrow, boolean isSelected) {
                multiSelectAdapter.showSelectedCrews(isSelected);
            }
        }));
        //Add Headers in Data
        List<Object> lstData = getDataWithHeader(context, lstPreSelectedCrew, false);
        multiSelectAdapter.updateList(lstData, true);
        bsdClockCrew.setContentView(sheetView);
        bsdClockCrew.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        multiSelectAdapter.filterData("");
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String text = charSequence.toString();
                if (text.length() > 0) {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                } else {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                }
                multiSelectAdapter.filterData(charSequence);

            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        btnDone.setOnClickListener(view -> {
            List<CrewSelectionData> lstSelectedData = new ArrayList<>();
            for (Object data : lstData) {
                if (data instanceof CrewSelectionData) {
                    CrewSelectionData selectionData = (CrewSelectionData) data;
                    if (selectionData.isSelected()) {
                        lstSelectedData.add(selectionData);
                    }
                }
            }
            if (!lstSelectedData.isEmpty()) {
                onMultiSelected.onSelected(lstSelectedData);
                bsdClockCrew.dismiss();
            }
        });
        btnCancel.setOnClickListener(view -> bsdClockCrew.dismiss());
        if (!context.isFinishing()) {
            bsdClockCrew.show();
            bsdClockCrew.setCanceledOnTouchOutside(false);
        }
    }


    private static List<Object> getDataWithHeader(Context context, List<Object> lstData, boolean showSelectedVew) {
        //Add Headers in Data
        List<CrewSelectionData> lstSelectedData = new ArrayList<>();
        List<CrewSelectionData> lstUnSelectedData = new ArrayList<>();
        if (!lstData.isEmpty()) {
            for (Object data : lstData) {
                if (data instanceof CrewSelectionData) {
                    CrewSelectionData selectionData = (CrewSelectionData) data;
                    if (selectionData.isSelected()) {
                        lstSelectedData.add(selectionData);
                        ((CrewSelectionData) data).setUsertype(Constants.CrewSelectionStatus.SelectedCrew.toString());
                    } else {
                        lstUnSelectedData.add(selectionData);
                        ((CrewSelectionData) data).setUsertype(Constants.CrewSelectionStatus.NormalCrew.toString());
                    }
                }
            }

            lstData.clear();
        }

        CrewSelectionHeader selectionHeader = new CrewSelectionHeader();
        selectionHeader.setTitle(context.getString(R.string.selected_crew, lstSelectedData.size()));
        selectionHeader.setHeaderId(1);
        selectionHeader.setSelected(showSelectedVew);
        lstData.add(selectionHeader);
        lstData.addAll(lstSelectedData);
        if (!lstUnSelectedData.isEmpty()) {
            CrewSelectionHeader selectionHeader2 = new CrewSelectionHeader();
            selectionHeader2.setTitle(context.getString(R.string.txt_crew));
            selectionHeader2.setHeaderId(2);
            lstData.add(selectionHeader2);
            lstData.addAll(lstUnSelectedData);
        }
        return lstData;
    }


    private static boolean isAnyCrewSelected(List<Object> lstData) {
        for (Object data : lstData) {
            if (data instanceof CrewSelectionData) {
                CrewSelectionData selectionData = (CrewSelectionData) data;
                if (selectionData.isSelected()) {
                    return true;
                }
            }
        }
        return false;
    }


    public static void showTagSelectionView(Activity context, List<Tags> lstTags,
                                            final OnTagSelected onTagSelected) {
        if (!checkActivityStatus(context))
            return;
        BottomSheetDialog sheetDialog = new BottomSheetDialog(context);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_tag, null);

        AppCompatTextView tvTagDone = sheetView.findViewById(R.id.tvTagDone);
        RecyclerView rvTag = sheetView.findViewById(R.id.rvTag);
        rvTag.setLayoutManager(new LinearLayoutManager(context));
        TagAdapter tagAdapter = new TagAdapter(context, lstTags) {
            @Override
            public void selectItem(int position, Tags tag, boolean isSelected) {
                int pos = getData().indexOf(tag);
                getData().get(pos).setSelected(isSelected);
                onTagSelected.onItemSelected(isSelected);
            }
        };
        rvTag.setAdapter(tagAdapter);
        tagAdapter.updateList(lstTags);
        sheetDialog.setContentView(sheetView);
        sheetDialog.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        tvTagDone.setOnClickListener(view -> {
            onTagSelected.onSelected(tagAdapter.getData());
            sheetDialog.dismiss();
        });
        if (!context.isFinishing()) {
            sheetDialog.show();
            sheetDialog.setCanceledOnTouchOutside(false);
        }
    }

    public static int selectionPos = -1;

    public static void showBottomViewForMaterialSelect(Activity context, int selectedPosition,
                                                       final List<Material> lstItems, final OnMaterialSelect onMaterialSelect) {
        if (!checkActivityStatus(context))
            return;
        final BottomSheetDialog mBottomSheetDialog = new BottomSheetDialog(context);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_meterial_selection, null);

        selectionPos = selectedPosition;


        RecyclerView rvMaterial = sheetView.findViewById(R.id.rvMaterial);
        AppCompatButton btnDone = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        rvMaterial.setLayoutManager(new LinearLayoutManager(context));
        MaterialViewAdapter materialViewAdapter = new MaterialViewAdapter(context, lstItems, selectedPosition) {
            @Override
            public void itemClick(int position) {

                selectionPos = position;
                if (selectionPos == -1) {
                    disableEnableColor(btnDone, context, false);
                } else {
                    disableEnableColor(btnDone, context, true);
                }
            }
        };

        rvMaterial.setAdapter(materialViewAdapter);

        if (selectedPosition > 0) {
            try {
                RecyclerView.SmoothScroller smoothScroller = new
                        LinearSmoothScroller(context) {
                            @Override
                            protected int getVerticalSnapPreference() {
                                return LinearSmoothScroller.SNAP_TO_START;
                            }
                        };
                smoothScroller.setTargetPosition(selectedPosition);
                if (rvMaterial.getLayoutManager() != null)
                    rvMaterial.getLayoutManager().startSmoothScroll(smoothScroller);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }

        if (selectionPos == -1) {
            disableEnableColor(btnDone, context, false);
        } else {
            disableEnableColor(btnDone, context, true);
        }

        btnCancel.setOnClickListener(v -> mBottomSheetDialog.dismiss());

        btnDone.setOnClickListener(v -> {
            if (selectionPos != -1) {
                onMaterialSelect.onSelectedMaterial(selectionPos);
                mBottomSheetDialog.dismiss();
            }
        });

        AppCompatEditText edtSearch = sheetView.findViewById(R.id.edtSearch);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String text = charSequence.toString();
                materialViewAdapter.filter(charSequence.toString());
                if (text.length() > 0) {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                } else {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

        if (!context.isFinishing()) {
            mBottomSheetDialog.setContentView(sheetView);
            mBottomSheetDialog.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
            mBottomSheetDialog.show();
            mBottomSheetDialog.setCanceledOnTouchOutside(false);
        }

    }

    public static void disableEnableColor(AppCompatButton view, Context context, boolean isEnable) {
        if (isEnable) {
            view.setTextColor(context.getResources().getColor(R.color.white));

        } else {
            view.setTextColor(context.getResources().getColor(R.color.gray));
        }
    }


    public static void ShowMarkerPins(Activity context, List<MapPinData> lstPins, final OnMarkerPinSelected onMarkerPinSelected) {
        if (!checkActivityStatus(context))
            return;
        BottomSheetDialog sheetDialog = new BottomSheetDialog(context);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_marker_pins, null);

        AppCompatTextView tvMarkerDone = sheetView.findViewById(R.id.tvMarkerDone);
        RecyclerView rvMarkerPin = sheetView.findViewById(R.id.rvMarkerPin);
        rvMarkerPin.setLayoutManager(new LinearLayoutManager(context));
        MarkerPinAdapter tagAdapter = new MarkerPinAdapter(context, lstPins) {
            @Override
            public void selectItem(int position, MapPinData mapPinData, boolean isSelected) {
                int pos = getData().indexOf(mapPinData);
                for (int i = 0; i < lstPins.size(); i++) {
                    if (lstPins.get(i).isSelected()) {
                        lstPins.get(i).setSelected(false);
                        notifyItemChanged(i);
                    }
                }
                getData().get(pos).setSelected(isSelected);
                onMarkerPinSelected.onItemSelected(isSelected, mapPinData);
            }
        };
        rvMarkerPin.setAdapter(tagAdapter);
        tagAdapter.updateList(lstPins);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(rvMarkerPin.getContext(), 1);
        rvMarkerPin.addItemDecoration(dividerItemDecoration);
        sheetDialog.setContentView(sheetView);
        sheetDialog.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        tvMarkerDone.setOnClickListener(view -> {
            onMarkerPinSelected.onSelected(tagAdapter.getData());
            sheetDialog.dismiss();
        });
        if (!context.isFinishing()) {
            sheetDialog.show();
            sheetDialog.setCanceledOnTouchOutside(false);
        }
    }


    public static void showImageComponentsView(Activity context, List<ImageComponent> lstImageComponents, final OnImageComponentSelected onImageComponentSelected) {
        if (!checkActivityStatus(context))
            return;
        BottomSheetDialog sheetDialog = new BottomSheetDialog(context);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_image_component, null);

        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnSelectionCancel);
        RecyclerView rvPhotoComponent = sheetView.findViewById(R.id.rvPhotoComponent);
        rvPhotoComponent.setLayoutManager(new LinearLayoutManager(context));
        ImageComponentsAdapter imageComponentsAdapter = new ImageComponentsAdapter(context, lstImageComponents) {
            @Override
            public void selectItem(ImageComponent imageComponent) {
                onImageComponentSelected.onItemSelected(imageComponent);
                sheetDialog.dismiss();
            }
        };
        rvPhotoComponent.setAdapter(imageComponentsAdapter);
        imageComponentsAdapter.updateList(lstImageComponents);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(rvPhotoComponent.getContext(), 1);
        rvPhotoComponent.addItemDecoration(dividerItemDecoration);
        sheetDialog.setContentView(sheetView);
        sheetDialog.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        btnCancel.setOnClickListener(view -> {
            sheetDialog.dismiss();
        });
        if (!context.isFinishing()) {
            sheetDialog.show();
            sheetDialog.setCanceledOnTouchOutside(true);
        }
    }

    public static void showCrewAndPreSelectServiceViewForCheckIn(Activity context, boolean isTmForm, SiteData siteData, FormData formData, JSONArray preSelectArray,
                                                                 List<Employees> lstPreSelectedItems, List<Employees> lstEmployee, LinearLayout llMultiSelect, OnCrewAndServiceActionTapped onCrewAndServiceActionTapped) {
        if (!checkActivityStatus(context))
            return;

        List<Object> lstAllData = new ArrayList<>();
        List<Employees> lstPreSelectedData = new ArrayList<>(lstPreSelectedItems);
        List<CrewSelectionData> lstPreSelectedCrewData = new ArrayList<>();
        HashMap<View, Object> validationMap = new HashMap<>();
        Animation leftOutAnimation = AnimationUtils.loadAnimation(context, R.anim.left_out);
        Animation leftInAnimation = AnimationUtils.loadAnimation(context, R.anim.left_in);
        Animation rightInAnimation = AnimationUtils.loadAnimation(context, R.anim.right_in);
        Animation rightOutAnimation = AnimationUtils.loadAnimation(context, R.anim.right_out);

        BottomSheetDialog bsCrewService = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_crew_select_service, null);
        AppCompatButton btnCheckIn = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        AppCompatTextView tvTitle = sheetView.findViewById(R.id.tvTitle);
        ViewFlipper vfCrewService = sheetView.findViewById(R.id.vfCrewService);
        disableEnableColor(btnCheckIn, context, !lstPreSelectedData.isEmpty());
        btnCheckIn.setText(context.getString(R.string.next));
        btnCheckIn.setTag(1);
        btnCancel.setTag(1);
        View viewCrew = context.getLayoutInflater().inflate(R.layout.layout_crew_selection, vfCrewService, false);

        AppCompatEditText edtSearch = viewCrew.findViewById(R.id.edtSearch);
        RecyclerView rvCrew = viewCrew.findViewById(R.id.rvCrew);
        edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        rvCrew.setLayoutManager(new LinearLayoutManager(context));
        vfCrewService.addView(viewCrew);

        View viewService = context.getLayoutInflater().inflate(R.layout.layout_service, vfCrewService, false);
        LinearLayout llServiceRoot = viewService.findViewById(R.id.llServiceRoot);

        vfCrewService.addView(viewService);
        bsCrewService.setContentView(sheetView);
        bsCrewService.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);
        addCrewInCrewSelectionData(lstAllData, lstPreSelectedData, lstPreSelectedCrewData, lstEmployee);
        setServiceInView(context, isTmForm, siteData, formData, preSelectArray, validationMap, llServiceRoot);

        CrewSelectionAdapter crewSelectionAdapter = new CrewSelectionAdapter(context) {

            @Override
            public void onItemClicked(List<Object> lstAllData, int position, CrewSelectionData crewSelectionData, boolean isSelected) {
                manageCrewAdapterItemClicked(context, lstAllData, crewSelectionData, isSelected, btnCheckIn);
                List<Object> lstData = getDataWithHeader(context, lstAllData, isVisibleSelectedCrews());
                if (edtSearch.getText() != null && !TextUtils.isEmpty(edtSearch.getText().toString())) {
                    updateList(lstData, false);
                    edtSearch.setSelection(edtSearch.getText().length());
                    filterData(edtSearch.getText().toString().trim());
                } else {
                    updateList(lstData, true);
                }
            }

            @Override
            public void onHeaderImageClicked(int position, View view) {
                showSelectedCrews(true);
            }
        };

        rvCrew.setAdapter(crewSelectionAdapter);
        manageCrewRecycleItemDecoration(rvCrew, crewSelectionAdapter);
        //Add Headers in Data
        List<Object> lstData = getDataWithHeader(context, lstAllData, !lstPreSelectedCrewData.isEmpty());
        crewSelectionAdapter.updateList(lstData, true);

        crewSelectionAdapter.filterData("");
        crewSelectionAdapter.showSelectedCrews(!lstPreSelectedData.isEmpty());
        PopUtils.manageSearchViewCloseTouchEvent(edtSearch);
       manageCrewSearchInCrewListingDialog(edtSearch,crewSelectionAdapter);
        btnCheckIn.setOnClickListener(view -> {
            List<CrewSelectionData> lstSelectedData = getFinalCrewSelection(lstData);
            if ((int) btnCheckIn.getTag() == 1) {
                if (!lstSelectedData.isEmpty()) {
                    btnCheckIn.setText(context.getString(R.string.check_in));
                    btnCheckIn.setTag(2);
                    btnCancel.setTag(2);
                    btnCancel.setText(context.getString(R.string.back));
                    tvTitle.setText(context.getString(R.string.txt_select_services));
                    setFlipAnimation(vfCrewService, leftOutAnimation, leftInAnimation, rightInAnimation, rightOutAnimation, true);
                    vfCrewService.showNext();
                }
            } else {
                //Return if no any of one service is selected from list
                if (!isAnyServiceSelected(preSelectArray)) {
                    showForeGroundToast(context, context.getString(R.string.atleast_select_one_service_for_preselect_service), false);
                    return;
                }
                //Return if validation is false
                if (!isFormValidate(context, validationMap)) {
                    return;
                }

                //Return if required all services are not attempted
                if (!allRequiredServicesAreCompleted(context, preSelectArray))
                    return;

                if (!lstSelectedData.isEmpty()) {
                    List<CrewSelectionData> lstFinalSelected = new ArrayList<>(lstSelectedData);
                    List<Integer> lstUnselected = getUnselectedCrewIds(false, lstFinalSelected, lstPreSelectedCrewData, false);
                    List<Integer> lstNewSelectedIds = getNewSelectedCrewData(lstFinalSelected);
                    Collections.sort(lstSelectedData, (employees1, employees2) -> employees1.getCrewName().compareTo(employees2.getCrewName()));
                    onCrewAndServiceActionTapped.onSelected(lstSelectedData, lstSelectedData, lstUnselected, lstNewSelectedIds, preSelectArray);
                    llMultiSelect.setEnabled(true);
                    bsCrewService.dismiss();
                }
            }

        });

        btnCancel.setOnClickListener(view -> {
            if ((int) btnCancel.getTag() == 2) {
                btnCancel.setText(context.getString(R.string.txt_cancel));
                btnCancel.setTag(1);
                btnCheckIn.setTag(1);
                btnCheckIn.setText(context.getString(R.string.next));
                tvTitle.setText(context.getString(R.string.txt_crew_selector));
                setFlipAnimation(vfCrewService, leftOutAnimation, leftInAnimation, rightInAnimation, rightOutAnimation, false);
                vfCrewService.showPrevious();
            } else {
                btnCancel.setText(context.getString(R.string.back));
                onCrewAndServiceActionTapped.onCancel();
                llMultiSelect.setEnabled(true);
                bsCrewService.dismiss();
            }
        });
        bsCrewService.setOnDismissListener(dialogInterface -> {
            onCrewAndServiceActionTapped.onCancel();
        });
        if (!context.isFinishing()) {
            bsCrewService.show();
            bsCrewService.setCanceledOnTouchOutside(false);
            bsCrewService.setCancelable(false);
        }
    }

    private static void manageCrewSearchInCrewListingDialog(AppCompatEditText edtSearch, CrewSelectionAdapter crewSelectionAdapter) {
        edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                String text = charSequence.toString();
                if (text.length() > 0) {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                } else {
                    edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                }
                crewSelectionAdapter.filterData(charSequence);
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });

    }

    private static void manageCrewAdapterItemClicked(Activity activity, List<Object> lstAllData, CrewSelectionData crewSelectionData, boolean isSelected, AppCompatButton btnCheckIn) {
        int pos = lstAllData.indexOf(crewSelectionData);
        crewSelectionData.setSelected(isSelected);
        lstAllData.set(pos, crewSelectionData);
        disableEnableColor(btnCheckIn, activity, isAnyCrewSelected(lstAllData));

    }

    public static void showPreSelectServiceView(Activity context, boolean isTmForm, SiteData siteData, FormData formData, JSONArray preSelectArray, OnPreSelectServiceActionTapped onPreSelectServiceActionTapped) {
        if (!checkActivityStatus(context))
            return;
        BottomSheetDialog bsdPreSelectService = new BottomSheetDialog(context, R.style.BottomSheetDialogTheme);
        View sheetView = context.getLayoutInflater().inflate(R.layout.bottom_dialog_pre_select_services, null);
        AppCompatButton btnCheckin = sheetView.findViewById(R.id.btnCheckin);
        AppCompatButton btnCancel = sheetView.findViewById(R.id.btnCancel);
        LinearLayout llServiceRoot = sheetView.findViewById(R.id.llServiceRoot);
        HashMap<View, Object> validationMap = new HashMap<>();
        setServiceInView(context, isTmForm, siteData, formData, preSelectArray, validationMap, llServiceRoot);

        bsdPreSelectService.setContentView(sheetView);
        bsdPreSelectService.getBehavior().setPeekHeight(Constants.SCREEN_HEIGHT);

        btnCheckin.setOnClickListener(view -> {
            if (isAnyServiceSelected(preSelectArray)) {
                if (allRequiredServicesAreCompleted(context, preSelectArray)) {
                    if (isFormValidate(context, validationMap)) {
                        onPreSelectServiceActionTapped.onSelected(preSelectArray);
                        bsdPreSelectService.dismiss();
                    }
                }
            } else {
                showForeGroundToast(context, context.getString(R.string.atleast_select_one_service_for_preselect_service), false);
            }
        });

        btnCancel.setOnClickListener(view -> {
            onPreSelectServiceActionTapped.onCancel();
            bsdPreSelectService.dismiss();
        });
        if (!context.isFinishing()) {
            bsdPreSelectService.show();
            bsdPreSelectService.setCanceledOnTouchOutside(false);
        }

    }

    private static void setServiceInView(Activity activity, boolean isTmForm, SiteData siteData, FormData formData, JSONArray preSelectArray, HashMap<View, Object> validationMap, LinearLayout llServiceRoot) {
        int tag = 0;

        for (int i = 0; i < preSelectArray.length(); i++) {
            try {
                inputTypeService(activity, isTmForm, siteData, formData, tag, validationMap, (JSONObject) preSelectArray.get(i), llServiceRoot);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            tag++;
        }
    }

    private static boolean isAnyServiceSelected(JSONArray preSelectArray) {
        if (preSelectArray.length() > 0) {
            for (int i = 0; i < preSelectArray.length(); i++) {
                try {
                    JSONObject jsonObject = (JSONObject) preSelectArray.get(i);
                    TMService tMService = new Gson().fromJson(jsonObject.getString(Constants.TnMService), TMService.class);
                    if (tMService.isCompleted()) {
                        return true;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    private static boolean allRequiredServicesAreCompleted(Context context, JSONArray preSelectArray) {
        boolean servicesAreSelected = true;
        if (preSelectArray.length() > 0) {
            for (int i = 0; i < preSelectArray.length(); i++) {
                try {
                    JSONObject jsonObject = (JSONObject) preSelectArray.get(i);
                    TMService tMService = new Gson().fromJson(jsonObject.getString(Constants.TnMService), TMService.class);
                    if (!tMService.isCompleted() && jsonObject.has(Constants.ISREQUIRED) && jsonObject.getBoolean(Constants.ISREQUIRED)) {
                        showForeGroundToast(context, context.getString(R.string.required, tMService.getServiceName()), false);
                        return false;
                    }
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return servicesAreSelected;
    }

    public static void showYouAreOutsideSiteRadius(Context context, String siteRadiusString, String siteDistance){

        float distanceInMeters = Float.parseFloat(siteDistance);
        String distanceString = getDistanceByUnitInShort(context,distanceInMeters);

        double siteRadiusInMeters = Float.parseFloat(siteRadiusString);
        String siteRadiusFormat = getDistanceByUnitInShort(context,siteRadiusInMeters);
        PopUtils.showCustomTwoButtonAlertDialog(context, context.getString(R.string.app_name), context.getString(R.string.location_not_within_radius, siteRadiusFormat,distanceString), context.getString(R.string.ok)
                , null, false, false, (dialog, which) -> {
                    dialog.dismiss();
                }, null);
    }

    /// parameter "meter" must be in meters
    public static String getDistanceByUnitInShort(Context context,double meter) {
        boolean isUnitInMiles = AppPrefShared.getBoolean(Constants.IS_UNIT_IN_MILES, false);

        if (isUnitInMiles) {
            // 1 meter = 3.28084 feet
            double feet = meter * 3.28084;
            if (feet < 1000.0) {
                return String.format(Locale.US, "%.0f ", feet) + context.getString(R.string.feet);
            } else  {
                // 1 miles = 1609.3 meters
                double miles = meter / 1609.3;
                String formatted = (miles < 10)
                        ? String.format(Locale.US, "%.1f ", miles)
                        : String.format(Locale.US, "%,.0f ", miles);
                return formatted + context.getString(R.string.miles);
            }
        } else {
            if (meter < 1000) {
                return String.format(Locale.US, "%.0f ", meter) + "m";
            } else {
                double kilometers = meter / 1000;
                String formatted = (kilometers < 10)
                        ? String.format(Locale.US, "%.1f ", kilometers)
                        : String.format(Locale.US, "%,.0f ", kilometers);
                return formatted + "km";
            }
        }
    }

    public static void showGPSIsOFF(Context context){
        PopUtils.showCustomTwoButtonAlertDialog(context, context.getString(R.string.app_name), context.getString(R.string.location_not_available_for_form), context.getString(R.string.ok)
                , null, false, false, (dialog, which) -> {
                    dialog.dismiss();
                }, null);
    }

    public static void showLocationPermissionRequiredMessage(Activity activity){
        PopUtils.showCustomTwoButtonAlertDialog(activity, activity.getString(R.string.app_name), activity.getString(R.string.location_permission_not_available_for_form), activity.getString(R.string.allow_permission)
                , activity.getString(R.string.no), false, false, (dialog, which) -> {
                    dialog.dismiss();
                    openSettingScreen(activity,10101);
                }, (dialog, which) -> {
                    dialog.dismiss();
                });
    }
}
