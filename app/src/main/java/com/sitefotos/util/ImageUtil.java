package com.sitefotos.util;

import static com.sitefotos.Constants.IMAGEPATHLOW;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.location.Location;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.text.TextUtils;
import android.view.View;
import android.view.WindowManager;
import android.webkit.URLUtil;
import android.widget.ImageView;

import androidx.core.content.FileProvider;
import androidx.exifinterface.media.ExifInterface;

import com.bumptech.glide.Glide;
import com.bumptech.glide.Priority;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.request.RequestOptions;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class ImageUtil {
    public static final String FOLDER_NAME = "/Sitefotos/";
    public static final String MAP_PIN_FOLDER_NAME = "/MapPins/";

    public static String saveImageFromBitmap(Context context, Bitmap bitmap, String imagePath, boolean isHighResolutionImage) {

        File appDirectory = new File(getRootPath(context) + ".temp/");
        String fileName;
        appDirectory.mkdirs();
        if (TextUtils.isEmpty(imagePath)) {
            if (isHighResolutionImage) {
                fileName = "sitefotos_high_" + System.currentTimeMillis() + ".jpg";
            } else {
                fileName = "sitefotos_low_" + System.currentTimeMillis() + ".jpg";
            }
        } else {
            if (isHighResolutionImage) {
                fileName = "sitefotos_high_gallery_" + System.currentTimeMillis() + "_" + URLUtil.guessFileName(imagePath, null, null);
            } else {
                fileName = "sitefotos_low_gallery_" + System.currentTimeMillis() + "_" + URLUtil.guessFileName(imagePath, null, null);
            }
        }

        File file = new File(appDirectory, fileName);
        String filePath = file.getAbsolutePath();
        if (file.exists()) file.delete();
        try {
            FileOutputStream out = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 80, out);
            out.flush();
            out.close();

        } catch (SecurityException e) {
            FirebaseEventUtils.logException(e);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return filePath;
    }

    public static Bitmap createScaledBitmap(Bitmap bitmap) {
        Bitmap scaledBitmap = null;
        try {
            int heightBitmapOriginalMultipleImageSelect, widthBitmapOriginalMultipleImageSelect, heightBitmapLowMultipleImageSelect,
                    widthBitmapLowMultipleImageSelect;

            heightBitmapOriginalMultipleImageSelect = bitmap.getHeight();
            widthBitmapOriginalMultipleImageSelect = bitmap.getWidth();
            if (widthBitmapOriginalMultipleImageSelect > heightBitmapOriginalMultipleImageSelect) {
                // Width is bigger compared to height
                widthBitmapLowMultipleImageSelect = 500;
                heightBitmapLowMultipleImageSelect = (widthBitmapLowMultipleImageSelect *
                        heightBitmapOriginalMultipleImageSelect) / (widthBitmapOriginalMultipleImageSelect);
            } else {
                // Height is bigger compared to width
                // Width is bigger compared to height
                heightBitmapLowMultipleImageSelect = 500;
                widthBitmapLowMultipleImageSelect = (heightBitmapLowMultipleImageSelect * widthBitmapOriginalMultipleImageSelect) /
                        (heightBitmapOriginalMultipleImageSelect);
            }

            // Convert bitmap to lower resolution
            scaledBitmap = Bitmap.createScaledBitmap(bitmap, widthBitmapLowMultipleImageSelect,
                    heightBitmapLowMultipleImageSelect, false);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return scaledBitmap;

    }


    public static String saveImage(Context context, Bitmap bitmap, boolean savePermanent) {

        File appDirectory;
        if (savePermanent) {
            appDirectory = new File(getRootPathToSaveFilesInExternalStorage() + "Sitefotos - Camera Uploads/");
        } else {
            appDirectory = new File(getRootPath(context) + ".temp/");
        }
        String fileName;
        appDirectory.mkdirs();
        fileName = "sitefotos_" + System.currentTimeMillis() + ".jpg";

        File file = new File(appDirectory, fileName);
        String filePath = file.getAbsolutePath();
        if (file.exists()) file.delete();
        try {
            FileOutputStream out = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out);
            out.flush();
            out.close();
            if (savePermanent) {
                MediaScannerConnection.scanFile(context, new String[]{filePath}, null, null);
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return filePath;
    }


    public static void saveMapPinImage(Context context, Bitmap bitmap, String fileName) {

        File fileDirectory = new File(getMapPinRootFolder(context));
        if (!fileDirectory.exists()) {
            fileDirectory.mkdirs();
        }
        File file = new File(fileDirectory, fileName);
        try {
            FileOutputStream out = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out);
            out.flush();
            out.close();

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static String getRootPathToSaveFilesInExternalStorage() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES).toString() + FOLDER_NAME;
        }
        return Environment.getExternalStorageDirectory().toString() + FOLDER_NAME;

    }

    public static String getRootPath(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return context.getFilesDir() + FOLDER_NAME;
        }
        return Environment.getExternalStorageDirectory().toString() + FOLDER_NAME;
    }

    /**
     * Method to delete specified file from app folder in SD card.
     *
     * @param imagePath file path
     */
    public static void deleteImageFromSDCard(String imagePath) {

        if (TextUtils.isEmpty(imagePath)) {
            return;
        }
        File file = new File(imagePath);
        try {
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static void deleteImagesFromStorage(String formImageData) {
        if (TextUtils.isEmpty(formImageData))
            return;

        try {
            JSONObject imageData = new JSONObject(formImageData);
            JSONArray jsonArray = imageData.getJSONArray(Constants.DATA);
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject imageObject = jsonArray.getJSONObject(i);
                if (!TextUtils.isEmpty(imageObject.getString(Constants.IMAGEPATHHIGH)))
                    ImageUtil.deleteImageFromSDCard(imageObject.getString(Constants.IMAGEPATHHIGH));

                if (!TextUtils.isEmpty(imageObject.getString(IMAGEPATHLOW)))
                    ImageUtil.deleteImageFromSDCard(imageObject.getString(Constants.IMAGEPATHLOW));
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

    }

    /**
     * Method to delete all files from app folder in SD card.
     */
    public static void deleteAllFilesFromFolder(Context context) {
        try {
            File dir = new File(getRootPath(context) + "temp");
            if (dir.isDirectory()) {
                String[] children = dir.list();
                if (children != null) {
                    for (String aChildren : children) {
                        new File(dir, aChildren).delete();
                    }
                }
            }

            File dirHidden = new File(getRootPath(context) + ".temp");
            if (dirHidden.isDirectory()) {
                String[] children = dirHidden.list();
                if (children != null) {
                    for (String aChildren : children) {
                        new File(dirHidden, aChildren).delete();
                    }
                }
            }
        } catch (SecurityException e) {
            FirebaseEventUtils.logException(e);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /*public static int getPendingFileCount(Context context) {
        File[] list;
        File[] listHidden;

        int size = 0;
        try {
            File dirTemp = new File(getRootPath(context) + "temp");
            list = dirTemp.listFiles();
            if (list != null) {
                size = list.length;
            }

            File dirTempHidden = new File(getRootPath(context) + ".temp");
            listHidden = dirTempHidden.listFiles();
            if (listHidden != null) {
                size = size + listHidden.length;
            }

            return size;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }

        return size;
    }

    public static String readStringFromFile(String fileName, Context context) {
        StringBuilder returnString = new StringBuilder();
        InputStream fIn = null;
        InputStreamReader isr = null;
        BufferedReader input = null;
        try {
            fIn = context.getResources().getAssets().open(fileName, Context.MODE_WORLD_READABLE);
            isr = new InputStreamReader(fIn);
            input = new BufferedReader(isr);
            String line = "";
            while ((line = input.readLine()) != null) {
                returnString.append(line);
            }
        } catch (Exception e) {
            e.getMessage();
        } finally {
            try {
                if (isr != null)
                    isr.close();
                if (fIn != null)
                    fIn.close();
                if (input != null)
                    input.close();
            } catch (Exception e2) {
                e2.getMessage();
            }
        }
        return returnString.toString();
    }*/


    public static Bitmap getBitmapFromPath(String path) {
        Bitmap bmp = null;
        BitmapFactory.Options options = new BitmapFactory.Options();

        // down sizing image as it throws OutOfMemory Exception for larger images
        options.inSampleSize = 1;

        boolean retry = true;
        while (retry) {
            try {
                bmp = BitmapFactory.decodeFile(path, options);
                retry = false;
            } catch (OutOfMemoryError e) {
                e.printStackTrace();
                options.inSampleSize = options.inSampleSize * 2;

                // Break the loop if the inSampleSize is too high to avoid infinite loops
                if (options.inSampleSize > 32) {
                    retry = false;
                }
            }
        }
        return bmp;
    }

    public static Bitmap getScaledBitmap(Bitmap highBitmap, int originalBitmapWidth, int originalBitmapHeight) {

        Bitmap lowBitmap;
        int lowBitmapWidth, lowBitmapHeight;
        if (originalBitmapWidth > originalBitmapHeight) {
            // Width is bigger compared to height
            lowBitmapWidth = 500;
            lowBitmapHeight = (lowBitmapWidth * originalBitmapHeight) / (originalBitmapWidth);
        } else {
            // Height is bigger compared to width
            // Width is bigger compared to height
            lowBitmapHeight = 500;
            lowBitmapWidth = (lowBitmapHeight * originalBitmapWidth) / (originalBitmapHeight);
        }

        // Convert bitmap to lower resolution
        lowBitmap = Bitmap.createScaledBitmap(highBitmap, lowBitmapWidth, lowBitmapHeight, false);

        return lowBitmap;
    }

    public static void writeExifDataInImage(Context context, String path, double currentLatitude, double currentLongitude) {
        ExifInterface exif;
        try {
            exif = new ExifInterface(path);
            exif.setAttribute(ExifInterface.TAG_GPS_LATITUDE,
                    dec2DMS(currentLatitude));
            exif.setAttribute(ExifInterface.TAG_GPS_LONGITUDE,
                    dec2DMS(currentLongitude));
            if (currentLatitude > 0)
                exif.setAttribute(ExifInterface.TAG_GPS_LATITUDE_REF, "N");
            else
                exif.setAttribute(ExifInterface.TAG_GPS_LATITUDE_REF, "S");
            if (currentLongitude > 0)
                exif.setAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF, "E");
            else
                exif.setAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF, "W");

            exif.saveAttributes();

            try {
                MediaScannerConnection.scanFile(context, new String[]{path}, null, null);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        } catch (IOException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private static String dec2DMS(double coord) {
        coord = coord > 0 ? coord : -coord;  // -105.9876543 -> 105.9876543
        String sOut = (int) coord + "/1,";   // 105/1,
        coord = (coord % 1) * 60;         // .987654321 * 60 = 59.259258
        sOut = sOut + (int) coord + "/1,";   // 105/1,59/1,
        coord = (coord % 1) * 60000;             // .259258 * 60000 = 15555
        sOut = sOut + (int) coord + "/1000";   // 105/1,59/1,15555/1000
        return sOut;
    }


    /**
     * Method to get location from exif info
     *
     * @param imagePath
     * @return return location in latitude and longitude format.
     */
    public static Location getLocationFromImage(String imagePath) {

        ExifInterface exif = null;

        try {
            exif = new ExifInterface(imagePath);
        } catch (IOException e) {
            FirebaseEventUtils.logException(e);
        }

        if (exif == null)
            return null;


        String sLat, sLatR, sLon, sLonR;
        try {
            sLat = exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE);
            sLon = exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE);
            sLatR = exif.getAttribute(ExifInterface.TAG_GPS_LATITUDE_REF);
            sLonR = exif.getAttribute(ExifInterface.TAG_GPS_LONGITUDE_REF);
        } catch (Exception e) {
            return null;
        }
        double lat = 0, lon = 0;
        if (!TextUtils.isEmpty(sLat)) {
            lat = dms2Dbl(sLat);
            if (lat > 180.0) return null;
        }
        if (!TextUtils.isEmpty(sLon)) {
            lon = dms2Dbl(sLon);
            if (lon > 180.0) return null;
        }
        if (lat != 0 || lon != 0) {
            if (sLatR != null && !TextUtils.isEmpty(sLatR)) {
                lat = sLatR.contains("S") ? -lat : lat;
            }
            if (sLonR != null && !TextUtils.isEmpty(sLonR)) {
                lon = sLonR.contains("W") ? -lon : lon;
            }

            if (lat != 0 || lon != 0) {
                Location location = new Location("exif");
                location.setLatitude(lat);
                location.setLongitude(lon);
                return location;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }


    private static double dms2Dbl(String sDMS) {
        double dRV = 999.0;
        try {
            String[] DMSs = sDMS.split(",", 3);
            String s[] = DMSs[0].split("/", 2);
            dRV = (Double.parseDouble(s[0]) / Double.parseDouble(s[1]));
            s = DMSs[1].split("/", 2);
            dRV += ((Double.parseDouble(s[0]) / Double.parseDouble(s[1])) / 60);
            s = DMSs[2].split("/", 2);
            dRV += ((Double.parseDouble(s[0]) / Double.parseDouble(s[1])) / 3600);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return dRV;
    }


    public static void recycleBitmap(Bitmap bitmap) {
        if (bitmap != null && !bitmap.isRecycled()) {
            bitmap.recycle();
        }
    }

    public static Bitmap loadBitmapFromView(View v,boolean highResolution) {
        if (v.getMeasuredWidth() > 0 && v.getMeasuredHeight() > 0) {
            Bitmap b;
            if (highResolution){
                b = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
            }else{
                b = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.ARGB_4444);
            }
            Canvas c = new Canvas(b);
            v.layout(v.getLeft(), v.getTop(), v.getRight(), v.getBottom());
            v.draw(c);
            return b;

        } else {
            v.measure(WindowManager.LayoutParams.WRAP_CONTENT, WindowManager.LayoutParams.WRAP_CONTENT);
            Bitmap b;
            if (highResolution){
                b = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.ARGB_8888);
            }else{
                b = Bitmap.createBitmap(v.getMeasuredWidth(), v.getMeasuredHeight(), Bitmap.Config.ARGB_4444);
            }
            Canvas c = new Canvas(b);
            v.layout(0, 0, v.getMeasuredWidth(), v.getMeasuredHeight());
            v.draw(c);
            return b;
        }
    }

    public static void loadImageInGlide(ImageView ivView, String imageUrl, boolean shouldAnimate) {
        try {
            if (TextUtils.isEmpty(imageUrl))
                return;
            RequestOptions requestOptions = new RequestOptions()
                    .centerCrop()
                    .placeholder(R.drawable.img_placeholder)
                    .priority(Priority.NORMAL)
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC);

            if (!shouldAnimate) {
                requestOptions.dontAnimate();
            }
            Glide.with(ivView.getContext())
                    .load(new File(imageUrl))
                    .apply(requestOptions)
                    .into(ivView);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static void loadImageInGlide(ImageView ivView, String imageUrl) {
        loadImageInGlide(ivView, imageUrl, true);
    }

    public static void loadImagePathInGlide(ImageView ivView, String imageUrl) {
        try {
            if (TextUtils.isEmpty(imageUrl))
                return;
            RequestOptions requestOptions = new RequestOptions()
                    .centerCrop()
                    .placeholder(R.drawable.img_placeholder)
                    .priority(Priority.NORMAL)
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC);

            Glide.with(ivView.getContext())
                    .load(imageUrl)
                    .apply(requestOptions)
                    .into(ivView);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static void loadActualImageInGlide(ImageView ivView, String imageUrl) {
        try {
            if (TextUtils.isEmpty(imageUrl))
                return;
            RequestOptions requestOptions = new RequestOptions()
                    .priority(Priority.NORMAL)
                    .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC);

            Glide.with(ivView.getContext())
                    .load(imageUrl)
                    .apply(requestOptions)
                    .into(ivView);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static void shareShareImage(Context context, boolean isEmail, String captionDetails, String strMediaPath, Bitmap bitmapCapturedImageWithDrawing) {

        // Get captured image bitmap here for sharing
        if (strMediaPath.trim().isEmpty()) {
            try {
                if (PermissionUtils.hasStoragePermissions(context)) {
                    strMediaPath = saveImage(context, bitmapCapturedImageWithDrawing, false);
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }

        if (!strMediaPath.trim().isEmpty()) {
            Uri uriBitmapMedia;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                if (!strMediaPath.contains("content://"))
                    uriBitmapMedia = uriFromFile(context, strMediaPath);
                else
                    uriBitmapMedia = Uri.parse(strMediaPath);
            } else {
                if (!strMediaPath.contains("file://"))
                    uriBitmapMedia = Uri.fromFile(new File(strMediaPath));
                else
                    uriBitmapMedia = Uri.parse(strMediaPath);
            }

            if (isEmail) {
                try {
                    Intent intent = new Intent();
                    // Create share intent as described above
                    if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
                        intent.setAction(Intent.ACTION_SEND);
                    } else {
                        intent.setAction(Intent.ACTION_SENDTO);

                    }
                    intent.setType("application/image");
                    intent.putExtra(Intent.EXTRA_SUBJECT, context.getResources().getString(R.string.app_name));
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.addFlags(Intent.FLAG_GRANT_WRITE_URI_PERMISSION);
                    intent.putExtra(Intent.EXTRA_STREAM, uriBitmapMedia);
                    intent.putExtra(Intent.EXTRA_TEXT, captionDetails.trim());
                    context.startActivity(Intent.createChooser(intent, context.getString(R.string.share_using)));
                } catch (Resources.NotFoundException e) {
                    FirebaseEventUtils.logException(e);
                }
            } else {
                try {
                    // Create share intent as described above
                    Intent intent = new Intent(Intent.ACTION_SEND);
                    intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
                    intent.setType("image/gif");
                    intent.putExtra(Intent.EXTRA_STREAM, uriBitmapMedia);
                    intent.putExtra(Intent.EXTRA_TEXT, captionDetails.trim());
                    context.startActivity(Intent.createChooser(intent, context.getString(R.string.share_using)));
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
    }

    /**
     * this method is convert internal storage file path in to url using file provider
     *
     * @param context application context
     * @param path    file path
     * @return file uri
     */
    public static Uri uriFromFile(Context context, String path) {
        if (path == null) return null;
        try {
            return FileProvider.getUriForFile(context, BuildConfig.APPLICATION_ID + ".provider", new File(path));
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            /*try {
                return Uri.fromFile(new File(path));
            } catch (Exception exception) {
                exception.printStackTrace();
                return null;
            }*/
        }
        return null;
    }

    /**
     * Method to get new image path after rotate image if device manufacturer is samsung.
     * else it returns same image path and skip re-save operation.
     *
     * @param mContext instance of activity
     * @param filePath file path of current image.
     * @return returns new path if image rotated else return same path which received as a input parameter.
     */
    @SuppressWarnings("unused")
    public static String rotateImageAndGetNewPath(Context mContext, String filePath) {
        String manufacturer = Build.MANUFACTURER;
        String model = Build.MODEL;
        String finalFilePath;
        Uri IMAGE_CAPTURE_URI = Uri.parse(filePath);

        if (manufacturer.equalsIgnoreCase("samsung") || model.equalsIgnoreCase("samsung")) {
            int rotation = getCameraPhotoOrientation(mContext, IMAGE_CAPTURE_URI, IMAGE_CAPTURE_URI.getPath());
            Matrix matrix = new Matrix();

            if (model.equalsIgnoreCase("GT-I9500")) {
                if (rotation == 0)
                    matrix.postRotate(90);
                else
                    matrix.postRotate(rotation);
            } else {
                matrix.postRotate(rotation);
            }

            final Bitmap bitmap = BitmapFactory.decodeFile(IMAGE_CAPTURE_URI.getPath());
            if (bitmap == null) {
                finalFilePath = IMAGE_CAPTURE_URI.getPath();
                return finalFilePath;
            }
            Bitmap rotatedBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);


            File newFile = null;
            try {
                newFile = saveImageToSDCard(rotatedBitmap, IMAGE_CAPTURE_URI, mContext);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
                FirebaseEventUtils.logException(e);
            }

            // Recycle the original bitmap
            if (!bitmap.isRecycled()) {
                bitmap.recycle();
            }

            // Recycle the rotated bitmap
            if (!rotatedBitmap.isRecycled()) {
                rotatedBitmap.recycle();
            }

            if (newFile != null) {
                finalFilePath = newFile.getPath();
            } else {
                finalFilePath = IMAGE_CAPTURE_URI.getPath();
            }
        } else {
            finalFilePath = IMAGE_CAPTURE_URI.getPath();
        }
        return finalFilePath;
    }

    /**
     * Method to save image in internal storage.
     * If any file is exist in this location then it will be deleted .
     * extension of image which will be saved is png.
     *
     * @param bitmap            image object which is stored in internal storage
     * @param IMAGE_CAPTURE_URI Uri where want to save image object.
     * @param context           instance of activity for send broadcast after image saved successfully
     * @return file object after image save in requested uri.
     */
    @SuppressWarnings("unused")
    private static File saveImageToSDCard(Bitmap bitmap, Uri IMAGE_CAPTURE_URI, Context context) {
        try {
            if (IMAGE_CAPTURE_URI == null)
                return null;
            String current;
            current = new File(IMAGE_CAPTURE_URI.getPath()).getName();
            File appDirectory = new File(getRootPath(context) + ".temp/");
            appDirectory.mkdirs();
            File file = new File(appDirectory, current);
            FileOutputStream fOut = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 70, fOut);
            fOut.flush();
            fOut.close();
            context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.fromFile(file)));
            return file;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            return null;
        }
    }

    /**
     * This method is used get orientation of camera photo
     *
     * @param context   context
     * @param imageUri  This parameter is Uri type
     * @param imagePath This parameter is String type
     * @return rotate
     */
    @SuppressWarnings("unused")
    private static int getCameraPhotoOrientation(Context context, Uri imageUri, String imagePath) {
        int rotate = 0;
        try {
           /* try {
                if (imageUri != null)
                    context.getContentResolver().notifyChange(imageUri, null);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }*/
            File imageFile = new File(imagePath);
            ExifInterface exif = new ExifInterface(imageFile.getAbsolutePath());
            int orientation = exif.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_270:
                    rotate = 270;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    rotate = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_90:
                    rotate = 90;
                    break;
                case ExifInterface.ORIENTATION_NORMAL:
                    rotate = 0;
                    break;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return rotate;
    }

    public static String getRootPathToSaveDocumentInExternalStorage() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS).toString() + FOLDER_NAME;
        }
        return Environment.getExternalStorageDirectory().toString() + FOLDER_NAME;

    }

    public static boolean checkIfImageExists(Context context, String fileName) {
        //Check Parent Directory Folder. If it is not exists, Return with false
        File mapPinDirectory = new File(getMapPinRootFolder(context));
        if (!mapPinDirectory.exists())
            return false;

        File searchingFile = new File(fileName);
        //try to get file from directory with searching file name.
        File searchLocationFile = new File(context.getFilesDir() + FOLDER_NAME + MAP_PIN_FOLDER_NAME + "/" + searchingFile.getName());

        return searchLocationFile.exists();
    }

    public static String getMapPinRootFolder(Context context) {
        return context.getFilesDir() + MAP_PIN_FOLDER_NAME;
    }

    public static BitmapDescriptor getMarkerBitmapFromPath(String path) {
        if (!TextUtils.isEmpty(path)) {
            Bitmap bitmap = BitmapFactory.decodeFile(path);
            if (bitmap != null && bitmap.getWidth() >0) {
                Bitmap smallMarker = Bitmap.createScaledBitmap(bitmap, bitmap.getWidth() * 2, bitmap.getHeight() * 2, false);
                return BitmapDescriptorFactory.fromBitmap(smallMarker);
            }
        }
        return BitmapDescriptorFactory.fromResource(R.drawable.icn_marker_blue);
    }

}