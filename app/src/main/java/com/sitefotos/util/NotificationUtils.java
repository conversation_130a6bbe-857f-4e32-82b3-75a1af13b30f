package com.sitefotos.util;

import static android.content.Context.ACTIVITY_SERVICE;

import android.app.ActivityManager;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.sitefotos.Constants;
import com.sitefotos.notification.LocalNotificationReceiver;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.logger.CustomLogKt;

import java.util.Calendar;
import java.util.List;

public class NotificationUtils {

    /**
     * Set Local Notification for Appointment.
     *
     * @param context    of Activity or Fragment
     * @param timeMillis timeInMillis
     */
    public static void setLocalNotification(Context context, long timeMillis) {
        try {
            //Format : yyyy-MM-dd
            String targetDate = DateUtil.getDateInYYYYMMDD(timeMillis);
            //Format : hh:mm
            String targetTime = DateUtil.getTimeInHHMM(timeMillis);
            Intent intent = new Intent(context.getApplicationContext(), LocalNotificationReceiver.class);

            int alarmManagerRequestId = 101010;

            AppPrefShared.putValue(Constants.PREF_NOTIFICATION_ID, 101010);

            PendingIntent pendingIntent;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pendingIntent = PendingIntent.getBroadcast(context.getApplicationContext(), alarmManagerRequestId, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
            } else {
                pendingIntent = PendingIntent.getBroadcast(context.getApplicationContext(), alarmManagerRequestId, intent, PendingIntent.FLAG_UPDATE_CURRENT);
            }
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            Calendar calNotificationTime = Calendar.getInstance();

            String[] dateSplit = targetDate.split("-");

            int year = Integer.parseInt(dateSplit[0]);
            int month = Integer.parseInt(dateSplit[1]);
            int day = Integer.parseInt(dateSplit[2]);

            int hour = Integer.parseInt(targetTime.split(":")[0].trim());
            int minutes = Integer.parseInt(targetTime.split(":")[1].trim());
            CustomLogKt.errorLog("NotificationTime::", "Time::" + day + ":" + month + ":" + year + ":" + hour + ":" + minutes);
            calNotificationTime.set(year, month - 1, day, hour, minutes, 0);
            if (alarmManager != null) {
                alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, calNotificationTime.getTimeInMillis(), pendingIntent);
                alarmManager.getNextAlarmClock();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static void removeLocalNotificationFromAlarmManagerIfSet(Context context) {
        try {
            int requestId = AppPrefShared.getInt(Constants.PREF_NOTIFICATION_ID, 0);

            if (requestId == 0)
                return;

            final int flag = PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE;
            Intent intent = new Intent(context, LocalNotificationReceiver.class);
            PendingIntent pendingIntent = PendingIntent.getActivity(context, requestId, intent, flag);
            pendingIntent.cancel();
            AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
            if (alarmManager != null) {
                alarmManager.cancel(pendingIntent);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static boolean checkIfActivityInForeGround(Context context) {
        try {
            ActivityManager activityManager = (ActivityManager) context.getSystemService(ACTIVITY_SERVICE);
            List<ActivityManager.RunningTaskInfo> taskList = null;
            if (activityManager != null) {
                taskList = activityManager.getRunningTasks(10);
            }
            if (taskList != null && !taskList.isEmpty()) {
                return taskList.get(0).topActivity.getClassName().contains(context.getApplicationContext().getPackageName());
            }
        } catch (SecurityException e) {
            FirebaseEventUtils.logException(e);
        }
        return false;
    }

}