package com.sitefotos.util.tags;


import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.LayoutRes;
import androidx.appcompat.widget.AppCompatTextView;

import com.sitefotos.R;
import com.sitefotos.models.Employees;

import java.util.ArrayList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;


public class CrewTagView extends ViewGroup implements Observer {
    private CrewTagViewAdapter mAdapter;
    private OnCrewTagClickListener mListener;
    private List<Integer> mLineHeightList;

    public CrewTagView(Context context) {
        super(context);
        init(context);
    }

    public CrewTagView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    public CrewTagView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        mLineHeightList = new ArrayList<>();
        setAdapter(new CrewTagViewAdapter(context) {
            @Override
            public int getLayoutRes(int position) {
                return 0;
            }

            @Override
            public int getBackgroundRes(int position) {
                return 0;
            }

            @Override
            public int getBackgroundColor(int position) {
                return 0;
            }

            @Override
            public int getBackgroundColorSelected(int position) {
                return 0;
            }

            @Override
            public void onLayout(View view, int position) {

            }
        });
    }

    @Override
    protected void onMeasure(final int widthMeasureSpec, final int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        measureChildren(widthMeasureSpec, heightMeasureSpec);

        mLineHeightList.clear();
        int width = getMeasuredWidth();
        int height = getPaddingTop() + getPaddingBottom();
        int lineHeight = 0;
        int lineWidth = 0;
        int childCount = getChildCount();

        for (int i = 0; i < childCount; i++) {
            View childView = getChildAt(i);
            MarginLayoutParams layoutParams = (MarginLayoutParams) childView.getLayoutParams();
            boolean lastChild = (i == childCount - 1);

            if (childView.getVisibility() == GONE) {
                if (lastChild)
                    mLineHeightList.add(lineHeight);

                continue;
            }
            int childWidth = (childView.getMeasuredWidth() + layoutParams.leftMargin + layoutParams.rightMargin);
            int childHeight = (childView.getMeasuredHeight() + layoutParams.topMargin + layoutParams.bottomMargin);
            lineHeight = Math.max(lineHeight, childHeight);

            if (childWidth > width / 2) {
                if (lineWidth < width / 2) {
                    if (i == 0) {
                        //layoutParams.width = (width / 2) - (layoutParams.leftMargin + layoutParams.rightMargin);
                        layoutParams.width = childWidth + (layoutParams.leftMargin + layoutParams.rightMargin);

                    } else {
                        layoutParams.width = width - lineWidth - (layoutParams.leftMargin + layoutParams.rightMargin);
                    }
                } else {
                    layoutParams.width = childWidth + (layoutParams.leftMargin + layoutParams.rightMargin);
                }
                layoutParams.setMargins(layoutParams.leftMargin, layoutParams.topMargin, layoutParams.rightMargin, layoutParams.bottomMargin);
                childView.setLayoutParams(layoutParams);
                AppCompatTextView hashTagName = childView.findViewById(R.id.tvCrewTag);
                if (hashTagName != null) {
                    LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) hashTagName.getLayoutParams();
                    params.weight = width;
                    hashTagName.setLayoutParams(params);
                }
            }

            if (childWidth > width)
                width = childWidth;

            if (lineWidth + childWidth > width) {
                mLineHeightList.add(lineHeight);
                lineWidth = childWidth;
            } else
                lineWidth += childWidth;

            if (lastChild)
                mLineHeightList.add(lineHeight);
        }

        for (Integer h : mLineHeightList)
            height += h;

        setMeasuredDimension(width, height);
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        if (mAdapter != null) {
            int width = getMeasuredWidth();
            int lineWidth = getPaddingLeft();
            int childCount = getChildCount();
            int j = 0;
            int lineHeight = (mLineHeightList.size() > 0 ? mLineHeightList.get(j) : 0);
            int childY = getPaddingTop();
            for (int i = 0; i < childCount; i++) {
                if (!mAdapter.getCrewTagList().isEmpty() && mAdapter.getCrewTagList().size() > 0) {
                    final Employees employee = mAdapter.getCrewTagList().get(i);
                    final View childView = getChildAt(i);
                    MarginLayoutParams layoutParamsMargin = (MarginLayoutParams) childView.getLayoutParams();

                    if (childView.getVisibility() == View.GONE)
                        continue;

                    int childWidth = (childView.getMeasuredWidth() + layoutParamsMargin.leftMargin + layoutParamsMargin.rightMargin);
                    int childHeight = (childView.getMeasuredHeight() + layoutParamsMargin.topMargin + layoutParamsMargin.bottomMargin);


                    if (lineWidth + childWidth + getPaddingRight() > width) {
                        childY += lineHeight;
                        lineHeight = mLineHeightList.get(j);
                        j++;
                        lineWidth = getPaddingLeft() + childWidth;
                    } else
                        lineWidth += childWidth;

                    int childX = lineWidth - childWidth;

                    //Animation addAnimation = AnimationUtils.loadAnimation(getContext(), R.anim.icon_anim_fade_in);
                    childView.layout((childX + layoutParamsMargin.leftMargin), (childY + layoutParamsMargin.topMargin), (lineWidth - layoutParamsMargin.rightMargin), (childY + childHeight - layoutParamsMargin.bottomMargin));
                    //childView.startAnimation(addAnimation);
                    if (mListener != null) {
                        childView.setOnClickListener(new OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                mListener.onHashTagClick(employee, childView.isSelected());
                            }
                        });
                    }
                }
            }
        }
    }

    @Override
    protected LayoutParams generateDefaultLayoutParams() {
        return new MarginLayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
    }

    @Override
    protected LayoutParams generateLayoutParams(LayoutParams p) {
        return new MarginLayoutParams(p);
    }

    @Override
    public LayoutParams generateLayoutParams(AttributeSet attrs) {
        return new MarginLayoutParams(getContext(), attrs);
    }

    public void refresh() {
        if (mAdapter != null) {
            removeAllViews();

            for (int i = 0; i < mAdapter.count(); i++) {
                View view = mAdapter.getView(this, i);

                if (view != null) {
                    if (mListener != null) {
                        view.setClickable(true);
                        view.setFocusable(true);
                    }
                    addView(view);
                }
            }

            invalidate();
        }
    }


    /**
     * Wrapper to add a HashTag
     */
    public void add(Employees hashTag) {
        mAdapter.add(hashTag);
    }

    /**
     * Wrapper to add a HashTag
     *
     * @param lstHashTag
     */
    public void addAll(List<Employees> lstHashTag) {
        mAdapter.addAll(lstHashTag);
    }

    /**
     * Wrapper to remove a HashTag
     *
     * @param hashTag
     */
    public void remove(Employees hashTag) {
        mAdapter.remove(hashTag);
    }

    /**
     * Wrapper to remove all HashTag
     *
     * @param lstHashTag
     */
    public void removeAll(List<Employees> lstHashTag) {
        mAdapter.removeAll(lstHashTag);
    }

    /**
     * How many HashTag do we have
     *
     * @return int
     */
    public int count() {
        return mAdapter.count();
    }

    public List<Employees> getCrewTagList() {
        return mAdapter.getCrewTagList();
    }

    public void setCrewTagList(ArrayList<Employees> crewTagList) {
        mAdapter.setCrewTagList(crewTagList);
    }

    public CrewTagViewAdapter getAdapter() {
        return mAdapter;
    }

    public void setAdapter(CrewTagViewAdapter adapter) {
        mAdapter = adapter;
        mAdapter.deleteObservers();
        mAdapter.addObserver(this);
        refresh();
    }

    public int getCrewTagLayoutRes() {
        return mAdapter.getCrewTagLayoutRes();
    }

    /**
     * Set overall CrewTag layout by res id
     * Can be fine tuned by overriding @see com.scanners.android.bao.view.CrewTagTextView.CrewTag#getLayoutRes
     */
    public void setCrewTagLayoutRes(@LayoutRes int crewTagRes) {
        mAdapter.setCrewTagLayoutRes(crewTagRes);
    }

    /**
     * Set CrewTag onClick listener
     *
     * @param listener
     */
    public void setOnCrewTagClickListener(OnCrewTagClickListener listener) {
        mListener = listener;
    }

    public boolean isToleratingDuplicate() {
        return mAdapter.isToleratingDuplicate();
    }

    /**
     * Set whether or not CrewTagTextView tolerate duplicate CrewTag
     *
     * @param toleratingDuplicate
     */
    public void setToleratingDuplicate(boolean toleratingDuplicate) {
        mAdapter.setToleratingDuplicate(toleratingDuplicate);
    }

    public boolean hasBackground() {
        return mAdapter.hasBackground();
    }

    public void setHasBackground(boolean hasBackground) {
        mAdapter.setHasBackground(hasBackground);
    }

    public int getCrewTagSpacing() {
        return mAdapter.getCrewTagSpacing();
    }

    public void setCrewTagSpacing(int crewTagSpacing) {
        mAdapter.setCrewTagSpacing(crewTagSpacing);
    }

    public int getLineSpacing() {
        return mAdapter.getLineSpacing();
    }

    public void setLineSpacing(int lineSpacing) {
        mAdapter.setLineSpacing(lineSpacing);
    }

    public int getCrewTagPadding() {
        return mAdapter.getCrewTagPadding();
    }

    public void setCrewTagPadding(int crewTagPadding) {
        mAdapter.setCrewTagPadding(crewTagPadding);
    }

    public int getCrewTagSidePadding() {
        return mAdapter.getCrewTagSidePadding();
    }

    public void setCrewTagSidePadding(int crewTagSidePadding) {
        mAdapter.setCrewTagSidePadding(crewTagSidePadding);
    }

    public int getCrewTagBackgroundColor() {
        return mAdapter.getCrewTagBackgroundColor();
    }

    public void setCrewTagBackgroundColor(@ColorInt int crewTagBackgroundColor) {
        mAdapter.setCrewTagBackgroundColor(crewTagBackgroundColor);
    }

    public int getCrewTagBackgroundColorSelected() {
        return mAdapter.getCrewTagBackgroundColorSelected();
    }

    public void setCrewTagBackgroundColorSelected(@DrawableRes int crewTagBackgroundColorSelected) {
        mAdapter.setCrewTagBackgroundColorSelected(crewTagBackgroundColorSelected);
    }

    @Override
    public void update(Observable observable, Object data) {
        refresh();
    }
}

