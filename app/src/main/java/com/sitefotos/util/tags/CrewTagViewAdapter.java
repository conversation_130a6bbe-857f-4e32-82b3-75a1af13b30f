package com.sitefotos.util.tags;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.DrawableRes;
import androidx.annotation.LayoutRes;
import androidx.appcompat.widget.AppCompatTextView;

import com.sitefotos.R;
import com.sitefotos.models.Employees;

import java.util.ArrayList;
import java.util.List;
import java.util.Observable;

public abstract class CrewTagViewAdapter extends Observable {
    private Context mContext;
    private int mCrewTagSpacing;
    private int mLineCrewTag;
    private int mCrewTagPadding;
    private int mCrewTagSidePadding;
    private int mCrewTagRes;
    private int mCrewTagBackgroundColor;
    private int mCrewTagBackgroundColorSelected;
    private boolean mHasBackground = true;
    private boolean mToleratingDuplicate = false;
    private LayoutInflater mInflater;
    private List<Employees> mlstCrewTags;

    public CrewTagViewAdapter(Context context) {
        mContext = context;
        mInflater = (LayoutInflater) mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        mlstCrewTags = new ArrayList<>();
        init();
    }

    /**
     * Return the CrewTag layout res id
     * Override it if you want to have different logic depending of CrewTag
     *
     * @return int
     */
    public abstract int getLayoutRes(int position);

    /**
     * Return the CrewTag background res id
     * Override it if you want to have different logic depending of CrewTag
     *
     * @return int
     */
    public abstract int getBackgroundRes(int position);

    /**
     * Return the CrewTag background color
     * Override it if you want to have different logic depending of CrewTag
     *
     * @return int
     */
    public abstract int getBackgroundColor(int position);

    /**
     * Return the CrewTag color for selected state
     * Override it if you want to have different logic depending of CrewT
     *
     * @return int
     */
    public abstract int getBackgroundColorSelected(int position);

    /**
     * Have a chance to modify the CrewTAG layout
     */
    public abstract void onLayout(View view, int position);

    private void init() {
        mCrewTagSpacing = (int) mContext.getResources().getDimension(R.dimen.p_reviewtag_spacing);
        mLineCrewTag = (int) mContext.getResources().getDimension(R.dimen.p_reviewtag_line_spacing);
        mCrewTagPadding = (int) mContext.getResources().getDimension(R.dimen.p_reviewtag_padding);
        mCrewTagSidePadding = (int) mContext.getResources().getDimension(R.dimen.p_reviewtag_side_padding);
    }

    View getView(ViewGroup parent, int position) {
        View view = null;
        Employees employees = getCrewTag(position);

        if (employees != null) {
            int crewTagLayoutRes = (getLayoutRes(position) != 0 ? getLayoutRes(position) : getCrewTagLayoutRes());
            view = mInflater.inflate(crewTagLayoutRes, parent, false);
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            View content = view.findViewById(R.id.llSubMain);
            layoutParams.setMargins(layoutParams.leftMargin, layoutParams.topMargin, (layoutParams.rightMargin > 0 ? layoutParams.rightMargin : mCrewTagSpacing), (layoutParams.bottomMargin > 0 ? layoutParams.bottomMargin : mLineCrewTag));
            AppCompatTextView crewTagName = view.findViewById(R.id.tvCrewTag);
            if (crewTagName != null) {
                crewTagName.setText(employees.getEmployeeFirstName().concat(" ").concat(employees.getEmployeeLastName()));
            }
            onLayout(view, position);
        }
        return view;

    }

    private String getCapitalizedTag(String crewTag) {
        String[] strArray = crewTag.split(" ");
        StringBuilder builder = new StringBuilder();
        for (String s : strArray) {
            String cap = s.substring(0, 1).toUpperCase() + s.substring(1);
            builder.append(cap + " ");
        }

        return builder.toString();
    }

    private void notifyUpdate() {
        setChanged();
        notifyObservers();
    }

    public Employees getCrewTag(int position) {
        return (position < count() ? mlstCrewTags.get(position) : null);
    }

    /**
     * Wrapper to add a Crew TAG
     *
     */
    public void add(Employees employees) {
        if (!mlstCrewTags.contains(employees) || mToleratingDuplicate) {
            mlstCrewTags.add(employees);
            notifyUpdate();
        }
    }

    /**
     * Wrapper to add a HashTag
     *
     * @param lstCrewTag
     */
    public void addAll(List<Employees> lstCrewTag) {
        mlstCrewTags.addAll(lstCrewTag);
        notifyUpdate();
    }


    /**
     * Wrapper to remove a HashTag
     *
     * @param crewTag
     */
    public void remove(Employees crewTag) {
        mlstCrewTags.remove(crewTag);
        notifyUpdate();
    }


    /**
     * Wrapper to remove a HashTag
     *
     * @param lstCrewTag
     */
    public void removeAll(List<Employees> lstCrewTag) {
        mlstCrewTags.removeAll(lstCrewTag);
        notifyUpdate();
    }

    /**
     * How many HashTag do we have
     *
     * @return int
     */
    public int count() {
        return mlstCrewTags.size();
    }

    protected int getColor(@ColorRes int colorRes) {
        return mContext.getResources().getColor(colorRes);
    }

    public Context getContext() {
        return mContext;
    }

    public List<Employees> getCrewTagList() {
        return mlstCrewTags;
    }

    public void setCrewTagList(List<Employees> HashTagList) {
        mlstCrewTags = HashTagList;
        notifyUpdate();
    }

    public boolean isToleratingDuplicate() {
        return mToleratingDuplicate;
    }

    /**
     * Set whether or not HashTagTextView tolerate duplicate HashTag
     *
     * @param toleratingDuplicate
     */
    public void setToleratingDuplicate(boolean toleratingDuplicate) {
        mToleratingDuplicate = toleratingDuplicate;
    }

    public boolean hasBackground() {
        return mHasBackground;
    }

    public void setHasBackground(boolean hasBackground) {
        mHasBackground = hasBackground;
    }

    public int getCrewTagSpacing() {
        return mCrewTagSpacing;
    }

    public void setCrewTagSpacing(int hashTagSpacing) {
        mCrewTagSpacing = hashTagSpacing;
    }

    public int getLineSpacing() {
        return mLineCrewTag;
    }

    public void setLineSpacing(int lineSpacing) {
        mLineCrewTag = lineSpacing;
    }

    public int getCrewTagPadding() {
        return mCrewTagPadding;
    }

    public void setCrewTagPadding(int hashTagPadding) {
        mCrewTagPadding = hashTagPadding;
    }

    public int getCrewTagSidePadding() {
        return mCrewTagSidePadding;
    }

    public void setCrewTagSidePadding(int hashTagSidePadding) {
        mCrewTagSidePadding = hashTagSidePadding;
    }

    public int getCrewTagBackgroundColor() {
        return mCrewTagBackgroundColor;
    }

    public void setCrewTagBackgroundColor(@ColorInt int hashTagBackgroundColor) {
        mCrewTagBackgroundColor = hashTagBackgroundColor;
    }

    public int getCrewTagBackgroundColorSelected() {
        return mCrewTagBackgroundColorSelected;
    }

    public void setCrewTagBackgroundColorSelected(@DrawableRes int hashTagBackgroundColorSelected) {
        mCrewTagBackgroundColorSelected = hashTagBackgroundColorSelected;
    }


    public int getCrewTagLayoutRes() {
        return mCrewTagRes;
    }

    /**
     * Set overall CrewTag layout by res id
     * Can be fine tuned by overriding @see com.scanners.android.bao.view.CrewTagTextView.HashTag#getLayoutRes
     */
    public void setCrewTagLayoutRes(@LayoutRes int crewTagRes) {
        mCrewTagRes = crewTagRes;
    }
}

