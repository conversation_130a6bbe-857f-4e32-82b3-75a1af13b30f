package com.sitefotos.util;

import android.annotation.TargetApi;
import android.content.Context;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;

import com.sitefotos.storage.AppPrefShared;

import java.util.Locale;

public class LocaleHelper {

	private static final String SELECTED_LANGUAGE = "Locale.Helper.Selected.Language";

	public static Context onAttach(Context context) {
        String lang = getPersistedData(context, Locale.getDefault().getLanguage());
		return setLocale(context, lang);
	}

	public static Context onAttach(Context context, String defaultLanguage) {
		String lang = getPersistedData(context, defaultLanguage);
		return setLocale(context, lang);
	}

	public static String getLanguage(Context context) {
		return getPersistedData(context, Locale.getDefault().getLanguage());
	}

	public static Context setLocale(Context context, String language) {
		persist(context, language);

		if (Build.VERSION.SDK_INT == Build.VERSION_CODES.N) {
			return updateResourcesInNougat(context, language);
		}
		if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
			return updateResources(context, language);
		}

		return updateResourcesLegacy(context, language);
	}

	private static String getPersistedData(Context context, String defaultLanguage) {
		return AppPrefShared.getString(SELECTED_LANGUAGE, defaultLanguage);
	}

	private static void persist(Context context, String language) {
		AppPrefShared.putValue(SELECTED_LANGUAGE, language);
	}

	@TargetApi(Build.VERSION_CODES.N_MR1)
	private static Context updateResources(Context context, String language) {
		Locale locale = new Locale(language);
		Locale.setDefault(locale);

		Configuration configuration = context.getResources().getConfiguration();
		configuration.setLocale(locale);

		return context.createConfigurationContext(configuration);
	}

	private static Context updateResourcesInNougat(Context context, String language) {
		Locale locale = new Locale(language);
		Locale.setDefault(locale);

		Configuration configuration = context.getResources().getConfiguration();
		configuration.setLocale(locale);
		context.getResources().updateConfiguration(configuration, context.getResources().getDisplayMetrics());
		return context;
	}

	@SuppressWarnings("deprecation")
	private static Context updateResourcesLegacy(Context context, String language) {
		Locale locale = new Locale(language);
		Locale.setDefault(locale);

		Resources resources = context.getResources();

		Configuration configuration = resources.getConfiguration();
		configuration.locale = locale;

		resources.updateConfiguration(configuration, resources.getDisplayMetrics());

		return context;
	}
}