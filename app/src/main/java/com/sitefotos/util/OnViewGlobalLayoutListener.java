package com.sitefotos.util;

import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.ScrollView;

import com.sitefotos.Constants;

public class OnViewGlobalLayoutListener implements ViewTreeObserver.OnGlobalLayoutListener {
    private final static int maxHeight = Constants.SCREEN_HEIGHT / 4;
    private ScrollView view;
    private View childView;

    public OnViewGlobalLayoutListener(ScrollView view, View childView) {
        this.view = view;
        this.childView = childView;
    }

    @Override
    public void onGlobalLayout() {
        // If Child view height is less than set max count then make view dynamic
        if (childView.getHeight() < maxHeight) {
            view.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
        } else if (childView.getHeight() >= maxHeight) { // to set limit scroll view and make it scrollable
            view.getLayoutParams().height = maxHeight;
            if (childView != null) { // Focus latest added child view
                view.fullScroll(View.FOCUS_DOWN);
            }
        }

    }
}
