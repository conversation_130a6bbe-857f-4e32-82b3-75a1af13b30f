package com.sitefotos.util;

import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.StaggeredGridLayoutManager;

public abstract class CustomStaggeredGridLayoutManager extends RecyclerView.OnScrollListener {

    private int countListScrolledItems = 0;
    private int countLastItemScrolled = 0;
    private int countCurrentPageIndexForItem = 1;
    private int resultPerPage;

    private int[] firstVisibleItem;

    private StaggeredGridLayoutManager staggeredGridLayoutManager;

    public CustomStaggeredGridLayoutManager(StaggeredGridLayoutManager staggeredGridLayoutManager, int spanCount, int resultPerPage) {
        this.staggeredGridLayoutManager = staggeredGridLayoutManager;
        this.firstVisibleItem = new int[spanCount];
        this.resultPerPage = resultPerPage;
    }

    @Override
    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);

        int visibleItemCount = staggeredGridLayoutManager.getChildCount();
        int totalItemCount = staggeredGridLayoutManager.getItemCount();
        firstVisibleItem = staggeredGridLayoutManager.findFirstVisibleItemPositions(firstVisibleItem);

        if (firstVisibleItem != null && firstVisibleItem.length > 0) {
            countListScrolledItems = firstVisibleItem[0];
            onRecyclerViewScrolled(countListScrolledItems, visibleItemCount);
        }

        int lastItem = countListScrolledItems + visibleItemCount;
        if (lastItem >= totalItemCount) {
            if (countLastItemScrolled != lastItem) { //to avoid multiple calls for last item
                countLastItemScrolled = lastItem;

                // Call API for next page items
                if (lastItem == (countCurrentPageIndexForItem * resultPerPage)) {
                    countCurrentPageIndexForItem++;
                    onLoadMore(lastItem);
                }
            }
        }
    }

    public abstract void onLoadMore(int countCurrentPageIndexForItem);

    public abstract void onRecyclerViewScrolled(int firstVisibleItem, int visibleItemCount);

    /*public abstract void onLoadMoreTopItems();*/
}
