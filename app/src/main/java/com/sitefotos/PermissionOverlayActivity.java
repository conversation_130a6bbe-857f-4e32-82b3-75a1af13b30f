package com.sitefotos;

import static com.sitefotos.Constants.LOGGED_IN_PARAM_IS_LOCATION_REQUIRED;
import static com.sitefotos.util.PermissionUtils.getLocationPermissions;
import static com.sitefotos.util.PermissionUtils.hasPermissionDenied;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityNoPermissionBinding;
import com.sitefotos.event.GPSSettingEventEvent;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class PermissionOverlayActivity extends BaseActivity implements View.OnClickListener {

    private String[] locationPermission;
    boolean isActivityFinishCalled;
    private ActivityNoPermissionBinding binding;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        init();
    }


    private void initBinding() {
        binding = ActivityNoPermissionBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    @Override
    protected void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (linearContentLayout.getChildCount() > 0) {
            linearContentLayout.removeAllViews();
        }
        init();
    }

    private void init() {
        setToolbarVisibility(false);
        setOnClickListener();
        locationPermission = getLocationPermissions();
        checkAndManageView();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void GPSSettingEventEventEvent(GPSSettingEventEvent event) {
        checkAndManageView();
    }

    /**
     * Method to check permissions in sequence and set message and button text accordingly..
     */
    private void checkAndManageView() {
        //If isGPSRequired = 2 then app must have fine location permission else ask user to grant fine location
        if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 2 && !checkFinePermissionLocationGranted()) {
            setMessageForLocation(true);
        } else if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 1 && !checkApproxPermissionLocationGranted()) {
            //If isGPSRequired = 1 then app should  have Approx or fine location permission else ask user to grant fine location
            setMessageForLocation(false);
        } else if (!canGetLocation(this)) {
            binding.tvPermissionMessage.setText(R.string.msg_GPS_enable_required);
            binding.btnLocation.setText(R.string.enable_gps);
        } else {
            finishActivityWithResult();
        }
    }

    private void finishActivityWithResult() {
        if (!isActivityFinishCalled) {
            isActivityFinishCalled = true;
            Intent intent = new Intent();
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    private void setMessageForLocation(boolean isFineLocationRequired) {
        if (!hasPermissionDenied(PermissionOverlayActivity.this, locationPermission)) {
            if (isFineLocationRequired)
                binding.tvPermissionMessage.setText(getString(R.string.msg_fine_location_required, getString(R.string.settings)));
            else
                binding.tvPermissionMessage.setText(getString(R.string.msg_location_required, getString(R.string.settings)));

            binding.btnLocation.setText(R.string.settings);
        } else {
            if (isFineLocationRequired)
                binding.tvPermissionMessage.setText(getString(R.string.msg_fine_location_required, getString(R.string.allow)));
            else
                binding.tvPermissionMessage.setText(getString(R.string.msg_location_required, getString(R.string.allow)));

            binding.btnLocation.setText(R.string.allow);
        }
    }

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }


    private void setOnClickListener() {
        binding.btnLocation.setOnClickListener(this);
    }

    @Override
    public void onClick(View view) {
        manageClickOfButton();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == Constants.LOCATION_PERMISSION_REQUEST) {
            checkAndManageView();
           /* if (grantResults.length == locationPermission.length && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                checkAndManageView();
            } else {
                setMessageForLocation();

            }*/
        }
    }

    private void manageClickOfButton() {
        if (!PermissionUtils.hasPermissions(this, locationPermission)) {
            if (!hasPermissionDenied(PermissionOverlayActivity.this, locationPermission)) {
                StaticUtils.openSettingScreen(this, Constants.LOCATION_PERMISSION_REQUEST);
            } else {
                PermissionUtils.requestPermission(this, locationPermission, Constants.LOCATION_PERMISSION_REQUEST);
            }
        } else if (!canGetLocation(this)) {
            PopUtils.displayLocationSettingsRequest(this, Constants.LOCATION_REQUEST_CODE);
        } else {
            finishActivityWithResult();
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constants.LOCATION_PERMISSION_REQUEST) {
            if (checkFinePermissionLocationGranted()) {
                checkAndManageView();
            }
        } else if (requestCode == Constants.LOCATION_REQUEST_CODE) {
            try {
                new Handler().postDelayed(() -> {
                    if (canGetLocation(getApplicationContext())) {
                        checkAndManageView();
                    }
                }, 800);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }

        }
    }


    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finishAffinity();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
