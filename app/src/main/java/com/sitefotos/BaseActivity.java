package com.sitefotos;

import static com.sitefotos.Constants.CLOCK_NORMAL;
import static com.sitefotos.Constants.FORM_SUBMISSION_ID;
import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.LOGGED_IN_PARAM_IS_LOCATION_REQUIRED;
import static com.sitefotos.Constants.LOGGED_IN_USER_COMPANY_ID;
import static com.sitefotos.Constants.LOGGED_IN_USER_EMAIL_ADDRESS;
import static com.sitefotos.Constants.OVERLAY_ACTIVITY_REQUEST;
import static com.sitefotos.Constants.PARAM_CANCEL_CHECK_IN;
import static com.sitefotos.Constants.PARAM_CREW;
import static com.sitefotos.Constants.PARAM_DT;
import static com.sitefotos.Constants.PARAM_LAT;
import static com.sitefotos.Constants.PARAM_LON;
import static com.sitefotos.Constants.PARAM_PROFILEID;
import static com.sitefotos.Constants.PARAM_SERVICEID;
import static com.sitefotos.Constants.PARAM_SERVICES;
import static com.sitefotos.Constants.PARAM_SITEID;
import static com.sitefotos.Constants.PARAM_STAG;
import static com.sitefotos.Constants.PARAM_SUBMITTED_FOR;
import static com.sitefotos.Constants.PARAM_TITLE;
import static com.sitefotos.Constants.PARAM_TYPE;
import static com.sitefotos.Constants.PREF_MD5_KEY_DATA;
import static com.sitefotos.util.ImageUtil.deleteImagesFromStorage;
import static com.sitefotos.util.StaticUtils.checkIfUploadIsPending;
import static com.sitefotos.util.logger.CustomLogKt.errorLog;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.location.LocationManager;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.google.android.gms.location.FusedLocationProviderClient;
import com.google.android.gms.location.LocationCallback;
import com.google.android.gms.location.LocationRequest;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.event.CustomEvent;
import com.sitefotos.event.EmployeeSavedEvent;
import com.sitefotos.event.SiteDataUpdateEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.event.UserDataSavedEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.map.PropertyMapActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.models.CheckedInMapFormDetails;
import com.sitefotos.models.ClockCrewData;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.DynamicDropDownItem;
import com.sitefotos.models.Employees;
import com.sitefotos.models.Equipments;
import com.sitefotos.models.FormData;
import com.sitefotos.models.MapPinData;
import com.sitefotos.models.Material;
import com.sitefotos.models.Md5Keys;
import com.sitefotos.models.Routes;
import com.sitefotos.models.ServiceTaskStatus;
import com.sitefotos.models.SimpleResponse;
import com.sitefotos.models.SiteConfig;
import com.sitefotos.models.SiteData;
import com.sitefotos.models.SiteListConfig;
import com.sitefotos.models.TMService;
import com.sitefotos.models.UploadImageData;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.models.Vehicles;
import com.sitefotos.service.LocationService;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblDynamicDropdownItems;
import com.sitefotos.storage.tables.TblEmployees;
import com.sitefotos.storage.tables.TblEquipment;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblMapPinData;
import com.sitefotos.storage.tables.TblMaterials;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.storage.tables.TblRoutes;
import com.sitefotos.storage.tables.TblServices;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.storage.tables.TblVehicles;
import com.sitefotos.uploadImage.UploadImagesActivity;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.LocaleHelper;
import com.sitefotos.util.NotificationUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.PropertyUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;
import com.sitefotos.util.views.CustomEditText;
import com.sitefotos.webview.AppWebViewActivity;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.TimeUnit;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public abstract class BaseActivity extends AppCompatActivity {

    public static final long BACKGROUND_CHECK_DELAY = 1000L;// Perform background/Foreground thread after 1 second
    public LayoutInflater inflater;
    public ProgressDialog progressDialog;
    public ProgressDialog pdCenter;
    public LocationManager locationManager;
    public ProgressBar progressBarWebView;
    private boolean canGetLocation = false;
    protected LinearLayout linearContentLayout;
    public static Md5Keys md5Keys = new Md5Keys();

    protected abstract OnAppDataApiResponse getApiCallBack();

    //List of current running activities.
    //Do not forget to extend your activity to BaseActivity then only this logic will work.
    public static ArrayList<String> runningActivity = new ArrayList<>();
    public static Handler backgroundHandler = new Handler();
    /**
     * When app goes in background here we start runnable with 1 second delay and update  isAppWentToBg flag.
     * If isAppWentToBg if true it means app is in background else app is in foreground state.
     * We have initialised isAppWentToBg flag in application class to get result of this flag in notification class or
     * service class while app is not running.
     **/
    public Runnable backgroundRunnable = () -> {

        //If runningActivity list size is 0 then app is in background.
        if (runningActivity.size() == 0) {
            BaseApplication.getInstance().isAppWentToBg = true;
        } else if (BaseApplication.getInstance().isAppWentToBg) {
            BaseApplication.getInstance().isAppWentToBg = false;
        }
    };

    BroadcastReceiver imageUploadDoneReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (action != null) {
                switch (action) {
                    case Constants.UPLOAD_DATA_START_EVENT:
                        EventBus.getDefault().post(new UploadFileStatusEvent(new CustomEvent(Constants.UPLOAD_DATA_START_EVENT)));
                        break;

                    case Constants.UPLOAD_DATA_DONE_EVENT:
                        EventBus.getDefault().post(new UploadFileStatusEvent(new CustomEvent(Constants.UPLOAD_DATA_DONE_EVENT)));
                        break;
                }
            }

        }
    };

    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_base_activity);
        setLocale(this);
        init();
        registerUploadEventDoneBroadcast();
    }

    private void registerUploadEventDoneBroadcast() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(Constants.UPLOAD_DATA_DONE_EVENT);
        intentFilter.addAction(Constants.UPLOAD_DATA_START_EVENT);
        registerAppReceiver(imageUploadDoneReceiver, intentFilter);
    }


    @Override
    protected void attachBaseContext(Context newBase) {
        super.attachBaseContext(LocaleHelper.onAttach(newBase));
    }


    @Override
    protected void onResume() {
        super.onResume();
        BaseApplication.getInstance().setTopActivityName(this.getLocalClassName());
        sendBroadCastForBothUploading();
    }

    private void init() {
        inflater = LayoutInflater.from(this);
        linearContentLayout = findViewById(R.id.linearContentLayout);
        progressBarWebView = findViewById(R.id.progressBarWebView);
    }


    /**
     * Method to set app in background
     * Visible activity name add in list and then remove background runnable
     * and again schedule new runnable with 1 second delay.
     * This is our custom logic to check app is in background or foreground. It may or may not work in some cases.
     */
    private void activityDidForeground() {
        runningActivity.add(this.getClass().getName());
        backgroundHandler.removeCallbacks(backgroundRunnable);
        backgroundHandler.postDelayed(backgroundRunnable, BACKGROUND_CHECK_DELAY);
    }

    /**
     * Method to set app in foreground
     * Visible activity name remove from list and then remove background runnable
     * and again schedule new runnable with 1 second delay.
     * This is our custom logic to check app is in background or foreground. It may or may not work in some cases.
     */
    private void activityDidBackground() {
        runningActivity.remove(this.getClass().getName());
        backgroundHandler.removeCallbacks(backgroundRunnable);
        backgroundHandler.postDelayed(backgroundRunnable, BACKGROUND_CHECK_DELAY);
    }

    public void showKeyboard(View view) {
        InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (inputMethodManager != null)
            inputMethodManager.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT);
    }


    public void setLocale(Context context) {
        LocaleHelper.setLocale(context, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
        Locale locale = new Locale(AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
        Locale.setDefault(locale);
        Configuration config = getBaseContext().getResources().getConfiguration();
        config.locale = locale;
        getBaseContext().getResources().updateConfiguration(config,
                getBaseContext().getResources().getDisplayMetrics());
    }

    /**
     * ***
     * To set Actionbar visibility
     * ********
     */
    public void setActionBarVisibility(Boolean visibility) {
        if (!visibility) {
            try {
                ActionBar actionBar = getSupportActionBar();
                if (actionBar != null) {
                    actionBar.hide();
                }
            } catch (NullPointerException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    protected LinearLayout getMiddleContent() {
        return linearContentLayout;
    }

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);

    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        // Update screen sizes.
        StaticUtils.setWindowDimensionsForCamera(this);
        super.onConfigurationChanged(newConfig);
    }

    /**
     * ShowToast Message Notification for short time when app is on foreground
     *
     * @param msg Toast Title Message
     */
    public void showForeGroundToast(String msg) {
        if (TextUtils.isEmpty(msg) || getApplicationContext() == null)
            return;
        StaticUtils.showForeGroundToast(getApplicationContext(), msg, false);
    }

    /**
     * ShowToast Message Notification for long time when app is on foreground
     *
     * @param msg Toast Title Message
     */
    public void showForeGroundToastLong(String msg) {
        if (TextUtils.isEmpty(msg) || getApplicationContext() == null)
            return;
        StaticUtils.showForeGroundToast(getApplicationContext(), msg, true);

    }

    public void showProgress(String msg) {
        try {
            showProgress(msg, true);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * showProgress for show Dialog
     *
     * @param msg Title Message For Progress Dialog using String
     */
    public void showProgress(String msg, boolean enableTouch) {
        try {
            if (progressDialog == null) {
                progressDialog = new ProgressDialog(this);
                progressDialog.setCancelable(enableTouch);
                progressDialog.setCanceledOnTouchOutside(enableTouch);
            }
            if (TextUtils.isEmpty(msg)) {
                if (progressDialog.getWindow() != null) {
                    progressDialog.getWindow()
                            .setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
                }
            } else {
                progressDialog.setMessage(msg);
            }

            progressDialog.show();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Cancel Progress Dialog
     */
    public void stopProgress() {
        try {
            if (progressDialog != null) {
                progressDialog.cancel();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * showProgress for show Dialog
     */
    public void startProgress() {
        try {
            if (pdCenter == null) {
                pdCenter = new ProgressDialog(this);
                pdCenter.setCancelable(false);
            }
            if (pdCenter.getWindow() != null) {
                pdCenter.getWindow()
                        .setBackgroundDrawable(new ColorDrawable(android.graphics.Color.TRANSPARENT));
            }
            pdCenter.show();
            pdCenter.setContentView(R.layout.layout_dialog);

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Cancel Progress Dialog
     */
    public void stopProgressDialog() {
        try {
            if (pdCenter != null) {
                pdCenter.cancel();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Handle Visibility Of Toolbar
     */
    public void setToolbarVisibility(Boolean visible) {
        if (!visible) {
            if (getSupportActionBar() != null)
                getSupportActionBar().hide();

        }
    }

    public void hideSoftKeyboard(Activity activity) {
        if (activity != null && activity.getCurrentFocus() != null) {
            InputMethodManager inputMethodManager = (InputMethodManager) activity.getSystemService(Activity.INPUT_METHOD_SERVICE);
            if (inputMethodManager != null)
                inputMethodManager.hideSoftInputFromWindow(activity.getCurrentFocus().getWindowToken(), 0);
        }
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_toolbar_root, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {

        /*MenuItem itemNext = menu.findItem(R.id.menu_item_toolbar_more);
        itemNext.setVisible(true);*/
        return super.onPrepareOptionsMenu(menu);
    }

    /**
     * Set Visibility of progressbar in web view
     *
     * @param visibilityProgressBar true/false
     */
    public void setVisibilityProgressBar(Boolean visibilityProgressBar) {
        if (visibilityProgressBar) {
            progressBarWebView.setVisibility(View.VISIBLE);
        } else {
            progressBarWebView.setVisibility(View.GONE);
        }
    }

    public boolean canGetLocation(Context context) {
        canGetLocation = false;
        if (PermissionUtils.hasPermissions(BaseActivity.this, PermissionUtils.getLocationPermissions())) {
            locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        }

        if (locationManager != null) {
            // getting GPS status
            boolean isGPSEnabled = locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER);
            boolean isNetworkEnabled = locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER);
            //CustomLogKt.error("NETWORK_STATUS  : ", isNetworkEnabled + "  GPS_STATUS  : " + isGPSEnabled);
            canGetLocation = !(!isGPSEnabled && !isNetworkEnabled);
        }
        return canGetLocation;
    }


    public boolean checkFinePermissionLocationGranted() {
        return checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * Check Approx user location
     *
     * @return
     */
    public boolean checkApproxPermissionLocationGranted() {
        if (checkSelfPermission(Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            return true;
        }
        return checkSelfPermission(Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }


    public void requestServerForUserData(Context context, boolean fromLogin, boolean forceApiCall, HashMap<String, Object> params) {
        if (BaseApplication.getInstance().isOnline(context)) {
            if (!forceApiCall) {
                long timeDifference = System.currentTimeMillis() - StaticUtils.getCallTime();
                long diffMinutes = TimeUnit.MILLISECONDS.toMinutes(timeDifference);
                if (StaticUtils.getCallTime() == 0 || (diffMinutes > 15)) {
                    //Navigate user to login screen if email id or access code  is null or empty
                    if (TextUtils.isEmpty(AppPrefShared.getString(LOGGED_IN_USER_EMAIL_ADDRESS, "")) ||
                            TextUtils.isEmpty(AppPrefShared.getString(LOGGED_IN_USER_COMPANY_ID, ""))) {
                        logoutUserAndNavigateUserToLoginScreen();
                        return;
                    }

                } else {
                    return;
                }
            }
            ApiInterface apiService = RetrofitProvider.createService(ApiInterface.class);
            Call<AppDataResponse> call = apiService.getAppData(AppPrefShared.getString(PARAM_STAG, ""), params);
            call.enqueue(new Callback<AppDataResponse>() {
                @Override
                public void onResponse(Call<AppDataResponse> call, Response<AppDataResponse> response) {
                    if (response.code() == 200) {
                        try {
                            AppPrefShared.putValue(PARAM_STAG, response.headers().get("stag"));
                        } catch (Exception e) {
                            FirebaseEventUtils.logException(e);
                        }
                        if (!fromLogin) {
                            doAfterAppUserDataResponseReceived(response, false);
                        } else {

                            if (getApiCallBack() != null) {
                                getApiCallBack().onSuccessResponse(response);
                            }
                        }
                    } else if (response.code() == 401) {
                        logoutUserOnInvalidAccessCode();
                    } else {
                        StaticUtils.setTime(System.currentTimeMillis());
                        stopProgress();
                        if (getApiCallBack() != null) {
                            getApiCallBack().onSuccessResponse(response);
                        }
                    }
                }

                @Override
                public void onFailure(Call<AppDataResponse> call, Throwable t) {
                    if (getApiCallBack() != null) {
                        stopProgress();
                        getApiCallBack().onFailureResponse(t);
                    }

                }
            });
        } else {
            if (getApiCallBack() != null) {
                stopProgress();
                getApiCallBack().onNoInternetConnection();
            }
        }
    }

    private void logoutUserOnInvalidAccessCode() {
        //Show popup to the user and clear database and navigate user to login screen
        PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.your_login_session_expired), getString(R.string.ok), false, (dialog, which) -> {
            dialog.dismiss();
            //Clear preference, local database and navigate user to login screen
            BaseApplication.getInstance().logoutUser(true);
        });
    }

    public void doAfterAppUserDataResponseReceived(Response<AppDataResponse> apiResponse, boolean fromLogin) {
        try {
            StaticUtils.setTime(System.currentTimeMillis());
            AppDataResponse response = apiResponse.body();
            if (response == null)
                return;
            try {

                AppPrefShared.putValue(Constants.LOGGED_IN_USER_COMPANY_NAME, response.getCompanyName());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_TYPE, response.getUserType());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_APP_SETTINGS_HI_RES, response.getHiRes());

                int hiRes = Integer.parseInt(AppPrefShared.getString(Constants.LOGGED_IN_USER_APP_SETTINGS_HI_RES, "0"));
                if (hiRes == 1) { // hiRes = 1 ==> turn hiRes setting inside app ON and disabled
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, true);
                } else if (hiRes == 2) { // hiRes = 2 ==> turn hiRes setting inside app OFF and disabled
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false);
                }

                AppPrefShared.putValue(Constants.LOGGED_IN_USER_APP_SETTINGS_RAPID_RH, response.getRapidRH());
                AppPrefShared.putValue(Constants.APP_BREADCRUMB, response.getBreadcrumbs());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_FORCE_SITES, response.isForceSites());
                AppPrefShared.putValue(Constants.POLYGONE_DISTANCE_DELTA, response.getDistanceDelta());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_SKIP_GEO, response.getSkipGeo());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_SCREEN_TYPE, response.getScreenType());
                if (!fromLogin) {
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_OLD_CLOCKINOUT, AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0));
                }
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_FORM_LOCK, response.getFormLock());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, response.getClockInOut());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_IS_FORM, response.getFormsPermission());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_EMPLOYEE_ID, response.getEmployeeID());
                AppPrefShared.putValue(Constants.LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, response.getIsGPSRequire());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_TAGS, StaticUtils.getStringFromStringArray(response.getTags()));
                AppPrefShared.putValue(Constants.APP_SITE_PERMISSION, response.getSitesPermission());
                AppPrefShared.putValue(Constants.APP_DISABLE_MAIN_CAMERA, response.isDisableMainCamera());
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_LOGIN_STATUS, true);
                AppPrefShared.putValue(Constants.PARAM_DEFAULT_MAP_PIN_URL, response.getDefaultMapPinURL());
                if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 0) {
                    NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(getApplicationContext());
                    AppPrefShared.putValue(Constants.PREF_NOTIFICATION_ID, 0);
                }
                AppPrefShared.putValue(Constants.LOGGED_IN_USER_EXTENDED_SEARCH_ENABLE, response.isExtendedCrewSearch());
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }

            if (getApiCallBack() != null) {
                getApiCallBack().onUpdateAppVersion(response.getAndroidVersionCode(), response.isForceUpdate());
            }
            if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_OLD_CLOCKINOUT, 0) > AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0)) {
                // Checkout from all form and clock out users
                //new ResetDataForRevokedClockPermission().execute();
                revokeClockCrewData();
            }
            new InsertOrUpdateOtherData(apiResponse, fromLogin).executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR);
        } catch (JsonSyntaxException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public boolean saveUserAppDataInDataBase(Response<AppDataResponse> apiResponse) {
        AppDataResponse response = apiResponse.body();
        if (response == null) {
            return false;
        }
        insertOrUpdatePropertyData(response.getSites());
        insertOrUpdateClusterData(response.getClusters());
        insertEmployeeDataInDB(response.getCrew());
        insertOrUpdateServiceData(response.getServices());
        insertOrUpdateMaterialData(response.getMaterials());
        insertOrUpdateFormData(response.getForms());
        doAfterResponseReceived(response);
        insertOrUpdateRouteData(response.getRoutes());
        insertOrUpdateMapPinData(response.getMapPinUrls());
        insertOrUpdateVehicleData(response.getLstVehicle());
        insertOrUpdateEquipmentData(response.getLstEquipment());
        insertOrUpdateDynamicDropDownItemData(response.getDynamicDropdownItems());
        return true;
    }

    private void insertOrUpdateVehicleData(List<Vehicles> lstVehicle) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstVehicle));
        if (md5Keys.getVehicleKey().equals(newMd5)) {
            return;
        }
        TblVehicles tblVehicles = new TblVehicles(getApplicationContext());
        if (lstVehicle != null && !lstVehicle.isEmpty()) {
            if (tblVehicles.isDataExist()) {
                tblVehicles.updateDataStatus(0);
                tblVehicles.insertVehicleBulkData(lstVehicle);
                tblVehicles.removeOldDataFromTable();
            } else {
                tblVehicles.insertVehicleBulkData(lstVehicle);
            }
        } else {
            tblVehicles.deleteDataFromTable();
        }
        md5Keys.setVehicleKey(newMd5);
    }

    private void insertOrUpdateEquipmentData(List<Equipments> lstEquipments) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstEquipments));
        if (md5Keys.getEquipmentKey().equals(newMd5)) {
            return;
        }
        TblEquipment tblEquipment = new TblEquipment(getApplicationContext());
        if (lstEquipments != null && !lstEquipments.isEmpty()) {
            if (tblEquipment.isDataExist()) {
                tblEquipment.updateDataStatus(0);
                tblEquipment.insertEquipmentBulkData(lstEquipments);
                tblEquipment.removeOldDataFromTable();
            } else {
                tblEquipment.insertEquipmentBulkData(lstEquipments);
            }
        } else {
            tblEquipment.deleteDataFromTable();
        }
        md5Keys.setEquipmentKey(newMd5);
    }

    private void insertOrUpdateDynamicDropDownItemData(List<DynamicDropDownItem> lstData) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstData));
        if (md5Keys.getDynamicDropDownItemsKey().equals(newMd5)) {
            return;
        }
        TblDynamicDropdownItems tblDynamicItem = new TblDynamicDropdownItems(getApplicationContext());
        if (lstData != null && !lstData.isEmpty()) {
            if (tblDynamicItem.isDataExist()) {
                tblDynamicItem.updateDataStatus(0);
                tblDynamicItem.insertBulkData(lstData);
                tblDynamicItem.removeOldDataFromTable();
            } else {
                tblDynamicItem.insertBulkData(lstData);
            }
        } else {
            tblDynamicItem.deleteDataFromTable();
        }
        md5Keys.setDynamicDropDownItemsKey(newMd5);
    }

    public void insertOrUpdateClusterData(List<Cluster> lstCluster) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstCluster));
        if (md5Keys.getClusterKey().equalsIgnoreCase(newMd5)) {
            //CustomLogKt.error("No change", "Cluster: No Change ");
            return;
        }
        try {
            TblCluster tblCluster = new TblCluster(BaseActivity.this);
            if (lstCluster != null && !lstCluster.isEmpty()) {
                tblCluster.updateClusterStatus(0);
                tblCluster.insertOrUpdateClusterBulkData(lstCluster);
                tblCluster.removeOldDataFromTable();
            } else {
                tblCluster.deleteAllDataFromClusterTable();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        md5Keys.setClusterKey(newMd5);
    }

    public void insertEmployeeDataInDB(List<Employees> lstCrew) {
        insertEmployeeDataInDB(lstCrew, this);
    }

    public void insertEmployeeDataInDB(List<Employees> lstCrew, Activity
            activity) {

        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstCrew));
        if (md5Keys.getCrewKey().equals(newMd5)) {
            //CustomLogKt.error("No change", "Employee: No Change ");
            return;
        }
        TblEmployees tblEmployees = new TblEmployees(activity);

        if (lstCrew != null) {
            long firstUpdateDate = System.currentTimeMillis();
            if (tblEmployees.isDataExist()) {
                tblEmployees.insertOrUpdateBulkData(lstCrew, firstUpdateDate);

                tblEmployees.deleteOldData(StaticUtils.getEmployeeIds(lstCrew));
            } else {
                tblEmployees.insertBulkData(lstCrew);
            }
        } else {
            tblEmployees.deleteData();
        }
        md5Keys.setCrewKey(newMd5);
        EventBus.getDefault().post(new EmployeeSavedEvent());
    }

    public void insertOrUpdatePropertyData(List<PropertiesVo> lstProperty) {
        insertOrUpdatePropertyData(lstProperty, this);
    }

    private void insertOrUpdatePropertyData(List<PropertiesVo> lstProperty, Activity activity) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstProperty));
        if (md5Keys.getPropertyKey().equals(newMd5)) {
            //CustomLogKt.error("No change", "Property: No Change ");
            return;
        }

        TblProperties tblProperties = new TblProperties(activity);
        if (lstProperty != null && !lstProperty.isEmpty()) {
            if (tblProperties.isDataExist()) {
                tblProperties.updatePropertyStatus();
                tblProperties.insertOrUpdatePropertyBulkData(lstProperty);
                tblProperties.removeOldDataFromTable();
            } else {
                tblProperties.insertPropertyBulkData(lstProperty);
            }
        } else {
            tblProperties.getAllProperties();
        }
        md5Keys.setPropertyKey(newMd5);
    }

    public void insertOrUpdateServiceData(List<TMService> lstServices) {

        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstServices));
        if (md5Keys.getServiceKey().equals(newMd5)) {
            //CustomLogKt.error("No change", "Service: No Change ");
            return;
        }
        TblServices tblServices = new TblServices(getApplicationContext());
        if (lstServices != null && !lstServices.isEmpty()) {
            long firstUpdateDate = System.currentTimeMillis();
            if (tblServices.isDataExist()) {
                tblServices.insertOrUpdateServicesBulkData(lstServices, firstUpdateDate);
                tblServices.deleteOldData(StaticUtils.getServiceIds(lstServices));
            } else {
                tblServices.insertServicesBulkData(lstServices);
            }
        } else {
            tblServices.deleteDataFromServiceTable();
        }
        md5Keys.setServiceKey(newMd5);
    }

    public void insertOrUpdateMaterialData(List<Material> lstMaterial) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstMaterial));
        if (md5Keys.getMaterialKey().equals(newMd5)) {
            //CustomLogKt.error("No change", "Material: No Change ");
            return;
        }
        TblMaterials tblMaterials = new TblMaterials(getApplicationContext());
        if (lstMaterial != null && !lstMaterial.isEmpty()) {
            long firstUpdateDate = System.currentTimeMillis();
            if (tblMaterials.isDataExist()) {
                tblMaterials.insertOrUpdateMaterialBulkData(lstMaterial, firstUpdateDate);
                tblMaterials.deleteOldData(StaticUtils.getMaterialIds(lstMaterial));
            } else {
                tblMaterials.insertMaterialBulkData(lstMaterial);
            }
        } else {
            tblMaterials.deleteDataFromMaterialTable();
        }
        md5Keys.setMaterialKey(newMd5);
    }

    public void insertOrUpdateFormData(List<FormData> lstFormData) {

        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstFormData));
        if (md5Keys.getFormKey().equals(newMd5)) {
            errorLog("No change", "Form Data: No Change ");
            return;
        }
        TblForms tblForms = new TblForms(getApplicationContext());
        try {
            if (lstFormData != null && !lstFormData.isEmpty()) {
                tblForms.updateFormStatus(0);
                tblForms.insertOrUpdateFormData(lstFormData);

                tblForms.removeOldDataFromTable();
            } else {
                tblForms.deleteDataFromFormTable();
                setBroadcastForFormToFinishDetailActivity();
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        md5Keys.setFormKey(newMd5);
    }

    public void insertOrUpdateMapPinData(List<MapPinData> lstMapPinData) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstMapPinData));
        if (md5Keys.getMapPinKey().equalsIgnoreCase(newMd5)) {
            return;
        }
        try {
            TblMapPinData tblMapPinData = new TblMapPinData(BaseActivity.this);
            if (lstMapPinData != null && !lstMapPinData.isEmpty()) {
                tblMapPinData.updateDataStatus(0);
                tblMapPinData.insertOrUpdateMapPinBulkData(lstMapPinData);
                tblMapPinData.removeOldDataFromTable();
                checkAndDownloadNewOrUpdatedPin(tblMapPinData.getUpdatedPinPath());
            } else {
                tblMapPinData.deleteAllDataFromTable();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        md5Keys.setClusterKey(newMd5);
    }

    /**
     * Method to check and download new Map Pins
     *
     * @param lstFilePath list of File Url fetched from DB
     */
    private void checkAndDownloadNewOrUpdatedPin(List<String> lstFilePath) {

        //todo We also need to check if downloaded file is corrupted
        for (String filePath : lstFilePath) {
            try {
                // If file does not exist, Download it in Device's App internal folder to prevent outside app access.
                if (!ImageUtil.checkIfImageExists(this, filePath)) {
                    URL url = new URL(filePath);
                    URLConnection conn = url.openConnection();
                    Bitmap bitmap = BitmapFactory.decodeStream(conn.getInputStream());
                    File fileName = new File(filePath);
                    ImageUtil.saveMapPinImage(this, bitmap, fileName.getName());
                }
            } catch (Exception ex) {
                FirebaseEventUtils.logException(ex);
            }
        }
    }

    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude,
                                         long siteId, TMService mService, long formId, String formName, String formSubmissionId, List<Integer> lstCrewIds, boolean isClearForm, String preSelectedServices, boolean isSubForm, int empId) {
        try {
            HashMap<String, Object> data = new HashMap<>();
            data.put(PARAM_LAT, currentLatitude);
            data.put(PARAM_LON, currentLongitude);
            data.put(PARAM_TYPE, type);
            //siteid = -2 for all clock breadcrumbs, -1 for normal forms and for other siteId
            data.put(PARAM_SITEID, siteId);
            String title = StaticUtils.getBreadcrumbTypeTitle(this, type);
            //profileid = -1 for all clock breadcrumbs else formId
            data.put(PARAM_PROFILEID, formId);
            if (mService == null) {
                if (!TextUtils.isEmpty(formName)) {
                    data.put(PARAM_TITLE, getString(R.string.breadcrumb_, title, formName));
                } else {
                    data.put(PARAM_TITLE, getString(R.string.breadcrumb_single, title));
                }
            }
            if (mService != null) {
                data.put(PARAM_SERVICEID, mService.getServiceID());
                data.put(PARAM_TITLE, "Breadcrumb upload - " + title + " - " + mService.getServiceName());
            }
            if (type == 3 && isClearForm) {
                //If type if checkout and user clear data from form list, we need to add this additional parameter.
                data.put(PARAM_CANCEL_CHECK_IN, true);
            }
            if (isSubForm) {
                data.put(PARAM_SUBMITTED_FOR, empId);
            }
            data.put(PARAM_DT, System.currentTimeMillis() / 1000);
            if (lstCrewIds != null) {
                data.put(PARAM_CREW, lstCrewIds);
            }

            if (!TextUtils.isEmpty(preSelectedServices)) {
                data.put(PARAM_SERVICES, preSelectedServices);
            }
            data.put(FORM_SUBMISSION_ID, formSubmissionId);

            StaticUtils.prepareBreadCrumbDataToAddInUploadQueue(this, data);

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude,
                                         long siteId, long formId, String formName, String formSubmissionId, String preSelectedServices) {
        prepareDataForBreadCrumb(type, currentLatitude, currentLongitude, siteId, null, formId, formName, formSubmissionId, null, false, preSelectedServices, false, 0);
    }


    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude,
                                         long siteId, long formId, String formName, String formSubmissionId, boolean isClearForm, boolean isSubForm, int empId) {
        prepareDataForBreadCrumb(type, currentLatitude, currentLongitude, siteId, null, formId, formName, formSubmissionId, null, isClearForm, null, isSubForm, empId);
    }

    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude,
                                         long siteId, long formId, String formName, String formSubmissionId, List<Integer> lstCrewIds) {
        prepareDataForBreadCrumb(type, currentLatitude, currentLongitude, siteId, null, formId, formName, formSubmissionId, lstCrewIds, false, null, false, 0);
    }


    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude,
                                         long siteId, long formId, String formName, String formSubmissionId, TMService service, boolean isSubForm, int empId) {
        prepareDataForBreadCrumb(type, currentLatitude, currentLongitude, siteId, service, formId, formName, formSubmissionId, null, false, null, isSubForm, empId);
    }

    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude,
                                         long siteId, long formId, String formName, String formSubmissionId, TMService service) {
        prepareDataForBreadCrumb(type, currentLatitude, currentLongitude, siteId, service, formId, formName, formSubmissionId, null, false, null, false, 0);
    }

    public void userLoginBreadcrumb() {
        BaseApplication.getInstance().prepareDataForBreadCrumb(17, MainActivity.currentLatitude, MainActivity.currentLongitude);
    }

    /**
     * Send broadcast to TMFormDetail, Sketch and Camera Activity if currently opened.
     * It will tell those screen to finish it because that form no more exist for the user.
     */
    private void setBroadcastForFormToFinishDetailActivity() {
        Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
        intent.putExtra(Constants.DELETED_FORM_ALL_DATA, true);
        sendBroadcast(intent);
    }


    public void insertOrUpdateSiteFormData(List<FormData> lstFormData) {

        TblTMForms tblTNMForms = new TblTMForms(getApplicationContext());
        if (!tblTNMForms.dataExist()) {
            tblTNMForms.insertBulkData(lstFormData);
        } else {
            tblTNMForms.insertOrUpdateBulkData(lstFormData);
        }
        removeDeletedTMFormFromDatabase(tblTNMForms);
    }

    private void removeDeletedTMFormFromDatabase(TblTMForms tblTNMForms) {
        ArrayList<CheckedInMapFormDetails> lstDeletedFormData = tblTNMForms.getFormDataWithColumnUpdateStatusZero();
        TblCheckInMap tblCheckInMap = new TblCheckInMap(getApplicationContext());
        ArrayList<Integer> lstDeletedFormIds = new ArrayList<>();
        for (CheckedInMapFormDetails checkedInMapFormDetails : lstDeletedFormData) {
            lstDeletedFormIds.add(checkedInMapFormDetails.getFormId());
            if (tblCheckInMap.isDataExist()) {
                checkAndCallCheckoutBreadcrumbForDeletedForms(tblCheckInMap, checkedInMapFormDetails);
                tblCheckInMap.deleteDataByFormAndSiteId(checkedInMapFormDetails.getFormId(), checkedInMapFormDetails.getSiteId());
            }
        }
        if (!lstDeletedFormData.isEmpty()) {
            sendBroadcastForSelectedWPForms(lstDeletedFormIds);
        }

        tblTNMForms.removeOldDataFromTable();
    }

    /**
     * Method to check and call this function if site is unassign from route or form.
     *
     * @param tblCheckInMap           TblCheckInMap
     * @param checkedInMapFormDetails CheckedInMapFormDetails
     */
    private void checkAndCallCheckoutBreadcrumbForDeletedForms(TblCheckInMap tblCheckInMap, CheckedInMapFormDetails checkedInMapFormDetails) {
        // Get Data from checked in cancel breadcrumb for deleted forms from backend
        CheckInMap checkInMap = tblCheckInMap.getDataByFormId(checkedInMapFormDetails.getFormPkId());
        //Execute line only if deleted data in in checked-in map table
        if (checkInMap.getFormPkId() > 0 && checkInMap.getFormPkId() == checkedInMapFormDetails.getFormPkId() && checkInMap.getSiteId() == checkedInMapFormDetails.getSiteId()) {
            prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude,
                    checkedInMapFormDetails.getSiteId(), checkedInMapFormDetails.getFormId(), checkedInMapFormDetails.getFormName(), checkedInMapFormDetails.getFormSubmissionId(), true, false, 0);
        }

    }

    private void sendBroadcastForSelectedWPForms(ArrayList<Integer> lstDeletedFormId) {
        Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
        intent.putIntegerArrayListExtra(Constants.DELETED_WP_LIST, lstDeletedFormId);
        sendBroadcast(intent);
    }

    public void insertOrUpdateRouteData(List<Routes> lstRoutes) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstRoutes));
        if (md5Keys.getRoutesKey().equals(newMd5)) {
            //CustomLogKt.error("No change", "Routes: No Change ");
            return;
        }
        TblRoutes tblRoutes = new TblRoutes(getApplicationContext());
        if (lstRoutes != null && !lstRoutes.isEmpty()) {
            if (tblRoutes.isDataExist()) {
                tblRoutes.insertOrUpdateBulkData(lstRoutes);
                tblRoutes.deleteOldData(StaticUtils.getRouteIds(lstRoutes));
            } else {
                tblRoutes.insertBulkData(lstRoutes);
            }
        } else {
            tblRoutes.deleteDataFromTable();
        }
        md5Keys.setRoutesKey(newMd5);
    }

    public void insertOrUpdateSiteData(List<SiteData> lstSiteData) {
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(lstSiteData));
        if (md5Keys.getSiteKey().equals(newMd5)) {
            return;
        }
        TblSites tblSites = new TblSites(getApplicationContext());
        if (tblSites.isDataExist()) {
            tblSites.insertOrUpdateBulkData(lstSiteData);
            // Check and call checkout breadcrumb call for deleted checked in form sites.
            List<String> lstDeletedSiteIds = tblSites.getDeletedSiteIds(StaticUtils.getSiteIds(lstSiteData));
            checkCheckedInFormsForSiteFromCheckInMap(lstDeletedSiteIds);
            tblSites.deleteOldData(StaticUtils.getSiteIds(lstSiteData));
            //Remove data from check in table after site is removed from server
            List<Integer> lstSiteIds = tblSites.getAllSiteIds();
            TblCheckInMap tblCheckInMap = new TblCheckInMap(getApplicationContext());
            tblCheckInMap.deleteOldData(lstSiteIds);
        } else {
            tblSites.insertBulkData(lstSiteData);
        }
        md5Keys.setSiteKey(newMd5);
    }

    /**
     * Method to check deleted Sited from check in map table and if found call checkout breadcrumb call
     * @param lstDeletedSiteIds List<String>
     */
    private void checkCheckedInFormsForSiteFromCheckInMap(List<String> lstDeletedSiteIds) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(getApplicationContext());
        List<String> lstFormPkIds = tblCheckInMap.getTmFormPkIdBySiteIds(lstDeletedSiteIds);
        TblTMForms tblTMForms = new TblTMForms(getApplicationContext());
        List<CheckedInMapFormDetails> lstFormData = tblTMForms.getAllDataByFormPKIds(lstFormPkIds);
        for (CheckedInMapFormDetails checkedInMapFormDetails : lstFormData) {
            if (tblCheckInMap.isDataExist()) {
                checkAndCallCheckoutBreadcrumbForDeletedForms(tblCheckInMap, checkedInMapFormDetails);
                tblCheckInMap.deleteDataByFormAndSiteId(checkedInMapFormDetails.getFormId(), checkedInMapFormDetails.getSiteId());
            }
        }
    }

    public void sendBroadCastForStartOtherDataUpload() {
        AsyncTask.execute(() -> BaseApplication.getInstance().startOtherUploads());
    }

    public void sendBroadCastForBothUploading() {
        try {
            AsyncTask.execute(() -> BaseApplication.getInstance().startBothUploads());
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void navigateToUploadActivityScreen(ImageView ivUploadData) {
        if (checkIfUploadIsPending(this)) {
            Intent intent = new Intent(this, UploadImagesActivity.class);
            startActivity(intent);
        } else {
            if (ivUploadData != null)
                ivUploadData.setVisibility(View.INVISIBLE);

        }
    }


    public void setInVisibilityOfUploadView(AppCompatImageView ivUploadData) {
        if (checkIfUploadIsPending(this)) {
            visibleUploadImageViewInScreen(ivUploadData);
        } else {
            visibleUploadImageViewInScreen(ivUploadData);

        }
    }


    public void setVisibilityOfUploadView(AppCompatImageView ivUploadData) {
        if (checkIfUploadIsPending(this)) {
            setUploadImageViewInScreen(ivUploadData, false);
        } else {
            setUploadImageViewInScreen(ivUploadData, true);

        }
    }

    public void setUploadImageViewInScreen(AppCompatImageView ivUploadData, boolean isUploadDone) {
        if (isUploadDone) {
            ivUploadData.setVisibility(View.GONE);
        } else {
            ivUploadData.setVisibility(View.VISIBLE);
        }
    }

    public void visibleUploadImageViewInScreen(AppCompatImageView ivUploadData) {
        // if (isUploadStarted && checkIfUploadIsPending()) {
        if (checkIfUploadIsPending(this)) {
            ivUploadData.setVisibility(View.VISIBLE);
        } else {
            ivUploadData.setVisibility(View.GONE);
        }
    }


    public void checkAndShowAppUpdateDialog(int versionCode, boolean isForceUpdate) {
        try {
            if (versionCode > 0) {
                if (BuildConfig.VERSION_CODE < versionCode) {
                    PopUtils.showDialogForUpdateApplication(this, isForceUpdate, "", v -> {
                        StaticUtils.navigateToPlayStore(this);
                        finish();
                    });
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public void killApp() {
        Intent intent = new Intent(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_HOME);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(intent);
    }

    public void setEditTextListeners(CustomEditText edtText, boolean setTouchListener) {
        /*edtText.setKeyImeChangeListener((keyCode, event) -> {
            if (event.getAction() == KeyEvent.ACTION_DOWN || event.getAction() == KeyEvent.ACTION_UP) {
                if (keyCode == KeyEvent.KEYCODE_BACK) {
                    edtText.setFocusable(false);
                    edtText.setFocusableInTouchMode(true);
                }
            }
        });
        edtText.setOnEditorActionListener((textView, i, keyEvent) -> {
            if (i == EditorInfo.IME_ACTION_DONE) {
                edtText.setFocusable(false);
                edtText.setFocusableInTouchMode(true);
                StaticUtils.hideSoftKeyboard(this, edtText);
                return false;
            }
            return false;
        });*/

    }


    /**
     * Common method to start location service in activity's onStart method.
     *
     * @param fusedLocationClient fusedLocationClient
     * @param locationRequest     locationRequest
     * @param locationCallback    locationCallback
     */
    @SuppressLint("MissingPermission")
    public void startLocationService(FusedLocationProviderClient
                                             fusedLocationClient, LocationRequest
                                             locationRequest, LocationCallback locationCallback) {
        if (fusedLocationClient != null) {
            try {
                fusedLocationClient.requestLocationUpdates(locationRequest, locationCallback, Looper.myLooper());
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    /**
     * Common method to stop ongoing location service.
     *
     * @param fusedLocationClient fusedLocationClient
     * @param locationCallback    locationCallback
     */
    public void stopLocationService(FusedLocationProviderClient fusedLocationClient, LocationCallback locationCallback) {
        if (fusedLocationClient != null) {
            fusedLocationClient.removeLocationUpdates(locationCallback);
        }
    }

    public void logoutUserAndNavigateUserToLoginScreen() {
        BaseApplication.getInstance().logoutUser(true);
        finishAffinity();
    }

    protected class ResetDataForRevokedClockPermission extends AsyncTask<Void, Void, Boolean> {

        @Override
        protected Boolean doInBackground(Void... voids) {
            revokeClockCrewData();
            return null;
        }
    }

    private void revokeClockCrewData() {
        checkAndClockOutCrew();
        //Reset to 0. we are done with necessary changes.
        AppPrefShared.putValue(Constants.LOGGED_IN_USER_PARAM_OLD_CLOCKINOUT, 0);
    }

    private void checkAndClockOutCrew() {
        TblClockCrew tblClockCrew = new TblClockCrew(this);
        List<Integer> lstSelected;
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_OLD_CLOCKINOUT, 0) == 2 && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            lstSelected = tblClockCrew.getAllClockedCrewDataIds();
            if (lstSelected.contains(StaticUtils.getEmployeeIdInInt())) {
                lstSelected.remove(Integer.valueOf(StaticUtils.getEmployeeIdInInt()));
            }
        } else {
            lstSelected = tblClockCrew.getAllClockedCrewDataIds();
        }
        if (lstSelected.size() > 0) {
            tblClockCrew.deleteDataByIds(lstSelected);
            prepareDataForBreadCrumb(5, MainActivity.currentLatitude, MainActivity.currentLongitude, -1, -1, "", "", lstSelected);

            TblEmployees tblEmployees = new TblEmployees(this);
            List<Employees> lstEmployeeData = tblEmployees.getAllDataByIds(lstSelected);
            List<ClockCrewData> lstCrewData = new ArrayList<>();
            for (Employees employees : lstEmployeeData) {
                ClockCrewData data = new ClockCrewData();
                data.setCrewId(employees.getEmployeeID());
                data.setCrew(employees.isCrew());
                data.setCrewName(StaticUtils.getSelectedCrewMemberName(employees));
                data.setClockInType(CLOCK_NORMAL);
                lstCrewData.add(data);
            }
            tblClockCrew.insertBulkData(lstCrewData);
        }
    }

    protected void addDataInUploadQueue(boolean isTMForm, FormData formData, boolean isImageUploaded, String lastUpdatedBuildingId, String lastBuildingId) {
        TblTMForms tblTNMForms = new TblTMForms(this);
        FormData tempFormData = tblTNMForms.getFormDataByPKId(formData.getFormPKId());
        String data = tempFormData.getModifiedFormData();
        TblUploadData tblUploadData = new TblUploadData(this);
        UploadOtherData uploadOtherData = new UploadOtherData();

        long currentTime = System.currentTimeMillis();
        uploadOtherData.setDataType(Constants.FORM_DATA);
        uploadOtherData.setCreatedAt(currentTime);
        uploadOtherData.setUpdatedAt(currentTime);
        if (isTMForm) {
            uploadOtherData.setTmFormPKId(formData.getFormPKId());
        } else {
            uploadOtherData.setFormPKId(formData.getFormPKId());
        }
        uploadOtherData.setProcessStartTime(currentTime);
        uploadOtherData.setFormSubmissionId(formData.getFormSubmissionId());
        uploadOtherData.setHasSubForm(formData.hasSubForm());
        uploadOtherData.setSubForm(formData.isSubForm());

        JSONObject dataJson = new JSONObject();
        try {
            dataJson.put(Constants.PARAM_FORM_ID, tempFormData.getFormId());
            dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            if (tempFormData.getLastBuildingId() == 0) {
                if (Integer.parseInt(lastUpdatedBuildingId) > 0) {
                    tempFormData.setLastBuildingId(Integer.parseInt(lastUpdatedBuildingId));
                } else if (Integer.parseInt(lastBuildingId) > 0) {
                    tempFormData.setLastBuildingId(Integer.parseInt(lastBuildingId));
                }
            }

            dataJson.put(Constants.PARAM_BUIL, tempFormData.getLastBuildingId());
            data = tempFormData.getModifiedFormData();
            dataJson.put(Constants.PARAMS_FORM_DATA_APP, data);
            dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
            //dataJson.put(Constants.PARAM_FORM_KEY_DATA, StaticUtils.getValueJson(data, tempFormData.getImageData()));
            dataJson.put(Constants.PARAM_LON, MainActivity.currentLongitude);
            dataJson.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
            dataJson.put(Constants.PARAM_SOURCE, "Sites");
            if (tempFormData.getIsCheckInOut()) {
                dataJson.put(Constants.PARAM_CHECK_IN_TIME, tempFormData.getCheckin_time());
                dataJson.put(Constants.PARAM_CHECK_OUT_TIME, tempFormData.getCheckout_time());
            }
            if (formData.isSubForm() && formData.getSubFormOtherData() != null) {
                dataJson.put(PARAM_SUBMITTED_FOR, formData.getSubFormOtherData().getCrewId());
            }
            dataJson.put(Constants.PARAM_LANG, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
            long submittedTime;
            if (tempFormData.getSf_submited() <= 0)
                submittedTime = System.currentTimeMillis() / 1000;
            else
                submittedTime = tempFormData.getSf_submited();

            dataJson.put(Constants.PARAM_DT, submittedTime);
            dataJson.put(Constants.PARAM_FORM_NAME, tempFormData.getFormName());
            dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        uploadOtherData.setTitle(getString(R.string.form_, formData.getFormName()));
        uploadOtherData.setImageUploaded(isImageUploaded);
        uploadOtherData.setRequestedData(dataJson.toString());
        uploadOtherData.setData(data);
        tblUploadData.insertFormData(uploadOtherData, isTMForm);
        if (isImageUploaded) {
            if (!formData.isSubForm() && !formData.hasSubForm()) {
                removeDataFromTableAfterFormSubmitted(isTMForm, formData);
            }
            if (!formData.isSubForm()) {
                insertCopyData(isTMForm, formData);
            }
            sendBroadCastForStartOtherDataUpload();
        }

    }


    protected void insertCopyData(boolean isTMForm, FormData formData) {
        FormData newFormData = new FormData();
        newFormData.setFormId(formData.getFormId());
        newFormData.setFormName(formData.getFormName());
        newFormData.setFormData(formData.getFormData());
        newFormData.setModifiedFormData(formData.getFormData());
        newFormData.setFormCreated(System.currentTimeMillis());
        newFormData.setFormUpdated(formData.getFormUpdated());
        newFormData.setSiteId(formData.getSiteId());
        newFormData.setIsCheckInOut(formData.getIsCheckInOut());
        newFormData.setCheckInOutComplete(formData.isCheckInOutComplete());
        newFormData.setPlotOnMap(formData.isPlotOnMap());
        newFormData.setPreSelectServices(formData.isPreSelectServices());
        if (formData.getIsCheckInOut() && formData.isCheckInOutComplete()) {
            newFormData.setCheckInOutFormComplete(true);
        }
        if (!isTMForm) {
            if (!formData.isSubForm()) {
                TblForms tblForms = new TblForms(this);
                tblForms.insertSingleFormData(newFormData);
            }
        } else {
            TblTMForms tblTNMForms = new TblTMForms(this);
            if (checkAndUpdateFormCompletedStatus(formData)) {
                newFormData.setCheckInOutFormComplete(true);
                EventBus.getDefault().post(new SiteDataUpdateEvent(newFormData.getSiteId()));
            } else {
                newFormData.setCheckInOutFormComplete(false);
            }
            if (!formData.isSubForm()) {
                tblTNMForms.insertSingleFormData(newFormData);
            }
        }
    }


    private boolean checkAndUpdateFormCompletedStatus(FormData formData) {
        if (formData.getIsCheckInOut() && formData.isCheckInOutComplete()) {
            ServiceTaskStatus taskStatus = StaticUtils.checkServiceTypeAsTask(formData.getModifiedFormData());
            if (taskStatus.isServiceTaskAvailable()) {
                if (taskStatus.isCompleted()) {
                    formData.setCheckInOutFormComplete(true);
                    return true;
                } else {
                    formData.setCheckInOutFormComplete(false);
                    return false;
                }
            } else {
                formData.setCheckInOutFormComplete(true);
                return true;
            }
        }
        return false;
    }

    protected void removeDataFromTableAfterFormSubmitted(boolean isTmForm, FormData formData) {
        if (isTmForm) {
            TblTMForms tblTNMForms = new TblTMForms(this);
            if (!formData.hasSubForm() || (formData.hasSubForm() && formData.isFormSubmitted() && StaticUtils.getTMFormPendingCount(formData.getFormPKId()) <= 0)) {
                //tblTNMForms.deleteDataByMainFormPKId(TblTMForms.TABLE_NAME, formData.getFormPKId());
                CustomLogKt.errorLog("removeDataFromTableAfterFormSubmitted", "FormPKId:::" + formData.getFormPKId());
                tblTNMForms.deleteDataByPKId(formData.getFormPKId());
                deleteImagesFromStorage(formData.getImageData());
            }
        } else {
            TblForms tblForms = new TblForms(this);
            if (!formData.hasSubForm() || (formData.hasSubForm() && formData.isFormSubmitted() && StaticUtils.getPendingCount(formData.getFormPKId()) <= 0)) {
                //tblForms.deleteDataByMainFormPKId(TblForms.TABLE_NAME, formData.getFormPKId());
                tblForms.deleteDataByPKId(formData.getFormPKId());
                deleteImagesFromStorage(formData.getImageData());
            }
        }
    }

    protected void checkAndDeleteCheckInMapData(FormData formData) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        if (tblCheckInMap.isDataExist()) {
            CheckInMap mCheckInMap = tblCheckInMap.getDataByFormId(formData.getFormPKId());
            if (mCheckInMap != null && mCheckInMap.getPkId() > 0) {
                tblCheckInMap.deleteDataById(formData.getFormPKId());
            }
        }
    }

    /**
     * Remove dump data from check in map and correct error.
     * this case is rarest of the rare but we did this to get out
     * user from looping. usually this case happen when data in is check in map but not in tblTmForm table
     */
    protected void deleteCheckInMapData(int pkId) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        if (tblCheckInMap.isDataExist()) {
            try {
                tblCheckInMap.deleteDataByPkId(pkId);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    /**
     * Remove dump data from check in map and correct error.
     * this case is rarest of the rare but we did this to get out
     * user from looping. usually this case happen when data in is check in map but not in tblTmForm table
     */
    protected void deleteCheckInMapDataByPK(int pkId) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        if (tblCheckInMap.isDataExist()) {
            try {
                tblCheckInMap.deleteDataByPkId(pkId);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    protected class InsertOrUpdateOtherData extends AsyncTask<Void, Void, Boolean> {

        Response<AppDataResponse> apiResponse;
        boolean fromLogin;

        public InsertOrUpdateOtherData(Response<AppDataResponse> apiResponse, boolean fromLogin) {
            this.apiResponse = apiResponse;
            this.fromLogin = fromLogin;
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            return saveUserAppDataInDataBase(apiResponse);
        }

        @Override
        protected void onPostExecute(Boolean success) {
            StaticUtils.setMd5Data(md5Keys);
            md5Keys = StaticUtils.getMd5ClassData(AppPrefShared.getString(PREF_MD5_KEY_DATA, ""));
            if (getApiCallBack() != null && !fromLogin) {
                getApiCallBack().onSuccessResponse(apiResponse);
            }
            EventBus.getDefault().post(new UserDataSavedEvent());
        }

    }


    private void doAfterResponseReceived(AppDataResponse body) {
        List<SiteData> lstSiteDataTemp = new ArrayList<>();
        AppPrefShared.putValue(Constants.PARAM_ROUTES_ACTIVE, body.isRoutesActive());
        SiteListConfig siteListConfig = body.getSiteListConfig();
        String newMd5 = StaticUtils.getMD5String(new Gson().toJson(siteListConfig));
        if (md5Keys.getSiteListConfigKey().equals(newMd5)) {
            return;
        }

        md5Keys.setSiteListConfigKey(newMd5);
        AppPrefShared.putValue(Constants.PARAM_SITE_ORDERED, siteListConfig.getOrdered());
        AppPrefShared.putValue(Constants.PARAM_ALL_SITES, siteListConfig.getAllSites());
        AppPrefShared.putValue(Constants.PARAM_SITE_FILTER_DISTANCE, siteListConfig.getSiteFilterDistance());

        TblTMForms tblTNMForms = new TblTMForms(getApplicationContext());
        List<SiteConfig> lstSiteConfigs = siteListConfig.getSiteConfig();

        if (lstSiteConfigs != null && !lstSiteConfigs.isEmpty()) {
            tblTNMForms.updateFormStatus(0);
            //List<FormData> lstTmFormData = new ArrayList<>();
            HashMap<Long, List<Integer>> lstSiteFormData = new HashMap<>();
            TblProperties tblProperties = new TblProperties(getApplicationContext());
            for (int i = 0; i < lstSiteConfigs.size(); i++) {
                SiteConfig siteConfig = lstSiteConfigs.get(i);
                PropertiesVo propertiesVo = tblProperties.getPropertyDataFromId(siteConfig.getSiteID());
                Location location = PropertyUtils.getPropertyLocation(propertiesVo);
                if (location == null) {
                    continue;
                }
                SiteData siteData = new SiteData();
                propertiesVo.setLat(location.getLatitude());
                propertiesVo.setLon(location.getLongitude());
                siteData.setPropertyLatitude(location.getLatitude());
                siteData.setPropertyLongitude(location.getLongitude());
                siteData.setSiteId(siteConfig.getSiteID());
                siteData.setOrdered(siteListConfig.getOrdered());
                siteData.setAllSites(siteListConfig.getAllSites());
                siteData.setSiteName(propertiesVo.getPropertyName());
                siteData.setPropertyAddress(propertiesVo.getPropertyAddress());
                siteData.setCreatedDate(System.currentTimeMillis());
                siteData.setUpdatedDate(System.currentTimeMillis());
                siteData.setPropertyData(String.valueOf(new Gson().toJsonTree(propertiesVo)));
                if (MainActivity.currentLatitude == 0.0 || MainActivity.currentLongitude == 0.0) {
                    siteData.setDistance(0.0);
                } else {
                    siteData.setDistance(PropertyUtils.getDistance(location.getLatitude(), location.getLongitude(), MainActivity.currentLatitude, MainActivity.currentLongitude));
                }
                siteData.setFormID(siteConfig.getFormId());
                siteData.setSiteConfig(siteConfig);
                lstSiteFormData.put(siteData.getSiteId(), siteData.getFormID());
                lstSiteDataTemp.add(siteData);
            }
            insertOrUpdateSiteData(lstSiteDataTemp);
            getSiteFormData(siteListConfig, lstSiteFormData);
        } else {
            tblTNMForms.removeAllDataFromTable();
            TblSites tblSites = new TblSites(getApplicationContext());
            tblSites.deleteDataFromTable();
            TblCheckInMap tblCheckInMap = new TblCheckInMap(getApplicationContext());
            tblCheckInMap.deleteDataFromTable();
            TblRoutes tblRoutes = new TblRoutes(getApplicationContext());
            tblRoutes.deleteDataFromTable();
            StaticUtils.resetCheckInDetailData();
            setBroadcastToFinishDetailActivity();
        }

    }

    private void getSiteFormData(SiteListConfig siteListConfig, HashMap<Long, List<Integer>> lstSiteFormData) {
        HashMap<Long, FormData> hashMapSiteListConfig = new HashMap<>();
        for (FormData data : siteListConfig.getSiteForms()) {
            hashMapSiteListConfig.put(data.getFormId(), data);
        }
        ;
        List<FormData> lstTmFormData = new ArrayList<>();
        for (Long key : lstSiteFormData.keySet()) {
            List<Integer> lstValue = lstSiteFormData.get(key);
            if (lstValue != null) {

                for (Integer formID : lstValue) {
                    FormData formData = hashMapSiteListConfig.get(formID.longValue());
                    if (formData != null) {
                        try {
                            FormData newFormData = (FormData) formData.clone();
                            newFormData.setSiteId(key);
                            newFormData.setFormCreated(System.currentTimeMillis());
                            lstTmFormData.add(newFormData);
                        } catch (CloneNotSupportedException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                }
            }
        }
        insertOrUpdateSiteFormData(lstTmFormData);
    }

    /**
     * Send broadcast to TMFormDetail, Sketch and Camera Activity if currently opened.
     * It will tell those screen to finish it because that form no more exist for the user.
     */
    private void setBroadcastToFinishDetailActivity() {
        Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
        intent.putExtra(Constants.DELETED_WP_FORM_ALL_DATA, true);
        sendBroadcast(intent);
    }

    public void checkLocationAvailabilityAndShowAlert() {
        if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 0) {
            return;
        }

        //If isGPSRequired = 2 then app must have fine location permission else ask user to grant fine location
        if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 2) {
            if (checkFinePermissionLocationGranted() && canGetLocation(this)) {
                return;
            }
        }
        //If isGPSRequired = 1 then app should  have Approx or fine location permission else ask user to grant fine location
        if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 1) {
            if (checkApproxPermissionLocationGranted() && canGetLocation(this)) {
                return;
            }

        }
        navigateToOverlayScreen();
    }

    private void navigateToOverlayScreen() {
        if (!TextUtils.isEmpty(PermissionOverlayActivity.class.getCanonicalName()) && BaseApplication.getInstance().getTopActivityName() != null && !BaseApplication.getInstance().getTopActivityName().equalsIgnoreCase(PermissionOverlayActivity.class.getCanonicalName())) {
            Intent intent = new Intent(this, PermissionOverlayActivity.class);
            startActivityForResult(intent, OVERLAY_ACTIVITY_REQUEST);
        }
    }

    /**
     * Method to submit all pending issues at form submission time
     */
    public void submitIssueAtFormSubmissionTime(Context context, boolean isTMForm, SiteData siteData, FormData formData) {
        JSONArray notSubmittedIssueDataArray = StaticUtils.getNotSubmittedIssueDataJson(formData.getModifiedFormData());
        if (notSubmittedIssueDataArray != null) {
            for (int i = 0; i < notSubmittedIssueDataArray.length(); i++) {
                try {
                    JSONObject issueJsonData = notSubmittedIssueDataArray.getJSONObject(i);
                    if (issueJsonData.has(Constants.VALUE)) {
                        String issueReason = "";
                        if (issueJsonData.has(Constants.ISSUES_TEXE)) {
                            issueReason = issueJsonData.getString(Constants.ISSUES_TEXE);
                        }

                        if (issueJsonData.getJSONArray(Constants.VALUE).length() == 0 && TextUtils.isEmpty(issueReason)) {
                            continue;
                        }

                        StaticUtils.submitIssueData(context, isTMForm, siteData, formData, issueReason, issueJsonData, issueJsonData.getInt(Constants.ID));
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
    }


    private void CheckAndUpdateServiceStopTime(JSONObject object) {
        if (object.has("serviceType")) {
            try {
                String serviceType = object.getString("serviceType");
                if (serviceType.equalsIgnoreCase("AsTimer")) {
                    if (object.has(Constants.START_TIME)) {
                        if (object.getLong(Constants.START_TIME) > 0 && object.getLong(Constants.STOP_TIME) == 0) {
                            object.put(Constants.STOP_TIME, System.currentTimeMillis() / 1000);
                        }
                    }
                    if (object.has("TnMservice")) {
                        TMService tMService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                        object.put(Constants.TIMELOG, StaticUtils.getTimeLogsForServiceAsATimer(tMService));
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }

    }

    protected void updateStopServiceTimeAtSubmissionTime(JSONObject updatedJsonObject) {
        readJsonFileForService(updatedJsonObject);
    }

    protected void readJsonFileForService(JSONObject updatedJsonObject) {
        try {
            JSONArray jsonArray = updatedJsonObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            if (array != null) {
                                jsonArrayReadForService(array);
                            }
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private void jsonArrayReadForService(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.getString(Constants.TYPE).equals(Constants.PANEL)) {
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    if (array != null) {
                        jsonArrayReadForService(array);
                    }
                } else {
                    objectReadForService(object);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private void objectReadForService(JSONObject object) {
        try {
            String type = (String) object.get(Constants.TYPE);
            switch (type) {
                case Constants.SERVICE:
                    CheckAndUpdateServiceStopTime(object);
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void addTMDataInUploadQueue(boolean isTMForm, SiteData siteData, FormData formData) {

        UploadOtherData uploadOtherData = new UploadOtherData();
        TblUploadData tblUploadData = new TblUploadData(this);

        uploadOtherData.setDataType(Constants.WP_DATA);

        String wpData = StaticUtils.getWPDataFromForm(this, formData.getModifiedFormData(), formData, siteData);
        if (TextUtils.isEmpty(wpData)) {
            //CustomLog.error("BaseFormDetailActivity","TM DATA NOT FOUND");
            //Do nothing if there is not WP components in the Form
            return;
        }
        JSONObject dataJson = new JSONObject();
        try {
            dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            dataJson.put(Constants.PARAMS_WP_DATA, wpData);
            dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
            dataJson.put(Constants.PARAM_LON, MainActivity.currentLongitude);
            dataJson.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
            if (siteData != null) {
                dataJson.put(Constants.PARAM_WPSITEID, siteData.getSiteId());
            } else {
                dataJson.put(Constants.PARAM_WPSITEID, -2);
            }
            dataJson.put(Constants.PARAM_WPPROFILEID, formData.getFormId());
            dataJson.put(Constants.PARAM_SUBMITTED_TIME, System.currentTimeMillis() / 1000);
            dataJson.put(Constants.PARAM_DT, System.currentTimeMillis() / 1000);
            if (isTMForm) {
                dataJson.put(Constants.PARAM_SOURCE, "Sites");
            } else {
                dataJson.put(Constants.PARAM_SOURCE, "Forms");
            }
            dataJson.put(Constants.PARAM_LANG, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
            dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        uploadOtherData.setImageUploaded(true);
        uploadOtherData.setData(wpData);
        if (isTMForm)
            uploadOtherData.setTmFormPKId(formData.getFormPKId());
        else
            uploadOtherData.setFormPKId(formData.getFormPKId());

        long currentTime = System.currentTimeMillis();
        uploadOtherData.setCreatedAt(currentTime);
        uploadOtherData.setUpdatedAt(currentTime);
        uploadOtherData.setProcessStartTime(currentTime);
        uploadOtherData.setRequestedData(dataJson.toString());
        uploadOtherData.setTitle(getString(R.string.tm_data_, formData.getFormName()));
        uploadOtherData.setFormSubmissionId(formData.getFormSubmissionId());
        tblUploadData.insertWPData(uploadOtherData);
        sendBroadCastForStartOtherDataUpload();
    }

    @Override
    protected void onStop() {
        super.onStop();
        activityDidBackground();
    }

    protected void navigateToWebViewScreen(Context context, String
            url, String title) {
        Intent intent = new Intent(context, AppWebViewActivity.class);
        intent.putExtra("url", url);
        intent.putExtra("title", title);
        startActivity(intent);
    }

    public void startLocationServiceIfNotStarted() {
        LocationService.startService(this);
    }


    public void navigateToPropertyScreenFromForm(int tagId, int requestCode) {
        Intent propertyMapActivityIntent = new Intent(this, PropertyMapActivity.class);
        propertyMapActivityIntent.putExtra(Constants.IS_FORM_COMPONENT, true);
        propertyMapActivityIntent.putExtra(Constants.TAG_ID, tagId);
        startActivityForResult(propertyMapActivityIntent, requestCode);
        overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
    }

    public void navigateToPropertyScreen(int requestCode) {
        Intent propertyMapActivityIntent = new Intent(this, PropertyMapActivity.class);
        startActivityForResult(propertyMapActivityIntent, requestCode);
        overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
    }


    public void manageSearchViewCloseTouchEvent(AppCompatEditText edtSearch) {
        edtSearch.setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                Drawable drawableRight = edtSearch.getCompoundDrawables()[2];
                if (drawableRight != null) {
                    if (motionEvent.getRawX() >= edtSearch.getRight() - edtSearch.getPaddingEnd() - drawableRight.getBounds().width() - 30) {
                        edtSearch.setText("");
                        return true;
                    }
                }
            }
            return false;
        });
    }

    public boolean isLocationAndGPSEnabled() {
        if (PermissionUtils.hasPermissions(this, PermissionUtils.getLocationPermissions())) {
            return canGetLocation(this);
        }
        return false;

    }

    public void requestToUpdateFCMToken() {
        if (!BaseApplication.getInstance().isOnline(this))
            return;
        ApiInterface apiService = RetrofitProvider.createService(ApiInterface.class);
        HashMap<String, Object> params = new HashMap<>();
        params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, ""));
        params.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
        params.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
        params.put(Constants.PARAM_LON, MainActivity.currentLongitude);
        params.put(Constants.PARAM_LANG, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
        params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        params.put(Constants.PARAM_FCM_TOKEN, AppPrefShared.getString(Constants.USER_FCM_TOKEN, ""));
        params.put(Constants.PARAM_DEVICE_TYPE, Build.MANUFACTURER);
        CustomLogKt.errorLog("FCM", AppPrefShared.getString(Constants.USER_FCM_TOKEN, ""));

        try {
            params.put(Constants.PARAM_DEVICE_MODEL, Build.MODEL.concat("-").concat(Build.DEVICE));
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            params.put(Constants.PARAM_DEVICE_MODEL, "");
        }
        params.put(Constants.PARAM_DEVICE_PLATFORM, Constants.DEVICE_PLATFORM);
        Call<SimpleResponse> call = apiService.updateFcmToken(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(Call<SimpleResponse> call, retrofit2.Response<SimpleResponse> response) {
            }

            @Override
            public void onFailure(Call<SimpleResponse> call, Throwable throwable) {
            }
        });
    }

    public void registerAppReceiver(BroadcastReceiver broadcastReceiver, IntentFilter intentFilter) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            //We need to set RECEIVER_EXPORTED as we upload our data on another process
            registerReceiver(broadcastReceiver, intentFilter, Context.RECEIVER_EXPORTED);
        } else {
            registerReceiver(broadcastReceiver, intentFilter);
        }
    }

    public void addSignaturInUploadingQueue(boolean isTMForm, FormData formData) {
        try {
            JSONArray imageArray = new JSONArray();
            JSONObject jsonImageData;
            FormData formData2;
            if (isTMForm) {
                TblTMForms tblTMForms = new TblTMForms(this);
                formData2 = tblTMForms.getFormDataByPKId(formData.getFormPKId());
            } else {
                TblForms tblForms = new TblForms(this);
                formData2 = tblForms.getFormDataByPKId(formData.getFormPKId());
            }
            jsonImageData = new JSONObject(formData2.getImageData());
            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);

            for (int k = 0; k < jsonImageArray.length(); k++) {
                JSONObject iJsonObject = jsonImageArray.getJSONObject(k);
                if (iJsonObject.has(Constants.IS_SIGNATURE) && iJsonObject.getBoolean(Constants.IS_SIGNATURE)) {
                    String exifDateString = null;
                    Metadata exifMetadata = null;

                    String imagePath;
                    UploadImageData uploadImageData = new UploadImageData();

                    try {
                        imagePath = StaticUtils.getRealPathFromURI(this, Uri.parse(iJsonObject.getString(IMAGEPATHLOW)));
                        try {
                            exifMetadata = ImageMetadataReader.readMetadata(new File(imagePath));
                        } catch (VerifyError | ImageProcessingException | IOException ignored) {
                        }
                        exifDateString = "";

                        if (exifMetadata != null) {
                            for (Directory directory : exifMetadata.getDirectories()) {
                                for (Tag tag : directory.getTags()) {
                                    if (tag.getTagName().equalsIgnoreCase("Date/Time")) {
                                        // Date time of image from EXIF interface .
                                        exifDateString = tag.getDescription().trim();
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    } finally {
                        uploadImageData.setImagePathLow(iJsonObject.getString(IMAGEPATHLOW));
                        uploadImageData.setBuilding(String.valueOf(formData2.getLastBuildingId()));
                        uploadImageData.setAccessCode(AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, ""));
                        uploadImageData.setEmail(AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                        uploadImageData.setValid(true);
                        uploadImageData.setUploadOriginalSize(false);

                        uploadImageData.setFormImage(false);
                        uploadImageData.setFormSign(true);
                        if (isTMForm) {
                            uploadImageData.setTmFormPkId(formData.getFormPKId());
                            uploadImageData.setWpId(formData.getSiteId());
                        } else {
                            uploadImageData.setFormPkId(formData.getFormPKId());
                            uploadImageData.setWpId(-2);
                        }
                        uploadImageData.setFormId(formData.getFormId());
                        uploadImageData.setUuid(StaticUtils.getUuid());
                        uploadImageData.setFormData(String.valueOf(formData.getFormPKId()).concat(",").concat(String.valueOf(formData.getFormId())));
                        uploadImageData.setRetryCount(0);
                        uploadImageData.setTagId(0);
                        uploadImageData.setTimeInUnix(System.currentTimeMillis() / 1000);

                        if (!TextUtils.isEmpty(exifDateString))
                            uploadImageData.setDate(exifDateString);
                        else
                            uploadImageData.setDate(DateUtil.fullDateTimeT.format(new Date()));
                        // get json object fro uploadVO object
                        TblUploadImage tblUploadImage = new TblUploadImage(this);
                        boolean isDataInserted = tblUploadImage.insertData(uploadImageData);
                        if (!isDataInserted) {
                            FirebaseEventUtils.InsertImageDataExceptionEvent(this, uploadImageData.getUuid());
                        } else {
                            FirebaseEventUtils.InsertImageDataEvent(this, uploadImageData.getUuid());
                        }
                    }
                }
            }

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Function to check if user clicked on cross mark of selected value
     *
     * @param motionEvent   MotionEvent
     * @param tvView        view
     * @param drawableRight Drawable
     * @return true if user clicked on cross icon else false
     */
    protected boolean shouldRemoveSelectedValue(MotionEvent motionEvent, TextView tvView, Drawable drawableRight) {
        return motionEvent.getRawX() >= tvView.getRight() - drawableRight.getBounds().width();
    }

}