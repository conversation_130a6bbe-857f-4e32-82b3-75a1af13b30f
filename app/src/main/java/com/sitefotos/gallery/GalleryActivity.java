package com.sitefotos.gallery;

import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.sitefotos.R;
import com.sitefotos.databinding.ActivityGalleryBinding;
import com.sitefotos.gallery.adapters.FolderAdapter;
import com.sitefotos.gallery.adapters.GalleryDataAdapter;
import com.sitefotos.gallery.interfaces.GalleryClicks;
import com.sitefotos.models.MediaModel;
import com.sitefotos.util.FirebaseEventUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class GalleryActivity extends AppCompatActivity implements AlbumCollection.AlbumCallbacks, AlbumMediaCollection.AlbumMediaCallbacks, GalleryClicks , View.OnClickListener{

    LinearLayoutManager linearLayoutManager;

    private ArrayList<AllImageModel> lstAllPhoto = new ArrayList<>();
    private ArrayList<AllImageModel> lstFolder = new ArrayList<>();
    private ArrayList<String> lstSelectedPath = new ArrayList<>();
    private ArrayList<AllImageModel> lstSelected = new ArrayList<>();
    private GalleryDataAdapter galleryDataAdapter;
    private FolderAdapter folderAdapter;

    private AlbumCollection mAlbumCollection;
    private AlbumMediaCollection mAlbumMediaCollection;
    private Cursor albumCursor;
    private ActivityGalleryBinding binding;
    private volatile boolean isRunning = false;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final int chunkSize = 500;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        init();
        mAlbumCollection = new AlbumCollection();
        mAlbumMediaCollection = new AlbumMediaCollection();
        mAlbumCollection.onCreate(this, this);
        mAlbumCollection.loadAlbums();


    }


    private void initBinding() {
        binding = ActivityGalleryBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }


    private void init() {
        setOnClickListener();
        setImageDataAdapter();
        if (lstSelected.isEmpty()) {
            binding.ivDone.setEnabled(false);
            binding.ivDone.setImageResource(R.drawable.ic_tick_gallery_gray);
        } else {
            binding.ivDone.setEnabled(true);
            binding.ivDone.setImageResource(R.drawable.ic_tick_gallery);
        }
    }

    private void setOnClickListener() {
        binding.ivBack.setOnClickListener(this);
        binding.ivDone.setOnClickListener(this);
    }

    /**
     * This method is set image adapter which
     * will be get all gallery images.
     */
    private void setPhotoAdapter() {
        folderAdapter = new FolderAdapter(lstFolder, this, this);
        binding.rvFolderView.setAdapter(folderAdapter);
        linearLayoutManager = (LinearLayoutManager) binding.rvFolderView.getLayoutManager();
    }

    /**
     * this method add selected image to list and set selected flag true for image
     * increases selected image count and set selected view to image
     */
    private void addImageToSelectedList(int position) {
        int maxImageCount = 10;
        if (lstSelected.size() >= maxImageCount) {
            Toast.makeText(this, getString(R.string.max_image_reached), Toast.LENGTH_SHORT).show();
        } else {
            if (lstSelectedPath.contains(lstAllPhoto.get(position).getImagePath())) {
                //removeDataFromList(position);
            } else {

                lstAllPhoto.get(position).setSelected(true);
                lstAllPhoto.get(position).setSelectionCount(lstAllPhoto.get(position).getSelectionCount() + 1);
                lstAllPhoto.get(position).setPosition(position);
                addSelectedImage(lstAllPhoto.get(position));
                galleryDataAdapter.updateAdapter(lstAllPhoto);
                binding.rvImageView.scrollToPosition(position);
            }
        }
    }


    /**
     * This method is set selected data into original data,
     * first of all check id is available in our selected list or not,
     * if it is available then check selected value image list
     * and set selected data on that id.
     */
    public void setSelection() {
        for (AllImageModel allImageModel : lstAllPhoto) {
            if (allImageModel.getSelectionCount() == 0) {
                allImageModel.setSelected(false);
            }
            if (!lstSelectedPath.contains(allImageModel.getImagePath())) {
                allImageModel.setSelectionCount(0);
                allImageModel.setSelected(false);
            }
        }
        galleryDataAdapter.updateAdapter(lstAllPhoto);
    }

    private void setImageDataAdapter() {
        galleryDataAdapter = new GalleryDataAdapter(lstAllPhoto, this, this);
        binding.rvImageView.setAdapter(galleryDataAdapter);
    }

    @Override
    public void directoryItemClickListener(int position) {

        int firstItem = linearLayoutManager.findFirstVisibleItemPosition();
        int lastItemCount = linearLayoutManager.findLastVisibleItemPosition();
        int middleItem = (firstItem + lastItemCount) / 2;
        int noScroll = position - middleItem;
        int scroll;
        if (noScroll < 0) {
            scroll = firstItem + noScroll;
            binding.rvFolderView.smoothScrollToPosition(Math.max(scroll, 0));
        } else {
            try {
                scroll = lastItemCount + noScroll - 1;
                if (scroll>=0 && scroll < lstFolder.size())
                    binding.rvFolderView.smoothScrollToPosition(scroll);
                else {
                    binding.rvFolderView.smoothScrollToPosition(lstFolder.size() - 1);
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }

        for (AllImageModel allImageModel : lstFolder) {
            allImageModel.setSelected(false);
        }
        lstFolder.get(position).setSelected(true);
        folderAdapter.notifyDataSetChanged();
        mAlbumMediaCollection.onDestroy();
        mAlbumMediaCollection.onCreate(this, this);
        mAlbumCollection.setStateCurrentSelection(position);
        albumCursor.moveToPosition(position);
        Album album = Album.valueOf(albumCursor);
        mAlbumMediaCollection.load(album);

    }

    @Override
    public void setOnRemoveItemClick(int position) {
//        removeDataFromList(position);
    }

    private void removeDataFromList(AllImageModel allImageModel) {
        lstSelectedPath.remove(allImageModel.getImagePath());
        lstSelected.remove(allImageModel);
        setSelection();
        if (!lstSelected.isEmpty()) {
            binding.ivDone.setEnabled(true);
            binding.ivDone.setImageResource(R.drawable.ic_tick_gallery);

            setTextToImageSize();
        } else {
            binding.tvCount.setVisibility(View.GONE);
            binding.ivDone.setEnabled(false);
            binding.ivDone.setImageResource(R.drawable.ic_tick_gallery_gray);
        }
    }


    @Override
    public void setOnImageItemClickListener(int position, int id) {
        if (!lstAllPhoto.get(position).isSelected()) {
            binding.ivDone.setEnabled(true);
            binding.ivDone.setImageResource(R.drawable.ic_tick_gallery);
            addImageToSelectedList(position);
        } else {
            removeDataFromList(lstAllPhoto.get(position));
        }
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.ivBack) {
            onBackPressed();
        } else if (viewId == R.id.ivDone) {
            if (!lstSelected.isEmpty())
                sendAllSelectedDataToRequestedScreen();
        }
    }

    private void sendAllSelectedDataToRequestedScreen() {
        Intent intent = new Intent();
        intent.putExtra("mediaData", lstSelected);
        setResult(RESULT_OK, intent);
        finish();
    }


    /**
     * This method is added all selected images or video into selected
     * recycler view.
     */
    public void addSelectedImage(AllImageModel allImageModel) {
        lstSelected.add(allImageModel);
        lstSelectedPath.add(allImageModel.getImagePath());
        setTextToImageSize();
        binding.tvCount.setVisibility(View.VISIBLE);
    }


    private void setTextToImageSize() {
        binding.tvCount.setText(String.valueOf(lstSelected.size()));
    }

    @Override
    public void onAlbumLoad(Cursor cursor) {

        ArrayList<AllImageModel> lstAllImgFolder = new ArrayList<>();
        mAlbumMediaCollection.onCreate(this, this);


        if (cursor != null && !cursor.isClosed()&& cursor.getCount() >0 && cursor.moveToFirst()) {

            albumCursor = cursor;

            cursor.moveToPosition(mAlbumCollection.getCurrentSelection());
            int columnIndex = cursor.getColumnIndex(AlbumLoader.COLUMN_URI);
            int folderIdIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.BUCKET_ID);
            int folderIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.BUCKET_DISPLAY_NAME);
            do {
                String clumn = cursor.getString(columnIndex);
                int folderId = cursor.getInt(folderIdIndex);
                String folderName = cursor.getString(folderIndex);
                Uri uri = Uri.parse(clumn != null ? clumn : "");
                if (folderName == null || folderName.isEmpty()) {
                    folderName = "Internal";
                }
                AllImageModel allImageModel = new AllImageModel(folderId, folderName, PathUtils.getPath(this, uri));
                lstAllImgFolder.add(allImageModel);

            } while (cursor.moveToNext());
            cursor.close();


            if (!isFinishing()) {
                findViewById(R.id.pbProgress).setVisibility(View.GONE);
                lstFolder.addAll(lstAllImgFolder);
                setPhotoAdapter();
                lstFolder.get(0).setSelected(true);
                cursor.moveToFirst();
                Album album = Album.valueOf(cursor);
                mAlbumMediaCollection.load(album);
            }
        }

    }

    @Override
    public void onAlbumReset() {

    }


    @Override
    public void onAlbumMediaLoad(Cursor cursor) {

        isRunning = false;
        galleryDataAdapter.clearItem();
        lstAllPhoto.clear();

        List<MediaModel> cachedCursorItems = new ArrayList<>();

        if (cursor != null && !cursor.isClosed()&& cursor.getCount() >0 && cursor.moveToFirst() ) {
            int idIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns._ID);
            int pathIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DATA);
            int nameIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DISPLAY_NAME);
            int durationIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DURATION);
            int mimeTypeIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.MIME_TYPE);
            do {
                int id = (int) cursor.getLong(idIndex);
                String path = cursor.getString(pathIndex);
                String name = cursor.getString(nameIndex);
                long duration = cursor.getLong(durationIndex);
                String mimetype = cursor.getString(mimeTypeIndex);
                cachedCursorItems.add(new MediaModel(id, name, path, duration, mimetype));
            } while (cursor.moveToNext());
            cursor.close();
        }

        new Thread(() -> {
            isRunning = true;

            List<AllImageModel> chunk = new ArrayList<>();
            for (MediaModel item : cachedCursorItems) {
                if (!isRunning) break;

                boolean isVideo = MimeType.isVideo(item.mimetype);
                if (new File(item.path).length() != 0) {
                    int position = lstSelectedPath.indexOf(item.path);
                    AllImageModel allImageModel;

                    if (position == -1) {
                        allImageModel = new AllImageModel(item.id, item.name, item.path, item.duration, isVideo);
                    } else {
                        allImageModel = lstSelected.get(position);
                        allImageModel.setSelectionCount(1);
                        allImageModel.setSelected(true);
                    }

                    chunk.add(allImageModel);
                }

                if (chunk.size() >= chunkSize) {
                    List<AllImageModel> finalChunk = new ArrayList<>(chunk);
                    mainHandler.post(() -> galleryDataAdapter.appendItems(finalChunk));
                    chunk.clear();
                }
            }

            if (!chunk.isEmpty() && isRunning) {
                mainHandler.post(() -> galleryDataAdapter.appendItems(chunk));
            }

            mainHandler.post(() -> {
                if (isRunning) setSelection();
            });
        }).start();

    }

    @Override
    public void onAlbumMediaReset() {

    }
}
