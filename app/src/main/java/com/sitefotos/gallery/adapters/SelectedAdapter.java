package com.sitefotos.gallery.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.gallery.interfaces.GalleryClicks;
import com.sitefotos.util.ImageUtil;

import java.util.ArrayList;


public class SelectedAdapter extends RecyclerView.Adapter<SelectedAdapter.ViewHolder> {

    private ArrayList<AllImageModel> lstSelectedImage;
    private Context context;
    private GalleryClicks galleryClicks;

    public SelectedAdapter(ArrayList<AllImageModel> lstAllImageVideo, Context context, GalleryClicks galleryClicks) {
        this.lstSelectedImage = lstAllImageVideo;
        this.context = context;
        this.galleryClicks = galleryClicks;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_selected_view, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AllImageModel allImageModel = lstSelectedImage.get(position);
        ImageUtil.loadImageInGlide(holder.ivGalleryPlaceholder,allImageModel.getImagePath());
        holder.ivDelete.setOnClickListener(v -> galleryClicks.setOnRemoveItemClick(position));
    }

    @Override
    public int getItemCount() {
        return lstSelectedImage.size();
    }

    public void updateAdapter(ArrayList<AllImageModel> lstAllImageVideo) {
        this.lstSelectedImage = lstAllImageVideo;
        notifyDataSetChanged();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatImageView ivGalleryPlaceholder;
        AppCompatImageView ivDelete;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivGalleryPlaceholder = itemView.findViewById(R.id.ivGalleryPlaceholder);
            ivDelete = itemView.findViewById(R.id.ivDelete);
        }
    }
}
