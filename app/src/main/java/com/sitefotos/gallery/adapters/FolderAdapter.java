package com.sitefotos.gallery.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.gallery.interfaces.GalleryClicks;
import com.sitefotos.util.ImageUtil;

import java.util.ArrayList;

public class FolderAdapter extends RecyclerView.Adapter<FolderAdapter.ViewHolder> {

    private ArrayList<AllImageModel> lstAllImageVideo;
    private Context context;
    private GalleryClicks galleryClicks;

    public FolderAdapter(ArrayList<AllImageModel> lstAllImageVideo, Context context, GalleryClicks galleryClicks) {
        this.lstAllImageVideo = lstAllImageVideo;
        this.context = context;
        this.galleryClicks = galleryClicks;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_gallery_folder_view, null);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        AllImageModel allImageModel = lstAllImageVideo.get(position);
        ImageUtil.loadImagePathInGlide(holder.ivGalleryPlaceholder,allImageModel.getImagePath());
        holder.tvImageName.setText(allImageModel.getDisplayName());
        if (allImageModel.isSelected()) {
            holder.ivSelection.setImageDrawable(context.getResources().getDrawable(R.drawable.drawable_folder_selected));
        } else {
            holder.ivSelection.setImageDrawable(context.getResources().getDrawable(R.drawable.drawable_folder_unselected));
        }
        holder.llMain.setOnClickListener(view -> galleryClicks.directoryItemClickListener(position));


    }

    @Override
    public int getItemCount() {
        return lstAllImageVideo.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        LinearLayout llMain;
        AppCompatImageView ivGalleryPlaceholder, ivSelection;
        AppCompatTextView tvImageName;

        ViewHolder(@NonNull View itemView) {
            super(itemView);
            llMain = itemView.findViewById(R.id.llMain);
            ivGalleryPlaceholder = itemView.findViewById(R.id.ivGalleryPlaceholder);
            tvImageName = itemView.findViewById(R.id.tvImageName);
            ivSelection = itemView.findViewById(R.id.ivSelection);
        }
    }
}
