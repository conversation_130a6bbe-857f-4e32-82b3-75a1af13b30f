package com.sitefotos.gallery.adapters;

import android.annotation.SuppressLint;
import android.content.Context;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.R;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.gallery.interfaces.GalleryClicks;
import com.sitefotos.util.ImageUtil;

import java.util.ArrayList;
import java.util.List;


public class GalleryDataAdapter extends RecyclerView.Adapter<GalleryDataAdapter.ViewHolder> {

    private ArrayList<AllImageModel> lstAllImageVideo;
    private Context context;
    private GalleryClicks galleryClicks;

    public GalleryDataAdapter(ArrayList<AllImageModel> lstAllImageVideo, Context context, GalleryClicks gallaryClicks) {
        this.lstAllImageVideo = lstAllImageVideo;
        this.context = context;
        this.galleryClicks = gallaryClicks;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_gallery_view, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, final int position) {
        final AllImageModel allImageModel = lstAllImageVideo.get(position);
        if (allImageModel != null) {
            if (allImageModel.isSelected()) {
                holder.ivSelectionCount.setVisibility(View.VISIBLE);
                holder.ivSelected.setBackgroundColor(ContextCompat.getColor(context, R.color.transparent_primary_50));
            } else {
                holder.ivSelectionCount.setVisibility(View.GONE);
                holder.ivSelected.setBackgroundColor(ContextCompat.getColor(context, R.color.transparent));
            }
            ImageUtil.loadImageInGlide(holder.ivGalleryPlaceholder, allImageModel.getImagePath());
            holder.llMain.setOnClickListener(v -> galleryClicks.setOnImageItemClickListener(position, allImageModel.getId()));
            if (allImageModel.isVideo()) {
                holder.tvVideoDuration.setText(DateUtils.formatElapsedTime(allImageModel.getDuration() / 1000));
            } else {
                holder.tvVideoDuration.setVisibility(View.GONE);
            }
        }
    }

    public void updateAdapter(ArrayList<AllImageModel> lstAllImageVideo) {
        this.lstAllImageVideo = lstAllImageVideo;
        notifyDataSetChanged();
    }

    @SuppressLint("NotifyDataSetChanged")
    public void appendItems(List<AllImageModel> newItems) {
        ArrayList<AllImageModel> items = (ArrayList<AllImageModel>) newItems;
        int oldSize = lstAllImageVideo.size();
        lstAllImageVideo.addAll(items);
        notifyItemRangeInserted(oldSize,oldSize + items.size());
    }

    @SuppressLint("NotifyDataSetChanged")
    public void clearItem() {
        lstAllImageVideo.clear();
        notifyDataSetChanged();
    }


    @Override
    public int getItemCount() {
        return lstAllImageVideo.size();
    }

    public class ViewHolder extends RecyclerView.ViewHolder {
        AppCompatImageView ivGalleryPlaceholder, ivSelected;
        RelativeLayout llMain;
        AppCompatTextView tvVideoDuration;
        AppCompatImageView ivSelectionCount;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivGalleryPlaceholder = itemView.findViewById(R.id.ivGalleryPlaceholder);
            ivSelected = itemView.findViewById(R.id.ivSelected);
            llMain = itemView.findViewById(R.id.llMain);
            ivSelectionCount = itemView.findViewById(R.id.ivSelectionCount);
            tvVideoDuration = itemView.findViewById(R.id.tvVideoDuration);

        }
    }
}
