package com.sitefotos.gallery;

import java.io.Serializable;

public class AllImageModel implements Serializable {
    private String imagePath;
    private int id;
    private String displayName;
    private long bucketId;
    private boolean isSelected;
    private int selectionCount;
    private int position;


    public boolean isVideo() {
        return isVideo;
    }

    public void setVideo(boolean video) {
        isVideo = video;
    }

    private boolean isVideo;

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    private long duration;


    public AllImageModel(int bucketId, String folderName, String path) {
        this.bucketId = bucketId;
        this.displayName = folderName;
        this.imagePath = path;


    }

    public AllImageModel(int id, String folderName, String path, long duration, boolean isVideo) {
        this.bucketId = id;
        this.displayName = folderName;
        this.imagePath = path;
        this.duration = duration;
        this.isVideo = isVideo;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }


    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }


    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }


    public long getBucketId() {
        return bucketId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getSelectionCount() {
        return selectionCount;
    }

    public void setSelectionCount(int selectionCount) {
        this.selectionCount = selectionCount;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }


}
