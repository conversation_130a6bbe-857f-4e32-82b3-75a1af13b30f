package com.sitefotos.login;

import static com.sitefotos.Constants.PARAM_FCM_TOKEN;
import static com.sitefotos.Constants.PARAM_STAG;
import static com.sitefotos.Constants.PREF_MD5_KEY_DATA;
import static com.sitefotos.util.StaticUtils.isValidCompanyId;
import static com.sitefotos.util.StaticUtils.isValidEmailId;
import static com.sitefotos.util.StaticUtils.isValidUrl;

import android.annotation.SuppressLint;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.messaging.FirebaseMessaging;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityLoginBinding;
import com.sitefotos.event.UserDataSavedEvent;
import com.sitefotos.language.AppLanguageActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.service.FileUploaderService;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;
import com.sitefotos.webview.WebViewActivity;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.HashMap;
import java.util.concurrent.TimeUnit;

import retrofit2.Response;

public class LoginActivity extends BaseActivity implements OnAppDataApiResponse, View.OnClickListener {

    int developerOptionCount = 0;
    int totalTap = 6;
    long starTime = 0;
    int maxDevTapSecond = 7;
    int toastTime = 0;
    boolean isShowToast = false;
    private Context context;
    private String mailAddress;
    private String companyId;

    private ActivityLoginBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        setLocale(this);
        init();
        DBOpenHelper.getInstance(context).truncateDatabase();
    }


    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return this;
    }

    private void init() {
        initBinding();
        setToolbarVisibility(false);
        getBundleData();
        setOnClickListener();
        setEditTextListeners(binding.edtEmailAddress, true);
        setEditTextListeners(binding.edtCompanyId, true);
        AppPrefShared.putValue(Constants.BASE_URL, BuildConfig.BASE_URL);
        changeDeveloperLayout();
        setBaseUrlData();
        AppPrefShared.putValue(PARAM_STAG, "");
        getFcmToken();
        if (isLocationAndGPSEnabled()) {
            startLocationServiceIfNotStarted();
        }
    }
    private void initBinding() {
        binding = ActivityLoginBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        manageSearchViewCloseTouchEvent(binding.edtChangeUrl);
    }

    private void setOnClickListener() {
        binding.btnLogin.setOnClickListener(this);
        binding.tvTermsOfService.setOnClickListener(this);
        binding.tvPrivacyPolicy.setOnClickListener(this);
        binding.ivLanguage.setOnClickListener(this);
        binding.imgHeader.setOnClickListener(this);
        binding.btnSave.setOnClickListener(this);
    }

    private void getBundleData() {
        if (getIntent() != null) {
            String email = null;
            String compId = null;
            if (getIntent().hasExtra("email")) {
                email = getIntent().getStringExtra("email");
                binding.edtEmailAddress.setText(email);
                if (email != null) {
                    binding.edtEmailAddress.setSelection(email.length());
                }
            } else {
                if (!TextUtils.isEmpty(mailAddress)) {
                    binding.edtEmailAddress.setText(mailAddress);
                    binding.edtEmailAddress.setSelection(mailAddress.length());
                }
            }

            if (getIntent().hasExtra("companyId")) {
                compId = getIntent().getStringExtra("companyId");
                binding.edtCompanyId.setText(compId);
                if (compId != null) {
                    binding.edtCompanyId.setSelection(compId.length());
                }
            } else {
                if (!TextUtils.isEmpty(companyId)) {
                    binding.edtCompanyId.setText(companyId);
                    binding.edtCompanyId.setSelection(companyId.length());
                }
            }

           /* if (TextUtils.isEmpty(email) && TextUtils.isEmpty(compId)) {
                if (getIntent().hasExtra("url")) {
                    openDeviceBrowser(getIntent().getStringExtra("url"));
                }
            }*/
        }
    }

    private void setBaseUrlData() {
        binding.edtChangeUrl.setText(AppPrefShared.getString(Constants.BASE_URL, BuildConfig.BASE_URL));
        if (binding.edtChangeUrl.getText() != null)
            showOrHideCrossInEditText(binding.edtChangeUrl.getText().toString());
        binding.edtChangeUrl.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                showOrHideCrossInEditText(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });
    }

    private void openDeviceBrowser(String url) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.setPackage("com.android.chrome");
        try {
            context.startActivity(intent);
        } catch (ActivityNotFoundException ex) {
            // Chrome browser presumably not installed so allow user to choose instead
            intent.setPackage(null);
            context.startActivity(intent);
        }
    }

    private boolean validate() {
        int companyCharCount = 0;

        if (binding.edtCompanyId.getText() != null) {
            companyCharCount = binding.edtCompanyId.getText().toString().trim().length();
        }
        if (binding.edtEmailAddress.getText() != null && binding.edtEmailAddress.getText().toString().trim().isEmpty()) {
            binding.edtEmailAddress.setError(getString(R.string.email_address_must_not_be_empty));
            showForeGroundToast(getString(R.string.email_address_must_not_be_empty));
            return false;
        } else if (!isValidEmailId(binding.edtEmailAddress.getText().toString().trim())) {
            binding.edtEmailAddress.setError(getString(R.string.email_address_must_be_valid));
            showForeGroundToast(getString(R.string.email_address_must_be_valid));
            return false;
        } else if (companyCharCount == 0) {
            binding.edtCompanyId.setError(getString(R.string.company_id_must_not_be_empty));
            showForeGroundToast(getString(R.string.company_id_must_not_be_empty));
            return false;
        } else if (8 > companyCharCount || companyCharCount > 32) {
            binding.edtCompanyId.setError(getString(R.string.msg_company_id_validation));
            showForeGroundToast(getString(R.string.msg_company_id_validation));
            return false;
        } else if (!isValidCompanyId(binding.edtCompanyId.getText().toString().trim())) {
            String specialChar = "#-_.,/&:+’@($`é";
            binding.edtCompanyId.setError(getString(R.string.msg_company_id_invalid,specialChar));
            showForeGroundToast(getString(R.string.msg_company_id_invalid,specialChar));
            return false;
        } else {
            return true;
        }
    }


    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        if (binding.edtCompanyId.getText() != null)
            companyId = binding.edtCompanyId.getText().toString().trim();
        if (binding.edtEmailAddress.getText() != null)
            mailAddress = binding.edtEmailAddress.getText().toString().trim();
        if (linearContentLayout.getChildCount() > 0) {
            linearContentLayout.removeAllViews();
        }
        init();
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.btnLogin) {
            doLoginUser();
        } else if (viewId == R.id.tvTermsOfService) {
            navigateUserToWebViewScreen(Constants.URL_TERMS_OF_SERVICE);
        } else if (viewId == R.id.tvPrivacyPolicy) {
            navigateUserToWebViewScreen(Constants.URL_PRIVACY_POLICY);
        } else if (viewId == R.id.ivLanguage) {
            navigateToSelectLanguageScreen();
        } else if (viewId == R.id.imgHeader) {
            checkAndEnableDevMode();
        } else if (viewId == R.id.btnSave) {
            saveBaseUrl();
        }
    }

    private void checkAndEnableDevMode() {
        if (!AppPrefShared.getBoolean(Constants.IS_DEVELOPER_OPTION_ON, false)) {
            if (!isShowToast) {
                checkDevOption();
            }
        }
    }

    private void navigateUserToWebViewScreen(String link) {
        Intent webViewIntent = new Intent(this, WebViewActivity.class);
        webViewIntent.putExtra("link", link);
        startActivity(webViewIntent);
    }

    private void doLoginUser() {
        if (validate()) {
            hideSoftKeyboard(LoginActivity.this);
            if (BaseApplication.getInstance().isOnline(context)) {
                getAndLockScreenOrientation();
                showProgress(getString(R.string.logging_In), false);
                HashMap<String, Object> params = new HashMap<>();
                if (binding.edtEmailAddress.getText() != null)
                    params.put(Constants.PARAM_EMAIL, binding.edtEmailAddress.getText().toString().trim());
                if (binding.edtCompanyId.getText() != null)
                    params.put(Constants.PARAM_ACCESS_CODE, binding.edtCompanyId.getText().toString().trim());
                params.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
                params.put(Constants.PARAM_LON, MainActivity.currentLongitude);
                params.put(Constants.PARAM_LANG, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
                params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
                requestServerForUserData(this, true, true, params);
            } else {
                showForeGroundToast(getString(R.string.internet_not_available));
            }
        }
    }


    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Override
    protected void onPause() {
        super.onPause();

    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUserDataSavedEvent(UserDataSavedEvent event) {
        //startOrStopBreadCrumbService(this);
        FirebaseEventUtils.setUserData(this);
        BaseApplication.getInstance().checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);
        stopProgress();
        navigateUserToMainScreen();
    }

    private void navigateUserToMainScreen() {
        Intent intent = new Intent(context, MainActivity.class);
        startActivity(intent);
        supportFinishAfterTransition();
    }

    @SuppressLint("SourceLockedOrientationActivity")
    private void getAndLockScreenOrientation() {
        if (getResources().getConfiguration().orientation == Configuration.ORIENTATION_LANDSCAPE) {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        } else {
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
        }
    }

    private void navigateToSelectLanguageScreen() {
        Intent intent = new Intent(this, AppLanguageActivity.class);
        startActivity(intent);

    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        try {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    @Override
    public void onSuccessResponse(Response<AppDataResponse> response) {
        if (response.body() != null){
            AppDataResponse appUserDataResponse = response.body();
            if (appUserDataResponse.isError()) {
                stopProgress();
                showForeGroundToast(response.body().getMessage());
            } else {
                if (response.code() == 200) {
                    manageLoginResponse(response);
                }
            }
        }else{
            stopProgress();
            showForeGroundToast(getString(R.string.some_error_occured));
            finish();
        }

    }

    @Override
    public void onFailureResponse(Throwable t) {
        stopProgress();
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
        showForeGroundToast(getString(R.string.some_error_occured));
    }

    @Override
    public void onNoInternetConnection() {
        showForeGroundToast(getString(R.string.internet_not_available));
    }

    @Override
    public void onUpdateAppVersion(int versionCode, boolean isForceUpdate) {

    }

    private void manageLoginResponse(Response<AppDataResponse> response) {
        AppDataResponse appUserDataResponse = response.body();
        if (appUserDataResponse != null) {
            String userType = appUserDataResponse.getUserType();
            if (!TextUtils.isEmpty(userType) && !userType.equalsIgnoreCase(Constants.PARAM_INVALID_ACCESS_CODE)) {
                if (binding.edtEmailAddress.getText() != null)
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, binding.edtEmailAddress.getText().toString().trim());
                if (binding.edtCompanyId.getText() != null)
                    AppPrefShared.putValue(Constants.LOGGED_IN_USER_COMPANY_ID, binding.edtCompanyId.getText().toString().trim()); // Use static company ID/access code for testing bugs
                md5Keys = StaticUtils.getMd5ClassData(AppPrefShared.getString(PREF_MD5_KEY_DATA, ""));
                userLoginBreadcrumb();
                doAfterAppUserDataResponseReceived(response, true);

            } else {
                stopProgress();
                AppPrefShared.putValue(PARAM_STAG, "");
                PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name), getString(R.string.verify_company_id),
                        getString(R.string.dismiss), false, (dialog, which) -> dialog.dismiss());
            }
        } else {
            stopProgress();
            showForeGroundToast(getString(R.string.some_error_occured));
            finish();
        }
    }


    public void checkDevOption() {
        toastTime = 0;
        if (developerOptionCount > 1) {
            long diff = System.currentTimeMillis() - starTime;
            if (TimeUnit.MILLISECONDS.toSeconds(diff) >= maxDevTapSecond) {
                developerOptionCount = 0;
            }
        }
        developerOptionCount += 1;
        if (developerOptionCount == 1) {
            starTime = System.currentTimeMillis();
        }
        if (developerOptionCount == totalTap) {
            showForeGroundToast(getString(R.string.msg_you_are_now_developer));
            developerOptionCount = 0;
            AppPrefShared.putValue(Constants.IS_DEVELOPER_OPTION_ON, true);
            changeDeveloperLayout();
        }
    }

    private void changeDeveloperLayout() {
        if (AppPrefShared.getBoolean(Constants.IS_DEVELOPER_OPTION_ON, false)) {
            binding.llBaseUrl.setVisibility(View.VISIBLE);

        } else {
            binding.llBaseUrl.setVisibility(View.GONE);
        }
    }


    private void showOrHideCrossInEditText(String data) {
        if (data.length() > 0) {
            binding.edtChangeUrl.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
        } else {
            binding.edtChangeUrl.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
    }


    private void saveBaseUrl() {
        if (binding.edtChangeUrl.getText() != null && !TextUtils.isEmpty(binding.edtChangeUrl.getText().toString())) {
            if (isValidUrl(binding.edtChangeUrl.getText().toString().trim())) {
                //check / al last. if not present and add at end to the url
                String url = binding.edtChangeUrl.getText().toString().trim();
                if (!url.endsWith("/")) {
                    url = url.concat("/");
                }
                if (!url.startsWith("http")) {
                    url = "http://" + url;
                }
                AppPrefShared.putValue(Constants.BASE_URL, url);
                binding.edtChangeUrl.setText(url);
                binding.edtChangeUrl.setSelection(binding.edtChangeUrl.getText().length());
                RetrofitProvider.resetInstance();
                showForeGroundToast(getString(R.string.msg_changed_base_url));
                StaticUtils.hideSoftKeyboard(this);

            } else {
                showForeGroundToast(getString(R.string.mes_enter_valid_url));
            }
        } else {
            AppPrefShared.putValue(Constants.BASE_URL, BuildConfig.BASE_URL);
            StaticUtils.hideSoftKeyboard(this);
        }
    }

    /**
     * Function to generate new fcm token for device.
     * Call for user login after getting fcm token
     */
    private void getFcmToken() {
        FirebaseMessaging.getInstance().getToken()
                .addOnCompleteListener(task -> {
                    if (!task.isSuccessful()) {
                        CustomLogKt.warning("Fetching FCM registration token failed", task.getException().getMessage());
                        return;
                    }
                    CustomLogKt.errorLog("Fcm Token", task.getResult());
                    AppPrefShared.putValue(Constants.USER_FCM_TOKEN, task.getResult());
                });
    }
}
