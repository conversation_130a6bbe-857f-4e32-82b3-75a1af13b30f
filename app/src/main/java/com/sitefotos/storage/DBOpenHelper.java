package com.sitefotos.storage;

import static com.sitefotos.Constants.PARAM_STAG;

import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.storage.tables.TblClockInfo;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblDynamicDropdownItems;
import com.sitefotos.storage.tables.TblEmployees;
import com.sitefotos.storage.tables.TblEquipment;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblMapPinData;
import com.sitefotos.storage.tables.TblMaterials;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.storage.tables.TblReadings;
import com.sitefotos.storage.tables.TblRoutes;
import com.sitefotos.storage.tables.TblServices;
import com.sitefotos.storage.tables.TblSiteProfileData;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.storage.tables.TblVehicles;
import com.sitefotos.util.FirebaseEventUtils;

public class DBOpenHelper extends SQLiteOpenHelper {

    private static final int DB_VERSION = 20;

    /**
     * Database name to be created with.
     */
    private static final String DB_NAME = "sitefotos_new.db";

    private static DBOpenHelper dbHelper;
    private static SQLiteDatabase writableDb;
    private static SQLiteDatabase readableDb;
    private Context context;

    public DBOpenHelper(Context context) {
        super(context, DB_NAME, null, DB_VERSION);
        this.context = context;
    }

    public static synchronized DBOpenHelper getInstance(Context context) {
        if (dbHelper == null) {
            dbHelper = new DBOpenHelper(context);
        }
        return dbHelper;
    }


    @Override
    public void onConfigure(SQLiteDatabase db) {
        super.onConfigure(db);
        db.enableWriteAheadLogging();
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        writableDb = db;
        db.execSQL(TblReadings.createTable());
        db.execSQL(TblServices.createTable());
        db.execSQL(TblMaterials.createTable());
        db.execSQL(TblProperties.createTable());
        db.execSQL(TblCluster.createTable());
        db.execSQL(TblForms.createTable());
        db.execSQL(TblTMForms.createTable());
        db.execSQL(TblUploadData.createTable());
        db.execSQL(TblUploadImage.createTable());
        db.execSQL(TblSiteProfileData.createTable());
        db.execSQL(TblSites.createTable());
        db.execSQL(TblEmployees.createTable());
        db.execSQL(TblClockInfo.createTable());
        db.execSQL(TblClockCrew.createTable());
        db.execSQL(TblTMForms.createIndexesForUpdate());
        db.execSQL(TblForms.createIndexesForUpdate());
        db.execSQL(TblRoutes.createTable());
        db.execSQL(TblCheckInMap.createTable());
        db.execSQL(TblCheckInMap.createIndexes());
        db.execSQL(TblMapPinData.createTable());
        db.execSQL(TblVehicles.createTable());
        db.execSQL(TblEquipment.createTable());
        db.execSQL(TblDynamicDropdownItems.createTable());

    }

    @Override
    public synchronized void onUpgrade(SQLiteDatabase sqLiteDatabase, int oldVersion, int newVersion) {
        writableDb = sqLiteDatabase;
        readableDb = sqLiteDatabase;

        if (newVersion > oldVersion) {
            try {
                TblUploadData tblUploadData = new TblUploadData(context);
                TblEmployees tblEmployees = new TblEmployees(context);
                TblUploadImage tblUploadImage = new TblUploadImage(context);
                TblServices tblServices = new TblServices(context);
                TblTMForms tblTmForms = new TblTMForms(context);
                clearAppDataApiCache();
                switch (oldVersion) {
                    case 1:
                        sqLiteDatabase.execSQL(TblMaterials.createTable());
                        sqLiteDatabase.execSQL(TblServices.createTable());
                    case 2:
                        sqLiteDatabase.execSQL(TblProperties.createTable());
                        sqLiteDatabase.execSQL(TblCluster.createTable());
                    case 3:
                        sqLiteDatabase.execSQL(TblForms.createTable());
                        sqLiteDatabase.execSQL(TblTMForms.createTable());
                        sqLiteDatabase.execSQL(TblUploadData.createTable());
                    case 5:
                        sqLiteDatabase.execSQL(TblUploadImage.createTable());
                        if (isTableExists(sqLiteDatabase, TblUploadData.TABLE_NAME)) {
                            tblUploadData.checkAndAlterTableTitle();
                            tblUploadData.checkAndAlterTableFormPKId();
                            tblUploadData.checkAndAlterTableIsImageUploaded();
                        } else {
                            sqLiteDatabase.execSQL(TblUploadData.createTable());
                        }

                    case 6:
                        if (isTableExists(sqLiteDatabase, TblUploadData.TABLE_NAME)) {
                            tblUploadData.checkAndAlterTableUploadingTimeProcess();
                            tblUploadData.checkAndAlterTableUploadingProcess();
                        } else {
                            sqLiteDatabase.execSQL(TblUploadData.createTable());
                        }
                    case 7:
                        sqLiteDatabase.execSQL(TblSiteProfileData.createTable());
                        sqLiteDatabase.execSQL(TblSites.createTable());
                        sqLiteDatabase.execSQL(TblEmployees.createTable());
                        if (isTableExists(sqLiteDatabase, TblUploadData.TABLE_NAME)) {
                            tblUploadData.checkAndAlterTableUUID();
                            tblUploadData.checkAndAlterTableWPPKId();
                        } else {
                            sqLiteDatabase.execSQL(TblUploadData.createTable());
                        }
                        if (isTableExists(sqLiteDatabase, TblUploadImage.TABLE_NAME)) {
                            tblUploadImage.checkAndAlterTableUUID();
                            tblUploadImage.checkAndAlterTableWpId();
                            tblUploadImage.checkAndAlterTableWPPkId();
                        } else {
                            sqLiteDatabase.execSQL(TblUploadImage.createTable());
                        }

                    case 8:
                        TblSites tblSites = new TblSites(context);
                        tblSites.checkAndAlterTableFilterDistance();

                        //From app version 2.0.4
                    case 9:
                        TblSiteProfileData tblSiteProfileData = new TblSiteProfileData(context);
                        tblSiteProfileData.checkAndAlterTableIsCrewSubmitted();

                        tblEmployees.checkAndAlterTableIsSelected();
                        tblEmployees.checkAndAlterTableCreated();
                        tblEmployees.checkAndAlterTableUpdated();

                        sqLiteDatabase.execSQL(TblClockInfo.createTable());

                        TblSites tblSites1 = new TblSites(context);
                        tblSites1.checkAndAlterTableSiteConfig();
                        tblSites1.checkAndAlterTableFormId();

                        sqLiteDatabase.execSQL(TblClockCrew.createTable());

                        tblServices.checkAndAlterTableOption();
                        tblServices.checkAndAlterTableCategory();
                        tblServices.checkAndAlterTableType();
                        tblServices.checkAndAlterTableCreated();
                        tblServices.checkAndAlterTableUpdated();

                        TblMaterials tblMaterials = new TblMaterials(context);
                        tblMaterials.checkAndAlterTableCreated();
                        tblMaterials.checkAndAlterTableUpdated();


                        tblUploadImage.checkAndAlterTableTagId();
                        tblUploadImage.checkAndAlterTableTmFormPkId();
                        tblUploadImage.checkAndAlterTableCaptureTime();

                        tblUploadData.checkAndAlterTableTMFormPKId();
                        tblUploadData.checkAndAlterTableIssueTagId();

                        sqLiteDatabase.execSQL(TblTMForms.createTable());

                        //From App version 2.1.0
                    case 10:
                        tblEmployees.checkAndAltTblSearchId();
                        tblEmployees.checkAndAltTblIsCrew();
                        if (isTableExists(sqLiteDatabase, TblForms.TABLE_NAME)) {
                            TblForms tblForms = new TblForms(context);
                            tblForms.checkAndAlterTableSubmittedDate(TblForms.TABLE_NAME);
                        } else {
                            sqLiteDatabase.execSQL(TblForms.createTable());
                        }
                        if (isTableExists(sqLiteDatabase, TblTMForms.TABLE_NAME)) {
                            TblTMForms tblTMForms = new TblTMForms(context);
                            tblTMForms.checkAndAlterTableSubmittedDate(TblTMForms.TABLE_NAME);
                        } else {
                            sqLiteDatabase.execSQL(TblTMForms.createTable());
                        }
                        //From App version 2.2.0
                    case 11:
                        if (isTableExists(sqLiteDatabase, TblTMForms.TABLE_NAME)) {
                            TblTMForms tblTMForms = new TblTMForms(context);
                            tblTMForms.checkAndAlterTableCheckInOutData(TblTMForms.TABLE_NAME);
                        } else {
                            sqLiteDatabase.execSQL(TblTMForms.createTable());
                        }
                        //From App version 2.2.3
                    case 12:
                        sqLiteDatabase.execSQL(TblTMForms.createIndexesForUpdate());
                        sqLiteDatabase.execSQL(TblForms.createIndexesForUpdate());

                        //from app version 2.2.7
                    case 13:
                        if (isTableExists(sqLiteDatabase, TblSites.TABLE_NAME)) {
                            TblSites tblSites2 = new TblSites(context);
                            tblSites2.checkAndAlterTableSiteIndex();
                        }

                        // App version 2.3.0
                    case 14:
                        if (isTableExists(sqLiteDatabase, TblUploadImage.TABLE_NAME)) {
                            tblUploadImage.checkAndAlterTableHighImageUploaded();
                            tblUploadImage.checkAndAlterTableTags();
                        }
                        if (isTableExists(sqLiteDatabase, TblTMForms.TABLE_NAME)) {
                            TblTMForms tblTMForms = new TblTMForms(context);
                            tblTMForms.alterTableCheckInOutTimeData(TblTMForms.TABLE_NAME);
                            tblTMForms.checkAndAlterTableCrewIds(TblTMForms.TABLE_NAME);
                        }
                        //Drop existing Indexes and create without Unique
                        dropIndex("idx_form_update", writableDb);
                        dropIndex("idx_update", writableDb);
                        sqLiteDatabase.execSQL(TblTMForms.createIndexesForUpdate());
                        sqLiteDatabase.execSQL(TblForms.createIndexesForUpdate());

                        if (!isTableExists(sqLiteDatabase, TblRoutes.TABLE_NAME)) {
                            isTableExists(sqLiteDatabase, TblRoutes.TABLE_NAME);
                        }

                        if (isTableExists(sqLiteDatabase, TblClockCrew.TABLE_NAME)) {
                            TblClockCrew tblClockCrew = new TblClockCrew(context);
                            tblClockCrew.checkAndAltTblIsCrew();
                            tblClockCrew.checkAndAltTblLoginUser();
                        }

                        if (!isTableExists(sqLiteDatabase, TblCheckInMap.TABLE_NAME)) {
                            sqLiteDatabase.execSQL(TblCheckInMap.createTable());
                        }
                        sqLiteDatabase.execSQL(TblCheckInMap.createIndexes());

                    // App version 2.3.9
                    case 15:
                        TblRoutes tblRoutes = new TblRoutes(context);
                        if (isTableExists(sqLiteDatabase, TblRoutes.TABLE_NAME)) {
                            tblRoutes.checkAndAlterTableOrderedAndSiteData();
                        }
                    //2.4.2
                    case 16:
                        if (isTableExists(sqLiteDatabase, TblTMForms.TABLE_NAME)) {
                            TblTMForms tmForms = new TblTMForms(context);
                            tmForms.checkAndAlterTablePlotOnMap(TblTMForms.TABLE_NAME);
                        }

                        if (isTableExists(sqLiteDatabase, TblForms.TABLE_NAME)) {
                            TblForms tblForms = new TblForms(context);
                            tblForms.checkAndAlterTablePlotOnMap(TblForms.TABLE_NAME);
                        }
                        // Create Table MapPinData if not exist
                        if (!isTableExists(sqLiteDatabase, TblMapPinData.TABLE_NAME)) {
                            sqLiteDatabase.execSQL(TblMapPinData.createTable());
                        }

                        //2.4.4
                    case 17:
                        if (isTableExists(sqLiteDatabase, TblTMForms.TABLE_NAME)) {
                            TblTMForms tblTmForm = new TblTMForms(context);
                            tblTmForm.checkAndAlterTablePreSelectService(TblTMForms.TABLE_NAME);
                        }
                        //We need to update this data to update preselected data in table by getting 200 response code for getAppData APi
                        //AppPrefShared.putValue(PARAM_STAG, ""); // We commented for now and my get uncommented in future.

                        //2.4.7
                    case 18:
                        if (isTableExists(sqLiteDatabase, TblUploadImage.TABLE_NAME)) {
                            tblUploadImage.checkAndAlterTableAppVersion();
                        }

                        //2.4.10
                    case 19:

                        // Add cameraImage column for images, 1 if captured from app, 2 if picked from gallery, 0 if old or signature or sketch
                        if(isTableExists(sqLiteDatabase, TblUploadImage.TABLE_NAME)){
                            tblUploadImage.checkAndAlterTableCameraImage();
                        }
                        if (!isTableExists(sqLiteDatabase, TblVehicles.TABLE_NAME)) {
                            sqLiteDatabase.execSQL(TblVehicles.createTable());
                        }
                        if (!isTableExists(sqLiteDatabase, TblEquipment.TABLE_NAME)) {
                            sqLiteDatabase.execSQL(TblEquipment.createTable());
                        }

                        if (!isTableExists(sqLiteDatabase, TblDynamicDropdownItems.TABLE_NAME)) {
                            sqLiteDatabase.execSQL(TblDynamicDropdownItems.createTable());
                        }

                        if (isTableExists(sqLiteDatabase,TblCheckInMap.TABLE_NAME)){
                            TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
                            tblCheckInMap.checkAndAlterTableFormSubmissionId();
                        }

                        if (isTableExists(sqLiteDatabase,TblUploadData.TABLE_NAME)){
                            tblUploadData.checkAndAlterTableFormSubmissionId();
                            tblUploadData.checkAndAlterTableForSubForms();
                        }

                        if (isTableExists(sqLiteDatabase,TblTMForms.TABLE_NAME)){
                            tblTmForms.checkAndAlterTableFormSubmissionId(TblTMForms.TABLE_NAME);
                            tblTmForms.checkAndAlterTableForSubForms(TblTMForms.TABLE_NAME);
                        }

                        if (isTableExists(sqLiteDatabase,TblForms.TABLE_NAME)){
                            TblForms tblForms = new TblForms(context);
                            tblForms.checkAndAlterTableForSubForms(TblForms.TABLE_NAME);
                            tblForms.checkAndAlterTableForCheckInCheckOutTime(TblForms.TABLE_NAME);
                            tblForms.checkAndAlterTableFormSubmissionId(TblForms.TABLE_NAME);
                        }
                        break;
                }
            } catch (SQLException e) {
                truncateDatabase();
                FirebaseEventUtils.logException(e);
            }
        }
    }

    @Override
    public SQLiteDatabase getWritableDatabase() {
        if (writableDb == null || !writableDb.isOpen()) {
            writableDb = super.getWritableDatabase();
        }
        return writableDb;
    }

    @Override
    public SQLiteDatabase getReadableDatabase() {
        if (readableDb == null || !readableDb.isOpen()) {
            readableDb = super.getReadableDatabase();
        }
        return readableDb;
    }

    /**
     * Only use if user logout with.
     * It will drop all table and recreate it.
     */
    public void truncateDatabase() {
        try {
            SQLiteDatabase db = getWritableDatabase();
            dropTable(TblReadings.TABLE_NAME, db);
            dropTable(TblServices.TABLE_NAME, db);
            dropTable(TblMaterials.TABLE_NAME, db);
            dropTable(TblProperties.TABLE_NAME, db);
            dropTable(TblCluster.TABLE_NAME, db);
            dropTable(TblForms.TABLE_NAME, db);
            dropTable(TblTMForms.TABLE_NAME, db);
            dropTable(TblUploadData.TABLE_NAME, db);
            dropTable(TblUploadImage.TABLE_NAME, db);
            dropTable(TblSites.TABLE_NAME, db);
            dropTable(TblSiteProfileData.TABLE_NAME, db);
            dropTable(TblEmployees.TABLE_NAME, db);
            dropTable(TblClockInfo.TABLE_NAME, db);
            dropTable(TblClockCrew.TABLE_NAME, db);
            dropTable(TblRoutes.TABLE_NAME, db);
            dropTable(TblCheckInMap.TABLE_NAME, db);
            dropTable(TblMapPinData.TABLE_NAME, db);
            dropTable(TblEquipment.TABLE_NAME,db);
            dropTable(TblVehicles.TABLE_NAME,db);
            dropTable(TblDynamicDropdownItems.TABLE_NAME,db);
            onCreate(db);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private boolean isTableExists(SQLiteDatabase sqLiteDatabase, String tableName) {
        Cursor cursor = sqLiteDatabase.rawQuery("select DISTINCT tbl_name from sqlite_master where tbl_name = '" + tableName + "'", null);
        if (cursor != null) {
            if (cursor.getCount() > 0) {
                cursor.close();
                return true;
            }
            cursor.close();
        }

        switch (tableName) {
            case TblUploadImage.TABLE_NAME:
                sqLiteDatabase.execSQL(TblUploadImage.createTable());
                break;
            case TblUploadData.TABLE_NAME:
                sqLiteDatabase.execSQL(TblUploadData.createTable());
                break;
            case TblSites.TABLE_NAME:
                sqLiteDatabase.execSQL(TblSites.createTable());
                break;
            case TblTMForms.TABLE_NAME:
                sqLiteDatabase.execSQL(TblTMForms.createTable());
                break;
            case TblClockInfo.TABLE_NAME:
                sqLiteDatabase.execSQL(TblClockInfo.createTable());
                break;
            case TblForms.TABLE_NAME:
                sqLiteDatabase.execSQL(TblForms.createTable());
                break;
            case TblMaterials.TABLE_NAME:
                sqLiteDatabase.execSQL(TblMaterials.createTable());
                break;
            case TblProperties.TABLE_NAME:
                sqLiteDatabase.execSQL(TblProperties.createTable());
                break;
            case TblReadings.TABLE_NAME:
                sqLiteDatabase.execSQL(TblReadings.createTable());
                break;
            case TblRoutes.TABLE_NAME:
                sqLiteDatabase.execSQL(TblRoutes.createTable());
                break;
            case TblServices.TABLE_NAME:
                sqLiteDatabase.execSQL(TblServices.createTable());
                break;
            case TblSiteProfileData.TABLE_NAME:
                sqLiteDatabase.execSQL(TblSiteProfileData.createTable());
                break;
            case TblCluster.TABLE_NAME:
                sqLiteDatabase.execSQL(TblCluster.createTable());
                break;
            case TblEmployees.TABLE_NAME:
                sqLiteDatabase.execSQL(TblEmployees.createTable());
                break;

            case TblClockCrew.TABLE_NAME:
                sqLiteDatabase.execSQL(TblClockCrew.createTable());
                break;

            case TblCheckInMap.TABLE_NAME:
                sqLiteDatabase.execSQL(TblCheckInMap.createTable());
                break;

            case TblMapPinData.TABLE_NAME:
                sqLiteDatabase.execSQL(TblMapPinData.createTable());
                break;

            case TblEquipment.TABLE_NAME:
                sqLiteDatabase.execSQL(TblEquipment.createTable());
                break;

            case TblVehicles.TABLE_NAME:
                sqLiteDatabase.execSQL(TblVehicles.createTable());
                break;

            case TblDynamicDropdownItems.TABLE_NAME:
                sqLiteDatabase.execSQL(TblDynamicDropdownItems.createTable());
                break;
        }
        return false;
    }

    private void dropTable(String tableName, SQLiteDatabase db) {
        try {
            db.execSQL("DROP TABLE IF EXISTS " + tableName);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void dropIndex(String indexName, SQLiteDatabase db) {
        try {
            db.execSQL("DROP INDEX IF EXISTS " + indexName);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Function to clear caching data for app data api. we required this to get latest data after db version upgrade
     */
    private void clearAppDataApiCache(){
        AppPrefShared.putValue(PARAM_STAG, "");
    }
}
