package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.Vehicles;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

//Added this table in app version 2.4.7
public class TblVehicles {

    public static final String TABLE_NAME = "tblVehicles";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_VEHICLE_ID = "vehicleId";
    private static final String COLUMN_VEHICLE_VIN = "vehicleVin";
    private static final String COLUMN_VEHICLE_LABEL = "vehicleLabel";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_UPDATE_STATUS = "c_update_status";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblVehicles(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_VEHICLE_ID + "` INTEGER , " +
                " `" + COLUMN_VEHICLE_VIN + "` TEXT, " +
                " '" + COLUMN_VEHICLE_LABEL + "' TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER " + " ) ";
    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    /**
     * Method to insert bulk data in vehicle table
     *
     * @param lstVehicles list of vehicles
     */
    public synchronized void insertVehicleBulkData(List<Vehicles> lstVehicles) {
        try {
            writableDb.beginTransactionNonExclusive();
            for (Vehicles vehicle : lstVehicles) {
                insertData(vehicle);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }finally {

            writableDb.endTransaction();
        }
    }


    /**
     * Method to insert data in table
     */
    public void insertData(Vehicles data) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_VEHICLE_ID, data.getSvId());
            initialValues.put(COLUMN_VEHICLE_VIN, data.getSvVehicleVin());
            initialValues.put(COLUMN_VEHICLE_LABEL, data.getSvVehicleLabel());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public synchronized void updateData(long id, Vehicles data) {
        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_VEHICLE_ID, data.getSvId());
            initialValues.put(COLUMN_VEHICLE_VIN, data.getSvVehicleVin());
            initialValues.put(COLUMN_VEHICLE_LABEL, data.getSvVehicleLabel());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?",
                    new String[]{String.valueOf(id)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public void updateDataStatus(int updateStatus) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, updateStatus);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPDATE_STATUS + "=?", new String[]{String.valueOf(1)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Method to get all vehicle data
     *
     * @return list of Vehicles
     */
    public List<Vehicles> getAllVehicles() {
        Cursor cursor = null;
        ArrayList<Vehicles> lstVehicles = new ArrayList<>();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, null);
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int vehicleIdIndex = cursor.getColumnIndex(COLUMN_VEHICLE_ID);
            int vehicleVinIndex = cursor.getColumnIndex(COLUMN_VEHICLE_VIN);
            int vehicleLabelIndex = cursor.getColumnIndex(COLUMN_VEHICLE_LABEL);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    Vehicles vehicle = new Vehicles();
                    vehicle.setPkId(cursor.getInt(pkIdIndex));
                    vehicle.setSvId(cursor.getInt(vehicleIdIndex));
                    vehicle.setSvVehicleVin(cursor.getString(vehicleVinIndex));
                    vehicle.setSvVehicleLabel(cursor.getString(vehicleLabelIndex));
                    vehicle.setCreatedDate(cursor.getLong(createdIndex));
                    vehicle.setUpdatedDate(cursor.getLong(updatedIndex));
                    lstVehicles.add(vehicle);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstVehicles;
    }

    public Vehicles getDataFromTableById(Long vehicleId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE "+ COLUMN_VEHICLE_ID +  "='" + vehicleId + "'" ;
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private Vehicles getSingleDataFromDB(Cursor cursor) {
        Vehicles data = new Vehicles();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int vehicleIdIndex = cursor.getColumnIndex(COLUMN_VEHICLE_ID);
            int vehicleVinIndex = cursor.getColumnIndex(COLUMN_VEHICLE_VIN);
            int vehicleLabelIndex = cursor.getColumnIndex(COLUMN_VEHICLE_LABEL);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setSvId(cursor.getInt(vehicleIdIndex));
                    data.setSvVehicleVin(cursor.getString(vehicleVinIndex));
                    data.setSvVehicleLabel(cursor.getString(vehicleLabelIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return data;

    }

    public void updateTimeData(long id, long currentSession, long totalSession) {
        try {
            ContentValues initialValues = new ContentValues();
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            //writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    /**
     * Delete all old data from table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=' 0 '", null);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(writableDb, query);
    }

}
