package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;
import static com.sitefotos.util.DBUtils.toCommaSeparatedString;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.models.SiteConfig;
import com.sitefotos.models.SiteData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

public class TblSites {

    public static final String TABLE_NAME = "tblSites";
    private static final String COLUMN_SITE_PK_ID = "pkId";
    private static final String COLUMN_SITE_ID = "siteId";
    private static final String COLUMN_SITE_NAME = "siteName";
    private static final String COLUMN_ADDRESS = "address";
    private static final String COLUMN_DISTANCE = "distance";
    private static final String COLUMN_FILTER_DISTANCE = "siteFilterDistance";
    private static final String COLUMN_PROPERTY_LAT = "propertyLatitude";
    private static final String COLUMN_SITE_CREATED = "created";
    private static final String COLUMN_SITE_UPDATED = "updated";
    private static final String COLUMN_PROPERTY_LONG = "propertyLongitude";
    private static final String COLUMN_PROPERTY_DATA = "propertyData";
    private static final String COLUMN_ORDERED = "ordered";
    private static final String COLUMN_ALLSITES = "allSites";
    private static final String COLUMN_FORM_ID = "formId";
    private static final String COLUMN_SITE_CONFIG = "siteConfig";
    private static final String COLUMN_SITE_INDEX = "siteIndex";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblSites(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_SITE_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_SITE_ID + "` INTEGER , " +
                " `" + COLUMN_SITE_NAME + "` TEXT, " +
                " `" + COLUMN_ADDRESS + "` TEXT, " +
                " '" + COLUMN_DISTANCE + "' REAL, " +
                " '" + COLUMN_FILTER_DISTANCE + "' REAL, " +
                " `" + COLUMN_PROPERTY_LAT + "` REAL, " +
                " `" + COLUMN_PROPERTY_LONG + "` REAL, " +
                " `" + COLUMN_ORDERED + "` INTEGER, " +
                " `" + COLUMN_FORM_ID + "` TEXT, " +
                " `" + COLUMN_SITE_CONFIG + "` TEXT, " +
                " `" + COLUMN_ALLSITES + "` INTEGER, " +
                " `" + COLUMN_SITE_CREATED + "` INTEGER, " +
                " `" + COLUMN_SITE_UPDATED + "` INTEGER, " +
                " `" + COLUMN_SITE_INDEX + "` INTEGER , " +
                " `" + COLUMN_PROPERTY_DATA + "` TEXT " + " ) ";

    }

    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableFilterDistance() {
        try {
            if (checkIsColumnExist(COLUMN_FILTER_DISTANCE) == -1) {
                writableDb.execSQL(alterTableFilterDistance());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public void checkAndAlterTableSiteConfig() {
        try {
            if (checkIsColumnExist(COLUMN_SITE_CONFIG) == -1) {
                writableDb.execSQL(alterTableSiteConfig());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static String alterTableSiteConfig() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_SITE_CONFIG + "` TEXT ";
    }

    public void checkAndAlterTableFormId() {
        try {
            if (checkIsColumnExist(COLUMN_FORM_ID) == -1) {
                writableDb.execSQL(alterTableFormId());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public static String alterTableFormId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FORM_ID + "` TEXT ";
    }


    public void checkAndAlterTableSiteIndex() {
        try {
            if (checkIsColumnExist(COLUMN_SITE_INDEX) == -1) {
                writableDb.execSQL(alterTableSiteIndex());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static String alterTableSiteIndex() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_SITE_INDEX + "` INTEGER ";
    }

    /**
     * Method to insert or update bulk data in table
     */
    public void insertOrUpdateBulkData(List<SiteData> lstSites) {
        writableDb.beginTransactionNonExclusive();
        try {
            int index = 1;
            for (SiteData data : lstSites) {
                ContentValues initialValues = new ContentValues();
                initialValues.put(COLUMN_SITE_ID, data.getSiteId());
                initialValues.put(COLUMN_SITE_NAME, data.getSiteName());
                initialValues.put(COLUMN_ADDRESS, data.getPropertyAddress());
                initialValues.put(COLUMN_SITE_CREATED, data.getCreatedDate());
                initialValues.put(COLUMN_SITE_UPDATED, data.getUpdatedDate());
                initialValues.put(COLUMN_DISTANCE, data.getDistance());
                initialValues.put(COLUMN_ORDERED, data.isOrdered() ? 1 : 0);
                initialValues.put(COLUMN_ALLSITES, data.isAllSites() ? 1 : 0);
                initialValues.put(COLUMN_PROPERTY_LAT, data.getPropertyLatitude());
                initialValues.put(COLUMN_PROPERTY_LONG, data.getPropertyLongitude());
                initialValues.put(COLUMN_PROPERTY_DATA, data.getPropertyData());
                initialValues.put(COLUMN_FORM_ID, toCommaSeparatedString(data.getFormID()));
                initialValues.put(COLUMN_SITE_CONFIG, new Gson().toJson(data.getSiteConfig()));
                initialValues.put(COLUMN_SITE_INDEX, index);
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_SITE_ID + "=?",
                        new String[]{String.valueOf(data.getSiteId())});
                if (id == 0) {
                    writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
                }
                index++;
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

             writableDb.endTransaction();
        }
    }

    /**
     * Method to insert bulk data in table
     */
    public void insertBulkData(List<SiteData> lstSites) {
        writableDb.beginTransactionNonExclusive();
        try {
            int index = 1;
            for (SiteData data : lstSites) {
                ContentValues initialValues = new ContentValues();
                initialValues.put(COLUMN_SITE_ID, data.getSiteId());
                initialValues.put(COLUMN_SITE_NAME, data.getSiteName());
                initialValues.put(COLUMN_ADDRESS, data.getPropertyAddress());
                initialValues.put(COLUMN_SITE_CREATED, data.getCreatedDate());
                initialValues.put(COLUMN_SITE_UPDATED, data.getUpdatedDate());
                initialValues.put(COLUMN_DISTANCE, data.getDistance());
                initialValues.put(COLUMN_ORDERED, data.isOrdered() ? 1 : 0);
                initialValues.put(COLUMN_ALLSITES, data.isAllSites() ? 1 : 0);
                initialValues.put(COLUMN_PROPERTY_LAT, data.getPropertyLatitude());
                initialValues.put(COLUMN_PROPERTY_LONG, data.getPropertyLongitude());
                initialValues.put(COLUMN_PROPERTY_DATA, data.getPropertyData());
                initialValues.put(COLUMN_FORM_ID, toCommaSeparatedString(data.getFormID()));
                initialValues.put(COLUMN_SITE_CONFIG, new Gson().toJson(data.getSiteConfig()));
                initialValues.put(COLUMN_SITE_INDEX, index);
                writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
                index++;
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

             writableDb.endTransaction();
        }
    }

    private String getFormIdInString(List<Integer> formID) {
        String ids = "";
        for (Integer id : formID) {
            ids = ids + id + ",";
        }
        return ids;
    }


    public void insertSingleData(SiteData data) {
        writableDb.beginTransactionNonExclusive();
        try {

            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_SITE_ID, data.getSiteId());
            initialValues.put(COLUMN_SITE_NAME, data.getSiteName());
            initialValues.put(COLUMN_ADDRESS, data.getPropertyAddress());
            initialValues.put(COLUMN_SITE_CREATED, data.getCreatedDate());
            initialValues.put(COLUMN_SITE_UPDATED, data.getUpdatedDate());
            initialValues.put(COLUMN_DISTANCE, data.getDistance());
            initialValues.put(COLUMN_ORDERED, data.isOrdered() ? 1 : 0);
            initialValues.put(COLUMN_ALLSITES, data.isAllSites() ? 1 : 0);
            initialValues.put(COLUMN_PROPERTY_LAT, data.getPropertyLatitude());
            initialValues.put(COLUMN_PROPERTY_LONG, data.getPropertyLongitude());
            initialValues.put(COLUMN_PROPERTY_DATA, data.getPropertyData());
            initialValues.put(COLUMN_SITE_CONFIG, new Gson().toJson(data.getSiteConfig()));
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public static String alterTableFilterDistance() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FILTER_DISTANCE + "` REAL ";
    }

    /**
     * Method to get all site data
     *
     * @return list of site data
     */
    public List<SiteData> getAllDataByIds(List<Long> ids) {
        if (ids.isEmpty())
            return new ArrayList<>();
        String orderBy = "CASE ts." + COLUMN_SITE_ID;

        int i = 1;
        for (long id : ids) {
            orderBy = orderBy.concat(" WHEN " + id + " THEN " + i);
            i++;
        }

        orderBy = orderBy.concat(" END");

        //String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + " IN (" + DBUtils.toCommaSeparatedStringFromList(ids) + ")";
        //String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + " IN (" + DBUtils.toCommaSeparatedStringFromList(ids) + ")";
        String rawQuery = "SELECT ts.*,(CASE WHEN SUM(tf." + TblTMForms.COLUMN_CHECKIN_OUT_FORM_COMPLETE + " + " + "tf." + TblTMForms.COLUMN_FORM_SUBMITTED + ") = SUM( tf." +
                TblTMForms.COLUMN_IS_CHECKIN_OUT + ") and tf." + TblTMForms.COLUMN_IS_CHECKIN_OUT_COMPLETE + "='1'" + " then 1 else 0 end) as 'allFormCompleted' from "
                + TblTMForms.TABLE_NAME + " tf CROSS JOIN " + TABLE_NAME + " ts ON ts." + COLUMN_SITE_ID + " = tf." + TblTMForms.COLUMN_SITE_ID + " WHERE tf." +
                TblTMForms.COLUMN_FORM_SUBMITTED + "='0' " + " AND tf." + TblTMForms.COLUMN_IS_CHECKIN_OUT + "='1' " + " AND tf." + TblTMForms.COLUMN_CHECKIN_OUT_FORM_COMPLETE + "='1' " + " AND tf." +
                TblTMForms.COLUMN_IS_CHECKIN_OUT_COMPLETE + "='1' OR ts." + COLUMN_SITE_ID + " IN (" + DBUtils.toCommaSeparatedStringFromList(ids) + ") group by ts." + COLUMN_SITE_ID +
                " ORDER BY " + orderBy;

        return getAllDataFromDb(readableDb.rawQuery(rawQuery, null));
    }

    /**
     * Method to get all  data
     *
     * @return list of Sites
     */
    public List<SiteData> getAllData() {
        String rawQuery = "SELECT ts.*,(CASE WHEN SUM(tf." + TblTMForms.COLUMN_CHECKIN_OUT_FORM_COMPLETE + " + " + "tf." + TblTMForms.COLUMN_FORM_SUBMITTED + ") = SUM( tf." +
                TblTMForms.COLUMN_IS_CHECKIN_OUT + ") and tf." + TblTMForms.COLUMN_IS_CHECKIN_OUT_COMPLETE + "='1'" + " then 1 else 0 end) as 'allFormCompleted' from "
                + TblTMForms.TABLE_NAME + " tf CROSS JOIN " + TABLE_NAME + " ts ON ts." + COLUMN_SITE_ID + " = tf." + TblTMForms.COLUMN_SITE_ID + " WHERE tf." +
                TblTMForms.COLUMN_FORM_SUBMITTED + "='0' " + " AND tf." + TblTMForms.COLUMN_IS_CHECKIN_OUT + "='1' " + " AND tf." + TblTMForms.COLUMN_CHECKIN_OUT_FORM_COMPLETE + "='1' " + " AND tf." +
                TblTMForms.COLUMN_IS_CHECKIN_OUT_COMPLETE + "='1' OR ts." + COLUMN_SITE_PK_ID + " > 0 group by ts." + COLUMN_SITE_ID + " ORDER BY " + COLUMN_SITE_INDEX;
        return getAllDataFromDb(writableDb.rawQuery(rawQuery, null));
    }

    private List<SiteData> getAllDataFromDb(Cursor cursor) {
        ArrayList<SiteData> lstSites = new ArrayList<>();
        try {
            int idIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_SITE_NAME);
            int addressIndex = cursor.getColumnIndex(COLUMN_ADDRESS);
            int distanceIndex = cursor.getColumnIndex(COLUMN_DISTANCE);
            int propertyLatIndex = cursor.getColumnIndex(COLUMN_PROPERTY_LAT);
            int propertyLongIndex = cursor.getColumnIndex(COLUMN_PROPERTY_LONG);
            int createdIndex = cursor.getColumnIndex(COLUMN_SITE_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_SITE_UPDATED);
            int dataIndex = cursor.getColumnIndex(COLUMN_PROPERTY_DATA);
            int orderedIndex = cursor.getColumnIndex(COLUMN_ORDERED);
            int allSitesIndex = cursor.getColumnIndex(COLUMN_ALLSITES);
            int formId = cursor.getColumnIndex(COLUMN_FORM_ID);
            int siteConfigIndex = cursor.getColumnIndex(COLUMN_SITE_CONFIG);
            int setAllFormCompletedIndex = cursor.getColumnIndex("allFormCompleted");
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    SiteData data = new SiteData();
                    data.setSiteId(cursor.getInt(idIndex));
                    data.setSiteName(cursor.getString(nameIndex));
                    data.setOrdered(cursor.getInt(orderedIndex) != 0);
                    data.setAllSites(cursor.getInt(allSitesIndex) != 0);
                    data.setPropertyLatitude(cursor.getDouble(propertyLatIndex));
                    data.setPropertyLongitude(cursor.getDouble(propertyLongIndex));
                    data.setPropertyAddress(cursor.getString(addressIndex));
                    data.setDistance(cursor.getDouble(distanceIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setPropertyData(cursor.getString(dataIndex));
                    data.setFormID(getFormIdFromString(cursor.getString(formId)));
                    data.setPropertiesVo(new Gson().fromJson(cursor.getString(dataIndex), PropertiesVo.class));
                    data.setSiteConfig(new Gson().fromJson(cursor.getString(siteConfigIndex), SiteConfig.class));
                    data.setAllFormCompleted(cursor.getInt(setAllFormCompletedIndex) == 1);
                    lstSites.add(data);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstSites;
    }

    public List<Integer> getAllSiteIds() {
        String rawQuery = "SELECT " + COLUMN_SITE_ID + " FROM " + TABLE_NAME;
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        List<Integer> lstCheckedSite = new ArrayList<>();
        try {
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstCheckedSite.add(cursor.getInt(siteIdIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstCheckedSite;
    }


    private List<Integer> getFormIdFromString(String string) {
        List<Integer> lstFormId = new ArrayList<>();
        if (!TextUtils.isEmpty(string)) {
            for (String id : string.split(",")) {
                lstFormId.add(Integer.parseInt(id.trim()));
            }
        }
        return lstFormId;
    }

    public SiteData getDataFromSiteId(long id) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='" + id + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private SiteData getSingleDataFromDB(Cursor cursor) {
        //writableDb.beginTransactionNonExclusive();
        SiteData data = new SiteData();
        try {
            int idIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_SITE_NAME);
            int addressIndex = cursor.getColumnIndex(COLUMN_ADDRESS);
            int distanceIndex = cursor.getColumnIndex(COLUMN_DISTANCE);
            int propertyLatIndex = cursor.getColumnIndex(COLUMN_PROPERTY_LAT);
            int propertyLongIndex = cursor.getColumnIndex(COLUMN_PROPERTY_LONG);
            int createdIndex = cursor.getColumnIndex(COLUMN_SITE_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_SITE_UPDATED);
            int dataIndex = cursor.getColumnIndex(COLUMN_PROPERTY_DATA);
            int orderedIndex = cursor.getColumnIndex(COLUMN_ORDERED);
            int allSitesIndex = cursor.getColumnIndex(COLUMN_ALLSITES);
            int siteConfigIndex = cursor.getColumnIndex(COLUMN_SITE_CONFIG);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setSiteId(cursor.getInt(idIndex));
                    data.setSiteName(cursor.getString(nameIndex));
                    data.setPropertyLatitude(cursor.getDouble(propertyLatIndex));
                    data.setPropertyLongitude(cursor.getDouble(propertyLongIndex));
                    data.setPropertyAddress(cursor.getString(addressIndex));
                    data.setDistance(cursor.getDouble(distanceIndex));
                    data.setOrdered(cursor.getInt(orderedIndex) != 0);
                    data.setAllSites(cursor.getInt(allSitesIndex) != 0);
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setPropertyData(cursor.getString(dataIndex));
                    TblTMForms tblTMForms = new TblTMForms(context);
                    data.setAllFormCompleted(tblTMForms.isAllCompletedFormCount(data.getSiteId()));
                    data.setPropertiesVo(new Gson().fromJson(cursor.getString(dataIndex), PropertiesVo.class));
                    data.setSiteConfig(new Gson().fromJson(cursor.getString(siteConfigIndex), SiteConfig.class));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }

        return data;

    }

    public void updateDistance(long siteId, double distance) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_DISTANCE, distance);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_SITE_ID + "=?  ", new String[]{String.valueOf(siteId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }
    public ArrayList<String> getDeletedSiteIds(List<Integer> lstSiteIds) {
        Cursor cursor = null;
        ArrayList<String> lstIds = new ArrayList<>();
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(lstSiteIds) + ")";
            cursor = readableDb.rawQuery(rawQuery, null);

            int idIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstIds.add(String.valueOf(cursor.getInt(idIndex)));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstIds;
    }


    /**
     * method to delete single entry table.
     */
    public void deleteDataById(long siteId) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_SITE_ID + "=? ", new String[]{String.valueOf(siteId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Delete all old data from the table
     */
    public synchronized void deleteOldData(List<Integer> lstSiteIds) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + " NOT IN (" + toCommaSeparatedString(lstSiteIds) + ")");
            //writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_UPDATED + " < " + lastUpdate);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }

}
