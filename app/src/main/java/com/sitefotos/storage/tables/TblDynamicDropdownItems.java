package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.DynamicDropDownItem;
import com.sitefotos.models.Equipments;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

//Added this table in app version 2.4.7
public class TblDynamicDropdownItems {

    public static final String TABLE_NAME = "tblDynamicDropdownItems";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_ITEM_ID = "itemId";
    private static final String COLUMN_VALUE = "value";
    private static final String COLUMN_TYPE = "type";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_UPDATE_STATUS = "c_update_status";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblDynamicDropdownItems(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_ITEM_ID + "` INTEGER , " +
                " `" + COLUMN_VALUE + "` TEXT, " +
                " `" + COLUMN_TYPE + "` TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER " + " ) ";

    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = writableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    /**
     * Method to insert bulk data in the table
     *
     * @param lstDynamicDropDown list of DropDownItems
     */
    public synchronized void insertBulkData(List<DynamicDropDownItem> lstDynamicDropDown) {
        try {
            writableDb.beginTransactionNonExclusive();
            for (DynamicDropDownItem dropDownItem : lstDynamicDropDown) {
                insertData(dropDownItem);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Method to insert data in table
     */
    public void insertData(DynamicDropDownItem data) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_ITEM_ID, data.getId());
            initialValues.put(COLUMN_VALUE, data.getValue());
            initialValues.put(COLUMN_TYPE, data.getType());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public DynamicDropDownItem getDataFromTable() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME;
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private DynamicDropDownItem getSingleDataFromDB(Cursor cursor) {
        DynamicDropDownItem data = new DynamicDropDownItem();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int idIndex = cursor.getColumnIndex(COLUMN_ITEM_ID);
            int valueIndex = cursor.getColumnIndex(COLUMN_VALUE);
            int typeIndex = cursor.getColumnIndex(COLUMN_TYPE);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setId(cursor.getInt(idIndex));
                    data.setValue(cursor.getString(valueIndex));
                    data.setType(cursor.getString(typeIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return data;

    }

    public void updateDataStatus(int updateStatus) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, updateStatus);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPDATE_STATUS + "=?", new String[]{String.valueOf(1)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    private ArrayList<DynamicDropDownItem> getAllData(Cursor cursor) {
        ArrayList<DynamicDropDownItem> lstData = new ArrayList<>();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int idIndex = cursor.getColumnIndex(COLUMN_ITEM_ID);
            int valueIndex = cursor.getColumnIndex(COLUMN_VALUE);
            int typeIndex = cursor.getColumnIndex(COLUMN_TYPE);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    DynamicDropDownItem data = new DynamicDropDownItem();
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setId(cursor.getInt(idIndex));
                    data.setValue(cursor.getString(valueIndex));
                    data.setType(cursor.getString(typeIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    lstData.add(data);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstData;
    }
    /**
     * Method to get all  data
     *
     * @return list of Dynamic DropDownData
     */
    public ArrayList<DynamicDropDownItem> getDataByType(String type) {
        Cursor cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_TYPE + "='"+type+"'" , null);
        return getAllData(cursor);
    }


    public ArrayList<DynamicDropDownItem> getDataByIdsType(List<Long> lstIds,String type) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_TYPE + "='"+type+"'" + " AND " + COLUMN_ITEM_ID + " IN (" + DBUtils.toCommaSeparatedStringFromLong(lstIds) + ")";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getAllData(cursor);
    }




    public DynamicDropDownItem getDataByIdType(Long Id,String type) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_TYPE + "='"+type+"'" + " AND " + COLUMN_ITEM_ID +"='"+Id+"'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            //writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    /**
     * Delete all old data from table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=' 0 '", null);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }


}
