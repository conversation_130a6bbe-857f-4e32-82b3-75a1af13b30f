package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.google.gson.Gson;
import com.sitefotos.models.ServiceOptions;
import com.sitefotos.models.TMService;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dk on 8/1/18.
 */

public class TblServices {

    public static final String TABLE_NAME = "tblServices";
    private static final String COLUMN_NAME_SERVICE_ID = "serviceId";
    private static final String COLUMN_NAME_SERVICE_NAME = "serviceName";
    private static final String COLUMN_NAME_SERVICE_CATEGORY = "ServiceCategory";
    private static final String COLUMN_NAME_SERVICE_TYPE = "ServiceType";
    private static final String COLUMN_SERVICE_OPTIONS = "ServiceOptions";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblServices(Context context) {

        this.context = context;
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }

    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_NAME_SERVICE_ID + "` integer PRIMARY KEY NOT NULL UNIQUE, " +
                " `" + COLUMN_NAME_SERVICE_CATEGORY + "` TEXT, " +
                " `" + COLUMN_NAME_SERVICE_TYPE + "` TEXT, " +
                " `" + COLUMN_SERVICE_OPTIONS + "` TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER, " +
                " `" + COLUMN_NAME_SERVICE_NAME + "` TEXT " + " ) ";
    }


    /**
     * Method to insert or update bulk data in service table
     *
     * @param lstServiceProvided list of services
     */
    public synchronized void insertOrUpdateServicesBulkData(List<TMService> lstServiceProvided, long firstUpdateDate) {
        try {
            writableDb.beginTransactionNonExclusive();
            for (TMService servicesProvided : lstServiceProvided) {
                insertOrUpdateSingleData(servicesProvided,firstUpdateDate);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Method to insert bulk data in service table
     *
     * @param lstServiceProvided list of services
     */
    public synchronized void insertServicesBulkData(List<TMService> lstServiceProvided) {
        try {
            writableDb.beginTransactionNonExclusive();
            for (TMService servicesProvided : lstServiceProvided) {
                insertSingleData(servicesProvided);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }finally {

            writableDb.endTransaction();
        }
    }

    private void insertSingleData(TMService servicesProvided) {
        try {

            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_NAME_SERVICE_ID, servicesProvided.getServiceID());
            initialValues.put(COLUMN_NAME_SERVICE_NAME, servicesProvided.getServiceName());
            initialValues.put(COLUMN_NAME_SERVICE_CATEGORY, servicesProvided.getServiceCategory());
            initialValues.put(COLUMN_NAME_SERVICE_TYPE, servicesProvided.getServiceType());
            if (servicesProvided.getServiceOptions() != null)
                initialValues.put(COLUMN_SERVICE_OPTIONS, new Gson().toJson(servicesProvided.getServiceOptions()));
            else
                initialValues.put(COLUMN_SERVICE_OPTIONS, "");
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());

            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            writableDb.insert(TABLE_NAME, null, initialValues);

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    private void insertOrUpdateSingleData(TMService servicesProvided,long firstUpdateDate) {
        try {
            //writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_NAME_SERVICE_ID, servicesProvided.getServiceID());
            initialValues.put(COLUMN_NAME_SERVICE_NAME, servicesProvided.getServiceName());
            initialValues.put(COLUMN_NAME_SERVICE_CATEGORY, servicesProvided.getServiceCategory());
            initialValues.put(COLUMN_NAME_SERVICE_TYPE, servicesProvided.getServiceType());
            if (servicesProvided.getServiceOptions() != null)
                initialValues.put(COLUMN_SERVICE_OPTIONS, new Gson().toJson(servicesProvided.getServiceOptions()));
            else
                initialValues.put(COLUMN_SERVICE_OPTIONS, "");
            initialValues.put(COLUMN_UPDATED,firstUpdateDate);

            int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_NAME_SERVICE_ID + "=?",
                    new String[]{String.valueOf(servicesProvided.getServiceID())});
            if (id == 0) {
                initialValues.put(COLUMN_CREATED, firstUpdateDate);
                writableDb.insert(TABLE_NAME, null, initialValues);
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
           // writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
    }

    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableCategory() {
        if (checkIsColumnExist(COLUMN_NAME_SERVICE_CATEGORY) == -1) {
            writableDb.execSQL(alterTableCategory());
        }
    }

    private static String alterTableCategory() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_NAME_SERVICE_CATEGORY + "` TEXT ";
    }

    public void checkAndAlterTableOption() {
        if (checkIsColumnExist(COLUMN_SERVICE_OPTIONS) == -1) {
            writableDb.execSQL(alterTableServiceOption());
        }
    }

    private static String alterTableServiceOption() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_SERVICE_OPTIONS + "` TEXT ";
    }

    public void checkAndAlterTableType() {
        if (checkIsColumnExist(COLUMN_NAME_SERVICE_TYPE) == -1) {
            writableDb.execSQL(alterTableServiceType());
        }
    }

    private static String alterTableServiceType() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_NAME_SERVICE_TYPE + "` TEXT ";
    }

    public void checkAndAlterTableCreated() {
        if (checkIsColumnExist(COLUMN_CREATED) == -1) {
            writableDb.execSQL(alterTableServiceCreated());
        }
    }

    public static String alterTableServiceCreated() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_CREATED + "` INTEGER ";
    }

    public void checkAndAlterTableUpdated() {
        if (checkIsColumnExist(COLUMN_UPDATED) == -1) {
            writableDb.execSQL(alterTableServiceUpdated());
        }
    }

    public static String alterTableServiceUpdated() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_UPDATED + "` INTEGER ";
    }

    /**
     * Method to get all service data
     *
     * @return list of services
     */
    public List<TMService> getAllServices() {
        Cursor cursor = null;
        ArrayList<TMService> listServiceProvided = new ArrayList<>();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, null);

            int idIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_ID);
            int serviceNameIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_NAME);
            int serviceCatIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_CATEGORY);
            int serviceTypeIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_TYPE);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    TMService servicesProvided = new TMService();
                    servicesProvided.setServiceID(cursor.getInt(idIndex));
                    servicesProvided.setServiceName(cursor.getString(serviceNameIndex));
                    servicesProvided.setServiceCategory(cursor.getString(serviceCatIndex));
                    servicesProvided.setServiceType(cursor.getString(serviceTypeIndex));
                    listServiceProvided.add(servicesProvided);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
        return listServiceProvided;
    }

    /**
     * Method to get all service data
     *
     * @return list of services
     */
    public TMService getAllServices(int id) {
        Cursor cursor = null;
        TMService servicesProvided = new TMService();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_NAME_SERVICE_ID + "='" + id + "'", null);

            int idIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_ID);
            int serviceNameIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_NAME);
            int serviceCatIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_CATEGORY);
            int serviceTypeIndex = cursor.getColumnIndex(COLUMN_NAME_SERVICE_TYPE);
            int serviceOptionIndex = cursor.getColumnIndex(COLUMN_SERVICE_OPTIONS);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    servicesProvided.setServiceID(cursor.getInt(idIndex));
                    servicesProvided.setServiceName(cursor.getString(serviceNameIndex));
                    servicesProvided.setServiceCategory(cursor.getString(serviceCatIndex));
                    servicesProvided.setServiceType(cursor.getString(serviceTypeIndex));
                    servicesProvided.setServiceOptions(new Gson().fromJson(cursor.getString(serviceOptionIndex), ServiceOptions.class));
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
        return servicesProvided;
    }

    /**
     * Delete all data from Service table
     */
    public synchronized void deleteDataFromServiceTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Delete all data from the table
     */
    public synchronized void deleteOldData(List<Integer> lstServiceId) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_NAME_SERVICE_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(lstServiceId) + ")");
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }
}
