package com.sitefotos.storage.tables;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.ClockInfo;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

//Not using this table since app version 2.3.0
public class TblClockInfo {

    public static final String TABLE_NAME = "tblClockInfo";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_SITE_ID = "siteId";
    private static final String COLUMN_SITE_NAME = "siteName";
    private static final String COLUMN_FORM_ID = "formId";
    private static final String COLUMN_FORM_NAME = "formName";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_CLOCKED_IN = "isClockedIn";
    private static final String COLUMN_IS_IN_BREAK = "isInBreak";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblClockInfo(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_SITE_ID + "` INTEGER , " +
                " `" + COLUMN_SITE_NAME + "` TEXT, " +
                " `" + COLUMN_FORM_ID + "` INTEGER, " +
                " '" + COLUMN_FORM_NAME + "' TEXT, " +
                " `" + COLUMN_CLOCKED_IN + "` INTEGER, " +
                " `" + COLUMN_IS_IN_BREAK + "` INTEGER, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER " + " ) ";

    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }


    /**
     * Method to insert data in table
     */
    public void insertData(ClockInfo data) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_SITE_ID, data.getSiteId());
            initialValues.put(COLUMN_SITE_NAME, data.getSiteName());
            initialValues.put(COLUMN_FORM_ID, data.getFormId());
            initialValues.put(COLUMN_FORM_NAME, data.getFormName());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCKED_IN, data.isClockedIn() ? 1 : 0);
            initialValues.put(COLUMN_IS_IN_BREAK, data.isInBreak() ? 1 : 0);
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public synchronized void updateData(long id, ClockInfo data) {
        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_SITE_ID, data.getSiteId());
            initialValues.put(COLUMN_SITE_NAME, data.getSiteName());
            initialValues.put(COLUMN_FORM_ID, data.getFormId());
            initialValues.put(COLUMN_FORM_NAME, data.getFormName());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCKED_IN, data.isClockedIn() ? 1 : 0);
            initialValues.put(COLUMN_IS_IN_BREAK, data.isInBreak() ? 1 : 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?",
                    new String[]{String.valueOf(id)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }


    public ClockInfo getDataFromTable() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME;
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private ClockInfo getSingleDataFromDB(Cursor cursor) {
        ClockInfo data = new ClockInfo();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int siteNameIndex = cursor.getColumnIndex(COLUMN_SITE_NAME);
            int formIdIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            int formNameIndex = cursor.getColumnIndex(COLUMN_FORM_NAME);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int clockedInIndex = cursor.getColumnIndex(COLUMN_CLOCKED_IN);
            int breakIndex = cursor.getColumnIndex(COLUMN_IS_IN_BREAK);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setSiteId(cursor.getLong(siteIdIndex));
                    data.setSiteName(cursor.getString(siteNameIndex));
                    data.setFormId(cursor.getLong(formIdIndex));
                    data.setFormName(cursor.getString(formNameIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setClockedIn(cursor.getInt(clockedInIndex) > 0);
                    data.setInBreak(cursor.getInt(breakIndex) > 0);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return data;

    }

    public void updateTimeData(long id, long currentSession, long totalSession) {
        try {
            ContentValues initialValues = new ContentValues();
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    public void updateSiteFormData(long id, long siteId, String siteName, long formId, String formName) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_SITE_ID, siteId);
            initialValues.put(COLUMN_SITE_NAME, siteName);
            initialValues.put(COLUMN_FORM_ID, formId);
            initialValues.put(COLUMN_FORM_NAME, formName);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void updateClockFlag(long id, boolean isClockedIn) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CLOCKED_IN, isClockedIn ? 1 : 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void resetData(long id) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCKED_IN, 0);
            initialValues.put(COLUMN_IS_IN_BREAK, 0);
            initialValues.put(COLUMN_SITE_ID, 0);
            initialValues.put(COLUMN_SITE_NAME, "");
            initialValues.put(COLUMN_FORM_ID, 0);
            initialValues.put(COLUMN_FORM_NAME, "");
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * method to delete single entry table.
     */
    public boolean isClockedIn() {

        Cursor cursor = null;
        boolean isClocked = false;
        try {
            cursor = writableDb.rawQuery("SELECT * FROM " + TABLE_NAME, null);
            int clockedInIndex = cursor.getColumnIndex(COLUMN_CLOCKED_IN);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    isClocked = cursor.getInt(clockedInIndex) > 0;
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return isClocked;

    }


    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            //writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

}
