package com.sitefotos.storage.tables;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.google.gson.Gson;
import com.sitefotos.models.SubFormOtherData;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class BaseTblForms {
    protected static final String COLUMN_FORM_PK_ID = "pkId";

    protected static final String COLUMN_FORM_ID = "formId";
    protected static final String COLUMN_FORM_NAME = "formName";
    protected static final String COLUMN_FORM_DATA = "formData";
    protected static final String COLUMN_IMAGE_DATA = "formImageData";
    protected static final String COLUMN_MODIFIED_FORM_DATA = "modifiedFormData";
    protected static final String COLUMN_FORM_CREATED = "created";
    protected static final String COLUMN_FORM_UPDATED = "updated";
    protected static final String COLUMN_FORM_SUBMISSION_ID = "formSubmissionID";
    protected static final String COLUMN_FORM_SUBMITTED_DATE = "submittedDate";
    protected static final String COLUMN_IMAGE_COUNT = "f_imageCount";
    protected static final String COLUMN_SIGNATURE_COUNT = "f_signatureCount";

    protected static final String COLUMN_LAST_BUILDING_ID = "lastBuildingId";
    protected static final String COLUMN_UPDATE_STATUS = "f_update_status";
    protected static final String COLUMN_CURRENT_LATITUDE = "f_latitude";
    protected static final String COLUMN_CURRENT_LONGITUDE = "f_longitude";
    protected static final String COLUMN_FORM_SUBMITTED = "formSubmitted";

    protected static final String COLUMN_PLOT_ON_MAP = "plotOnMap";

    protected static final String COLUMN_IS_SUB_FORM = "isSubForm";

    protected static final String COLUMN_HAS_SUB_FORM = "hasSubForm";

    protected static final String COLUMN_SUB_FORM_OTHER_DATA = "subFormOther";

    protected static final String COLUMN_IS_CHECKIN_OUT = "isCheckInOut";
    protected static final String COLUMN_IS_CHECKIN_OUT_COMPLETE = "isCheckInOutComplete";
    protected static final String COLUMN_CHECKIN_OUT_FORM_COMPLETE = "CheckInOutFormComplete";
    protected static final String COLUMN_SITE_ID = "siteId";
    protected static final String COLUMN_FORM_CHECKIN_TIME = "checkin_time";
    protected static final String COLUMN_FORM_CHECKOUT_TIME = "checkout_time";
    protected static final String COLUMN_CREW_IDS = "crewIds";
    protected static final String COLUMN_PRE_SELECT_SERVICE = "preSelectServices";

    protected static final String COLUMN_FORM_UPLOADED= "isSubUploaded";

    protected static final String COLUMN_MAIN_FORM_PK_ID= "mainFormPKId";
    abstract SQLiteDatabase getSQLiteDatabase();

    abstract Context getContext();

    protected int checkIsColumnExist(String columnName,String tableName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + tableName;
            cursor = getSQLiteDatabase().rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableSubmittedDate(String tableName) {
        if (checkIsColumnExist(COLUMN_FORM_SUBMITTED_DATE,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableSubmittedDate(tableName));
        }
    }


    private static String alterTableSubmittedDate(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_FORM_SUBMITTED_DATE + "` INTEGER ";
    }


    public void checkAndAlterTablePlotOnMap(String tableName) {
        if (checkIsColumnExist(COLUMN_PLOT_ON_MAP,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTablePlotOnMap(tableName));
        }
    }

    private static String alterTablePlotOnMap(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_PLOT_ON_MAP + "` INTEGER ";
    }

    public void checkAndAlterTableFormSubmissionId(String tableName) {
        if (checkIsColumnExist(COLUMN_FORM_SUBMISSION_ID,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableFormSubmissionId(tableName));
        }
    }

    private static String alterTableFormSubmissionId(String TABLE_NAME) {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FORM_SUBMISSION_ID + "` TEXT ";
    }

    public void checkAndAlterTableForSubForms(String tableName) {
        if (checkIsColumnExist(COLUMN_IS_SUB_FORM,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableIsSubForm(tableName));
        }
        if (checkIsColumnExist(COLUMN_HAS_SUB_FORM,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableHasSubForm(tableName));
        }
        if (checkIsColumnExist(COLUMN_SUB_FORM_OTHER_DATA,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableSubFormOtherData(tableName));
        }

        if (checkIsColumnExist(COLUMN_FORM_UPLOADED,tableName) == -1){
            getSQLiteDatabase().execSQL(alterTableFormUploaded(tableName));
        }

        if (checkIsColumnExist(COLUMN_MAIN_FORM_PK_ID,tableName) == -1){
            getSQLiteDatabase().execSQL(alterTableMainFormPKId(tableName));
        }
    }
    private static String alterTableIsSubForm(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_IS_SUB_FORM + "` INTEGER DEFAULT 0";
    }

    private static String alterTableHasSubForm(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_HAS_SUB_FORM + "` INTEGER DEFAULT 0";
    }


    private static String alterTableSubFormOtherData(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_SUB_FORM_OTHER_DATA + "` TEXT ";
    }

    private static String alterTableFormUploaded(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_FORM_UPLOADED + "` INTEGER DEFAULT 0 ";
    }

    private static String alterTableMainFormPKId(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_MAIN_FORM_PK_ID + "` INTEGER DEFAULT 0 ";
    }

    private static String alterTableFormCheckInOut(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_IS_CHECKIN_OUT + "` INTEGER ";
    }


    public void checkAndAlterTableCheckInOutData(String tableName) {
        if (checkIsColumnExist(COLUMN_IS_CHECKIN_OUT_COMPLETE, tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableCheckInoutComplete(tableName));
        }

        if (checkIsColumnExist(COLUMN_CHECKIN_OUT_FORM_COMPLETE, tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableCheckInoutFormComplete(tableName));
        }
    }

    public void alterTableCheckInOutTimeData(String tableName) {
        if (checkIsColumnExist(COLUMN_FORM_CHECKIN_TIME, tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableCheckInTime(tableName));
        }

        if (checkIsColumnExist(COLUMN_FORM_CHECKOUT_TIME, tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableCheckOutTime(tableName));
        }
    }


    private static String alterTableCheckInTime(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_FORM_CHECKIN_TIME + "` INTEGER ";
    }

    private static String alterTableCheckInoutComplete(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_IS_CHECKIN_OUT_COMPLETE + "` INTEGER ";
    }

    private static String alterTableCheckInoutFormComplete(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_CHECKIN_OUT_FORM_COMPLETE + "` INTEGER ";
    }

    private static String alterTableCheckOutTime(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_FORM_CHECKOUT_TIME + "` INTEGER ";
    }

    public void checkAndAlterTableCrewIds(String tableName) {
        if (checkIsColumnExist(COLUMN_CREW_IDS, tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableCrewIds(tableName));
        }
    }

    private static String alterTableCrewIds(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_CREW_IDS + "` TEXT ";
    }

    public void checkAndAlterTablePreSelectService(String tableName) {
        if (checkIsColumnExist(COLUMN_PRE_SELECT_SERVICE, tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTablePreSelectService(tableName));
        }
    }

    private static String alterTablePreSelectService(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_PRE_SELECT_SERVICE + "` INTEGER DEFAULT 0";
    }





    public void checkAndAlterTableForCheckInCheckOutTime(String tableName) {
        if (checkIsColumnExist(COLUMN_FORM_CHECKIN_TIME,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableFormCheckInTime(tableName));
        }
        if (checkIsColumnExist(COLUMN_FORM_CHECKOUT_TIME,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableFormCheckoutTime(tableName));
        }

        if (checkIsColumnExist(COLUMN_IS_CHECKIN_OUT,tableName) == -1) {
            getSQLiteDatabase().execSQL(alterTableFormCheckInOut(tableName));
        }

    }
    private static String alterTableFormCheckInTime(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_FORM_CHECKIN_TIME + "` INTEGER ";
    }

    private static String alterTableFormCheckoutTime(String tableName) {
        return "ALTER TABLE " + tableName + " ADD" +
                " `" + COLUMN_FORM_CHECKOUT_TIME + "` INTEGER ";
    }


    public void updateFormCheckInTime(String tableName,long checkInTime, int formPkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_FORM_CHECKIN_TIME, checkInTime);

            getSQLiteDatabase().update(tableName, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateFormDataAndCheckInTime(String tableName,SubFormOtherData subFormOtherData, long checkInTime, int formPkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_FORM_CHECKIN_TIME, checkInTime);
            initialValues.put(COLUMN_SUB_FORM_OTHER_DATA, new Gson().toJson(subFormOtherData));
            getSQLiteDatabase().update(tableName, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateFormOtherData(String tableName,int formPkId,SubFormOtherData subFormOtherData ) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_SUB_FORM_OTHER_DATA, new Gson().toJson(subFormOtherData));
            getSQLiteDatabase().update(tableName, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateMainFormHasSubForm(String tableName,int formPkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_HAS_SUB_FORM, 1);
            getSQLiteDatabase().update(tableName, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateFormCheckOutTime(String tableName,long checkInTime, int formPkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_FORM_CHECKOUT_TIME, checkInTime);
            getSQLiteDatabase().update(tableName, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateFormUploaded(String tableName,int formPKId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_FORM_UPLOADED, 1);
            getSQLiteDatabase().update(tableName, initialValues, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPKId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * method to delete single entry from table.
     */
    public void deleteBlankSubFormDataByPKId(String tableName,int mainFormPkId) {
        try {
            getSQLiteDatabase().delete(tableName, COLUMN_MAIN_FORM_PK_ID + "=? AND "+ COLUMN_FORM_CHECKIN_TIME + "=? AND " + COLUMN_IS_SUB_FORM + "=?",
                    new String[]{String.valueOf(mainFormPkId),String.valueOf(0),String.valueOf(1)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public int getPkIdOfNotCheckedInSubForm(String tableName,int mainFormPkId) {
        String rawQuery = "SELECT " + COLUMN_FORM_PK_ID + " FROM " + tableName + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "='" + mainFormPkId + "' AND " + COLUMN_IS_SUB_FORM + "='1' " + " AND " + COLUMN_FORM_CHECKIN_TIME + "= '0'"+" AND " + COLUMN_FORM_UPLOADED + "= '0'";
        Cursor cursor = getSQLiteDatabase().rawQuery(rawQuery, null);
        return getFormPkId(cursor);
    }

    private int getFormPkId(Cursor cursor){
        int formPKIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
        int formPkID = 0;
        if (cursor.moveToFirst()) {
            while (!cursor.isAfterLast()) {
                formPkID = cursor.getInt(formPKIdIndex);
                cursor.moveToNext();
            }
        }

        return formPkID;
    }


    public List<Integer> getPkIdsOfSubmittedSubForm(String tableName,int mainFormPKId) {
        String rawQuery = "SELECT " + COLUMN_FORM_PK_ID + " FROM " + tableName + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "='" + mainFormPKId + "' AND " + COLUMN_IS_SUB_FORM + "='1' "+" AND " + COLUMN_FORM_SUBMITTED + "='1'"+" AND " + COLUMN_FORM_UPLOADED + "='0'";
        Cursor cursor = getSQLiteDatabase().rawQuery(rawQuery, null);
        return getPkIdsOfSubForm(cursor);
    }

    public List<Integer> getPkIdsOfUploadedSubForm(String tableName,int mainFormPKId) {
        String rawQuery = "SELECT " + COLUMN_FORM_PK_ID + " FROM " + tableName + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "='" + mainFormPKId + "' AND "  + COLUMN_IS_SUB_FORM + "='1' "+" AND " + COLUMN_FORM_SUBMITTED + "='1'"+" AND " + COLUMN_FORM_UPLOADED + "='1'";
        Cursor cursor = getSQLiteDatabase().rawQuery(rawQuery, null);
        return getPkIdsOfSubForm(cursor);
    }

    public List<Integer> getPkIdsOfSubForm(String tableName,int mainFormPkId) {
        String rawQuery = "SELECT " + COLUMN_FORM_PK_ID + " FROM " + tableName + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "='" + mainFormPkId + "' AND " + COLUMN_IS_SUB_FORM + "='1' ";
        Cursor cursor = getSQLiteDatabase().rawQuery(rawQuery, null);
        return getPkIdsOfSubForm(cursor);
    }

    private List<Integer> getPkIdsOfSubForm(Cursor cursor) {
        List<Integer> lstPkIds = new ArrayList<>();
        int formPKIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
        int formPkID;
        try {
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    formPkID = cursor.getInt(formPKIdIndex);
                    lstPkIds.add(formPkID);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            cursor.close();
        }

        return lstPkIds;
    }

    /**
     * method to delete single entry from table.
     */
    public void deleteDataByMainFormPKId(String tableName, int formMainPKId) {
        try {
            try {
                getSQLiteDatabase().beginTransactionNonExclusive();
                getSQLiteDatabase().delete(tableName, COLUMN_MAIN_FORM_PK_ID + "=? AND " + COLUMN_IS_SUB_FORM + "=? ", new String[]{String.valueOf(formMainPKId), String.valueOf(1)});
                getSQLiteDatabase().setTransactionSuccessful();
            } catch (SQLException e) {
                FirebaseEventUtils.logException(e);
            } finally {
                getSQLiteDatabase().endTransaction();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

}
