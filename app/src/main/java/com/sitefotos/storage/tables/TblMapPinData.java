package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.MapPinData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;


public class TblMapPinData {

    public static final String TABLE_NAME = "tblMapPinData";
    private static final String COLUMN_FORM_PK_ID = "pk_id";
    private static final String COLUMN_ID = "pin_id";
    private static final String COLUMN_PNG_URL = "pngUrl";
    private static final String COLUMN_SVG_URL = "svgURL";
    private static final String COLUMN_PIN_LABEL = "pinLabel";
    private static final String COLUMN_UPDATE_STATUS = "c_update_status";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblMapPinData(Context context) {
        this.context = context;

        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }

    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_FORM_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_ID + "` INTEGER , " +
                " `" + COLUMN_PNG_URL + "` TEXT, " +
                " `" + COLUMN_SVG_URL + "` TEXT, " +
                " `" + COLUMN_PIN_LABEL + "` TEXT, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER " + " ) ";
    }

    /**
     * Method to insert or update bulk data in MapPinUrl table
     *
     * @param lstMapPinData list of map pin url
     */
    public void insertOrUpdateMapPinBulkData(List<MapPinData> lstMapPinData) {

        ContentValues initialValues = new ContentValues();
        try {
            for (MapPinData mapPinUrl : lstMapPinData) {
                initialValues.put(COLUMN_ID, mapPinUrl.getId());
                initialValues.put(COLUMN_PNG_URL, mapPinUrl.getPngURL());
                initialValues.put(COLUMN_SVG_URL, mapPinUrl.getSvgURL());
                initialValues.put(COLUMN_PIN_LABEL, mapPinUrl.getPinLabel());
                initialValues.put(COLUMN_UPDATE_STATUS, 1);
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_ID + "=?",
                        new String[]{String.valueOf(mapPinUrl.getId())});
                if (id == 0) {
                    writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_REPLACE);
                }

            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    public void updateDataStatus(int updateStatus) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, updateStatus);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPDATE_STATUS + "=?", new String[]{String.valueOf(1)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Method to get all property data
     *
     * @return list of cluster
     */
    public ArrayList<MapPinData> getAllPinData() {
        Cursor cursor = null;
        ArrayList<MapPinData> lstMapPinData = new ArrayList<>();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, new String[]{});
            int idIndex = cursor.getColumnIndex(COLUMN_ID);
            int pngUrlIndex = cursor.getColumnIndex(COLUMN_PNG_URL);
            int svgUrlIndex = cursor.getColumnIndex(COLUMN_SVG_URL);
            int pinLabelIndex = cursor.getColumnIndex(COLUMN_PIN_LABEL);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    MapPinData mapPinData = new MapPinData();
                    mapPinData.setId(cursor.getInt(idIndex));
                    mapPinData.setPngURL(cursor.getString(pngUrlIndex));
                    mapPinData.setSvgURL(cursor.getString(svgUrlIndex));
                    mapPinData.setPinLabel(cursor.getString(pinLabelIndex));
                    lstMapPinData.add(mapPinData);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstMapPinData;
    }


    /**
     * Delete all data from  table
     */
    public void deleteAllDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Delete all old data from table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=' 0 '", null);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }

    /**
     * Function to get Only File Url to Check and download in local device folder
     * @return lst of File Urls
     */
    public List<String> getUpdatedPinPath() {
        Cursor cursor = null;
        ArrayList<String> lstPinPath = new ArrayList<>();
        try {
            String rawQuery = "SELECT " + COLUMN_PNG_URL + " FROM " + TABLE_NAME + " WHERE " + COLUMN_UPDATE_STATUS + "='" + 1 + "'";
            cursor = writableDb.rawQuery(rawQuery, null);
            int pngUrlIndex = cursor.getColumnIndex(COLUMN_PNG_URL);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    String fileUrl = cursor.getString(pngUrlIndex);
                    lstPinPath.add(fileUrl);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstPinPath;
    }
}
