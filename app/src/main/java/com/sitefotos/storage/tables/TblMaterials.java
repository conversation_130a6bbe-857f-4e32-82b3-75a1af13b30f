package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.Material;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dk on 8/1/18.
 */

public class TblMaterials{


    public static final String TABLE_NAME = "tblMaterials";
    private static final String COLUMN_NAME_MATERIAL_ID = "materialId";
    private static final String COLUMN_NAME_MATERIAL_NAME = "materialName";
    private static final String COLUMN_NAME_MATERIAL_UNIT = "materialUnit";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblMaterials(Context context) {
        this.context = context;

        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }


    public static String createTable() {
        return  "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_NAME_MATERIAL_ID + "` VARCHAR PRIMARY KEY NOT NULL UNIQUE , " +
                " `" + COLUMN_NAME_MATERIAL_NAME + "` TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER, " +
                " `" + COLUMN_NAME_MATERIAL_UNIT + "` TEXT " + " ) ";
    }

    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableCreated() {
        if (checkIsColumnExist(COLUMN_CREATED) == -1) {
            writableDb.execSQL(alterTableServiceCreated());
        }
    }

    private static String alterTableServiceCreated() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_CREATED + "` INTEGER ";
    }
    public void checkAndAlterTableUpdated() {
        if (checkIsColumnExist(COLUMN_UPDATED) == -1) {
            writableDb.execSQL(alterTableServiceUpdated());
        }
    }

    private static String alterTableServiceUpdated() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_UPDATED + "` INTEGER ";
    }

    /**
     * Method to insert or update bulk data in material table
     * @param lstMaterial list of materials
     */
    public void insertOrUpdateMaterialBulkData(List<Material> lstMaterial, long firstUpdateDate) {
        ContentValues initialValues = new ContentValues();
        //CustomLogKt.error("Material operation","insertOrUpdateMaterialBulkData start");
        try {
            for (Material material : lstMaterial) {
                initialValues.put(COLUMN_NAME_MATERIAL_ID, material.getMaterialID());
                initialValues.put(COLUMN_NAME_MATERIAL_NAME, material.getMaterialName());
                initialValues.put(COLUMN_NAME_MATERIAL_UNIT, material.getMaterialUnit());

                initialValues.put(COLUMN_UPDATED, firstUpdateDate);

                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_NAME_MATERIAL_ID + "=?",
                        new String[]{String.valueOf(material.getMaterialID())});
                if (id == 0) {
                    initialValues.put(COLUMN_CREATED, firstUpdateDate);
                    writableDb.insert(TABLE_NAME, null, initialValues);
                }
            }
            //CustomLogKt.error("Material operation","insertOrUpdateMaterialBulkData end");
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    /**
     * Method to insert bulk data in material table
     *
     * @param lstMaterial list of materials
     */
    public void insertMaterialBulkData(List<Material> lstMaterial) {
        ContentValues initialValues = new ContentValues();
        try {
            for (Material material : lstMaterial) {
                initialValues.put(COLUMN_NAME_MATERIAL_ID, material.getMaterialID());
                initialValues.put(COLUMN_NAME_MATERIAL_NAME, material.getMaterialName());
                initialValues.put(COLUMN_NAME_MATERIAL_UNIT, material.getMaterialUnit());
                initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
                initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
                writableDb.insert(TABLE_NAME, null, initialValues);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Method to get all material data
     * @return list of materials
     */
    public List<Material> getAllMaterials() {
        Cursor cursor = null;
        ArrayList<Material> listMaterials = new ArrayList<>();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, new String[]{});

            int idIndex = cursor.getColumnIndex(COLUMN_NAME_MATERIAL_ID);
            int materialNameIndex = cursor.getColumnIndex(COLUMN_NAME_MATERIAL_NAME);
            int materialUnitIndex = cursor.getColumnIndex(COLUMN_NAME_MATERIAL_UNIT);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    Material material = new Material();
                    material.setMaterialID(cursor.getInt(idIndex));
                    material.setMaterialName(cursor.getString(materialNameIndex));
                    material.setMaterialUnit(cursor.getString(materialUnitIndex));
                    listMaterials.add(material);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
           // writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
        return listMaterials;
    }


    /**
     * Method to get all material data
     * @return list of materials
     */
    public Material getMaterialsByID(int materialId) {
        Material material = new Material();
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME+" WHERE "+COLUMN_NAME_MATERIAL_ID+"='"+materialId+"'", new String[]{});

            int idIndex = cursor.getColumnIndex(COLUMN_NAME_MATERIAL_ID);
            int materialNameIndex = cursor.getColumnIndex(COLUMN_NAME_MATERIAL_NAME);
            int materialUnitIndex = cursor.getColumnIndex(COLUMN_NAME_MATERIAL_UNIT);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    material.setMaterialID(cursor.getInt(idIndex));
                    material.setMaterialName(cursor.getString(materialNameIndex));
                    material.setMaterialUnit(cursor.getString(materialUnitIndex));
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            // writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
        return material;
    }


    /**
     * Delete all data from material table
     */
    public void deleteDataFromMaterialTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from "+ TABLE_NAME);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }finally {
            writableDb.endTransaction();
        }
    }
    /**
     * Delete all data from the table
     */
    public synchronized void deleteOldData(List<Integer> lstMaterialIds) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_NAME_MATERIAL_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(lstMaterialIds) + ")");
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }
}
