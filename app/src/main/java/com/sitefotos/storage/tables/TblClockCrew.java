package com.sitefotos.storage.tables;

import static com.sitefotos.Constants.CLOCK_BREAK;
import static com.sitefotos.Constants.CLOCK_RESUME;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.Constants;
import com.sitefotos.models.ClockCrewData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;

public class TblClockCrew {

    public static final String TABLE_NAME = "tblClockCrew";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_ID = "crewId";
    private static final String COLUMN_CREW_NAME = "crewName";
    private static final String COLUMN_START_SESSION = "startSession";
    private static final String COLUMN_TOTAL_SESSION = "totalSession";
    private static final String COLUMN_CLOCK_CREATED = "created";
    private static final String COLUMN_CLOCK_UPDATED = "updated";
    private static final String COLUMN_CLOCK_BREAK_TIME = "breakTime";
    private static final String COLUMN_CLOCK_TYPE = "clockInType";
    private static final String COLUMN_IS_CREW = "isCrew";
    private static final String COLUMN_IS_LOGIN_USER_ID = "loginUser";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblClockCrew(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }

        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_ID + "` INTEGER , " +
                " `" + COLUMN_CREW_NAME + "` TEXT, " +
                " `" + COLUMN_TOTAL_SESSION + "` INTEGER, " +
                " `" + COLUMN_START_SESSION + "` INTEGER, " +
                " `" + COLUMN_CLOCK_BREAK_TIME + "` INTEGER, " +
                " `" + COLUMN_CLOCK_TYPE + "` TEXT, " +
                " `" + COLUMN_CLOCK_CREATED + "` INTEGER, " +
                " `" + COLUMN_IS_CREW + "` INTEGER, " +
                " `" + COLUMN_IS_LOGIN_USER_ID + "` INTEGER, " +
                " `" + COLUMN_CLOCK_UPDATED + "` INTEGER " + " ) ";

    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAltTblIsCrew() {
        try {
            if (checkIsColumnExist(COLUMN_IS_CREW) == -1) {
                writableDb.execSQL(altTblIsCrew());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public static String altTblIsCrew() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_CREW + "` INTEGER ";
    }

    public static String altTblLoginUser() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_LOGIN_USER_ID + "` INTEGER ";
    }

    public void checkAndAltTblLoginUser() {
        try {
            if (checkIsColumnExist(COLUMN_IS_LOGIN_USER_ID) == -1) {
                writableDb.execSQL(altTblLoginUser());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * Method to insert data in table
     */
    public void insertData(ClockCrewData data) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_ID, data.getCrewId());
            initialValues.put(COLUMN_CREW_NAME, data.getCrewName());
            initialValues.put(COLUMN_TOTAL_SESSION, 0);
            initialValues.put(COLUMN_START_SESSION, 0);
            initialValues.put(COLUMN_CLOCK_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCK_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCK_TYPE, data.getClockInType());
            initialValues.put(COLUMN_CLOCK_BREAK_TIME, data.getBreakTime());
            initialValues.put(COLUMN_IS_CREW, data.isCrew() ? 1 : 0);
            initialValues.put(COLUMN_IS_LOGIN_USER_ID, StaticUtils.getEmployeeIdInInt() == data.getCrewId());
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    public synchronized void updateData(long id, ClockCrewData data) {
        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_ID, data.getCrewId());
            initialValues.put(COLUMN_CREW_NAME, data.getCrewName());
            initialValues.put(COLUMN_TOTAL_SESSION, data.getTotalSession());
            initialValues.put(COLUMN_START_SESSION, data.getStartSession());
            initialValues.put(COLUMN_CLOCK_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCK_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCK_TYPE, data.getClockInType());
            initialValues.put(COLUMN_CLOCK_BREAK_TIME, data.getBreakTime());
            initialValues.put(COLUMN_IS_CREW, data.isCrew() ? 1 : 0);
            initialValues.put(COLUMN_IS_LOGIN_USER_ID, data.isLoginUser() ? 1 : 0);

            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?",
                    new String[]{String.valueOf(id)});

            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }


    public synchronized void updateCrewNormalData(long id, String crewName, int isCrew) {
        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_CREW_NAME, crewName);
            initialValues.put(COLUMN_IS_CREW, isCrew);

            writableDb.update(TABLE_NAME, initialValues, COLUMN_ID + "=?",
                    new String[]{String.valueOf(id)});

            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    public synchronized void updateBulkData(List<ClockCrewData> lstCrewData) {

        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            for (ClockCrewData data : lstCrewData) {
                initialValues.put(COLUMN_ID, data.getCrewId());
                initialValues.put(COLUMN_CREW_NAME, data.getCrewName());
                initialValues.put(COLUMN_IS_CREW, data.isCrew() ? 1 : 0);
                initialValues.put(COLUMN_TOTAL_SESSION, data.getTotalSession());
                initialValues.put(COLUMN_START_SESSION, data.getStartSession());
                initialValues.put(COLUMN_CLOCK_TYPE, data.getClockInType());
                initialValues.put(COLUMN_CLOCK_BREAK_TIME, data.getBreakTime());
                initialValues.put(COLUMN_IS_LOGIN_USER_ID, data.isLoginUser() ? 1 : 0);
                writableDb.update(TABLE_NAME, initialValues, COLUMN_ID + "=?",
                        new String[]{String.valueOf(data.getCrewId())});

            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public synchronized void insertOrUpdateBulkData(List<ClockCrewData> lstCrewData) {

        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            for (ClockCrewData data : lstCrewData) {
                initialValues.put(COLUMN_ID, data.getCrewId());
                initialValues.put(COLUMN_CREW_NAME, data.getCrewName());
                initialValues.put(COLUMN_IS_CREW, data.isCrew() ? 1 : 0);
                initialValues.put(COLUMN_TOTAL_SESSION, data.getTotalSession());
                initialValues.put(COLUMN_START_SESSION, data.getStartSession());
                initialValues.put(COLUMN_CLOCK_UPDATED, System.currentTimeMillis());
                initialValues.put(COLUMN_CLOCK_TYPE, data.getClockInType());
                initialValues.put(COLUMN_CLOCK_BREAK_TIME, data.getBreakTime());
                initialValues.put(COLUMN_IS_LOGIN_USER_ID, StaticUtils.getEmployeeIdInInt() == data.getCrewId());
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_ID + "=?",
                        new String[]{String.valueOf(data.getCrewId())});
                if (id == 0) {
                    initialValues.put(COLUMN_CLOCK_CREATED, System.currentTimeMillis());
                    initialValues.put(COLUMN_CLOCK_BREAK_TIME, 0);
                    initialValues.put(COLUMN_TOTAL_SESSION, 0);
                    writableDb.insert(TABLE_NAME, null, initialValues);
                }
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }


    public synchronized void insertOrUpdateSingleData(ClockCrewData clockCrewData) {

        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_ID, clockCrewData.getCrewId());
            initialValues.put(COLUMN_CREW_NAME, clockCrewData.getCrewName());
            initialValues.put(COLUMN_IS_CREW, clockCrewData.isCrew() ? 1 : 0);
            initialValues.put(COLUMN_TOTAL_SESSION, clockCrewData.getTotalSession());
            initialValues.put(COLUMN_START_SESSION, clockCrewData.getStartSession());
            initialValues.put(COLUMN_CLOCK_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_CLOCK_TYPE, clockCrewData.getClockInType());
            initialValues.put(COLUMN_IS_LOGIN_USER_ID, StaticUtils.getEmployeeIdInInt() == clockCrewData.getCrewId());
            initialValues.put(COLUMN_CLOCK_BREAK_TIME, clockCrewData.getBreakTime());
            int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_ID + "=?",
                    new String[]{String.valueOf(clockCrewData.getCrewId())});
            if (id == 0) {
                initialValues.put(COLUMN_CLOCK_CREATED, System.currentTimeMillis());
                initialValues.put(COLUMN_CLOCK_BREAK_TIME, 0);
                initialValues.put(COLUMN_TOTAL_SESSION, 0);
                writableDb.insert(TABLE_NAME, null, initialValues);
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public synchronized void insertBulkData(List<ClockCrewData> lstCrewData) {

        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            for (ClockCrewData data : lstCrewData) {
                initialValues.put(COLUMN_ID, data.getCrewId());
                initialValues.put(COLUMN_CREW_NAME, data.getCrewName());
                initialValues.put(COLUMN_IS_CREW, data.isCrew() ? 1 : 0);
                initialValues.put(COLUMN_START_SESSION, data.getStartSession());
                initialValues.put(COLUMN_CLOCK_UPDATED, System.currentTimeMillis());
                initialValues.put(COLUMN_CLOCK_TYPE, data.getClockInType());
                initialValues.put(COLUMN_CLOCK_CREATED, System.currentTimeMillis());
                initialValues.put(COLUMN_CLOCK_BREAK_TIME, 0);
                initialValues.put(COLUMN_TOTAL_SESSION, 0);
                initialValues.put(COLUMN_IS_LOGIN_USER_ID, StaticUtils.getEmployeeIdInInt() == data.getCrewId());
                writableDb.insert(TABLE_NAME, null, initialValues);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }


    public List<ClockCrewData> getAllData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }


    public List<ClockCrewData> getAllResumedWorkCrewData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<ClockCrewData> getAllBreakedCrewData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_BREAK + "'" + " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<ClockCrewData> getAllNormalCrewData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_NORMAL + "'" +" AND " + COLUMN_IS_CREW + "='1'"+ " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }


    public List<ClockCrewData> getAllExtendedCrewData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_NORMAL + "'" + " AND " + COLUMN_IS_CREW + "='0'"+" ORDER BY " + COLUMN_CREW_NAME + " ASC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<ClockCrewData> getAllExtendedClockedCrewData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " +
                COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " OR " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_BREAK + "'" +
                " AND " + COLUMN_IS_CREW + "='0'"+ " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<ClockCrewData> getAllNormalData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_NORMAL + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<ClockCrewData> getAllClockedCrewData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " +
                COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " OR " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_BREAK + "'" + " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<Integer> getAllResumedWorkCrewId() {
        String rawQuery = "SELECT DISTINCT " + COLUMN_ID + " FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getCrewIdsFromDb(cursor);
    }


    public List<Integer> getAllResumedWorkWithoutCrewIds(List<Integer> lstCrewIds) {
        String rawQuery = "SELECT DISTINCT " + COLUMN_ID + " FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " AND " + COLUMN_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(lstCrewIds) + ")";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getCrewIdsFromDb(cursor);
    }


    public List<ClockCrewData> getDataByCrewIds(List<Integer> lstCrewIds) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_ID + " IN (" + DBUtils.toCommaSeparatedString(lstCrewIds) + ")";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getDatFromDb(cursor);
    }

    public List<Integer> getAllClockedCrewDataIds() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " +
                COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " OR " + COLUMN_CLOCK_TYPE + "='" + Constants.CLOCK_BREAK + "'" + " ORDER BY " + COLUMN_IS_LOGIN_USER_ID + " DESC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getCrewIdsFromDb(cursor);
    }

    private List<ClockCrewData> getDatFromDb(Cursor cursor) {
        List<ClockCrewData> lstData = new ArrayList<>();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_ID);
            int siteNameIndex = cursor.getColumnIndex(COLUMN_CREW_NAME);
            int createdIndex = cursor.getColumnIndex(COLUMN_CLOCK_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_CLOCK_UPDATED);
            int totalSessionIndex = cursor.getColumnIndex(COLUMN_TOTAL_SESSION);
            int currentSessionIndex = cursor.getColumnIndex(COLUMN_START_SESSION);
            int clockTypeIndex = cursor.getColumnIndex(COLUMN_CLOCK_TYPE);
            int breakTimeIndex = cursor.getColumnIndex(COLUMN_CLOCK_BREAK_TIME);
            int isCrewIndex = cursor.getColumnIndex(COLUMN_IS_CREW);
            int isLoginUserIndex = cursor.getColumnIndex(COLUMN_IS_LOGIN_USER_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    ClockCrewData data = new ClockCrewData();
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setCrewId(cursor.getInt(siteIdIndex));
                    data.setCrewName(cursor.getString(siteNameIndex));
                    data.setTotalSession(cursor.getLong(totalSessionIndex));
                    data.setStartSession(cursor.getLong(currentSessionIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setClockInType(cursor.getString(clockTypeIndex));
                    data.setBreakTime(cursor.getLong(breakTimeIndex));
                    data.setCrew(cursor.getInt(isCrewIndex) > 0);
                    data.setLoginUser(cursor.getInt(isLoginUserIndex) > 0);
                    lstData.add(data);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
        return lstData;
    }


    private List<Integer> getCrewIdsFromDb(Cursor cursor) {
        List<Integer> lstData = new ArrayList<>();
        try {
            int crewIdIndex = cursor.getColumnIndex(COLUMN_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstData.add(cursor.getInt(crewIdIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstData;
    }

    public ClockCrewData getDataById(int id) {

        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_ID + "='" + id + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }


    private ClockCrewData getSingleDataFromDB(Cursor cursor) {
        //writableDb.beginTransactionNonExclusive();
        ClockCrewData data = new ClockCrewData();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_ID);
            int siteNameIndex = cursor.getColumnIndex(COLUMN_CREW_NAME);
            int createdIndex = cursor.getColumnIndex(COLUMN_CLOCK_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_CLOCK_UPDATED);
            int totalSessionIndex = cursor.getColumnIndex(COLUMN_TOTAL_SESSION);
            int currentSessionIndex = cursor.getColumnIndex(COLUMN_START_SESSION);
            int clockTypeIndex = cursor.getColumnIndex(COLUMN_CLOCK_TYPE);
            int breakTimeIndex = cursor.getColumnIndex(COLUMN_CLOCK_BREAK_TIME);
            int isCrewIndex = cursor.getColumnIndex(COLUMN_IS_CREW);
            int isLoginUserIndex = cursor.getColumnIndex(COLUMN_IS_LOGIN_USER_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setCrewId(cursor.getInt(siteIdIndex));
                    data.setCrewName(cursor.getString(siteNameIndex));
                    data.setTotalSession(cursor.getLong(totalSessionIndex));
                    data.setStartSession(cursor.getLong(currentSessionIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setClockInType(cursor.getString(clockTypeIndex));
                    data.setBreakTime(cursor.getLong(breakTimeIndex));
                    data.setCrew(cursor.getInt(isCrewIndex) > 0);
                    data.setLoginUser(cursor.getInt(isLoginUserIndex) > 0);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }

        return data;

    }

    public boolean checkLoginUserClockedIn() {

        String rawQuery = "SELECT count(*)  FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " AND " + COLUMN_ID + "='" + StaticUtils.getEmployeeIdInInt() + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        try {
            cursor.moveToFirst();
            return cursor.getInt(0) > 0;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return false;
    }

    public void updateTimeData(long id, long currentSession, long totalSession, long breakTime) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_START_SESSION, currentSession);
            initialValues.put(COLUMN_TOTAL_SESSION, totalSession);
            initialValues.put(COLUMN_CLOCK_BREAK_TIME, breakTime);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void updateTypeAndTimeData(long id, long currentSession, long totalSession, long breakTime, String type) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_START_SESSION, currentSession);
            initialValues.put(COLUMN_TOTAL_SESSION, totalSession);
            initialValues.put(COLUMN_CLOCK_BREAK_TIME, breakTime);
            initialValues.put(COLUMN_CLOCK_TYPE, type);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?  ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            //writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Delete  data by Ids from table
     */
    public void deleteDataByIds(List<Integer> ids) {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME + " WHERE " + COLUMN_ID + " IN (" + DBUtils.toCommaSeparatedString(ids) + ")");
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * Delete  data by Ids from table
     */
    public void deleteDataWithoutIds(List<Integer> ids) {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME + " WHERE " + COLUMN_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(ids) + ")");
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Delete  data by Id from table
     */
    public void deleteDataById(Integer id) {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME + " WHERE " + COLUMN_ID + "='" + id + "'");
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }


    public int getClockedInDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME + " WHERE " + COLUMN_CLOCK_TYPE + "='" + CLOCK_RESUME + "'" + " OR " + COLUMN_CLOCK_TYPE + "='" + CLOCK_BREAK + "'";
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

}
