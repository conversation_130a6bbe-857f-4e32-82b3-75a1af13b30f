package com.sitefotos.storage.tables;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.text.TextUtils;

import com.sitefotos.Constants;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import java.util.ArrayList;
import java.util.List;


public class TblUploadData {

    public static final String TABLE_NAME = "tblUploadData";
    private static final String COLUMN_DATA_PK_ID = "pkId";
    private static final String COLUMN_TITLE = "title";
    private static final String COLUMN_BASE_URL = "serverUrl";
    private static final String COLUMN_DATA = "data";
    private static final String COLUMN_REQUESTED_DATA = "requestedData";
    private static final String COLUMN_DATA_TYPE = "dataType";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_FORM_PK_ID = "formPKId";
    private static final String COLUMN_ISSUE_TAG_ID = "issueTagId";
    private static final String COLUMN_TM_FORM_PK_ID = "tmFormPKId";
    private static final String COLUMN_WP_PK_ID = "wpPKId";
    private static final String COLUMN_IS_IMAGE_UPLOADED = "isImageUploaded";
    //NOTE:  Just ignore this from app version 2.2.9
    private static final String COLUMN_IS_UPLOAD_PROCESS_START = "isUploadProcessStart";

    //NOTE:  Just ignore this from app version 2.2.9
    private static final String COLUMN_PROCESS_START_TIME = "processStartTime";
    private static final String COLUMN_UUID = "uuid";
    private static final String COLUMN_FORM_SUBMISSION_ID = "formSubmissionID";
    protected static final String COLUMN_IS_SUB_FORM = "isSubForm";
    protected static final String COLUMN_HAS_SUB_FORM = "hasSubForm";

    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;
    private Context context;

    public TblUploadData(Context context) {
        this.context = context;
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_DATA_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_BASE_URL + "` TEXT, " +
                " `" + COLUMN_DATA + "` TEXT, " +
                " `" + COLUMN_REQUESTED_DATA + "` TEXT, " +
                " `" + COLUMN_TITLE + "` TEXT, " +
                " `" + COLUMN_DATA_TYPE + "` TEXT, " +
                " `" + COLUMN_FORM_PK_ID + "` INTEGER, " +
                " `" + COLUMN_ISSUE_TAG_ID + "` INTEGER, " +
                " `" + COLUMN_TM_FORM_PK_ID + "` INTEGER, " +
                " `" + COLUMN_WP_PK_ID + "` INTEGER, " +
                " `" + COLUMN_IS_IMAGE_UPLOADED + "` INTEGER, " +
                " `" + COLUMN_IS_UPLOAD_PROCESS_START + "` INTEGER, " +
                " `" + COLUMN_PROCESS_START_TIME + "` TEXT, " +
                " `" + COLUMN_UUID + "` TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_FORM_SUBMISSION_ID + "` TEXT, " +
                " `" + COLUMN_IS_SUB_FORM + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_HAS_SUB_FORM + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_UPDATED + "` INTEGER " + " ) ";
    }

    private static String alterTableTitle() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_TITLE + "` TEXT ";
    }

    private static String alterTableFormPKId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FORM_PK_ID + "` INTEGER ";
    }

    private static String alterTableIssueTagId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_ISSUE_TAG_ID + "` INTEGER ";
    }

    private static String alterTableTMFormPKId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_TM_FORM_PK_ID + "` INTEGER ";
    }

    private static String alterTableWPPKId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_WP_PK_ID + "` INTEGER ";
    }

    private static String alterTableUUID() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_UUID + "` TEXT ";
    }

    private static String alterTableIsImageUploaded() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_IMAGE_UPLOADED + "` INTEGER ";
    }

    private static String alterTableUploadingTimeProcess() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_PROCESS_START_TIME + "` TEXT ";
    }

    private static String alterTableUploadingProcess() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_UPLOAD_PROCESS_START + "` INTEGER ";
    }

    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableTitle() {
        if (checkIsColumnExist(COLUMN_TITLE) == -1) {
            writableDb.execSQL(alterTableTitle());
        }
    }

    public void checkAndAlterTableFormPKId() {
        if (checkIsColumnExist(COLUMN_FORM_PK_ID) == -1) {
            writableDb.execSQL(alterTableFormPKId());
        }
    }

    public void checkAndAlterTableIssueTagId() {
        if (checkIsColumnExist(COLUMN_ISSUE_TAG_ID) == -1) {
            writableDb.execSQL(alterTableIssueTagId());
        }
    }

    public void checkAndAlterTableTMFormPKId() {
        if (checkIsColumnExist(COLUMN_TM_FORM_PK_ID) == -1) {
            writableDb.execSQL(alterTableTMFormPKId());
        }
    }

    public void checkAndAlterTableWPPKId() {

        if (checkIsColumnExist(COLUMN_WP_PK_ID) == -1) {
            writableDb.execSQL(alterTableWPPKId());
        }

    }

    public void checkAndAlterTableUUID() {
        if (checkIsColumnExist(COLUMN_UUID) == -1) {
            writableDb.execSQL(alterTableUUID());
        }
    }

    public void checkAndAlterTableIsImageUploaded() {
        if (checkIsColumnExist(COLUMN_IS_IMAGE_UPLOADED) == -1) {
            writableDb.execSQL(alterTableIsImageUploaded());
        }
    }

    public void checkAndAlterTableUploadingTimeProcess() {
        if (checkIsColumnExist(COLUMN_PROCESS_START_TIME) == -1) {
            writableDb.execSQL(alterTableUploadingTimeProcess());
        }
    }

    public void checkAndAlterTableUploadingProcess() {
        if (checkIsColumnExist(COLUMN_IS_UPLOAD_PROCESS_START) == -1) {
            writableDb.execSQL(alterTableUploadingProcess());
        }
    }

    public void checkAndAlterTableFormSubmissionId() {
        if (checkIsColumnExist(COLUMN_FORM_SUBMISSION_ID) == -1) {
            writableDb.execSQL(alterTableFormSubmissionId());
        }
    }

    private static String alterTableFormSubmissionId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FORM_SUBMISSION_ID + "` TEXT ";
    }

    public void checkAndAlterTableForSubForms() {
        if (checkIsColumnExist(COLUMN_IS_SUB_FORM) == -1) {
            writableDb.execSQL(alterTableIsSubForm());
        }
        if (checkIsColumnExist(COLUMN_HAS_SUB_FORM) == -1) {
            writableDb.execSQL(alterTableHasSubForm());
        }
    }

    private static String alterTableIsSubForm() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_SUB_FORM + "` INTEGER DEFAULT 0";
    }

    private static String alterTableHasSubForm() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_HAS_SUB_FORM + "` INTEGER DEFAULT 0";
    }

    /**
     * Method to insert data in table
     *
     * @param uploadOtherData UploadOtherData
     */
    public void insertData(UploadOtherData uploadOtherData) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_TITLE, uploadOtherData.getTitle());
            initialValues.put(COLUMN_BASE_URL, uploadOtherData.getBaseUrl());
            initialValues.put(COLUMN_DATA, uploadOtherData.getData());
            initialValues.put(COLUMN_DATA_TYPE, uploadOtherData.getDataType());
            initialValues.put(COLUMN_REQUESTED_DATA, uploadOtherData.getRequestedData());
            initialValues.put(COLUMN_FORM_PK_ID, uploadOtherData.getFormPKId());
            initialValues.put(COLUMN_TM_FORM_PK_ID, uploadOtherData.getTmFormPKId());
            initialValues.put(COLUMN_WP_PK_ID, uploadOtherData.getWpPKId());
            initialValues.put(COLUMN_IS_IMAGE_UPLOADED, uploadOtherData.isImageUploaded());
            initialValues.put(COLUMN_CREATED, uploadOtherData.getCreatedAt());
            initialValues.put(COLUMN_UPDATED, uploadOtherData.getUpdatedAt());
            initialValues.put(COLUMN_ISSUE_TAG_ID, uploadOtherData.getIssueTagId());
            initialValues.put(COLUMN_FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
            initialValues.put(COLUMN_IS_SUB_FORM, uploadOtherData.isSubForm() ? 1 : 0);
            initialValues.put(COLUMN_HAS_SUB_FORM, uploadOtherData.hasSubForm() ? 1 : 0);
            if (!TextUtils.isEmpty(uploadOtherData.getUuid())) {
                uploadOtherData.setUuid(StaticUtils.getUuid());
            }
            initialValues.put(COLUMN_UUID, uploadOtherData.getUuid());
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb, null);
            writableDb.endTransaction();
        }
    }


    /**
     * Method to insert data in table
     *
     * @param uploadOtherData UploadOtherData
     */
    public void insertFormData(UploadOtherData uploadOtherData, boolean isTMForm) {
        try {
            writableDb.beginTransactionNonExclusive();

            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_TITLE, uploadOtherData.getTitle());
            initialValues.put(COLUMN_BASE_URL, uploadOtherData.getBaseUrl());
            initialValues.put(COLUMN_DATA, uploadOtherData.getData());
            initialValues.put(COLUMN_DATA_TYPE, uploadOtherData.getDataType());
            initialValues.put(COLUMN_REQUESTED_DATA, uploadOtherData.getRequestedData());
            initialValues.put(COLUMN_FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
            initialValues.put(COLUMN_FORM_PK_ID, uploadOtherData.getFormPKId());
            initialValues.put(COLUMN_TM_FORM_PK_ID, uploadOtherData.getTmFormPKId());
            initialValues.put(COLUMN_WP_PK_ID, uploadOtherData.getWpPKId());
            initialValues.put(COLUMN_IS_IMAGE_UPLOADED, uploadOtherData.isImageUploaded());
            initialValues.put(COLUMN_ISSUE_TAG_ID, uploadOtherData.getIssueTagId());
            initialValues.put(COLUMN_IS_SUB_FORM, uploadOtherData.isSubForm() ? 1 : 0);
            initialValues.put(COLUMN_HAS_SUB_FORM, uploadOtherData.hasSubForm() ? 1 : 0);

            int id;

            if (isTMForm) {
                id = writableDb.update(TABLE_NAME, initialValues, COLUMN_TM_FORM_PK_ID + "=? " + " AND " + COLUMN_DATA_TYPE + "=?",
                        new String[]{String.valueOf(uploadOtherData.getTmFormPKId()), Constants.FORM_DATA});
            } else {
                id = writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=? " + " AND " + COLUMN_DATA_TYPE + "=?",
                        new String[]{String.valueOf(uploadOtherData.getFormPKId()), Constants.FORM_DATA});
            }

            if (id == 0) {
                initialValues.put(COLUMN_CREATED, uploadOtherData.getCreatedAt());
                initialValues.put(COLUMN_UPDATED, uploadOtherData.getUpdatedAt());
                uploadOtherData.setUuid(StaticUtils.getUuid());
                initialValues.put(COLUMN_UUID, uploadOtherData.getUuid());
                writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb, null);

            writableDb.endTransaction();
        }
    }

    /**
     * Method to insert data in table
     *
     * @param uploadOtherData UploadOtherData
     */
    public void insertWPData(UploadOtherData uploadOtherData) {
        try {
            writableDb.beginTransactionNonExclusive();

            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_TITLE, uploadOtherData.getTitle());
            initialValues.put(COLUMN_BASE_URL, uploadOtherData.getBaseUrl());
            initialValues.put(COLUMN_DATA, uploadOtherData.getData());
            initialValues.put(COLUMN_DATA_TYPE, uploadOtherData.getDataType());
            initialValues.put(COLUMN_REQUESTED_DATA, uploadOtherData.getRequestedData());
            initialValues.put(COLUMN_FORM_PK_ID, uploadOtherData.getFormPKId());
            initialValues.put(COLUMN_TM_FORM_PK_ID, uploadOtherData.getTmFormPKId());
            initialValues.put(COLUMN_WP_PK_ID, uploadOtherData.getWpPKId());
            initialValues.put(COLUMN_IS_IMAGE_UPLOADED, uploadOtherData.isImageUploaded());
            initialValues.put(COLUMN_ISSUE_TAG_ID, uploadOtherData.getIssueTagId());
            initialValues.put(COLUMN_FORM_SUBMISSION_ID, uploadOtherData.getFormSubmissionId());
           /* int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_WP_PK_ID + "=? " + " AND " + COLUMN_DATA_TYPE + "=?",
                    new String[]{String.valueOf(uploadOtherData.getWpPKId()), Constants.WP_DATA});*/

            // if (id == 0) {
            initialValues.put(COLUMN_CREATED, uploadOtherData.getCreatedAt());
            initialValues.put(COLUMN_UPDATED, uploadOtherData.getUpdatedAt());
            uploadOtherData.setUuid(StaticUtils.getUuid());
            initialValues.put(COLUMN_UUID, uploadOtherData.getUuid());
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);

            //CustomLogKt.error("TblUploadData wp", "Data inserted");
            //} else {
            // CustomLogKt.error("TblUploadData wp", "Data updated");
            // }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb, null);

            writableDb.endTransaction();
        }
    }


    /**
     * Method to get all form data
     *
     * @return lstUploadImageData of forms
     */
    public ArrayList<UploadOtherData> getAllUploadImageData() {
        Cursor cursor = null;
        ArrayList<UploadOtherData> lstUploadImages = new ArrayList<>();
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + " " + COLUMN_UPDATED + " ASC";
            cursor = readableDb.rawQuery(rawQuery, null);
            int title = cursor.getColumnIndex(COLUMN_TITLE);
            int pkIdIndex = cursor.getColumnIndex(COLUMN_DATA_PK_ID);
            int baseUrlIndex = cursor.getColumnIndex(COLUMN_BASE_URL);
            int dataTypeIndex = cursor.getColumnIndex(COLUMN_DATA_TYPE);
            int dataIndex = cursor.getColumnIndex(COLUMN_DATA);
            int requestedDataIndex = cursor.getColumnIndex(COLUMN_REQUESTED_DATA);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int formPKIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int wpPKIdIndex = cursor.getColumnIndex(COLUMN_WP_PK_ID);
            int IsImageUploadedIndex = cursor.getColumnIndex(COLUMN_IS_IMAGE_UPLOADED);
            int tmFormPKIdIndex = cursor.getColumnIndex(COLUMN_TM_FORM_PK_ID);
            int issueTagIdIndex = cursor.getColumnIndex(COLUMN_ISSUE_TAG_ID);
            int uuidIndex = cursor.getColumnIndex(COLUMN_UUID);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            int isSubFormIndex = cursor.getColumnIndex(COLUMN_IS_SUB_FORM);
            int hasSubFormIndex = cursor.getColumnIndex(COLUMN_HAS_SUB_FORM);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    UploadOtherData uploadOtherData = new UploadOtherData();
                    uploadOtherData.setTitle(cursor.getString(title));
                    uploadOtherData.setId(cursor.getInt(pkIdIndex));
                    uploadOtherData.setBaseUrl(cursor.getString(baseUrlIndex));
                    uploadOtherData.setDataType(cursor.getString(dataTypeIndex));
                    uploadOtherData.setData(cursor.getString(dataIndex));
                    uploadOtherData.setCreatedAt(cursor.getLong(createdIndex));
                    uploadOtherData.setUpdatedAt(cursor.getLong(updatedIndex));
                    uploadOtherData.setRequestedData(cursor.getString(requestedDataIndex));
                    uploadOtherData.setFormPKId(cursor.getInt(formPKIdIndex));
                    uploadOtherData.setIssueTagId(cursor.getInt(issueTagIdIndex));
                    uploadOtherData.setTmFormPKId(cursor.getInt(tmFormPKIdIndex));
                    uploadOtherData.setWpPKId(cursor.getInt(wpPKIdIndex));
                    uploadOtherData.setImageUploaded(cursor.getInt(IsImageUploadedIndex) == 1);
                    uploadOtherData.setUuid(cursor.getString(uuidIndex));
                    uploadOtherData.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                    uploadOtherData.setSubForm(cursor.getInt(isSubFormIndex) > 0);
                    uploadOtherData.setHasSubForm(cursor.getInt(hasSubFormIndex) > 0);
                    lstUploadImages.add(uploadOtherData);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb,  cursor);
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstUploadImages;
    }

    /**
     * Method to get all form data
     *
     * @return lstUploadImageData of forms
     */
    public UploadOtherData getUploadDataByFormId(int formPkId, int tagId, boolean isTMForm) {
        Cursor cursor = null;
        UploadOtherData uploadOtherData = new UploadOtherData();
        try {
            String rawQuery;
            if (isTMForm)
                rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_TM_FORM_PK_ID + "='" + formPkId + "' AND " + COLUMN_ISSUE_TAG_ID + "='" + tagId + "'";
            else
                rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + "='" + formPkId + "' AND " + COLUMN_ISSUE_TAG_ID + "='" + tagId + "'";
            cursor = readableDb.rawQuery(rawQuery, null);
            int title = cursor.getColumnIndex(COLUMN_TITLE);
            int pkIdIndex = cursor.getColumnIndex(COLUMN_DATA_PK_ID);
            int baseUrlIndex = cursor.getColumnIndex(COLUMN_BASE_URL);
            int dataTypeIndex = cursor.getColumnIndex(COLUMN_DATA_TYPE);
            int dataIndex = cursor.getColumnIndex(COLUMN_DATA);
            int requestedDataIndex = cursor.getColumnIndex(COLUMN_REQUESTED_DATA);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int formPKIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int wpPKIdIndex = cursor.getColumnIndex(COLUMN_WP_PK_ID);
            int IsImageUploadedIndex = cursor.getColumnIndex(COLUMN_IS_IMAGE_UPLOADED);
            int tmFormPKIdIndex = cursor.getColumnIndex(COLUMN_TM_FORM_PK_ID);
            int issueTagIdIndex = cursor.getColumnIndex(COLUMN_ISSUE_TAG_ID);
            int uuidIndex = cursor.getColumnIndex(COLUMN_UUID);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            int isSubFormIndex = cursor.getColumnIndex(COLUMN_IS_SUB_FORM);
            int hasSubFormIndex = cursor.getColumnIndex(COLUMN_HAS_SUB_FORM);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    uploadOtherData.setTitle(cursor.getString(title));
                    uploadOtherData.setId(cursor.getInt(pkIdIndex));
                    uploadOtherData.setBaseUrl(cursor.getString(baseUrlIndex));
                    uploadOtherData.setDataType(cursor.getString(dataTypeIndex));
                    uploadOtherData.setData(cursor.getString(dataIndex));
                    uploadOtherData.setCreatedAt(cursor.getLong(createdIndex));
                    uploadOtherData.setUpdatedAt(cursor.getLong(updatedIndex));
                    uploadOtherData.setRequestedData(cursor.getString(requestedDataIndex));
                    uploadOtherData.setFormPKId(cursor.getInt(formPKIdIndex));
                    uploadOtherData.setIssueTagId(cursor.getInt(issueTagIdIndex));
                    uploadOtherData.setTmFormPKId(cursor.getInt(tmFormPKIdIndex));
                    uploadOtherData.setUuid(cursor.getString(uuidIndex));
                    uploadOtherData.setWpPKId(cursor.getInt(wpPKIdIndex));
                    uploadOtherData.setImageUploaded(cursor.getInt(IsImageUploadedIndex) == 1);
                    uploadOtherData.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                    uploadOtherData.setSubForm(cursor.getInt(isSubFormIndex) > 0);
                    uploadOtherData.setHasSubForm(cursor.getInt(hasSubFormIndex) > 0);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return uploadOtherData;
    }


    public UploadOtherData getLastUploadData() {

        String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + " " + COLUMN_UPDATED + " DESC LIMIT 1 ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    /**
     * Method to get all form data
     *
     * @return lstUploadImageData of forms
     */
    public UploadOtherData getUploadDataByFormPKId(int formPkId, boolean isTMForm) {
        Cursor cursor;
        String rawQuery;
        if (isTMForm)
            rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_TM_FORM_PK_ID + "='" + formPkId + "'";
        else
            rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + "='" + formPkId + "'";
        cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    /**
     * Method to get next upload data
     */
    public UploadOtherData getNextUploadDataWithImageStatusZero() {

        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_IMAGE_UPLOADED + "='0'" + " ORDER BY " + " " + COLUMN_UPDATED + "  ASC LIMIT 1 ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    /**
     * Method to get next upload data
     */
    public UploadOtherData getNextUploadData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_IMAGE_UPLOADED + "='1'" + " ORDER BY " + " " + COLUMN_UPDATED + " ASC LIMIT 1 ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    public UploadOtherData checkNotLargeSiteUploadData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_IMAGE_UPLOADED + "='1'" + " AND " + COLUMN_IS_SUB_FORM + "='0'" + " AND " + COLUMN_HAS_SUB_FORM + "='0'" + " ORDER BY " + " " + COLUMN_UPDATED + " ASC LIMIT 1 ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private UploadOtherData getSingleDataFromDB(Cursor cursor) {
        UploadOtherData uploadOtherData = null;
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_DATA_PK_ID);
            int titleIndex = cursor.getColumnIndex(COLUMN_TITLE);
            int baseUrlIndex = cursor.getColumnIndex(COLUMN_BASE_URL);
            int dataTypeIndex = cursor.getColumnIndex(COLUMN_DATA_TYPE);
            int dataIndex = cursor.getColumnIndex(COLUMN_DATA);
            int requestedDataIndex = cursor.getColumnIndex(COLUMN_REQUESTED_DATA);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int formPKIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int IsImageUploadedIndex = cursor.getColumnIndex(COLUMN_IS_IMAGE_UPLOADED);
            int wpPKIdIndex = cursor.getColumnIndex(COLUMN_WP_PK_ID);
            int tmFormPKIdIndex = cursor.getColumnIndex(COLUMN_TM_FORM_PK_ID);
            int uuid = cursor.getColumnIndex(COLUMN_UUID);
            int issueTagIdIndex = cursor.getColumnIndex(COLUMN_ISSUE_TAG_ID);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            int isSubFormIndex = cursor.getColumnIndex(COLUMN_IS_SUB_FORM);
            int hasSubFormIndex = cursor.getColumnIndex(COLUMN_HAS_SUB_FORM);
            if (cursor.moveToFirst()) {
                uploadOtherData = new UploadOtherData();
                uploadOtherData.setId(cursor.getInt(pkIdIndex));
                uploadOtherData.setBaseUrl(cursor.getString(baseUrlIndex));
                uploadOtherData.setDataType(cursor.getString(dataTypeIndex));
                uploadOtherData.setData(cursor.getString(dataIndex));
                uploadOtherData.setCreatedAt(cursor.getLong(createdIndex));
                uploadOtherData.setWpPKId(cursor.getInt(wpPKIdIndex));
                uploadOtherData.setUpdatedAt(cursor.getLong(updatedIndex));
                uploadOtherData.setRequestedData(cursor.getString(requestedDataIndex));
                uploadOtherData.setFormPKId(cursor.getInt(formPKIdIndex));
                uploadOtherData.setTitle(cursor.getString(titleIndex));
                uploadOtherData.setImageUploaded(cursor.getInt(IsImageUploadedIndex) == 1);
                uploadOtherData.setTmFormPKId(cursor.getInt(tmFormPKIdIndex));
                uploadOtherData.setUuid(cursor.getString(uuid));
                uploadOtherData.setIssueTagId(cursor.getInt(issueTagIdIndex));
                uploadOtherData.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                uploadOtherData.setSubForm(cursor.getInt(isSubFormIndex) > 0);
                uploadOtherData.setHasSubForm(cursor.getInt(hasSubFormIndex) > 0);
                cursor.moveToNext();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return uploadOtherData;

    }


    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    public void updateProcessFlag(int pkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IS_UPLOAD_PROCESS_START, 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_DATA_PK_ID + "=? ", new String[]{String.valueOf(pkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateProcessTimeAndFlag(int pkId, long updateTime) {
        try {
            if (updateTime > 0) {
                ContentValues initialValues = new ContentValues();
                initialValues.put(COLUMN_UPDATED, updateTime);
                writableDb.update(TABLE_NAME, initialValues, COLUMN_DATA_PK_ID + "=? ", new String[]{String.valueOf(pkId)});
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    public void updateRequestedIssueData(int formPKId, int tagId, String requestedData, boolean isTMForm) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_REQUESTED_DATA, requestedData);
            if (isTMForm)
                writableDb.update(TABLE_NAME, initialValues, COLUMN_TM_FORM_PK_ID + "=?  AND " + COLUMN_ISSUE_TAG_ID + "=? ", new String[]{String.valueOf(formPKId), String.valueOf(tagId)});
            else
                writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=?  AND " + COLUMN_ISSUE_TAG_ID + "=? ", new String[]{String.valueOf(formPKId), String.valueOf(tagId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    /**
     * Method to insert data in table
     */
    public void updateImageStatus(int uploadId, boolean isImageUploaded) {
        try {
            writableDb.beginTransactionNonExclusive();

            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IS_IMAGE_UPLOADED, isImageUploaded ? 1 : 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_DATA_PK_ID + "=? ", new String[]{String.valueOf(uploadId)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb, null);

            writableDb.endTransaction();
        }
    }

    /**
     * Method to insert data in table
     */
    public void updateImageStatusByFormPKID(int formPkID, int tagId, boolean isImageUploaded, boolean isTMForm) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IS_IMAGE_UPLOADED, isImageUploaded ? 1 : 0);
            if (isTMForm)
                writableDb.update(TABLE_NAME, initialValues, COLUMN_TM_FORM_PK_ID + "=?  AND " + COLUMN_ISSUE_TAG_ID + "=? ", new String[]{String.valueOf(formPkID), String.valueOf(tagId)});
            else
                writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=?  AND " + COLUMN_ISSUE_TAG_ID + "=? ", new String[]{String.valueOf(formPkID), String.valueOf(tagId)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb, null);

            writableDb.endTransaction();
        }
    }

    /**
     * Method to get next upload data
     */
    public long getLastDataUpdateTime() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_IMAGE_UPLOADED + "='1'" + " ORDER BY " + " " + COLUMN_UPDATED + " DESC LIMIT 1 ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        long updateTime = 0;
        try {
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                updateTime = cursor.getLong(updatedIndex);
                cursor.moveToNext();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return updateTime;
    }

    /**
     * method to delete single entry from table.
     */
    public void deleteDataAfterUploading(int id) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_DATA_PK_ID + "=? ", new String[]{String.valueOf(id)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public boolean isSubFormDataExistForNormalForm(List<Integer> lstIds) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + " IN (" + DBUtils.toCommaSeparatedString(lstIds) + ")";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        int formTmPKIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
        return isSubFormExist(cursor, formTmPKIdIndex);
    }

    public boolean isSubFormDataExistForTmForm(List<Integer> lstIds) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_TM_FORM_PK_ID + " IN (" + DBUtils.toCommaSeparatedString(lstIds) + ")";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        int formTmPKIdIndex = cursor.getColumnIndex(COLUMN_TM_FORM_PK_ID);
        return isSubFormExist(cursor, formTmPKIdIndex);
    }


    private boolean isSubFormExist(Cursor cursor, int pKIdIndex) {
        List<Integer> lstPkIds = new ArrayList<>();
        int formPkID;
        try {
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    formPkID = cursor.getInt(pKIdIndex);
                    lstPkIds.add(formPkID);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            cursor.close();
        }
        return !lstPkIds.isEmpty();
    }


}
