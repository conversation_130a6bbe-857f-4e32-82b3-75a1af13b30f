package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;
import static com.sitefotos.util.DBUtils.toCommaSeparatedString;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.CheckInMap;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;

public class TblCheckInMap {

    public static final String TABLE_NAME = "tblCheckinMap";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_SITE_ID = "siteId";
    private static final String COLUMN_ROUTE_ID = "routeId";
    private static final String COLUMN_FORM_PK_ID = "formPkId";
    private static final String COLUMN_FORM_ID = "formId";
    private static final String COLUMN_SITE_NAME = "siteName";
    private static final String COLUMN_FORM_NAME = "formName";
    private static final String COLUMN_CREW_IDS = "crewIds";
    private static final String COLUMN_BREAK_CREW_IDS = "breakCrewIds";
    private static final String COLUMN_FORM_VALIDATED = "formValidate";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_FORM_SUBMISSION_ID = "formSubmissionID";

    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDB;


    public TblCheckInMap(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }

        if (readableDB == null) {
            readableDB = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_ROUTE_ID + "` INTEGER , " +
                " `" + COLUMN_SITE_ID + "` INTEGER , " +
                " `" + COLUMN_FORM_ID + "` INTEGER, " +
                " `" + COLUMN_FORM_PK_ID + "` INTEGER, " +
                " `" + COLUMN_CREW_IDS + "` TEXT, " +
                " `" + COLUMN_BREAK_CREW_IDS + "` TEXT, " +
                " `" + COLUMN_SITE_NAME + "` TEXT, " +
                " `" + COLUMN_FORM_NAME + "` TEXT, " +
                " `" + COLUMN_FORM_VALIDATED + "` INTEGER, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_FORM_SUBMISSION_ID + "` TEXT, " +
                " `" + COLUMN_UPDATED + "` INTEGER " + " ) ";
    }

    public static String createIndexes() {
        return "CREATE INDEX IF NOT EXISTS `idx_checkin_map` ON `" + TABLE_NAME + "` (`" + COLUMN_ROUTE_ID + "`,`" + COLUMN_SITE_ID + "`)";
    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDB.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableFormSubmissionId() {
        if (checkIsColumnExist(COLUMN_FORM_SUBMISSION_ID) == -1) {
            writableDb.execSQL(alterTableFormSubmissionId());
        }
    }

    private static String alterTableFormSubmissionId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FORM_SUBMISSION_ID + "` TEXT ";
    }

    public void insertSingleData(CheckInMap checkInMap) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_ROUTE_ID, checkInMap.getRouteId());
            initialValues.put(COLUMN_SITE_ID, checkInMap.getSiteId());
            initialValues.put(COLUMN_FORM_ID, checkInMap.getFormId());
            initialValues.put(COLUMN_FORM_PK_ID, checkInMap.getFormPkId());
            initialValues.put(COLUMN_SITE_NAME, checkInMap.getSiteName());
            initialValues.put(COLUMN_FORM_NAME, checkInMap.getFormName());
            initialValues.put(COLUMN_CREATED, checkInMap.getCreatedDate());
            initialValues.put(COLUMN_CREW_IDS, StaticUtils.getStringFromIntegerList(checkInMap.getCrewIds()));
            initialValues.put(COLUMN_BREAK_CREW_IDS, StaticUtils.getStringFromIntegerList(checkInMap.getBreakCrewIds()));
            initialValues.put(COLUMN_UPDATED, checkInMap.getUpdatedDate());
            initialValues.put(COLUMN_FORM_VALIDATED, checkInMap.getUpdatedDate());
            initialValues.put(COLUMN_FORM_SUBMISSION_ID,checkInMap.getFormSubmissionId());
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }

    public List<CheckInMap> getAllData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME;
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        return getAllDataFromDb(cursor);
    }

    private List<CheckInMap> getAllDataFromDb(Cursor cursor) {
        ArrayList<CheckInMap> lstData = new ArrayList<>();
        try {
            int idIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int routeIdIdIndex = cursor.getColumnIndex(COLUMN_ROUTE_ID);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int formPkIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int formIdIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            int crewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            int breakCrewIdsIndex = cursor.getColumnIndex(COLUMN_BREAK_CREW_IDS);
            int siteNameIndex = cursor.getColumnIndex(COLUMN_SITE_NAME);
            int formNameIndex = cursor.getColumnIndex(COLUMN_FORM_NAME);
            int formValidateIndex = cursor.getColumnIndex(COLUMN_FORM_VALIDATED);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    CheckInMap data = new CheckInMap();
                    data.setPkId(cursor.getInt(idIndex));
                    data.setRouteId(cursor.getInt(routeIdIdIndex));
                    data.setSiteId(cursor.getInt(siteIdIndex));
                    data.setFormId(cursor.getInt(formIdIndex));
                    data.setFormPkId(cursor.getInt(formPkIdIndex));
                    data.setCrewIds(DBUtils.getIdFromString(cursor.getString(crewIdsIndex)));
                    data.setBreakCrewIds(DBUtils.getIdFromString(cursor.getString(breakCrewIdsIndex)));
                    data.setSiteName(cursor.getString(siteNameIndex));
                    data.setFormName(cursor.getString(formNameIndex));
                    data.setFormValidate(cursor.getInt(formValidateIndex) != 0);
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                    lstData.add(data);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstData;
    }

    public CheckInMap getDataByFormId(long formPkId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + "='" + formPkId + "'";
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    public CheckInMap getDataByRouteId(long id) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_ROUTE_ID + "='" + id + "'";
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    public CheckInMap getDataByCrewId(int crewId) {
        //select * from tblCheckinMap where (',' || crewIds || ',') LIKE '%,1042,%'
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + "(',' " + " || " + COLUMN_CREW_IDS + " || " + " ',') LIKE '%," + crewId + ",%'";
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }


    public CheckInMap getBreakDataByCrewId(int crewId) {
        //select * from tblCheckinMap where (',' || crewIds || ',') LIKE '%,1042,%'
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + "(',' " + " || " + COLUMN_BREAK_CREW_IDS + " || " + " ',') LIKE '%," + crewId + ",%'";
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private CheckInMap getSingleDataFromDB(Cursor cursor) {
        CheckInMap data = new CheckInMap();
        try {
            int idIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int formPkIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int routeIdIdIndex = cursor.getColumnIndex(COLUMN_ROUTE_ID);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int formIdIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            int crewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            int breakCrewIdsIndex = cursor.getColumnIndex(COLUMN_BREAK_CREW_IDS);
            int siteNameIndex = cursor.getColumnIndex(COLUMN_SITE_NAME);
            int formNameIndex = cursor.getColumnIndex(COLUMN_FORM_NAME);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int formValidateIndex = cursor.getColumnIndex(COLUMN_FORM_VALIDATED);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(idIndex));
                    data.setRouteId(cursor.getInt(routeIdIdIndex));
                    data.setFormPkId(cursor.getInt(formPkIdIndex));
                    data.setSiteId(cursor.getInt(siteIdIndex));
                    data.setFormId(cursor.getInt(formIdIndex));
                    data.setCrewIds(DBUtils.getIdFromString(cursor.getString(crewIdsIndex)));
                    data.setBreakCrewIds(DBUtils.getIdFromString(cursor.getString(breakCrewIdsIndex)));
                    data.setSiteName(cursor.getString(siteNameIndex));
                    data.setFormName(cursor.getString(formNameIndex));
                    data.setFormValidate(cursor.getInt(formValidateIndex) != 0);
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return data;

    }


    public void updateFormValidation(long formPkId, boolean validated) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_FORM_VALIDATED, validated ? 1 : 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    public void updateCrewInForm(long formPkId, List<Integer> lstSelectedCrew) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CREW_IDS, StaticUtils.getStringFromIntegerList(lstSelectedCrew));
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void updateBreakCrewInForm(long pkId, List<Integer> lstUpdatedCrew, List<Integer> lstBreakCrews) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CREW_IDS, StaticUtils.getStringFromIntegerList(lstUpdatedCrew));
            initialValues.put(COLUMN_BREAK_CREW_IDS, StaticUtils.getStringFromIntegerList(lstBreakCrews));
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=? ", new String[]{String.valueOf(pkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * method to delete single entry table.
     */
    public void deleteDataByPkId(long pkId) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_PK_ID + "=? ", new String[]{String.valueOf(pkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * method to delete single entry from table by form pk id
     */
    public void deleteDataById(long formPkId) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Delete all old data from the table
     */
    public synchronized void deleteOldData(List<Integer> lstSiteIds) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + " NOT IN (" + toCommaSeparatedString(lstSiteIds) + ")");
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }


    /**
     * Delete all deleted form data from the table
     */
    public synchronized void deleteDataByFormAndSiteId(int formId, int siteId) {
        try {
            writableDb.beginTransactionNonExclusive();
            String query = "DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_ID + "=?  AND " + COLUMN_SITE_ID + "=? ";
            writableDb.execSQL(query, new String[]{String.valueOf(formId), String.valueOf(siteId)});
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDB, query);
    }

    public List<Long> getAllSiteIds() {
        String rawQuery = "SELECT DISTINCT " + COLUMN_SITE_ID + " FROM " + TABLE_NAME;
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        List<Long> lstCheckedSite = new ArrayList<>();
        try {
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstCheckedSite.add(cursor.getLong(siteIdIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstCheckedSite;
    }

    public List<Integer> getAllCrewIdsInList() {
        List<List<Integer>> lstOccupiedCrew = getAllCrewIds();
        List<Integer> lstCrewIds = new ArrayList<>();
        for (List<Integer> lstData : lstOccupiedCrew) {
            lstCrewIds.addAll(lstData);
        }
        return lstCrewIds;
    }

    public List<List<Integer>> getAllCrewIds() {
        String rawQuery = "SELECT DISTINCT " + COLUMN_CREW_IDS + " FROM " + TABLE_NAME;
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        List<List<Integer>> lstCheckedSite = new ArrayList<>();
        try {
            int crewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    List<Integer> crewData = DBUtils.getIdFromString(cursor.getString(crewIdsIndex));
                    if (!crewData.isEmpty()) {
                        lstCheckedSite.add(crewData);
                    }
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstCheckedSite;
    }


    public List<List<Integer>> getCrewsOfForm(long siteId, long formId) {
        String rawQuery = "SELECT " + COLUMN_CREW_IDS + " FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='" + siteId + "'" + " AND " + COLUMN_FORM_ID + "='" + formId + "'";
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        List<List<Integer>> lstCheckedSite = new ArrayList<>();
        try {
            int crewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    List<Integer> crewData = DBUtils.getIdFromString(cursor.getString(crewIdsIndex));
                    if (!crewData.isEmpty()) {
                        lstCheckedSite.add(crewData);
                    }
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstCheckedSite;
    }


    public List<Long> getAllFormsBySiteIds(long siteId) {
        String rawQuery = "SELECT DISTINCT " + COLUMN_FORM_ID + " FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='" + siteId + "'";
        ;
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
        List<Long> lstCheckedForm = new ArrayList<>();
        try {
            int formIdIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstCheckedForm.add(cursor.getLong(formIdIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstCheckedForm;
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDB.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    /**
     * Method to get FormPK id by Site Ids
     * @param lstIds ArrayList<String>
     * @return ArrayList<String>
     */
    public List<String> getTmFormPkIdBySiteIds(List<String> lstIds) {
        String rawQuery = "SELECT " + COLUMN_FORM_PK_ID + " FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + " IN (" + DBUtils.toCommaSeparated(lstIds) + ")";
        Cursor cursor = readableDB.rawQuery(rawQuery, null);
       ArrayList<String> lstPKIds = new ArrayList<>();
        try {
            int pkIdsIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstPKIds.add(cursor.getString(pkIdsIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            cursor.close();
        }
        return lstPKIds;
    }
}
