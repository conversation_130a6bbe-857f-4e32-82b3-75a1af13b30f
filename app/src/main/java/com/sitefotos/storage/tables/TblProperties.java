package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dk on 8/1/18.
 */

public class TblProperties {

    public static final String TABLE_NAME = "tblProperties";
    private static final String COLUMN_ID = "p_id";
    private static final String COLUMN_NAME = "P_nickname";
    private static final String COLUMN_ADDRESS1 = "p_address1";
    private static final String COLUMN_ZIP_CODE = "P_zip_code";
    private static final String COLUMN_GEO = "p_geo";
    private static final String COLUMN_CITYNAME = "P_cityname";
    private static final String COLUMN_STATENAME = "P_statename";
    private static final String COLUMN_UPDATE_STATUS = "P_update_status";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblProperties(Context context) {

        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }

        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }

        this.context = context;
    }

    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_ID + "` integer PRIMARY KEY NOT NULL UNIQUE, " +
                " `" + COLUMN_NAME + "` TEXT, " +
                " `" + COLUMN_ADDRESS1 + "` TEXT, " +
                " `" + COLUMN_ZIP_CODE + "` TEXT, " +
                " `" + COLUMN_GEO + "` TEXT, " +
                " `" + COLUMN_CITYNAME + "` TEXT, " +
                " `" + COLUMN_STATENAME + "` TEXT, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER " + " ) ";
    }


    /**
     * Method to insert or update bulk data in property table
     *
     * @param lstProperty list of property
     */
    public void insertOrUpdatePropertyBulkData(List<PropertiesVo> lstProperty) {
        //CustomLogKt.error("Property operation starts","insertOrUpdatePropertyBulkData start");
        try {
            writableDb.beginTransactionNonExclusive();
            for (PropertiesVo property : lstProperty) {
                insertOrUpdateSingleData(property);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }

    }

    /**
     * Method to insert  bulk data in property table
     *
     * @param lstProperty list of property
     */
    public void insertPropertyBulkData(List<PropertiesVo> lstProperty) {
        //CustomLogKt.error("Property operation", "insertOrUpdatePropertyBulkData start");
        try {
            writableDb.beginTransactionNonExclusive();
            for (PropertiesVo property : lstProperty) {
                insertSingleData(property);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }


    private void insertSingleData(PropertiesVo property) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_ID, property.getPropertyId());
            initialValues.put(COLUMN_NAME, property.getPropertyName());
            initialValues.put(COLUMN_ADDRESS1, property.getPropertyAddress());
            initialValues.put(COLUMN_ZIP_CODE, property.getPropertyZip());
            initialValues.put(COLUMN_GEO, property.getPropertyGeo());
            initialValues.put(COLUMN_CITYNAME, property.getPropertyCity());
            initialValues.put(COLUMN_STATENAME, property.getPropertyState());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_IGNORE);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void insertOrUpdateSingleData(PropertiesVo property) {

        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_ID, property.getPropertyId());
            initialValues.put(COLUMN_NAME, property.getPropertyName());
            initialValues.put(COLUMN_ADDRESS1, property.getPropertyAddress());
            initialValues.put(COLUMN_ZIP_CODE, property.getPropertyZip());
            initialValues.put(COLUMN_GEO, property.getPropertyGeo());
            initialValues.put(COLUMN_CITYNAME, property.getPropertyCity());
            initialValues.put(COLUMN_STATENAME, property.getPropertyState());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_ID + "=?",
                    new String[]{String.valueOf(property.getPropertyId())});
            if (id == 0) {
                //CustomLogKt.error("Insert PropertiesVo","InsertPropertiesVo");
                writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_IGNORE);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Method to insert or update bulk data in property table
     */
    public void insertProperty(PropertiesVo property) {
        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_ID, property.getPropertyId());
            initialValues.put(COLUMN_NAME, property.getPropertyName());
            initialValues.put(COLUMN_ADDRESS1, property.getPropertyAddress());
            initialValues.put(COLUMN_ZIP_CODE, property.getPropertyZip());
            initialValues.put(COLUMN_GEO, property.getPropertyGeo());
            initialValues.put(COLUMN_CITYNAME, property.getPropertyCity());
            initialValues.put(COLUMN_STATENAME, property.getPropertyState());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_IGNORE);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }

    public void updatePropertyStatus() {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPDATE_STATUS + "=?", new String[]{String.valueOf(1)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }


    /**
     * Method to get all property data
     *
     * @return list of properties
     */
    public ArrayList<PropertiesVo> getAllProperties() {
        Cursor cursor = null;
        ArrayList<PropertiesVo> listProperties = new ArrayList<>();
        //writableDb.beginTransactionNonExclusive();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, new String[]{});

            int idIndex = cursor.getColumnIndex(COLUMN_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_NAME);
            int addressIndex = cursor.getColumnIndex(COLUMN_ADDRESS1);
            int zipCodeIndex = cursor.getColumnIndex(COLUMN_ZIP_CODE);
            int geoIndex = cursor.getColumnIndex(COLUMN_GEO);
            int cityNameIndex = cursor.getColumnIndex(COLUMN_CITYNAME);
            int stateNameIndex = cursor.getColumnIndex(COLUMN_STATENAME);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    PropertiesVo property = new PropertiesVo();
                    property.setPropertyId(cursor.getInt(idIndex));
                    property.setPropertyName(cursor.getString(nameIndex));
                    property.setPropertyAddress(cursor.getString(addressIndex));
                    property.setPropertyZip(cursor.getString(zipCodeIndex));
                    property.setPropertyGeo(cursor.getString(geoIndex));
                    property.setPropertyCity(cursor.getString(cityNameIndex));
                    property.setPropertyState(cursor.getString(stateNameIndex));
                    listProperties.add(property);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            // writableDb.setTransactionSuccessful();
            // writableDb.endTransaction();
        }
        //CustomLogKt.error("property Size", "Size:::"+listProperties.size());
        return listProperties;
    }


    /**
     * Method to get all property data
     *
     * @return list of properties
     */
    public ArrayList<PropertiesVo> getBulkData(Cursor cursor) {
        ArrayList<PropertiesVo> listProperties = new ArrayList<>();
        try {
            int idIndex = cursor.getColumnIndex(COLUMN_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_NAME);
            int addressIndex = cursor.getColumnIndex(COLUMN_ADDRESS1);
            int zipCodeIndex = cursor.getColumnIndex(COLUMN_ZIP_CODE);
            int geoIndex = cursor.getColumnIndex(COLUMN_GEO);
            int cityNameIndex = cursor.getColumnIndex(COLUMN_CITYNAME);
            int stateNameIndex = cursor.getColumnIndex(COLUMN_STATENAME);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    PropertiesVo property = new PropertiesVo();
                    property.setPropertyId(cursor.getInt(idIndex));
                    property.setPropertyName(cursor.getString(nameIndex));
                    property.setPropertyAddress(cursor.getString(addressIndex));
                    property.setPropertyZip(cursor.getString(zipCodeIndex));
                    property.setPropertyGeo(cursor.getString(geoIndex));
                    property.setPropertyCity(cursor.getString(cityNameIndex));
                    property.setPropertyState(cursor.getString(stateNameIndex));
                    listProperties.add(property);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            // writableDb.setTransactionSuccessful();
            // writableDb.endTransaction();
        }
        //CustomLogKt.error("property Size", "Size:::"+listProperties.size());
        return listProperties;
    }


    /**
     * Method to get all property data for IDs
     *
     * @return ArrayList<PropertiesVo> lstPropertyId
     */
    public ArrayList<PropertiesVo> getPropertyDataByIds(List<String> lstPropertyId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_ID + " IN (" + DBUtils.toCommaSeparated(lstPropertyId) + ")";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getBulkData(cursor);
    }


    /**
     * Method to get all property data
     *
     * @return PropertiesVo PropertiesVo
     */
    public PropertiesVo getPropertyDataFromId(int propertyId) {

        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_ID + "='" + propertyId + "'";
            //writableDb.beginTransactionNonExclusive();
            Cursor cursor = readableDb.rawQuery(rawQuery, null);
            return getSingleDataFromDB(cursor);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        return new PropertiesVo();
    }


    private PropertiesVo getSingleDataFromDB(Cursor cursor) {
        PropertiesVo propertiesVo = null;
        try {
            propertiesVo = new PropertiesVo();
            int idIndex = cursor.getColumnIndex(COLUMN_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_NAME);
            int addressIndex = cursor.getColumnIndex(COLUMN_ADDRESS1);
            int zipCodeIndex = cursor.getColumnIndex(COLUMN_ZIP_CODE);
            int geoIndex = cursor.getColumnIndex(COLUMN_GEO);
            int cityNameIndex = cursor.getColumnIndex(COLUMN_CITYNAME);
            int stateNameIndex = cursor.getColumnIndex(COLUMN_STATENAME);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    propertiesVo.setPropertyId(cursor.getInt(idIndex));
                    propertiesVo.setPropertyName(cursor.getString(nameIndex));
                    propertiesVo.setPropertyAddress(cursor.getString(addressIndex));
                    propertiesVo.setPropertyZip(cursor.getString(zipCodeIndex));
                    propertiesVo.setPropertyGeo(cursor.getString(geoIndex));
                    propertiesVo.setPropertyCity(cursor.getString(cityNameIndex));
                    propertiesVo.setPropertyState(cursor.getString(stateNameIndex));
                    cursor.moveToNext();
                }
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }
        return propertiesVo;

    }


    /**
     * Delete all data from property table
     */
    public void deleteAllDataFromPropertyTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
            writableDb.endTransaction();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        }
    }


    /**
     * Delete all old data from property table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=' 0 '", null);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }

    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(writableDb, query);
    }

}
