package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.Cluster;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;


public class TblCluster {

    public static final String TABLE_NAME = "tblCluster";
    private static final String COLUMN_VENDOR_ID = "vendor_id";
    private static final String COLUMN_CLUSTER_ID = "cluster_id";
    private static final String COLUMN_CENTRO_ID = "centroId";
    private static final String COLUMN_BUILDINGS = "cluster_building";
    private static final String COLUMN_NUMBUILDINGS = "sgc_numbuildings";
    private static final String COLUMN_UPDATE_STATUS = "c_update_status";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblCluster(Context context) {
        this.context = context;

        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }

    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_VENDOR_ID + "` INTEGER, " +
                " `" + COLUMN_CLUSTER_ID + "` INTEGER PRIMARY KEY NOT NULL UNIQUE, " +
                " `" + COLUMN_CENTRO_ID + "` TEXT, " +
                " `" + COLUMN_BUILDINGS + "` TEXT, " +
                " `" + COLUMN_NUMBUILDINGS + "` TEXT, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER " + " ) ";
    }

    /**
     * Method to insert or update bulk data in cluster table
     *
     * @param lstCluster list of cluster
     */
    public void insertOrUpdateClusterBulkData(List<Cluster> lstCluster) {

        ContentValues initialValues = new ContentValues();
        try {
            for (Cluster cluster : lstCluster) {
                initialValues.put(COLUMN_VENDOR_ID, cluster.getVendorId());
                initialValues.put(COLUMN_CLUSTER_ID, cluster.getSgcCluster());
                initialValues.put(COLUMN_CENTRO_ID, cluster.getCentroid());
                initialValues.put(COLUMN_BUILDINGS, cluster.getBuildings());
                initialValues.put(COLUMN_NUMBUILDINGS, cluster.getNumbuildings());
                initialValues.put(COLUMN_UPDATE_STATUS, 1);
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_CLUSTER_ID + "=?",
                        new String[]{String.valueOf(cluster.getSgcCluster())});
                if (id == 0) {
                    // CustomLogKt.error("cluster Insert Dat","InsertData");
                    writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_IGNORE);
                }

            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    /**
     * Method to insert single data in  cluster table
     */
    public void insertClusterData(Cluster cluster) {

        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_VENDOR_ID, cluster.getVendorId());
            initialValues.put(COLUMN_CLUSTER_ID, cluster.getSgcCluster());
            initialValues.put(COLUMN_CENTRO_ID, cluster.getCentroid());
            initialValues.put(COLUMN_BUILDINGS, cluster.getBuildings());
            initialValues.put(COLUMN_NUMBUILDINGS, cluster.getNumbuildings());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_IGNORE);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    public void updateClusterStatus(int updateStatus) {

        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, updateStatus);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPDATE_STATUS + "=?", new String[]{String.valueOf(1)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb,  null);

            writableDb.endTransaction();
        }
    }

    /**
     * Method to update cluster
     * @param cluster updated cluster
     */
    public void updateClusterById(Cluster cluster) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_BUILDINGS, cluster.getBuildings());
            writableDb.update(TABLE_NAME, initialValues, COLUMN_CLUSTER_ID + "=?", new String[]{String.valueOf(cluster.getSgcCluster())});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Method to get all property data
     *
     * @return list of cluster
     */
    public ArrayList<Cluster> getAllClusterData() {
        Cursor cursor = null;
        //writableDb.beginTransactionNonExclusive();
        ArrayList<Cluster> listCluster = new ArrayList<>();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, new String[]{});

            int idIndex = cursor.getColumnIndex(COLUMN_VENDOR_ID);
            int clusterIdIndex = cursor.getColumnIndex(COLUMN_CLUSTER_ID);
            int centroIdIndex = cursor.getColumnIndex(COLUMN_CENTRO_ID);
            int buildingIndex = cursor.getColumnIndex(COLUMN_BUILDINGS);
            int numBuildingIndex = cursor.getColumnIndex(COLUMN_NUMBUILDINGS);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    Cluster cluster = new Cluster();
                    cluster.setVendorId(String.valueOf(cursor.getInt(idIndex)));
                    cluster.setSgcCluster(cursor.getString(clusterIdIndex));
                    cluster.setCentroid(cursor.getString(centroIdIndex));
                    cluster.setSgcBuildings(cursor.getString(buildingIndex));
                    cluster.setNumbuildings(cursor.getString(numBuildingIndex));
                    listCluster.add(cluster);
                    cursor.moveToNext();
                }
            }
        } finally {
            //closeResource(writableDb,  cursor);

            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
            //CustomLogKt.error("Cluster Size", "Size:::"+listCluster.size());
        }
        return listCluster;
    }


    /**
     * Delete all data from cluster table
     */
    public void deleteAllDataFromClusterTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb,  null);

            writableDb.endTransaction();
        }
    }


    /**
     * Delete all old data from cluster table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=' 0 '", null);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            // closeResource(writableDb,  null);

            writableDb.endTransaction();
        }
    }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }

}
