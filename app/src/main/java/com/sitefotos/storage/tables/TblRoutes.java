package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.Routes;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;

public class TblRoutes {

    public static final String TABLE_NAME = "tblRoutes";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_ROUTE_ID = "routeId";
    private static final String COLUMN_ROUTE_NAME = "routeName";
    private static final String COLUMN_ROUTE_CATEGORY = "routeCategory";
    private static final String COLUMN_SITE_LIST = "siteList";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_ORDERED = "Ordered";
    private static final String COLUMN_FILTER = "filter";
    private static final String COLUMN_INDEX = "routeIndex";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblRoutes(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_ROUTE_ID + "` INTEGER , " +
                " `" + COLUMN_ROUTE_NAME + "` TEXT, " +
                " `" + COLUMN_ROUTE_CATEGORY + "` TEXT, " +
                " `" + COLUMN_SITE_LIST + "` TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_INDEX + "` INTEGER , " +
                " `" + COLUMN_ORDERED + "` INTEGER, " +
                " `" + COLUMN_FILTER + "` INTEGER , " +
                " `" + COLUMN_UPDATED + "` INTEGER " + " ) ";

    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }


    public void checkAndAlterTableOrderedAndSiteData() {
        if (checkIsColumnExist(COLUMN_ORDERED) == -1) {
            writableDb.execSQL(alterTableOrdered());
        }

        if (checkIsColumnExist(COLUMN_FILTER) == -1) {
            writableDb.execSQL(alterTableAllSites());
        }
    }

    private static String alterTableOrdered() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_ORDERED + "` INTEGER ";
    }

    private static String alterTableAllSites() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_FILTER + "` INTEGER ";
    }

    /**
     * Method to insert or update bulk data in table
     */
    public void insertOrUpdateBulkData(List<Routes> lstRoutes) {
        writableDb.beginTransactionNonExclusive();
        try {
            int index = 1;
            for (Routes data : lstRoutes) {
                ContentValues initialValues = new ContentValues();
                initialValues.put(COLUMN_ROUTE_ID, data.getRouteId());
                initialValues.put(COLUMN_ROUTE_NAME, data.getRouteName());
                initialValues.put(COLUMN_ROUTE_CATEGORY, data.getRouteCategory());
                initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
                initialValues.put(COLUMN_ORDERED, data.isOrdered() ? 1 : 0);
                initialValues.put(COLUMN_FILTER, data.isFilter() ? 1 : 0);
                initialValues.put(COLUMN_SITE_LIST, toCommaSeparatedString(data.getSiteList()));
                initialValues.put(COLUMN_INDEX, index);
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_ROUTE_ID + "=?",
                        new String[]{String.valueOf(data.getRouteId())});
                if (id == 0) {
                    initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
                    writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
                }
                index++;
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }


    /**
     * Method to insert bulk data in table
     */
    public void insertBulkData(List<Routes> lstRoutes) {
        writableDb.beginTransactionNonExclusive();
        try {
            int index = 1;
            for (Routes data : lstRoutes) {
                ContentValues initialValues = new ContentValues();
                initialValues.put(COLUMN_ROUTE_ID, data.getRouteId());
                initialValues.put(COLUMN_ROUTE_NAME, data.getRouteName());
                initialValues.put(COLUMN_ROUTE_CATEGORY, data.getRouteCategory());
                initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
                initialValues.put(COLUMN_SITE_LIST, toCommaSeparatedString(data.getSiteList()));
                initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
                initialValues.put(COLUMN_ORDERED, data.isOrdered() ? 1 : 0);
                initialValues.put(COLUMN_FILTER, data.isFilter() ? 1 : 0);
                initialValues.put(COLUMN_INDEX, index);
                writableDb.insertOrThrow(TABLE_NAME, null, initialValues);

                index++;
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    private String toCommaSeparatedString(List<Long> list) {
        if (list.size() > 0) {
            StringBuilder nameBuilder = new StringBuilder();
            for (Long item : list) {
                nameBuilder.append(item).append(", ");
            }
            nameBuilder.deleteCharAt(nameBuilder.length() - 1);
            nameBuilder.deleteCharAt(nameBuilder.length() - 1);
            return nameBuilder.toString();
        } else {
            return "";
        }
    }


    public void insertSingleData(Routes data) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_ROUTE_ID, data.getRouteId());
            initialValues.put(COLUMN_ROUTE_NAME, data.getRouteName());
            initialValues.put(COLUMN_ROUTE_CATEGORY, data.getRouteCategory());
            initialValues.put(COLUMN_CREATED, data.getCreatedDate());
            initialValues.put(COLUMN_UPDATED, data.getUpdatedDate());
            initialValues.put(COLUMN_ORDERED, data.isOrdered() ? 1 : 0);
            initialValues.put(COLUMN_FILTER, data.isFilter() ? 1 : 0);
            initialValues.put(COLUMN_SITE_LIST, toCommaSeparatedString(data.getSiteList()));
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Method to get all  data
     *
     * @return list of Routes
     */
    public List<Routes> getAllData() {
        Cursor cursor = null;
        ArrayList<Routes> lstRoutes = new ArrayList<>();
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + COLUMN_INDEX;
            cursor = readableDb.rawQuery(rawQuery, null);

            int idIndex = cursor.getColumnIndex(COLUMN_ROUTE_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_ROUTE_NAME);
            int catIndex = cursor.getColumnIndex(COLUMN_ROUTE_CATEGORY);
            int siteListIndex = cursor.getColumnIndex(COLUMN_SITE_LIST);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int orderedIndex = cursor.getColumnIndex(COLUMN_ORDERED);
            int filterIndex = cursor.getColumnIndex(COLUMN_FILTER);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    Routes data = new Routes();
                    data.setRouteId(String.valueOf(cursor.getInt(idIndex)));
                    data.setRouteName(cursor.getString(nameIndex));
                    data.setRouteCategory(cursor.getString(catIndex));
                    data.setSiteList(StaticUtils.getListFromString(cursor.getString(siteListIndex)));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setOrdered(cursor.getInt(orderedIndex) == 1);
                    data.setFilter(cursor.getInt(filterIndex) == 1);
                    lstRoutes.add(data);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            //closeResource(writableDb,  cursor);
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            // writableDb.endTransaction();
        }
        return lstRoutes;
    }

    public Routes getDataById(int routeId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_ROUTE_ID + "='" + routeId + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    /**
     * Method to get routes date from its site id. We need to get routeId from list of SiteIds
     *
     * @param siteId siteId in ling
     * @return routes object
     */
    public Routes getRouteDataBySiteId(long siteId) {

        // Sqlite does not support find in set for exact search, so we used bellow trick to get desired data based on exact search
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE (',' || " + COLUMN_SITE_LIST + " || " + " ',') LIKE '%," + siteId + ",%'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private Routes getSingleDataFromDB(Cursor cursor) {
        Routes data = new Routes();
        try {
            int idIndex = cursor.getColumnIndex(COLUMN_ROUTE_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_ROUTE_NAME);
            int catIndex = cursor.getColumnIndex(COLUMN_ROUTE_CATEGORY);
            int siteListIndex = cursor.getColumnIndex(COLUMN_SITE_LIST);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int orderedIndex = cursor.getColumnIndex(COLUMN_ORDERED);
            int filterIndex = cursor.getColumnIndex(COLUMN_FILTER);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setRouteId(String.valueOf(cursor.getInt(idIndex)));
                    data.setRouteName(cursor.getString(nameIndex));
                    data.setRouteCategory(cursor.getString(catIndex));
                    data.setSiteList(StaticUtils.getListFromString(cursor.getString(siteListIndex)));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    data.setOrdered(cursor.getInt(orderedIndex) == 1);
                    data.setFilter(cursor.getInt(filterIndex) == 1);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return data;

    }


    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Delete all data from the table
     */
    public synchronized void deleteOldData(List<String> lstRouteIds) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_ROUTE_ID + " NOT IN (" + DBUtils.toCommaSeparated(lstRouteIds) + ")");
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }

}
