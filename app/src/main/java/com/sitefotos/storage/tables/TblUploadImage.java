package com.sitefotos.storage.tables;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteDatabaseLockedException;

import com.sitefotos.models.UploadImageData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;

public class TblUploadImage {

    public static final String TABLE_NAME = "tblUploadImage";
    private static final String COLUMN_UPLOADURLLOW = "uploadURLLow";
    private static final String COLUMN_UPLOAD_Id = "uploadId";
    private static final String COLUMN_UPLOADURLHIGH = "uploadURLHigh";
    private static final String COLUMN_ACCESSCODE = "accessCode";
    private static final String COLUMN_LRIMG = "lrimg";
    private static final String COLUMN_HRIMG = "hrimg";
    private static final String COLUMN_DESCRIPTION = "description";
    private static final String COLUMN_DATE = "date";
    private static final String COLUMN_BUILDING = "building";
    private static final String COLUMN_EMAIL = "email";
    private static final String COLUMN_LATITUDE = "latitude";
    private static final String COLUMN_LONGITUDE = "longitude";
    private static final String COLUMN_ISVALID = "isValid";
    private static final String COLUMN_TOTALPROGRESS = "totalProgress";
    private static final String COLUMN_IMAGEPATHLOW = "imagepathlow";
    private static final String COLUMN_IMAGEPATHHIGH = "imagepathhigh";
    //NOTE: Now used for combined failure count. if possible rename or just keep it.
    private static final String COLUMN_RETRYCOUNTIMAGELOW = "retryCountImageLow";
    //NOTE:  Just ignore this from app version 2.2.9
    private static final String COLUMN_RETRYCOUNTIMAGEHIGH = "retryCountImageHigh";
    private static final String COLUMN_IS_FORM_IMAGE = "isFormImage";
    private static final String COLUMN_IS_FORM_SIGNATURE = "isFormSignature";
    private static final String COLUMN_EXTRA_LATITUDE = "latExtra";
    private static final String COLUMN_EXTRA_LONGITUDE = "lonExtra";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_CAPTURE_TIME = "captureTime";
    private static final String COLUMN_FORM_PK_ID = "pkId";
    private static final String COLUMN_TAG_ID = "viewTagId";
    private static final String COLUMN_FORM_ID = "formId";
    private static final String COLUMN_WP_PK_ID = "wpPKId";
    private static final String COLUMN_TM_FORM_PK_ID = "tmFormPKId";
    private static final String COLUMN_WP_ID = "wpId"; //Site Id
    private static final String COLUMN_IS_UPLOAD_ORIGINAL_SIZE = "isUploadOriginalSize";
    //NOTE:  Just ignore this from app version 2.2.9
    private static final String COLUMN_IS_IMAGE_UPLOAD_PROCESS_START = "isImageUploadProcessStart";
    //NOTE:  Just ignore this from app version 2.2.9
    private static final String COLUMN_IMAGE_PROCESS_START_TIME = "imageProcessStartTime";
    private static final String COLUMN_TAGS = "tags";
    private static final String COLUMN_UUID = "uuid";
    private static final String COLUMN_SITE_ID = "siteId";
    private static final String COLUMN_HIGH_RESOLUTION_UPLOADED = "highImageUploaded";
    private static final String COLUMN_LOW_RESOLUTION_UPLOADED = "lowImageUploaded";
    private static final String COLUMN_APP_VERSION = "appVersion";

    private static final String COLUMN_CAMERA_IMAGE = "cameraImage";

    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " " + COLUMN_UPLOAD_Id + " integer PRIMARY KEY AUTOINCREMENT, " +
                " " + COLUMN_FORM_ID + " INTEGER, " +
                " " + COLUMN_TAG_ID + " INTEGER, " +
                " " + COLUMN_FORM_PK_ID + " INTEGER, " +
                " " + COLUMN_TM_FORM_PK_ID + " INTEGER, " +
                " " + COLUMN_WP_PK_ID + " INTEGER, " +
                " " + COLUMN_WP_ID + " INTEGER, " +
                " `" + COLUMN_CAPTURE_TIME + "` INTEGER, " +
                " " + COLUMN_UPLOADURLLOW + " TEXT, " +
                " " + COLUMN_UPLOADURLHIGH + " TEXT, " +
                " " + COLUMN_ACCESSCODE + " TEXT, " +
                " " + COLUMN_LRIMG + " TEXT, " +
                " " + COLUMN_HRIMG + " TEXT, " +
                " " + COLUMN_DATE + " REAL, " +
                " " + COLUMN_DESCRIPTION + " TEXT, " +
                " " + COLUMN_BUILDING + " TEXT, " +
                " " + COLUMN_EMAIL + " TEXT, " +
                " " + COLUMN_IMAGEPATHLOW + " TEXT, " +
                " " + COLUMN_IMAGEPATHHIGH + " TEXT, " +
                " " + COLUMN_LATITUDE + " REAL, " +
                " " + COLUMN_LONGITUDE + " REAL, " +
                " " + COLUMN_EXTRA_LATITUDE + " REAL, " +
                " " + COLUMN_EXTRA_LONGITUDE + " REAL, " +
                " " + COLUMN_ISVALID + " INTEGER, " +
                " " + COLUMN_IS_FORM_IMAGE + " INTEGER, " +
                " " + COLUMN_IS_FORM_SIGNATURE + " INTEGER, " +
                " " + COLUMN_IS_UPLOAD_ORIGINAL_SIZE + " INTEGER, " +
                " " + COLUMN_RETRYCOUNTIMAGELOW + " INTEGER, " +
                " " + COLUMN_RETRYCOUNTIMAGEHIGH + " INTEGER, " +
                " `" + COLUMN_IS_IMAGE_UPLOAD_PROCESS_START + "` INTEGER, " +
                " `" + COLUMN_IMAGE_PROCESS_START_TIME + "` TEXT, " +
                " `" + COLUMN_UUID + "` TEXT, " +
                " " + COLUMN_TOTALPROGRESS + " REAL, " +
                " " + COLUMN_HIGH_RESOLUTION_UPLOADED + " INTEGER, " +
                " " + COLUMN_LOW_RESOLUTION_UPLOADED + " INTEGER, " +
                " " + COLUMN_CREATED + " REAL, " +
                " " + COLUMN_TAGS + " TEXT, " +
                " " + COLUMN_APP_VERSION + " TEXT, " +
                " " + COLUMN_CAMERA_IMAGE + " INTEGER DEFAULT 0, " +
                " " + COLUMN_UPDATED + " REAL " + " ) ";
    }


    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblUploadImage(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }

        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    private static String alterTableUUID() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_UUID + "` TEXT ";
    }

    private static String alterTableWpId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_WP_ID + "` INTEGER ";
    }

    private static String alterTableTagId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_TAG_ID + "` INTEGER ";
    }

    private static String alterTableWPPkId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_WP_PK_ID + "` INTEGER ";
    }

    private static String alterTableTmFormPkId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_TM_FORM_PK_ID + "` INTEGER ";
    }

    private static String alterTableCaptureTime() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_CAPTURE_TIME + "` INTEGER ";
    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }


    public void checkAndAlterTableTagId() {
        if (checkIsColumnExist(COLUMN_TAG_ID) == -1) {
            writableDb.execSQL(alterTableTagId());
        }
    }

    public void checkAndAlterTableUUID() {
        if (checkIsColumnExist(COLUMN_WP_ID) == -1) {
            writableDb.execSQL(alterTableWpId());
        }
    }

    public void checkAndAlterTableWpId() {
        if (checkIsColumnExist(COLUMN_UUID) == -1) {
            writableDb.execSQL(alterTableUUID());
        }
    }

    public void checkAndAlterTableWPPkId() {
        if (checkIsColumnExist(COLUMN_WP_PK_ID) == -1) {
            writableDb.execSQL(alterTableWPPkId());
        }
    }

    public void checkAndAlterTableTmFormPkId() {
        if (checkIsColumnExist(COLUMN_TM_FORM_PK_ID) == -1) {
            writableDb.execSQL(alterTableTmFormPkId());
        }
    }

    public void checkAndAlterTableCaptureTime() {
        if (checkIsColumnExist(COLUMN_CAPTURE_TIME) == -1) {
            writableDb.execSQL(alterTableCaptureTime());
        }
    }

    public void checkAndAlterTableTags() {
        if (checkIsColumnExist(COLUMN_TAGS) == -1) {
            writableDb.execSQL(alterTableTags());
        }
    }

    private static String alterTableTags() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_TAGS + "` TEXT ";
    }

    public void checkAndAlterTableHighImageUploaded() {
        if (checkIsColumnExist(COLUMN_HIGH_RESOLUTION_UPLOADED) == -1) {
            writableDb.execSQL(alterTableHighImageUploaded());
        }

        if (checkIsColumnExist(COLUMN_LOW_RESOLUTION_UPLOADED) == -1) {
            writableDb.execSQL(alterTableLowImageUploaded());
        }
    }

    public void checkAndAlterTableAppVersion() {
        if (checkIsColumnExist(COLUMN_APP_VERSION) == -1) {
            writableDb.execSQL(alterTableAppVersion());
        }
    }

    private static String alterTableAppVersion() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_APP_VERSION + "` TEXT ";
    }

    private static String alterTableHighImageUploaded() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_HIGH_RESOLUTION_UPLOADED + "` INTEGER ";
    }

    private static String alterTableLowImageUploaded() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_LOW_RESOLUTION_UPLOADED + "` INTEGER ";
    }

    private static String alterTableCameraImage() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_CAMERA_IMAGE + "` INTEGER ";
    }


    public void checkAndAlterTableCameraImage() {
        if (checkIsColumnExist(COLUMN_CAMERA_IMAGE) == -1) {
            writableDb.execSQL(alterTableCameraImage());
        }
    }

    /**
     * Method to insert data in table
     *
     * @param uploadImageData UploadImageData
     */
    public boolean insertData(UploadImageData uploadImageData) {
        boolean dataInserted = false;
        try {
            uploadImageData.setCreatedDate(System.currentTimeMillis());
            uploadImageData.setUpdatedDate(System.currentTimeMillis());
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = getContentValues(uploadImageData);
            long rawInserted = writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            dataInserted = rawInserted != -1;
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            dataInserted = false;
        } finally {
            writableDb.endTransaction();
            return dataInserted;
        }
    }


    public void updateRetryCount(int uploadId, int retryCountLow) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_RETRYCOUNTIMAGELOW, retryCountLow);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + " =? ", new String[]{String.valueOf(uploadId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void updateData(int uploadId, UploadImageData uploadImageData) {
        try {
            ContentValues initialValues = getContentValues(uploadImageData);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + " =? ", new String[]{String.valueOf(uploadId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private ContentValues getContentValues(UploadImageData uploadImageData) {
        ContentValues initialValues = new ContentValues();

        initialValues.put(COLUMN_FORM_PK_ID, uploadImageData.getFormPkId());
        initialValues.put(COLUMN_FORM_ID, uploadImageData.getFormId());
        initialValues.put(COLUMN_WP_PK_ID, uploadImageData.getWpPkId());
        initialValues.put(COLUMN_WP_ID, uploadImageData.getWpId());
        initialValues.put(COLUMN_CREATED, uploadImageData.getCreatedDate());
        initialValues.put(COLUMN_UPDATED, uploadImageData.getUpdatedDate());
        initialValues.put(COLUMN_LRIMG, uploadImageData.getLrImage());
        initialValues.put(COLUMN_IS_UPLOAD_ORIGINAL_SIZE, uploadImageData.isUploadOriginalSize());
        initialValues.put(COLUMN_HRIMG, uploadImageData.getHrImage());
        initialValues.put(COLUMN_DATE, uploadImageData.getDate());
        initialValues.put(COLUMN_DESCRIPTION, uploadImageData.getDescription());
        initialValues.put(COLUMN_ACCESSCODE, uploadImageData.getAccessCode());
        initialValues.put(COLUMN_BUILDING, uploadImageData.getBuilding());
        initialValues.put(COLUMN_EMAIL, uploadImageData.getEmail());
        initialValues.put(COLUMN_LATITUDE, uploadImageData.getLatitude());
        initialValues.put(COLUMN_LONGITUDE, uploadImageData.getLongitude());
        initialValues.put(COLUMN_EXTRA_LATITUDE, uploadImageData.getExtraLatitude());
        initialValues.put(COLUMN_EXTRA_LONGITUDE, uploadImageData.getExtraLongitude());
        initialValues.put(COLUMN_RETRYCOUNTIMAGELOW, uploadImageData.getRetryCount());
        initialValues.put(COLUMN_IS_FORM_IMAGE, uploadImageData.isFormImage());
        initialValues.put(COLUMN_IS_FORM_SIGNATURE, uploadImageData.isFormSign());
        initialValues.put(COLUMN_ISVALID, uploadImageData.isValid());
        initialValues.put(COLUMN_IMAGEPATHLOW, uploadImageData.getImagePathLow());
        initialValues.put(COLUMN_IMAGEPATHHIGH, uploadImageData.getImagePathHigh());
        initialValues.put(COLUMN_UPLOADURLLOW, uploadImageData.getUploadUrlLow());
        initialValues.put(COLUMN_UPLOADURLHIGH, uploadImageData.getUploadUrlHigh());
        initialValues.put(COLUMN_UUID, uploadImageData.getUuid());
        initialValues.put(COLUMN_TAG_ID, uploadImageData.getTagId());
        initialValues.put(COLUMN_TM_FORM_PK_ID, uploadImageData.getTmFormPkId());
        initialValues.put(COLUMN_CAPTURE_TIME, uploadImageData.getTimeInUnix());
        initialValues.put(COLUMN_TAGS, uploadImageData.getTags());
        initialValues.put(COLUMN_CAMERA_IMAGE, uploadImageData.getCameraImage());
        initialValues.put(COLUMN_APP_VERSION, uploadImageData.getAppVersion());
        initialValues.put(COLUMN_HIGH_RESOLUTION_UPLOADED, uploadImageData.isHighImageUploaded() ? 1 : 0);
        initialValues.put(COLUMN_LOW_RESOLUTION_UPLOADED, uploadImageData.isLowImageUploaded() ? 1 : 0);
        return initialValues;
    }


    public void updateTimeWhenFailed(int uploadId, long updateTime) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATED, updateTime);
            initialValues.put(COLUMN_RETRYCOUNTIMAGELOW, 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * method to delete single entry from table.
     */
    public void deleteDataByUploadId(int uploadId) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * Method to get all form data
     *
     * @return lstUploadImageData of forms
     */
    public ArrayList<UploadImageData> getAllUploadImageData() {
        Cursor cursor = null;
        ArrayList<UploadImageData> lstUploadImages = new ArrayList<>();
        //writableDb.beginTransactionNonExclusive();
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + COLUMN_UPDATED;
            cursor = readableDb.rawQuery(rawQuery, null);
            int uploadId = cursor.getColumnIndex(COLUMN_UPLOAD_Id);
            int formId = cursor.getColumnIndex(COLUMN_FORM_ID);
            int formPkId = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int wpId = cursor.getColumnIndex(COLUMN_WP_ID);
            int wpPkId = cursor.getColumnIndex(COLUMN_WP_PK_ID);
            int uploadUrlLow = cursor.getColumnIndex(COLUMN_UPLOADURLLOW);
            int uploadUrlHigh = cursor.getColumnIndex(COLUMN_UPLOADURLHIGH);
            int accessCode = cursor.getColumnIndex(COLUMN_ACCESSCODE);
            int lrImage = cursor.getColumnIndex(COLUMN_LRIMG);
            int hrImage = cursor.getColumnIndex(COLUMN_HRIMG);
            int date = cursor.getColumnIndex(COLUMN_DATE);
            int description = cursor.getColumnIndex(COLUMN_DESCRIPTION);
            int building = cursor.getColumnIndex(COLUMN_BUILDING);
            int email = cursor.getColumnIndex(COLUMN_EMAIL);
            int imagePathLow = cursor.getColumnIndex(COLUMN_IMAGEPATHLOW);
            int imagePathHigh = cursor.getColumnIndex(COLUMN_IMAGEPATHHIGH);
            int latitude = cursor.getColumnIndex(COLUMN_LATITUDE);
            int longitude = cursor.getColumnIndex(COLUMN_LONGITUDE);
            int locationLatitude = cursor.getColumnIndex(COLUMN_EXTRA_LATITUDE);
            int locationLongitude = cursor.getColumnIndex(COLUMN_EXTRA_LONGITUDE);
            int isValid = cursor.getColumnIndex(COLUMN_ISVALID);
            int isFormImage = cursor.getColumnIndex(COLUMN_IS_FORM_IMAGE);
            int isFormSign = cursor.getColumnIndex(COLUMN_IS_FORM_SIGNATURE);
            int retryCountImageLow = cursor.getColumnIndex(COLUMN_RETRYCOUNTIMAGELOW);
            int createdDate = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedDate = cursor.getColumnIndex(COLUMN_UPDATED);
            int isUploadOriginalSize = cursor.getColumnIndex(COLUMN_IS_UPLOAD_ORIGINAL_SIZE);
            int tagIdIndex = cursor.getColumnIndex(COLUMN_TAG_ID);
            int tagTmFormPkIdIndex = cursor.getColumnIndex(COLUMN_TM_FORM_PK_ID);
            int capturedTimeIndex = cursor.getColumnIndex(COLUMN_CAPTURE_TIME);
            int tagIndex = cursor.getColumnIndex(COLUMN_TAGS);
            int highImageUploadedIndex = cursor.getColumnIndex(COLUMN_HIGH_RESOLUTION_UPLOADED);
            int lowImageUploadedIndex = cursor.getColumnIndex(COLUMN_LOW_RESOLUTION_UPLOADED);
            int appVersionIndex = cursor.getColumnIndex(COLUMN_APP_VERSION);
            int cameraImageIndex = cursor.getColumnIndex(COLUMN_CAMERA_IMAGE);

            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    UploadImageData uploadImageData = new UploadImageData();
                    uploadImageData.setUploadId(cursor.getInt(uploadId));
                    uploadImageData.setFormPkId(cursor.getInt(formPkId));
                    uploadImageData.setFormId(cursor.getInt(formId));
                    uploadImageData.setWpPkId(cursor.getInt(wpPkId));
                    uploadImageData.setWpId(cursor.getInt(wpId));
                    uploadImageData.setUploadUrlLow(cursor.getString(uploadUrlLow));
                    uploadImageData.setUploadUrlHigh(cursor.getString(uploadUrlHigh));
                    uploadImageData.setAccessCode(cursor.getString(accessCode));
                    uploadImageData.setLrImage(cursor.getString(lrImage));
                    uploadImageData.setHrImage(cursor.getString(hrImage));
                    uploadImageData.setDate(cursor.getString(date));
                    uploadImageData.setDescription(cursor.getString(description));
                    uploadImageData.setBuilding(cursor.getString(building));
                    uploadImageData.setEmail(cursor.getString(email));
                    uploadImageData.setImagePathLow(cursor.getString(imagePathLow));
                    uploadImageData.setImagePathHigh(cursor.getString(imagePathHigh));
                    uploadImageData.setLatitude(cursor.getDouble(latitude));
                    uploadImageData.setLongitude(cursor.getDouble(longitude));
                    uploadImageData.setExtraLatitude(cursor.getDouble(locationLatitude));
                    uploadImageData.setExtraLongitude(cursor.getDouble(locationLongitude));
                    uploadImageData.setValid(cursor.getInt(isValid) == 1);
                    uploadImageData.setFormImage(cursor.getInt(isFormImage) == 1);
                    uploadImageData.setFormSign(cursor.getInt(isFormSign) == 1);
                    uploadImageData.setUploadOriginalSize(cursor.getInt(isUploadOriginalSize) == 1);
                    uploadImageData.setRetryCount(cursor.getInt(retryCountImageLow));
                    uploadImageData.setCreatedDate(cursor.getLong(createdDate));
                    uploadImageData.setUpdatedDate(cursor.getLong(updatedDate));
                    uploadImageData.setTagId(cursor.getInt(tagIdIndex));
                    uploadImageData.setTmFormPkId(cursor.getInt(tagTmFormPkIdIndex));
                    uploadImageData.setTimeInUnix(cursor.getLong(capturedTimeIndex));
                    uploadImageData.setTags(cursor.getString(tagIndex));
                    uploadImageData.setHighImageUploaded(cursor.getInt(highImageUploadedIndex) == 1);
                    uploadImageData.setLowImageUploaded(cursor.getInt(lowImageUploadedIndex) == 1);
                    uploadImageData.setAppVersion(cursor.getString(appVersionIndex));
                    uploadImageData.setCameraImage(cursor.getInt(cameraImageIndex));

                    lstUploadImages.add(uploadImageData);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstUploadImages;
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    /**
     * Method to get next upload data
     */
    public UploadImageData getNextUploadData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + " " + COLUMN_UPDATED + " ASC LIMIT 1 ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getUploadImageDataFromDB(cursor);
    }

    private UploadImageData getUploadImageDataFromDB(Cursor cursor) {

        UploadImageData uploadImageData = null;
        try {
            int uploadId = cursor.getColumnIndex(COLUMN_UPLOAD_Id);
            int formId = cursor.getColumnIndex(COLUMN_FORM_ID);
            int formPkId = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int wpId = cursor.getColumnIndex(COLUMN_WP_ID);
            int wpPkId = cursor.getColumnIndex(COLUMN_WP_PK_ID);
            int uploadUrlLow = cursor.getColumnIndex(COLUMN_UPLOADURLLOW);
            int uploadUrlHigh = cursor.getColumnIndex(COLUMN_UPLOADURLHIGH);
            int accessCode = cursor.getColumnIndex(COLUMN_ACCESSCODE);
            int lrImage = cursor.getColumnIndex(COLUMN_LRIMG);
            int hrImage = cursor.getColumnIndex(COLUMN_HRIMG);
            int date = cursor.getColumnIndex(COLUMN_DATE);
            int description = cursor.getColumnIndex(COLUMN_DESCRIPTION);
            int building = cursor.getColumnIndex(COLUMN_BUILDING);
            int email = cursor.getColumnIndex(COLUMN_EMAIL);
            int imagePathLow = cursor.getColumnIndex(COLUMN_IMAGEPATHLOW);
            int imagePathHigh = cursor.getColumnIndex(COLUMN_IMAGEPATHHIGH);
            int latitude = cursor.getColumnIndex(COLUMN_LATITUDE);
            int longitude = cursor.getColumnIndex(COLUMN_LONGITUDE);
            int locationLatitude = cursor.getColumnIndex(COLUMN_EXTRA_LATITUDE);
            int locationLongitude = cursor.getColumnIndex(COLUMN_EXTRA_LONGITUDE);
            int isValid = cursor.getColumnIndex(COLUMN_ISVALID);
            int isFormImage = cursor.getColumnIndex(COLUMN_IS_FORM_IMAGE);
            int isFormSign = cursor.getColumnIndex(COLUMN_IS_FORM_SIGNATURE);
            int retryCountImageLow = cursor.getColumnIndex(COLUMN_RETRYCOUNTIMAGELOW);
            int createdDate = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedDate = cursor.getColumnIndex(COLUMN_UPDATED);
            int isUploadOriginalSize = cursor.getColumnIndex(COLUMN_IS_UPLOAD_ORIGINAL_SIZE);
            int uuid = cursor.getColumnIndex(COLUMN_UUID);
            int tagIdIndex = cursor.getColumnIndex(COLUMN_TAG_ID);
            int tagTmFormPkIdIndex = cursor.getColumnIndex(COLUMN_TM_FORM_PK_ID);
            int capturedTimeIndex = cursor.getColumnIndex(COLUMN_CAPTURE_TIME);
            int tagIndex = cursor.getColumnIndex(COLUMN_TAGS);
            int highImageUploadedIndex = cursor.getColumnIndex(COLUMN_HIGH_RESOLUTION_UPLOADED);
            int lowImageUploadedIndex = cursor.getColumnIndex(COLUMN_LOW_RESOLUTION_UPLOADED);
            int appVersionIndex = cursor.getColumnIndex(COLUMN_APP_VERSION);
            int cameraImageIndex = cursor.getColumnIndex(COLUMN_CAMERA_IMAGE);

            if (cursor.moveToFirst()) {
                uploadImageData = new UploadImageData();
                uploadImageData.setUploadId(cursor.getInt(uploadId));
                uploadImageData.setFormPkId(cursor.getInt(formPkId));
                uploadImageData.setFormId(cursor.getInt(formId));
                uploadImageData.setWpPkId(cursor.getInt(wpPkId));
                uploadImageData.setWpId(cursor.getInt(wpId));
                uploadImageData.setUploadUrlLow(cursor.getString(uploadUrlLow));
                uploadImageData.setUploadUrlHigh(cursor.getString(uploadUrlHigh));
                uploadImageData.setAccessCode(cursor.getString(accessCode));
                uploadImageData.setLrImage(cursor.getString(lrImage));
                uploadImageData.setHrImage(cursor.getString(hrImage));
                uploadImageData.setDate(cursor.getString(date));
                uploadImageData.setDescription(cursor.getString(description));
                uploadImageData.setBuilding(cursor.getString(building));
                uploadImageData.setEmail(cursor.getString(email));
                uploadImageData.setImagePathLow(cursor.getString(imagePathLow));
                uploadImageData.setImagePathHigh(cursor.getString(imagePathHigh));
                uploadImageData.setLatitude(cursor.getDouble(latitude));
                uploadImageData.setLongitude(cursor.getDouble(longitude));
                uploadImageData.setExtraLatitude(cursor.getDouble(locationLatitude));
                uploadImageData.setExtraLongitude(cursor.getDouble(locationLongitude));
                uploadImageData.setValid(cursor.getInt(isValid) == 1);
                uploadImageData.setFormImage(cursor.getInt(isFormImage) == 1);
                uploadImageData.setFormSign(cursor.getInt(isFormSign) == 1);
                uploadImageData.setUploadOriginalSize(cursor.getInt(isUploadOriginalSize) == 1);
                uploadImageData.setRetryCount(cursor.getInt(retryCountImageLow));
                uploadImageData.setCreatedDate(cursor.getLong(createdDate));
                uploadImageData.setUpdatedDate(cursor.getLong(updatedDate));
                uploadImageData.setUuid(cursor.getString(uuid));
                uploadImageData.setTagId(cursor.getInt(tagIdIndex));
                uploadImageData.setTmFormPkId(cursor.getInt(tagTmFormPkIdIndex));
                uploadImageData.setTimeInUnix(cursor.getLong(capturedTimeIndex));
                uploadImageData.setTags(cursor.getString(tagIndex));
                uploadImageData.setHighImageUploaded(cursor.getInt(highImageUploadedIndex) == 1);
                uploadImageData.setLowImageUploaded(cursor.getInt(lowImageUploadedIndex) == 1);
                uploadImageData.setAppVersion(cursor.getString(appVersionIndex));
                uploadImageData.setCameraImage(cursor.getInt(cameraImageIndex));
                cursor.moveToNext();
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return uploadImageData;

    }

    public void changeOriginalDataStatus(int uploadId, boolean isOriginalSize) {
        ContentValues initialValues = new ContentValues();
        initialValues.put(COLUMN_IS_UPLOAD_ORIGINAL_SIZE, isOriginalSize ? 1 : 0);
        initialValues.put(COLUMN_UPLOADURLHIGH, "");
        initialValues.put(COLUMN_HRIMG, "");
        writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadId)});
    }

    public void updateLowImageUploaded(int uploadId) {
        ContentValues initialValues = new ContentValues();
        initialValues.put(COLUMN_LOW_RESOLUTION_UPLOADED, 1);

        int maxRetries = 3;
        for (int i = 1; i <= maxRetries; i++) {
            //SQLiteDatabase writableDb = getWritableDatabase();
            writableDb.beginTransactionNonExclusive();
            try {
                writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadId)});
                writableDb.setTransactionSuccessful();
                break;
            } catch (SQLiteDatabaseLockedException e) {
                if (i == maxRetries) {
                    FirebaseEventUtils.logException(e);
                }
                try {
                    Thread.sleep(100); // Wait for 100 milliseconds before retrying
                } catch (InterruptedException ex) {
                    FirebaseEventUtils.logException(ex);
                }
            } finally {
                writableDb.endTransaction();
            }
        }
    }

    public void updateHighImageUploaded(int uploadId) {
        ContentValues initialValues = new ContentValues();
        initialValues.put(COLUMN_HIGH_RESOLUTION_UPLOADED, 1);
        writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadId)});
    }

    public void updateHighImageURLs(UploadImageData uploadImageData) {
        ContentValues initialValues = new ContentValues();
        initialValues.put(COLUMN_UPLOADURLHIGH, uploadImageData.getUploadUrlHigh());
        initialValues.put(COLUMN_HRIMG, uploadImageData.getHrImage());
        writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadImageData.getUploadId())});
    }

    public void updateLowImageURLs(UploadImageData uploadImageData) {
        ContentValues initialValues = new ContentValues();
        initialValues.put(COLUMN_UPLOADURLLOW, uploadImageData.getUploadUrlLow());
        initialValues.put(COLUMN_LRIMG, uploadImageData.getLrImage());
        // writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadImageData.getUploadId())});
        int maxRetries = 3;
        for (int i = 1; i <= maxRetries; i++) {
            //SQLiteDatabase writableDb = getWritableDatabase();
            writableDb.beginTransactionNonExclusive();
            try {
                writableDb.update(TABLE_NAME, initialValues, COLUMN_UPLOAD_Id + "=? ", new String[]{String.valueOf(uploadImageData.getUploadId())});
                writableDb.setTransactionSuccessful();
                break;
            } catch (SQLiteDatabaseLockedException e) {
                if (i == maxRetries) {
                    FirebaseEventUtils.logException(e);
                }
                try {
                    Thread.sleep(100); // Wait for 100 milliseconds before retrying
                } catch (InterruptedException ex) {
                    FirebaseEventUtils.logException(ex);
                }
            } finally {
                writableDb.endTransaction();
            }
        }
    }
}