package com.sitefotos.storage.tables;

import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.google.gson.Gson;
import com.sitefotos.Constants;
import com.sitefotos.models.WorkLogProfileData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

// Now we are not using this table. kept to manage old versions.
public class TblSiteProfileData {

    public static final String TABLE_NAME = "tblSiteProfileData";
    private static final String COLUMN_PROFILE_PK_ID = "pkId";
    private static final String COLUMN_SITE_ID = "siteId";
    private static final String COLUMN_PROFILE_ID = "profileId";
    private static final String COLUMN_PROFILE_RAW_DATA = "rawData";
    private static final String COLUMN_PROFILE_IMAGE_DATA = "siteImageData";
    private static final String COLUMN_MODIFIED_PROFILE_DATA = "modifiedProfileData";
    private  static final String COLUMN_PROFILE_CATEGORY = "profileCategory";
    private  static final String COLUMN_CHECKINOUT = "checkInOut";
    private  static final String COLUMN_CREWTRACKING = "crewTracking";
    private static final String COLUMN_CURRENT_LATITUDE = "wp_latitude";
    private static final String COLUMN_CURRENT_LONGITUDE = "wp_longitude";
    private static final String COLUMN_PROFILE_CREATED = "created";
    private static final String COLUMN_PROFILE_UPDATED = "updated";
    private static final String COLUMN_IS_CREW_SUBMITTED = "isCrewSubmitted";
    private static final String COLUMN_PROFILE_SUBMITTED = "profileSubmitted";
    private static final String COLUMN_UPDATE_STATUS = "wp_update_status";

    private static final String COLUMN_PHOTO_COUNT = "wp_photoCount";
    private static final String COLUMN_ISSUE_PHOTO_COUNT = "wp_issue_photoCount";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblSiteProfileData(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PROFILE_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_SITE_ID + "` INTEGER , " +
                " `" + COLUMN_PROFILE_ID + "` INTEGER , " +
                " `" + COLUMN_PROFILE_RAW_DATA + "` TEXT, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER, " +
                " '" + COLUMN_PROFILE_IMAGE_DATA + "' TEXT, " +
                " `" + COLUMN_PROFILE_CATEGORY + "` TEXT, " +
                " `" + COLUMN_CHECKINOUT + "` INTEGER, " +
                " `" + COLUMN_CREWTRACKING + "` INTEGER, " +
                " `" + COLUMN_PHOTO_COUNT + "` INTEGER, " +
                " `" + COLUMN_ISSUE_PHOTO_COUNT + "` INTEGER, " +
                " `" + COLUMN_MODIFIED_PROFILE_DATA + "` TEXT, " +
                " `" + COLUMN_PROFILE_CREATED + "` INTEGER, " +
                " `" + COLUMN_PROFILE_UPDATED + "` INTEGER, " +
                " `" + COLUMN_CURRENT_LATITUDE + "` REAL, " +
                " `" + COLUMN_CURRENT_LONGITUDE + "` REAL, " +
                " `" + COLUMN_IS_CREW_SUBMITTED + "` INTEGER, " +
                " `" + COLUMN_PROFILE_SUBMITTED + "` INTEGER " + " ) ";

    }

    /**
     * Method to insert or update bulk data in site profile  table
     *
     * @param lstProfileData list of site profile
     */
    public void insertOrUpdateProfileData(List<WorkLogProfileData> lstProfileData) {
        try {
            ArrayList<Integer> lstUpdatedId = new ArrayList<>();
            for (WorkLogProfileData data : lstProfileData) {
                ContentValues initialValues = new ContentValues();
                initialValues.put(COLUMN_SITE_ID, data.getSiteId());
                initialValues.put(COLUMN_PROFILE_ID, data.getProfileID());
                initialValues.put(COLUMN_PROFILE_RAW_DATA, data.getRawData());
                initialValues.put(COLUMN_PROFILE_CREATED, data.getCreated());
                initialValues.put(COLUMN_PROFILE_CATEGORY, data.getProfileCategory());
                initialValues.put(COLUMN_CHECKINOUT, data.isCheckInOut()?1:0);
                initialValues.put(COLUMN_CREWTRACKING, data.isCrewTracking()?1:0);

                initialValues.put(COLUMN_UPDATE_STATUS, 1);
                //initialValues.put(COLUMN_MODIFIED_PROFILE_DATA, data.getModifiedData());
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_SITE_ID + "=? AND " +COLUMN_PROFILE_ID + "=? AND "+ COLUMN_PROFILE_UPDATED + " !=? AND " + COLUMN_PROFILE_SUBMITTED + "=? ",
                        new String[]{String.valueOf(data.getSiteId()),String.valueOf(data.getProfileID()), String.valueOf(data.getUpdated()), String.valueOf(0)});
                if (id == 0) {
                    //initialValues.remove(COLUMN_MODIFIED_PROFILE_DATA);
                    initialValues.remove(COLUMN_PROFILE_RAW_DATA);
                    initialValues.remove(COLUMN_SITE_ID);
                    id = writableDb.update(TABLE_NAME, initialValues, COLUMN_SITE_ID + "=? AND " +COLUMN_PROFILE_ID + "=? AND " + COLUMN_PROFILE_SUBMITTED + "=? ", new String[]{String.valueOf(data.getSiteId()),String.valueOf(data.getProfileID()), String.valueOf(0)});

                    if (id == 0) {
                        initialValues.put(COLUMN_SITE_ID, data.getSiteId());
                        initialValues.put(COLUMN_MODIFIED_PROFILE_DATA, data.getRawData());
                        initialValues.put(COLUMN_PROFILE_RAW_DATA,data.getRawData());
                        initialValues.put(COLUMN_PROFILE_SUBMITTED, 0);
                        initialValues.put(COLUMN_CURRENT_LATITUDE, 0.0);
                        initialValues.put(COLUMN_CURRENT_LONGITUDE, 0.0);
                        initialValues.put(COLUMN_PHOTO_COUNT, 0);
                        initialValues.put(COLUMN_ISSUE_PHOTO_COUNT, 0);
                        initialValues.put(COLUMN_IS_CREW_SUBMITTED,0);
                        writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_IGNORE);
                    }

                } else {
                    lstUpdatedId.add(data.getProfileID());
                }

            }
            Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
            intent.putIntegerArrayListExtra(Constants.UPDATED_WP_LIST, lstUpdatedId);
            context.sendBroadcast(intent);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Method to insert data in profile table
     *
     * @param data profile data
     */
    public void insertSingleData(WorkLogProfileData data) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_SITE_ID, data.getSiteId());
            initialValues.put(COLUMN_PROFILE_ID, data.getProfileID());
            initialValues.put(COLUMN_PROFILE_RAW_DATA, data.getRawData());
            initialValues.put(COLUMN_PROFILE_CREATED, data.getCreated());
            initialValues.put(COLUMN_PROFILE_CATEGORY, data.getProfileCategory());
            initialValues.put(COLUMN_CHECKINOUT, data.isCheckInOut()?1:0);
            initialValues.put(COLUMN_CREWTRACKING, data.isCrewTracking()?1:0);
            initialValues.put(COLUMN_PROFILE_UPDATED, data.getUpdated());
            initialValues.put(COLUMN_PROFILE_UPDATED, data.getUpdated());
            initialValues.put(COLUMN_MODIFIED_PROFILE_DATA, data.getModifiedData());
            initialValues.put(COLUMN_PROFILE_SUBMITTED, 0);
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            initialValues.put(COLUMN_PROFILE_IMAGE_DATA, "");
            initialValues.put(COLUMN_CURRENT_LATITUDE, data.getLatitude());
            initialValues.put(COLUMN_CURRENT_LONGITUDE, data.getLongitude());
            initialValues.put(COLUMN_PHOTO_COUNT, 0);
            initialValues.put(COLUMN_ISSUE_PHOTO_COUNT, 0);
            initialValues.put(COLUMN_IS_CREW_SUBMITTED,0);
            writableDb.insert(TABLE_NAME, null, initialValues);

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    public ArrayList<Integer> getDataWithColumnUpdateStatusZero() {
        String rawQuery = "SELECT " + COLUMN_PROFILE_ID + " FROM " + TABLE_NAME + " WHERE " + COLUMN_UPDATE_STATUS + "='" + 0 + "' AND " + COLUMN_PROFILE_SUBMITTED + "='" + 0 + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        ArrayList<Integer> lstFormId = new ArrayList<>();
        if (cursor != null && cursor.moveToFirst()) {
            while (!cursor.isAfterLast()) {

                lstFormId.add((int) cursor.getFloat(cursor.getColumnIndex(COLUMN_PROFILE_ID)));
                cursor.moveToNext();
            }
        }

        return lstFormId;
    }


    /**
     * Method to get all profile data
     *
     * @return list of site profile
     */
    public List<WorkLogProfileData> getAllProfilesFromSiteId(long siteId) {
        Cursor cursor;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " +COLUMN_SITE_ID+"='"+siteId+ "' AND "  + COLUMN_PROFILE_SUBMITTED + "='" + 0 + "' ORDER BY " + COLUMN_PROFILE_ID + " COLLATE NOCASE ASC ";
            cursor = readableDb.rawQuery(rawQuery, null);
            return getDataFromCursor(cursor);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
        return new ArrayList<>();
    }


    /**
     * Method to get all profile data
     *
     * @return list of site profile
     */
    public List<WorkLogProfileData> getAllProfiles() {
        Cursor cursor;
        //writableDb.beginTransactionNonExclusive();
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_PROFILE_SUBMITTED + "='" + 0 + "' ORDER BY " + COLUMN_PROFILE_ID + " COLLATE NOCASE ASC ";
            cursor = readableDb.rawQuery(rawQuery, null);
            return getDataFromCursor(cursor);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
        return new ArrayList<>();
    }

    private List<WorkLogProfileData> getDataFromCursor(Cursor cursor){

        ArrayList<WorkLogProfileData> listProfileData = new ArrayList<>();
        try{
        int pkIdIndex = cursor.getColumnIndex(COLUMN_PROFILE_PK_ID);
        int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
        int profileIdIndex = cursor.getColumnIndex(COLUMN_PROFILE_ID);
        int rawDataIndex = cursor.getColumnIndex(COLUMN_PROFILE_RAW_DATA);
        int imageDataIndex = cursor.getColumnIndex(COLUMN_PROFILE_IMAGE_DATA);
        int modifiedDataIndex = cursor.getColumnIndex(COLUMN_MODIFIED_PROFILE_DATA);
        int profileCreatedIndex = cursor.getColumnIndex(COLUMN_PROFILE_CREATED);
        int profileUpdatedIndex = cursor.getColumnIndex(COLUMN_PROFILE_UPDATED);
        int submittedStatusIndex = cursor.getColumnIndex(COLUMN_PROFILE_SUBMITTED);
        int categoryIndex = cursor.getColumnIndex(COLUMN_PROFILE_CATEGORY);
        int checkInOutIndex = cursor.getColumnIndex(COLUMN_CHECKINOUT);
        int crackingIndex = cursor.getColumnIndex(COLUMN_CREWTRACKING);
        int currentLatitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LATITUDE);
        int currentLongitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LONGITUDE);
        int photoCountIndex = cursor.getColumnIndex(COLUMN_PHOTO_COUNT);
        int issueCountIndex = cursor.getColumnIndex(COLUMN_ISSUE_PHOTO_COUNT);
        int isCrewSubmittedIndex = cursor.getColumnIndex(COLUMN_IS_CREW_SUBMITTED);

        if (cursor.moveToFirst()) {
            while (!cursor.isAfterLast()) {
                WorkLogProfileData data = new WorkLogProfileData();
                data.setPkId(cursor.getInt(pkIdIndex));
                data.setSiteId(cursor.getInt(siteIdIndex));
                data.setProfileID(cursor.getInt(profileIdIndex));
                data.setModifiedData(cursor.getString(modifiedDataIndex));
                data.setRawData(cursor.getString(rawDataIndex));
                data.setImageData(cursor.getString(imageDataIndex));
                data.setCreated(cursor.getLong(profileCreatedIndex));
                data.setUpdated(cursor.getLong(profileUpdatedIndex));
                data.setProfileSubmitted(cursor.getInt(submittedStatusIndex) != 0);
                data.setProfileCategory(cursor.getString(categoryIndex));
                data.setCheckInOut(cursor.getInt(checkInOutIndex) != 0);
                data.setCrewTracking(cursor.getInt(crackingIndex) !=0);
                data.setCrewSubmitted(cursor.getInt(isCrewSubmittedIndex) != 0);
                data.setLatitude(cursor.getDouble(currentLatitudeIndex));
                data.setLongitude(cursor.getDouble(currentLongitudeIndex));
                data.setPhotoCount(cursor.getInt(photoCountIndex));
                data.setIssuePhotoCount(cursor.getInt(issueCountIndex));
                WorkLogProfileData subData = new Gson().fromJson(data.getModifiedData(), WorkLogProfileData.class);
                data.setServices(subData.getServices());
                data.setMaterials(subData.getMaterials());
                data.setNotes(subData.getNotes());
                data.setMapDetails(subData.getMapDetails());
                data.setFormDetails(subData.getFormDetails());
                data.setLstFormDetails(subData.getLstFormDetails());
                data.setLstEmployees(subData.getLstEmployees());
                data.setClientDetails(subData.getClientDetails());
                data.setCheckinTime(subData.getCheckinTime());
                data.setCheckoutTime(subData.getCheckoutTime());
                data.setNotes(subData.getNotes());
                data.setIssues(subData.isIssues());
                data.setNotesSelected(subData.isNotesSelected());
                data.setPhotos(subData.isPhotos());
                data.setServicesType(subData.getServicesType());
                data.setUserNote(subData.getUserNote());
                data.setPhotosRequired(subData.isPhotosRequired());
                data.setNotesRequired(subData.isNotesRequired());
                listProfileData.add(data);
                cursor.moveToNext();
            }
        }
    } catch (Exception e) {
        FirebaseEventUtils.logException(e);
            
    } finally {
        //closeResource(writableDb,  cursor);
        if (cursor != null) {
            cursor.close();
        }
        //writableDb.setTransactionSuccessful();
        // writableDb.endTransaction();
    }
        return listProfileData;
    }


    public WorkLogProfileData getDataFromPKId(int pkId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_PROFILE_PK_ID + "='" + pkId + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    private WorkLogProfileData getSingleDataFromDB(Cursor cursor) {
        //writableDb.beginTransactionNonExclusive();
        WorkLogProfileData data = null;
        try {
            data = new WorkLogProfileData();
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PROFILE_PK_ID);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int profileIdIndex = cursor.getColumnIndex(COLUMN_PROFILE_ID);
            int rawDataIndex = cursor.getColumnIndex(COLUMN_PROFILE_RAW_DATA);
            int imageDataIndex = cursor.getColumnIndex(COLUMN_PROFILE_IMAGE_DATA);
            int modifiedDataIndex = cursor.getColumnIndex(COLUMN_MODIFIED_PROFILE_DATA);
            int profileCreatedIndex = cursor.getColumnIndex(COLUMN_PROFILE_CREATED);
            int profileUpdatedIndex = cursor.getColumnIndex(COLUMN_PROFILE_UPDATED);
            int submittedStatusIndex = cursor.getColumnIndex(COLUMN_PROFILE_SUBMITTED);
            int categoryIndex = cursor.getColumnIndex(COLUMN_PROFILE_CATEGORY);
            int checkInOutIndex = cursor.getColumnIndex(COLUMN_CHECKINOUT);
            int crackingIndex = cursor.getColumnIndex(COLUMN_CREWTRACKING);
            int currentLatitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LATITUDE);
            int currentLongitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LONGITUDE);
            int photoCountIndex = cursor.getColumnIndex(COLUMN_PHOTO_COUNT);
            int issueCountIndex = cursor.getColumnIndex(COLUMN_ISSUE_PHOTO_COUNT);
            int crewSubmittedIndex = cursor.getColumnIndex(COLUMN_IS_CREW_SUBMITTED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setSiteId(cursor.getInt(siteIdIndex));
                    data.setProfileID(cursor.getInt(profileIdIndex));
                    data.setModifiedData(cursor.getString(modifiedDataIndex));
                    data.setRawData(cursor.getString(rawDataIndex));
                    data.setImageData(cursor.getString(imageDataIndex));
                    data.setCreated(cursor.getLong(profileCreatedIndex));
                    data.setUpdated(cursor.getLong(profileUpdatedIndex));
                    data.setProfileSubmitted(cursor.getInt(submittedStatusIndex) != 0);
                    data.setProfileCategory(cursor.getString(categoryIndex));
                    data.setCheckInOut(cursor.getInt(checkInOutIndex) != 0);
                    data.setCrewSubmitted(cursor.getInt(crewSubmittedIndex) != 0);
                    data.setCrewTracking(cursor.getInt(crackingIndex) !=0);
                    data.setLatitude(cursor.getDouble(currentLatitudeIndex));
                    data.setLongitude(cursor.getDouble(currentLongitudeIndex));
                    data.setPhotoCount(cursor.getInt(photoCountIndex));
                    data.setIssuePhotoCount(cursor.getInt(issueCountIndex));

                    WorkLogProfileData subData = new Gson().fromJson(data.getModifiedData(), WorkLogProfileData.class);
                    data.setServices(subData.getServices());
                    data.setMaterials(subData.getMaterials());
                    data.setNotes(subData.getNotes());
                    data.setMapDetails(subData.getMapDetails());
                    data.setFormDetails(subData.getFormDetails());
                    data.setLstFormDetails(subData.getLstFormDetails());
                    data.setLstEmployees(subData.getLstEmployees());
                    data.setClientDetails(subData.getClientDetails());
                    data.setCheckinTime(subData.getCheckinTime());
                    data.setCheckoutTime(subData.getCheckoutTime());
                    data.setNotes(subData.getNotes());
                    data.setIssues(subData.isIssues());
                    data.setPhotos(subData.isPhotos());
                    data.setNotesSelected(subData.isNotesSelected());
                    data.setServicesType(subData.getServicesType());
                    data.setUserNote(subData.getUserNote());
                    data.setPhotosRequired(subData.isPhotosRequired());
                    data.setNotesRequired(subData.isNotesRequired());
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
            //writableDb.setTransactionSuccessful();
            //writableDb.endTransaction();
        }

        return data;

    }


    public void updateModifiedDataByPkId(int pkId, String modifiedData) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_MODIFIED_PROFILE_DATA, modifiedData);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_PK_ID + "=?  ", new String[]{String.valueOf(pkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    public void updateImageDataByPkId(int pkId, String imageData) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_PROFILE_IMAGE_DATA, imageData);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_PK_ID + "=? ", new String[]{String.valueOf(pkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    public void updateStatus(int status) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, status);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_SUBMITTED + "=? ", new String[]{String.valueOf(0)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void updateStatusById(int status, long profileId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, status);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_ID + "=? AND " + COLUMN_PROFILE_SUBMITTED + "=? ", new String[]{String.valueOf(profileId), String.valueOf(0)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void updateSubmittedStatus(long pkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_PROFILE_SUBMITTED, 1);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_PK_ID + " =? ", new String[]{String.valueOf(pkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void updateLocationsByPkId(long pkId, double latitude, double longitude) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CURRENT_LATITUDE, latitude);
            initialValues.put(COLUMN_CURRENT_LONGITUDE, longitude);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_PK_ID + " =? ", new String[]{String.valueOf(pkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Delete all old data from  table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
           writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=? AND " + COLUMN_PROFILE_SUBMITTED + "=? ", new String[]{String.valueOf(0), String.valueOf(0)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * method to delete single entry table.
     */
    public void deleteDataByPKId(long pKId) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_PROFILE_PK_ID + "=? ", new String[]{String.valueOf(pKId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    /**
     * Delete all data
     */
    public void deleteDataFromTable() {
        try {
            //writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = writableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableIsCrewSubmitted() {
        try {
            if (checkIsColumnExist(COLUMN_IS_CREW_SUBMITTED) == -1) {
                writableDb.execSQL(alterTableIsCrewSubmitted());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private static String alterTableIsCrewSubmitted() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_CREW_SUBMITTED + "` INTEGER ";
    }

    public void updateIsCrewSubmittedStatus(long pkId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IS_CREW_SUBMITTED, 1);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PROFILE_PK_ID + " =? ", new String[]{String.valueOf(pkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


}
