package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.Employees;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;

public class TblEmployees {

    public static final String TABLE_NAME = "tblEmployees";
    private static final String COLUMN_E_ID = "eID";
    private static final String COLUMN_E_FIRST_NAME = "eFirstName";
    private static final String COLUMN_E_LAST_NAME = "eLastName";
    private static final String COLUMN_IS_SELECTED = "isSelected";
    private static final String COLUMN_E_SEARCH_ID = "employeeSearchId";
    private static final String COLUMN_IS_CREW = "isCrew";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblEmployees(Context context) {

        this.context = context;
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
    }

    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_E_ID + "` integer PRIMARY KEY NOT NULL UNIQUE, " +
                " `" + COLUMN_E_FIRST_NAME + "` TEXT, " +
                " `" + COLUMN_IS_SELECTED + "` INTEGER, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER, " +
                " `" + COLUMN_E_SEARCH_ID + "` TEXT, " +
                " `" + COLUMN_IS_CREW + "` INTEGER, " +
                " `" + COLUMN_E_LAST_NAME + "` TEXT " + " ) ";
    }

    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    public void checkAndAlterTableIsSelected() {
        try {
            if (checkIsColumnExist(COLUMN_IS_SELECTED) == -1) {
                writableDb.execSQL(alterTableIsSelected());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void checkAndAlterTableCreated() {
        try {
            if (checkIsColumnExist(COLUMN_CREATED) == -1) {
                writableDb.execSQL(alterTableCreated());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public void checkAndAlterTableUpdated() {
        try {
            if (checkIsColumnExist(COLUMN_UPDATED) == -1) {
                writableDb.execSQL(alterTableUpdated());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public static String alterTableIsSelected() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_SELECTED + "` INTEGER ";
    }

    public static String alterTableCreated() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_CREATED + "` INTEGER ";
    }

    public static String alterTableUpdated() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_UPDATED + "` INTEGER ";
    }

    /*public void checkAndAltTblEmploeeId() {
        try {
            if (checkIsColumnExist(COLUMN_EMPLOYEE_ID) == -1) {
                writableDb.execSQL(altTblEmployeeId());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
        }
    }*/

    public void checkAndAltTblSearchId() {
        try {
            if (checkIsColumnExist(COLUMN_E_SEARCH_ID) == -1) {
                writableDb.execSQL(altTblSearchId());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    private static String altTblSearchId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_E_SEARCH_ID + "` TEXT ";
    }

   /* private static String altTblEmployeeId() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_EMPLOYEE_ID + "` TEXT ";
    }*/

    public void checkAndAltTblIsCrew() {
        try {
            if (checkIsColumnExist(COLUMN_IS_CREW) == -1) {
                writableDb.execSQL(altTblIsCrew());
            }
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public static String altTblIsCrew() {
        return "ALTER TABLE " + TABLE_NAME + " ADD" +
                " `" + COLUMN_IS_CREW + "` INTEGER ";
    }

    /**
     * Method to insert or update bulk data in table
     *
     * @param lstEmployee     list of Employees
     * @param firstUpdateDate
     */
    public synchronized void insertOrUpdateBulkData(List<Employees> lstEmployee, long firstUpdateDate) {

       // writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            for (Employees employee : lstEmployee) {
                initialValues.put(COLUMN_E_ID, employee.getEmployeeID());
                initialValues.put(COLUMN_E_FIRST_NAME, employee.getEmployeeFirstName());
                initialValues.put(COLUMN_E_LAST_NAME, employee.getEmployeeLastName());
                initialValues.put(COLUMN_UPDATED, firstUpdateDate);
                initialValues.put(COLUMN_E_SEARCH_ID, employee.getEmployeeSearchID());
                initialValues.put(COLUMN_IS_CREW, employee.isCrew()?1:0);
                // initialValues.put(COLUMN_IS_SELECTED, employee.isSelected());
                int id = writableDb.update(TABLE_NAME, initialValues, COLUMN_E_ID + "=?",
                        new String[]{String.valueOf(employee.getEmployeeID())});
                if (id == 0) {
                    initialValues.put(COLUMN_CREATED, firstUpdateDate);
                    writableDb.insert(TABLE_NAME, null, initialValues);
                }else{
                    TblClockCrew tblClockCrew = new TblClockCrew(context);
                    tblClockCrew.updateCrewNormalData(employee.getEmployeeID(), StaticUtils.getSelectedCrewMemberName(employee),employee.isCrew()?1:0);
                }
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
        //    writableDb.setTransactionSuccessful();
        //    writableDb.endTransaction();
        }
    }

    /**
     * Method to insert  bulk data in table
     *
     * @param lstEmployee list of Employees
     */
    public synchronized void insertBulkData(List<Employees> lstEmployee) {
        ContentValues initialValues = new ContentValues();
        try {
            for (Employees employee : lstEmployee) {
                initialValues.put(COLUMN_E_ID, employee.getEmployeeID());
                initialValues.put(COLUMN_E_FIRST_NAME, employee.getEmployeeFirstName());
                initialValues.put(COLUMN_E_LAST_NAME, employee.getEmployeeLastName());
                initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
                initialValues.put(COLUMN_E_SEARCH_ID, employee.getEmployeeSearchID());
                initialValues.put(COLUMN_IS_CREW, employee.isCrew() ? 1 : 0);
                initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
                writableDb.insert(TABLE_NAME, null, initialValues);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Method to get extended employee data
     *
     * @return list of employee
     */
    public List<Employees> getExtendedUserData() {
        Cursor cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_CREW + "='0'" + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ", null);
        return getAllDataFromDb(cursor);
    }

    /**
     * Method to get extended employee data
     *
     * @return list of employee
     */
    public List<Employees> getAllData() {
        Cursor cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ", null);
        return getAllDataFromDb(cursor);
    }


    /**
     * Method to get all employee data
     *
     * @return list of employee
     */
    public List<Employees> getCrewData() {
        Cursor cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_CREW + "='1'" + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ", null);
        return getAllDataFromDb(cursor);
    }

    /**
     * Method to get all employee data
     *
     * @return list of selected employees
     */
    public List<Employees> getSelectedCrewData() {
        Cursor cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_CREW + "='1' AND "+ COLUMN_IS_SELECTED + "='1'" + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ", null);
        return getAllDataFromDb(cursor);
    }


    private List<Employees> getAllDataFromDb(Cursor cursor) {
        ArrayList<Employees> lstEmployee = new ArrayList<>();
        try {
            //int idIndex = cursor.getColumnIndex(COLUMN_EMPLOYEE_ID);
            int pIdIndex = cursor.getColumnIndex(COLUMN_E_ID);
            int fNameIndex = cursor.getColumnIndex(COLUMN_E_FIRST_NAME);
            int lNameIndex = cursor.getColumnIndex(COLUMN_E_LAST_NAME);
            int isSelectedIndex = cursor.getColumnIndex(COLUMN_IS_SELECTED);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int searchIDIndex = cursor.getColumnIndex(COLUMN_E_SEARCH_ID);
            int isCrewIndex = cursor.getColumnIndex(COLUMN_IS_CREW);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    Employees employee = new Employees();
                    //employee.setPkId(cursor.getInt(pIdIndex));
                    employee.setEmployeeID(cursor.getInt(pIdIndex));
                    employee.setEmployeeFirstName(cursor.getString(fNameIndex));
                    employee.setEmployeeLastName(cursor.getString(lNameIndex));
                    employee.setCreatedDate(cursor.getLong(createdIndex));
                    employee.setUpdatedDate(cursor.getLong(updatedIndex));
                    employee.setSelected(cursor.getInt(isSelectedIndex) > 0);
                    employee.setEmployeeSearchID(cursor.getString(searchIDIndex));
                    employee.setCrew(cursor.getInt(isCrewIndex) > 0);
                    lstEmployee.add(employee);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstEmployee;
    }


    private Employees getSingleDataFromDb(Cursor cursor) {
        Employees employee = new Employees();
        try {
            //int idIndex = cursor.getColumnIndex(COLUMN_EMPLOYEE_ID);
            int pIdIndex = cursor.getColumnIndex(COLUMN_E_ID);
            int fNameIndex = cursor.getColumnIndex(COLUMN_E_FIRST_NAME);
            int lNameIndex = cursor.getColumnIndex(COLUMN_E_LAST_NAME);
            int isSelectedIndex = cursor.getColumnIndex(COLUMN_IS_SELECTED);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            int searchIDIndex = cursor.getColumnIndex(COLUMN_E_SEARCH_ID);
            int isCrewIndex = cursor.getColumnIndex(COLUMN_IS_CREW);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                   // employee.setPkId(cursor.getInt(pIdIndex));
                    employee.setEmployeeID(cursor.getInt(pIdIndex));
                    employee.setEmployeeFirstName(cursor.getString(fNameIndex));
                    employee.setEmployeeLastName(cursor.getString(lNameIndex));
                    employee.setCreatedDate(cursor.getLong(createdIndex));
                    employee.setUpdatedDate(cursor.getLong(updatedIndex));
                    employee.setSelected(cursor.getInt(isSelectedIndex) > 0);
                    employee.setEmployeeSearchID(cursor.getString(searchIDIndex));
                    employee.setCrew(cursor.getInt(isCrewIndex) > 0);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return employee;
    }

    /**
     * Method to get all employee data
     *
     * @return list of employee
     */
    public List<Employees> getAllDataWithoutSelected(List<Integer> ids) {

        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_CREW + "='1'" + " AND " + COLUMN_E_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(ids) + ")" + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getAllDataFromDb(cursor);
    }

    /**
     * Method to get all employee data
     *
     * @return list of employee
     */
    public List<Employees> getAllDataByIds(List<Integer> ids) {

        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_E_ID + " IN (" + DBUtils.toCommaSeparatedString(ids) + ")" + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getAllDataFromDb(cursor);
    }


    /**
     * Method to get all none crew data
     *
     * @return list of employee with isCrew = 0
     */
    public List<Employees> getAllNonCrewData() {

        String rawQuery ="SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_IS_CREW + "='0'" + " ORDER BY " + COLUMN_E_FIRST_NAME + " COLLATE NOCASE ASC ";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getAllDataFromDb(cursor);
    }


    /**
     * Method to get employee by particular id
     *
     * @return Object of requested Id
     */
    public Employees getDataById(int id) {

        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_E_ID + "='" + id + "'";
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDb(cursor);
    }

    public void insertSingleData(Employees data) {
        writableDb.beginTransactionNonExclusive();
        try {

            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_E_ID, data.getEmployeeID());
            initialValues.put(COLUMN_E_FIRST_NAME, data.getEmployeeFirstName());
            initialValues.put(COLUMN_E_LAST_NAME, data.getEmployeeLastName());
            initialValues.put(COLUMN_IS_SELECTED, data.isSelected());
            initialValues.put(COLUMN_CREATED, data.getCreatedDate());
            initialValues.put(COLUMN_UPDATED, data.getUpdatedDate());
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public void updateSingleSelection(int crewId, boolean isSelected) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IS_SELECTED, isSelected ? 1 : 0);
       //     writableDb.update(TABLE_NAME, initialValues, COLUMN_EMPLOYEE_ID + "=?  ", new String[]{String.valueOf(crewId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Method to update bulk data selection in table
     *
     * @param lstEmployee list of Employees
     */
    public void updateBulkSelectionData(List<Employees> lstEmployee) {
        ContentValues initialValues = new ContentValues();
        try {
            for (Employees employee : lstEmployee) {
                initialValues.put(COLUMN_IS_SELECTED, employee.isSelected() ? 1 : 0);
                writableDb.update(TABLE_NAME, initialValues, COLUMN_E_ID + "=?",
                        new String[]{String.valueOf(employee.getEmployeeID())});
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Method to update bulk data selection in table
     *
     * @param lstDeselected list of Employee Ids
     */
    public void updateBulkDeSelectionData(List<Integer> lstDeselected) {
        ContentValues initialValues = new ContentValues();
        try {
                initialValues.put(COLUMN_IS_SELECTED, 0);
                for(Integer id : lstDeselected){
                writableDb.update(TABLE_NAME, initialValues, COLUMN_E_ID + "=?",
                        new String[]{String.valueOf(id)});
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    /**
     * Method to update bulk data selection in table
     *
     * @param lstEmployee list of Employees
     */
    public void updateEmployeeSelectionOnDemand(List<Employees> lstEmployee, boolean shoudSelect) {

        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            for (Employees employee : lstEmployee) {
                initialValues.put(COLUMN_IS_SELECTED, shoudSelect);
                writableDb.update(TABLE_NAME, initialValues, COLUMN_E_ID + "=?",
                        new String[]{String.valueOf(employee.getEmployeeID())});
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Delete all data from the table
     */
    public synchronized void deleteData() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Delete all data from the table
     */
    public synchronized void deleteOldData(List<Integer> lstEmpId) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_E_ID + " NOT IN (" + DBUtils.toCommaSeparatedString(lstEmpId) + ")");
            //writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_UPDATED + " < " + lastUpdate);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

        public int getDataCount() {
            String query = "SELECT count(*) FROM " + TABLE_NAME;
            int count = 0;
            Cursor cursor = null;
            try {
                cursor = readableDb.rawQuery(query, null);
                cursor.moveToFirst();
                count = cursor.getInt(0);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
                
            } finally {
                if (cursor != null) {
                    cursor.close();
                }
            }
            return count;
        }


    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }
}
