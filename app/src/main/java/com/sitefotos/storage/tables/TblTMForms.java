package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.toCommaSeparatedString;

import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;
import android.util.Pair;

import com.google.gson.Gson;
import com.sitefotos.Constants;
import com.sitefotos.models.CheckedInMapFormDetails;
import com.sitefotos.models.FormData;
import com.sitefotos.models.SubFormOtherData;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import java.util.ArrayList;
import java.util.List;

import kotlin.Triple;

public class TblTMForms extends BaseTblForms {

    public static final String TABLE_NAME = "tblTMForms";


    private Context context;
    private SQLiteDatabase writableDb;

    @Override
    SQLiteDatabase getSQLiteDatabase() {
        return writableDb;
    }

    @Override
    Context getContext() {
        return context;
    }

    public TblTMForms(Context context) {
        writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_FORM_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_FORM_ID + "` INTEGER , " +
                " `" + COLUMN_SITE_ID + "` INTEGER , " +
                " `" + COLUMN_FORM_NAME + "` TEXT, " +
                " `" + COLUMN_FORM_DATA + "` TEXT, " +
                " '" + COLUMN_IMAGE_DATA + "' Text, " +
                " `" + COLUMN_MODIFIED_FORM_DATA + "` TEXT, " +
                " `" + COLUMN_FORM_CREATED + "` INTEGER, " +
                " `" + COLUMN_FORM_UPDATED + "` INTEGER, " +
                " `" + COLUMN_FORM_SUBMITTED_DATE + "` INTEGER, " +
                " `" + COLUMN_IS_CHECKIN_OUT + "` INTEGER, " +
                " `" + COLUMN_IS_CHECKIN_OUT_COMPLETE + "` INTEGER, " +
                " `" + COLUMN_CHECKIN_OUT_FORM_COMPLETE + "` INTEGER, " +
                " `" + COLUMN_IMAGE_COUNT + "` INTEGER, " +
                " `" + COLUMN_SIGNATURE_COUNT + "` INTEGER, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER, " +
                " `" + COLUMN_LAST_BUILDING_ID + "` INTEGER, " +
                " `" + COLUMN_CURRENT_LATITUDE + "` REAL, " +
                " `" + COLUMN_CURRENT_LONGITUDE + "` REAL, " +
                " `" + COLUMN_FORM_CHECKIN_TIME + "` INTEGER, " +
                " `" + COLUMN_FORM_CHECKOUT_TIME + "` INTEGER, " +
                " `" + COLUMN_PLOT_ON_MAP + "` INTEGER, " +
                " `" + COLUMN_CREW_IDS + "` TEXT, " +
                " `" + COLUMN_PRE_SELECT_SERVICE + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_FORM_SUBMISSION_ID + "` TEXT, " +
                " `" + COLUMN_IS_SUB_FORM + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_HAS_SUB_FORM + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_SUB_FORM_OTHER_DATA + "` TEXT, " +
                " `" + COLUMN_FORM_UPLOADED + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_MAIN_FORM_PK_ID + "` INTEGER DEFAULT 0, " +
                " `" + COLUMN_FORM_SUBMITTED + "` INTEGER " + " )";

    }

    public static String createIndexesForUpdate() {
        return "CREATE INDEX IF NOT EXISTS `idx_tmsf_update` ON `" + TABLE_NAME + "` (`" + COLUMN_FORM_ID + "`,`" + COLUMN_SITE_ID + "`,`" + COLUMN_FORM_UPDATED + "`,`" + COLUMN_FORM_SUBMITTED + "`)";
    }


    /**
     * Method to insert or update bulk data in table
     *
     * @param lstFormData list of tmForm data
     */
    public synchronized void insertOrUpdateBulkData(List<FormData> lstFormData) {
        //writableDb.beginTransactionNonExclusive();
        try {
            ArrayList<Integer> lstUpdatedId = new ArrayList<>();
            insertOrUpdateSingleFormData(lstFormData, lstUpdatedId);
            Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
            intent.putIntegerArrayListExtra(Constants.UPDATED_WP_LIST, lstUpdatedId);
            context.sendBroadcast(intent);
            // writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            // writableDb.endTransaction();
        }
    }


    /**
     * Method to insert bulk data in table
     *
     * @param lstFormData list of tmForm data
     */
    public synchronized void insertBulkData(List<FormData> lstFormData) {
        writableDb.beginTransactionNonExclusive();
        //CustomLogKt.error("insertBulkData", "insertBulkData started");
        try {
            for (FormData form : lstFormData) {
                insertSingleFormData(form);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Method to insert or update data in form table
     */
    public void insertOrUpdateSingleFormData(List<FormData> lstFormData, ArrayList<Integer> lstUpdatedId) {
        writableDb.beginTransactionNonExclusive();
        try {
            for (FormData form : lstFormData) {
                insertOrUpdateData(form, lstUpdatedId);
            }
            writableDb.setTransactionSuccessful();
        } catch (
                Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }

    }


    private void insertOrUpdateData(FormData form, ArrayList<Integer> lstUpdatedId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_FORM_NAME, form.getFormName());
            initialValues.put(COLUMN_FORM_DATA, form.getFormData());
            initialValues.put(COLUMN_FORM_UPDATED, form.getFormUpdated());
            initialValues.put(COLUMN_FORM_CREATED, form.getFormCreated());
            initialValues.put(COLUMN_FORM_ID, form.getFormId());
            initialValues.put(COLUMN_PLOT_ON_MAP, form.isPlotOnMap() ? 1 : 0);
            initialValues.put(COLUMN_IS_CHECKIN_OUT, form.getIsCheckInOut() ? 1 : 0);
            initialValues.put(COLUMN_SITE_ID, form.getSiteId());
            initialValues.put(COLUMN_IS_CHECKIN_OUT_COMPLETE, form.isCheckInOutComplete() ? 1 : 0);
            initialValues.put(COLUMN_PRE_SELECT_SERVICE, form.isPreSelectServices() ? 1 : 0);
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            //initialValues.put(COLUMN_IS_SUB_FORM, form.isSubForm() ? 1 : 0);
            initialValues.put(COLUMN_HAS_SUB_FORM, form.hasSubForm() ? 1 : 0);

            int id = writableDb.update(TABLE_NAME, initialValues,
                    COLUMN_FORM_ID + "=? AND " + COLUMN_SITE_ID + "=? AND " + COLUMN_FORM_UPDATED + " =? AND " + COLUMN_FORM_SUBMITTED + "=? ",
                    new String[]{String.valueOf(form.getFormId()), String.valueOf(form.getSiteId()), String.valueOf(form.getFormUpdated()), String.valueOf(0)});
            if (id == 0) {
                initialValues.put(COLUMN_FORM_CREATED, form.getFormCreated());
                initialValues.put(COLUMN_FORM_CHECKIN_TIME, 0);
                initialValues.put(COLUMN_FORM_CHECKOUT_TIME, 0);
                initialValues.put(COLUMN_MODIFIED_FORM_DATA, form.getFormData());
                initialValues.put(COLUMN_IMAGE_DATA, form.getImageData());
                initialValues.put(COLUMN_CHECKIN_OUT_FORM_COMPLETE, 0);
                initialValues.put(COLUMN_CREW_IDS, toCommaSeparatedString(form.getCrewIds()));
                initialValues.put(COLUMN_HAS_SUB_FORM, 0);
                id = writableDb.update(TABLE_NAME, initialValues,
                        COLUMN_FORM_ID + "=? AND " + COLUMN_SITE_ID + "=? AND " + COLUMN_FORM_UPDATED
                                + " < ? AND " + COLUMN_FORM_SUBMITTED + "=? " + " < ? AND " + COLUMN_FORM_CHECKIN_TIME + "= ?",
                        new String[]{String.valueOf(form.getFormId()), String.valueOf(form.getSiteId()), String.valueOf(form.getFormUpdated()),
                                String.valueOf(0), String.valueOf(form.getCheckin_time())});
                if (id == 0) {
                    initialValues.remove(COLUMN_FORM_CHECKIN_TIME);
                    initialValues.remove(COLUMN_FORM_CHECKOUT_TIME);
                    initialValues.remove(COLUMN_MODIFIED_FORM_DATA);
                    initialValues.remove(COLUMN_IMAGE_DATA);
                    initialValues.remove(COLUMN_CHECKIN_OUT_FORM_COMPLETE);
                    initialValues.remove(COLUMN_FORM_CREATED);
                    initialValues.remove(COLUMN_CREW_IDS);
                    initialValues.remove(COLUMN_HAS_SUB_FORM);
                    id = writableDb.update(TABLE_NAME, initialValues,
                            COLUMN_FORM_ID + "=? AND " + COLUMN_SITE_ID + "=? AND " + COLUMN_FORM_UPDATED
                                    + " < ? AND " + COLUMN_FORM_SUBMITTED + "=?  AND " + COLUMN_FORM_CHECKIN_TIME + "> ?",
                            new String[]{String.valueOf(form.getFormId()), String.valueOf(form.getSiteId()), String.valueOf(form.getFormUpdated()),
                                    String.valueOf(0), String.valueOf(form.getCheckin_time())});
                    if (id == 0) {
                        initialValues.put(COLUMN_FORM_SUBMITTED, 0);
                        initialValues.put(COLUMN_CURRENT_LATITUDE, form.getLatitude());
                        initialValues.put(COLUMN_CURRENT_LONGITUDE, form.getLongitude());
                        initialValues.put(COLUMN_IMAGE_COUNT, 0);
                        initialValues.put(COLUMN_SIGNATURE_COUNT, 0);
                        initialValues.put(COLUMN_LAST_BUILDING_ID, form.getLastBuildingId());
                        initialValues.put(COLUMN_IMAGE_DATA, form.getImageData());
                        initialValues.put(COLUMN_FORM_CREATED, form.getFormCreated());
                        initialValues.put(COLUMN_FORM_CHECKIN_TIME, 0);
                        initialValues.put(COLUMN_FORM_CHECKOUT_TIME, 0);
                        initialValues.put(COLUMN_MODIFIED_FORM_DATA, form.getFormData());
                        initialValues.put(COLUMN_FORM_SUBMISSION_ID, StaticUtils.getUuid());
                        initialValues.put(COLUMN_CHECKIN_OUT_FORM_COMPLETE, 0);
                        initialValues.put(COLUMN_HAS_SUB_FORM, form.hasSubForm() ? 1 : 0);

                        if (form.getSubFormOtherData() != null)
                            initialValues.put(COLUMN_SUB_FORM_OTHER_DATA, new Gson().toJson(form.getSubFormOtherData()));
                        else
                            initialValues.put(COLUMN_SUB_FORM_OTHER_DATA, "");

                        initialValues.put(COLUMN_CREW_IDS, toCommaSeparatedString(form.getCrewIds()));

                        writableDb.insertWithOnConflict(TABLE_NAME, null, initialValues, SQLiteDatabase.CONFLICT_REPLACE);

                    } else {
                        lstUpdatedId.add((int) form.getFormId());
                    }
                }
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Method to insert data in form table
     *
     * @param formData form data
     */
    public void insertSingleFormData(FormData formData) {
        //writableDb.beginTransactionNonExclusive();
        try {
            ContentValues values = new ContentValues();
            values.put(COLUMN_FORM_ID, formData.getFormId());
            values.put(COLUMN_FORM_NAME, formData.getFormName());
            values.put(COLUMN_FORM_DATA, formData.getFormData());
            values.put(COLUMN_FORM_CREATED, formData.getFormCreated());
            values.put(COLUMN_FORM_UPDATED, formData.getFormUpdated());
            values.put(COLUMN_IMAGE_DATA, formData.getImageData());
            values.put(COLUMN_UPDATE_STATUS, 1);
            values.put(COLUMN_LAST_BUILDING_ID, formData.getLastBuildingId());
            values.put(COLUMN_FORM_SUBMITTED, 0);
            values.put(COLUMN_SITE_ID, formData.getSiteId());
            values.put(COLUMN_PLOT_ON_MAP, formData.isPlotOnMap() ? 1 : 0);
            values.put(COLUMN_MODIFIED_FORM_DATA, formData.getFormData());
            values.put(COLUMN_CURRENT_LATITUDE, formData.getLatitude());
            values.put(COLUMN_CURRENT_LONGITUDE, formData.getLatitude());
            values.put(COLUMN_IS_CHECKIN_OUT, formData.getIsCheckInOut());
            values.put(COLUMN_IS_CHECKIN_OUT_COMPLETE, formData.isCheckInOutComplete() ? 1 : 0);
            values.put(COLUMN_CHECKIN_OUT_FORM_COMPLETE, formData.isCheckInOutFormComplete() ? 1 : 0);
            values.put(COLUMN_IMAGE_COUNT, 0);
            values.put(COLUMN_SIGNATURE_COUNT, 0);
            values.put(COLUMN_FORM_CHECKOUT_TIME, 0);
            values.put(COLUMN_FORM_CHECKIN_TIME, 0);
            values.put(COLUMN_FORM_SUBMISSION_ID, StaticUtils.getUuid());
            values.put(COLUMN_CREW_IDS, toCommaSeparatedString(formData.getCrewIds()));
            values.put(COLUMN_PRE_SELECT_SERVICE, formData.isPreSelectServices() ? 1 : 0);
            values.put(COLUMN_IS_SUB_FORM, formData.isSubForm() ? 1 : 0);
            values.put(COLUMN_HAS_SUB_FORM, formData.hasSubForm() ? 1 : 0);
            values.put(COLUMN_MAIN_FORM_PK_ID, formData.getMainFormPkId());

            if (formData.getSubFormOtherData() != null)
                values.put(COLUMN_SUB_FORM_OTHER_DATA, new Gson().toJson(formData.getSubFormOtherData()));
            else
                values.put(COLUMN_SUB_FORM_OTHER_DATA, "");

            writableDb.insertWithOnConflict(TABLE_NAME, null, values, SQLiteDatabase.CONFLICT_REPLACE);
            //CustomLogKt.error("Insert", "FormData data");

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } /*finally {
            writableDb.setTransactionSuccessful();
            writableDb.endTransaction();
        }*/
    }
/*
    public ArrayList<Integer> getFormDataWithColumnUpdateStatusZero() {
        String rawQuery = "SELECT " + COLUMN_FORM_ID + " FROM " + TABLE_NAME + " WHERE " + COLUMN_UPDATE_STATUS + "='" + 0 + "' AND " + COLUMN_FORM_SUBMITTED + "='" + 0 + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        ArrayList<Integer> lstFormId = new ArrayList<>();
        if (cursor != null && cursor.moveToFirst()) {
            while (!cursor.isAfterLast()) {

                lstFormId.add((int) cursor.getFloat(cursor.getColumnIndex(COLUMN_FORM_ID)));
                cursor.moveToNext();
            }
        }

        return lstFormId;
    }*/

    /**
     * Method to get all form data
     *
     * @return list of forms
     */
    public List<FormData> getAllFormsBySiteId(long siteId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "=? " + "AND " + COLUMN_IS_SUB_FORM + "='0' " + " AND " + COLUMN_FORM_SUBMITTED + "='" + 0 + "' ORDER BY " + COLUMN_FORM_NAME + " COLLATE NOCASE ASC ";
        Cursor cursor = writableDb.rawQuery(rawQuery, new String[]{String.valueOf(siteId)});
        return getDataFromDB(cursor);
    }


    public List<FormData> getDataFromDB(Cursor cursor) {
        ArrayList<FormData> listForms = new ArrayList<>();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int idIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            int formNameIndex = cursor.getColumnIndex(COLUMN_FORM_NAME);
            int formDataIndex = cursor.getColumnIndex(COLUMN_FORM_DATA);
            int formImageDataIndex = cursor.getColumnIndex(COLUMN_IMAGE_DATA);
            int modifiedFormDataIndex = cursor.getColumnIndex(COLUMN_MODIFIED_FORM_DATA);
            int formCreatedIndex = cursor.getColumnIndex(COLUMN_FORM_CREATED);
            int formUpdatedIndex = cursor.getColumnIndex(COLUMN_FORM_UPDATED);
            int formSubmittedStatusIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMITTED);
            int formLatitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LATITUDE);
            int formLongitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LONGITUDE);
            int formImageCountIndex = cursor.getColumnIndex(COLUMN_IMAGE_COUNT);
            int formSignatureCountIndex = cursor.getColumnIndex(COLUMN_SIGNATURE_COUNT);
            int lastBuildingIdIndex = cursor.getColumnIndex(COLUMN_LAST_BUILDING_ID);
            int isCheckINOutIndex = cursor.getColumnIndex(COLUMN_IS_CHECKIN_OUT);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int submittedDateIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMITTED_DATE);
            int isCheckINOutCompleteIndex = cursor.getColumnIndex(COLUMN_IS_CHECKIN_OUT_COMPLETE);
            int checkINOutCompleteFormIndex = cursor.getColumnIndex(COLUMN_CHECKIN_OUT_FORM_COMPLETE);
            int formCheckInTimeIndex = cursor.getColumnIndex(COLUMN_FORM_CHECKIN_TIME);
            int formCheckOutTimeIndex = cursor.getColumnIndex(COLUMN_FORM_CHECKOUT_TIME);
            int plotOnMapIndex = cursor.getColumnIndex(COLUMN_PLOT_ON_MAP);
            int formCrewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            int preSelectFromIndex = cursor.getColumnIndex(COLUMN_PRE_SELECT_SERVICE);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            int isSubFormIndex = cursor.getColumnIndex(COLUMN_IS_SUB_FORM);
            int hasSubFormIndex = cursor.getColumnIndex(COLUMN_HAS_SUB_FORM);
            int subFormOtherDataIndex = cursor.getColumnIndex(COLUMN_SUB_FORM_OTHER_DATA);
            int isFormUploadedIndex = cursor.getColumnIndex(COLUMN_FORM_UPLOADED);
            int mainFormPKIdIndex = cursor.getColumnIndex(COLUMN_MAIN_FORM_PK_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    FormData form = new FormData();
                    form.setFormPKId(cursor.getInt(pkIdIndex));
                    form.setFormId(cursor.getInt(idIndex));
                    form.setFormName(cursor.getString(formNameIndex));
                    form.setModifiedFormData(cursor.getString(modifiedFormDataIndex));
                    form.setFormData(cursor.getString(formDataIndex));
                    form.setImageData(cursor.getString(formImageDataIndex));
                    form.setFormCreated(cursor.getLong(formCreatedIndex));
                    form.setFormUpdated(cursor.getLong(formUpdatedIndex));
                    form.setFormSubmitted(cursor.getInt(formSubmittedStatusIndex) != 0);
                    form.setLatitude(cursor.getDouble(formLatitudeIndex));
                    form.setLongitude(cursor.getDouble(formLongitudeIndex));
                    form.setImageCount(cursor.getInt(formImageCountIndex));
                    form.setSignatureCount(cursor.getInt(formSignatureCountIndex));
                    form.setLastBuildingId(cursor.getInt(lastBuildingIdIndex));
                    form.setIsCheckInOut(cursor.getInt(isCheckINOutIndex) > 0);
                    form.setCheckin_time(cursor.getLong(formCheckInTimeIndex));
                    form.setCheckout_time(cursor.getLong(formCheckOutTimeIndex));
                    form.setCheckInOutComplete(cursor.getInt(isCheckINOutCompleteIndex) > 0);
                    form.setCheckInOutFormComplete(cursor.getInt(checkINOutCompleteFormIndex) > 0);
                    form.setSiteId(cursor.getLong(siteIdIndex));
                    form.setSf_submited(cursor.getInt(submittedDateIndex));
                    form.setCrewIds(DBUtils.getIdFromString(cursor.getString(formCrewIdsIndex)));
                    form.setPlotOnMap(cursor.getInt(plotOnMapIndex) > 0);
                    form.setPreSelectServices(cursor.getInt(preSelectFromIndex) > 0);
                    form.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                    form.setSubForm(cursor.getInt(isSubFormIndex) > 0);
                    form.setHasSubForm(cursor.getInt(hasSubFormIndex) > 0);
                    form.setFormUploaded(cursor.getInt(isFormUploadedIndex) > 0);
                    form.setMainFormPkId(cursor.getInt(mainFormPKIdIndex));
                    form.setSubFormOtherData(new Gson().fromJson(cursor.getString(subFormOtherDataIndex), SubFormOtherData.class));
                    listForms.add(form);
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return listForms;
    }

    public FormData getFormDataByPKId(int pkId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + "='" + pkId + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    public FormData getLastFormData() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " ORDER BY " + COLUMN_FORM_PK_ID + " DESC LIMIT 1";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }


    public List<Integer> getAllFormIds() {
        String rawQuery = "SELECT DISTINCT " + COLUMN_FORM_ID + " FROM " + TABLE_NAME;
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        List<Integer> lstFormIds = new ArrayList<>();
        try {
            int siteIdIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstFormIds.add(cursor.getInt(siteIdIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstFormIds;
    }


    public FormData getDataFromSiteAndFormId(long siteId, long formId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='" + siteId + "' AND " + COLUMN_FORM_ID + "='" + formId + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }


    public FormData getSingleDataFromDB(Cursor cursor) {
        FormData form = null;
        try {
            form = new FormData();
            int pkIdIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
            int idIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
            int formNameIndex = cursor.getColumnIndex(COLUMN_FORM_NAME);
            int formDataIndex = cursor.getColumnIndex(COLUMN_FORM_DATA);
            int formImageDataIndex = cursor.getColumnIndex(COLUMN_IMAGE_DATA);
            int modifiedFormDataIndex = cursor.getColumnIndex(COLUMN_MODIFIED_FORM_DATA);
            int formCreatedIndex = cursor.getColumnIndex(COLUMN_FORM_CREATED);
            int formUpdatedIndex = cursor.getColumnIndex(COLUMN_FORM_UPDATED);
            int formSubmittedStatusIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMITTED);
            int formLatitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LATITUDE);
            int formLongitudeIndex = cursor.getColumnIndex(COLUMN_CURRENT_LONGITUDE);
            int lastBuildingIdIndex = cursor.getColumnIndex(COLUMN_LAST_BUILDING_ID);
            int isCheckINOutIndex = cursor.getColumnIndex(COLUMN_IS_CHECKIN_OUT);
            int isCheckINOutCompleteIndex = cursor.getColumnIndex(COLUMN_IS_CHECKIN_OUT_COMPLETE);
            int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
            int submittedDateIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMITTED_DATE);
            int checkINOutCompleteFormIndex = cursor.getColumnIndex(COLUMN_CHECKIN_OUT_FORM_COMPLETE);
            int formCheckInTimeIndex = cursor.getColumnIndex(COLUMN_FORM_CHECKIN_TIME);
            int formCheckOutTimeIndex = cursor.getColumnIndex(COLUMN_FORM_CHECKOUT_TIME);
            int formCrewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            int plotOnMapIndex = cursor.getColumnIndex(COLUMN_PLOT_ON_MAP);
            int preSelectFromIndex = cursor.getColumnIndex(COLUMN_PRE_SELECT_SERVICE);
            int formSubmissionIdIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
            int isSubFormIndex = cursor.getColumnIndex(COLUMN_IS_SUB_FORM);
            int subFormOtherDataIndex = cursor.getColumnIndex(COLUMN_SUB_FORM_OTHER_DATA);
            int hasSubFormIndex = cursor.getColumnIndex(COLUMN_HAS_SUB_FORM);
            int isFormUploadedIndex = cursor.getColumnIndex(COLUMN_FORM_UPLOADED);
            int mainFormPKIdIndex = cursor.getColumnIndex(COLUMN_MAIN_FORM_PK_ID);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    form.setFormPKId(cursor.getInt(pkIdIndex));
                    form.setFormId(cursor.getInt(idIndex));
                    form.setFormName(cursor.getString(formNameIndex));
                    form.setModifiedFormData(cursor.getString(modifiedFormDataIndex));
                    form.setFormData(cursor.getString(formDataIndex));
                    form.setImageData(cursor.getString(formImageDataIndex));
                    form.setFormCreated(cursor.getLong(formCreatedIndex));
                    form.setFormUpdated(cursor.getLong(formUpdatedIndex));
                    form.setLastBuildingId(cursor.getInt(lastBuildingIdIndex));
                    form.setFormSubmitted(cursor.getInt(formSubmittedStatusIndex) != 0);
                    form.setLatitude(cursor.getDouble(formLatitudeIndex));
                    form.setLongitude(cursor.getDouble(formLongitudeIndex));
                    form.setIsCheckInOut(cursor.getInt(isCheckINOutIndex) > 0);
                    form.setCheckInOutComplete(cursor.getInt(isCheckINOutCompleteIndex) > 0);
                    form.setCheckInOutFormComplete(cursor.getInt(checkINOutCompleteFormIndex) > 0);
                    form.setSiteId(cursor.getLong(siteIdIndex));
                    form.setCheckin_time(cursor.getLong(formCheckInTimeIndex));
                    form.setCheckout_time(cursor.getLong(formCheckOutTimeIndex));
                    form.setSf_submited(cursor.getInt(submittedDateIndex));
                    form.setCheckin_time(cursor.getLong(formCheckInTimeIndex));
                    form.setCheckout_time(cursor.getLong(formCheckOutTimeIndex));
                    form.setCrewIds(DBUtils.getIdFromString(cursor.getString(formCrewIdsIndex)));
                    form.setPlotOnMap(cursor.getInt(plotOnMapIndex) > 0);
                    form.setPreSelectServices(cursor.getInt(preSelectFromIndex) > 0);
                    form.setFormSubmissionId(cursor.getString(formSubmissionIdIndex));
                    form.setHasSubForm(cursor.getInt(hasSubFormIndex) > 0);
                    form.setSubForm(cursor.getInt(isSubFormIndex) > 0);
                    form.setFormUploaded(cursor.getInt(isFormUploadedIndex) > 0);
                    form.setMainFormPkId(cursor.getInt(mainFormPKIdIndex));
                    form.setSubFormOtherData(new Gson().fromJson(cursor.getString(subFormOtherDataIndex), SubFormOtherData.class));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return form;

    }

    public FormData getDataByCrewId(int crewId) {
        //select * from tblTmForm where (',' || crewIds || ',') LIKE '%,1042,%'
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_SUBMITTED + "='" + 0 + "' AND " + COLUMN_IS_CHECKIN_OUT + "='1' AND " + "(',' " + " || " + COLUMN_CREW_IDS + " || " + " ',') LIKE '%," + crewId + ",%'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }

    public void resetFormDataByPkId(int formPkId, String formData, boolean isCheckInOutComplete) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_MODIFIED_FORM_DATA, formData);
            initialValues.put(COLUMN_IMAGE_DATA, "");
            initialValues.put(COLUMN_FORM_SUBMITTED, 0);
            initialValues.put(COLUMN_CURRENT_LATITUDE, 0.0);
            initialValues.put(COLUMN_CURRENT_LONGITUDE, 0.0);
            initialValues.put(COLUMN_IMAGE_COUNT, 0);
            initialValues.put(COLUMN_SIGNATURE_COUNT, 0);
            initialValues.put(COLUMN_FORM_CHECKIN_TIME, 0);
            initialValues.put(COLUMN_FORM_CHECKOUT_TIME, 0);
            initialValues.put(COLUMN_LAST_BUILDING_ID, 0);
            initialValues.put(COLUMN_HAS_SUB_FORM, 0);
            initialValues.put(COLUMN_IS_CHECKIN_OUT_COMPLETE, isCheckInOutComplete);
            initialValues.put(COLUMN_CREW_IDS, "");
            initialValues.put(COLUMN_FORM_SUBMISSION_ID, StaticUtils.getUuid());
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=? AND " + COLUMN_FORM_SUBMITTED + "=? ", new String[]{String.valueOf(formPkId), String.valueOf(0)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateModifiedFormByPkId(int formPkId, String modifiedData) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_MODIFIED_FORM_DATA, modifiedData);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=?  ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateImageDataByPkId(int formPkId, String imageData) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IMAGE_DATA, imageData);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateImageCountByPkId(int formPkId, int count) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_IMAGE_COUNT, count);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateFormSubmittedStatus(long formPkId, double latitude, double longitude) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CURRENT_LATITUDE, latitude);
            initialValues.put(COLUMN_CURRENT_LONGITUDE, longitude);
            initialValues.put(COLUMN_FORM_SUBMITTED, 1);
            initialValues.put(COLUMN_FORM_SUBMITTED_DATE, System.currentTimeMillis() / 1000);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateLastBuildingIdByPkId(long formPkId, int lastBuildingId) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_LAST_BUILDING_ID, lastBuildingId);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public void updateCheckInOutFormByPkId(long formPkId, boolean isFormCompleted) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CHECKIN_OUT_FORM_COMPLETE, isFormCompleted ? 1 : 0);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Method to get Form updated time from table to compare it with the time from server response.
     *
     * @param id
     * @return
     */
    public long getFormUpdatedTime(int id, long siteId) {
        String rawQuery = "SELECT " + COLUMN_FORM_UPDATED + " FROM " + TABLE_NAME
                + " WHERE " + COLUMN_FORM_ID + "='" + id + "'" + " AND " + COLUMN_SITE_ID + "='" + siteId + "'" + " AND " + COLUMN_FORM_SUBMITTED + "='" + 0 + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        long formUpdatedTime = 0;
        try {

            int formUpdatedTimeIndex = cursor.getColumnIndex(COLUMN_FORM_UPDATED);
            //if (cursor.moveToNext()) {
            while (cursor.moveToNext()) {
                formUpdatedTime = cursor.getLong(formUpdatedTimeIndex);
                //cursor.moveToNext();
            }
            //}
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return formUpdatedTime;
    }


    /**
     * method to delete single entry from table.
     */
    public void deleteDataByPKId(long formPKId) {
        try {
            writableDb.delete(TABLE_NAME, COLUMN_FORM_PK_ID + "=? ", new String[]{String.valueOf(formPKId)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Delete all data from the table
     */
    public synchronized void deleteOldData(long lastUpdate) {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("DELETE  FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_SUBMITTED + "='0' AND " + COLUMN_FORM_CREATED + " < " + lastUpdate);
            writableDb.setTransactionSuccessful();
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        } finally {

            writableDb.endTransaction();
        }
    }

    public void updateFormStatus(int status) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, status);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_SUBMITTED + "=? ", new String[]{String.valueOf(0)});
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    public ArrayList<CheckedInMapFormDetails> getFormDataWithColumnUpdateStatusZero() {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_UPDATE_STATUS + "='" + 0 + "' AND " + COLUMN_FORM_SUBMITTED + "='" + 0 + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        return getCheckedInMapFormDetail(cursor);
    }

    public ArrayList<CheckedInMapFormDetails> getAllDataByFormPKIds(List<String> lstFormPkId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + " IN (" + DBUtils.toCommaSeparated(lstFormPkId) + ")";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        return getCheckedInMapFormDetail(cursor);
    }

    private  ArrayList<CheckedInMapFormDetails> getCheckedInMapFormDetail( Cursor cursor){
        ArrayList<CheckedInMapFormDetails> lstData = new ArrayList<>();
        int idIndex = cursor.getColumnIndex(COLUMN_FORM_ID);
        int formPKIDIndex = cursor.getColumnIndex(COLUMN_FORM_PK_ID);
        int siteIdIndex = cursor.getColumnIndex(COLUMN_SITE_ID);
        int formNameIndex = cursor.getColumnIndex(COLUMN_FORM_NAME);
        int formSubmissionIndex = cursor.getColumnIndex(COLUMN_FORM_SUBMISSION_ID);
        if (cursor.moveToFirst()) {
            while (!cursor.isAfterLast()) {
                CheckedInMapFormDetails checkedInMapFormDetails = new CheckedInMapFormDetails();
                checkedInMapFormDetails.setFormPkId(cursor.getInt(formPKIDIndex));
                checkedInMapFormDetails.setSiteId(cursor.getInt(siteIdIndex));
                checkedInMapFormDetails.setFormId(cursor.getInt(idIndex));
                checkedInMapFormDetails.setFormName(cursor.getString(formNameIndex));
                checkedInMapFormDetails.setFormSubmissionId(cursor.getString(formSubmissionIndex));
                lstData.add(checkedInMapFormDetails);
                cursor.moveToNext();
            }
        }
        return lstData;
    }

    /**
     * Delete all old data from TM form table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=? AND " + COLUMN_FORM_SUBMITTED + "=? ", new String[]{String.valueOf(0), String.valueOf(0)});
            writableDb.setTransactionSuccessful();

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {


            writableDb.endTransaction();
        }
    }


    /**
     * Delete all old data from form table
     */
    public void removeAllDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_FORM_SUBMITTED + "=? ", new String[]{String.valueOf(0)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    /**
     * Delete all data from form table
     */
    public void deleteDataFromFormTable() {
        try {
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    /**
     * Delete all not submitted  data from form table
     */
    public void removeNotSubmittedDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_FORM_SUBMITTED + "=? ", new String[]{String.valueOf(0)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            writableDb.endTransaction();
        }
    }

    public void updateCrewInForm(long formPkId, List<Integer> lstSelectedCrew) {
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_CREW_IDS, StaticUtils.getStringFromIntegerList(lstSelectedCrew));
            writableDb.update(TABLE_NAME, initialValues, COLUMN_FORM_PK_ID + " =? ", new String[]{String.valueOf(formPkId)});

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    public List<Integer> getFormCrewData(long formPkId) {
        String rawQuery = "SELECT " + COLUMN_CREW_IDS + " FROM " + TABLE_NAME + " WHERE " + COLUMN_FORM_PK_ID + "='" + formPkId + "'";
        Cursor cursor = writableDb.rawQuery(rawQuery, null);
        List<Integer> lstCrews = new ArrayList<>();
        try {
            int formCrewIdsIndex = cursor.getColumnIndex(COLUMN_CREW_IDS);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    lstCrews = DBUtils.getIdFromString(cursor.getString(formCrewIdsIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstCrews;
    }

    public boolean isAllCompletedFormCount(long siteId) {

        //Example
        /*select case when (select count(*) from tblTMForms  where CheckInOutFormComplete = 1  and isCheckInOutComplete = 1 and siteId = 110840 and formSubmitted = 0 )
        = (select count(*) from tblTMForms where siteId = 110840 and formSubmitted = 0) then 1 else 0 end as RowCountResult*/


        String query = "SELECT CASE WHEN ( SELECT COUNT(*) FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='"
                + siteId + "'" + " AND " + COLUMN_FORM_SUBMITTED + "='0' " + " AND " + COLUMN_IS_CHECKIN_OUT + "='1' " + " AND " + COLUMN_CHECKIN_OUT_FORM_COMPLETE + "='1' " + " AND " + COLUMN_IS_CHECKIN_OUT_COMPLETE + "='1') " +
                "= ( SELECT COUNT(*) FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='"
                + siteId + "'  AND " + COLUMN_FORM_SUBMITTED + "='0') THEN  1 ELSE 0 END AS   RowCountResult";


        /*String query1 = "SELECT count(*) FROM " + TABLE_NAME + " WHERE " + COLUMN_SITE_ID + "='"
                + siteId + "'" + " AND " + COLUMN_CHECKIN_OUT_FORM_COMPLETE + "='1' "+ " AND " + COLUMN_IS_CHECKIN_OUT_COMPLETE + "='1'";*/
        Cursor cursor = null;
        int count = 0;
        try {
            cursor = writableDb.rawQuery(query, null);
            int rowCountResultIndex = cursor.getColumnIndex("RowCountResult");
            if (cursor.moveToFirst()) {
                count = cursor.getInt(rowCountResultIndex);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return count >= 1;
    }

    public boolean dataExist() {
        try {
            String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
            Cursor cursor = writableDb.rawQuery(query, null);
            cursor.moveToFirst();
            if (cursor.getInt(0) == 1) {
                cursor.close();
                return true;
            } else {
                cursor.close();
                return false;
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        return false;
    }


    public List<FormData> getAllSubFormsByFormPKId(int formPkId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "=? " + " AND " + COLUMN_IS_SUB_FORM + "='1'" + " AND " + COLUMN_FORM_CHECKIN_TIME + "> '0'";
        Cursor cursor = writableDb.rawQuery(rawQuery, new String[]{String.valueOf(formPkId)});
        return getDataFromDB(cursor);
    }


    public List<FormData> getAllSubmittedSubFormsByMainFormPKId(int mainFormPKId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "=? " + " AND " + COLUMN_IS_SUB_FORM + "='1'" + " AND " + COLUMN_FORM_SUBMITTED + "='1'" + " AND " + COLUMN_FORM_UPLOADED + "= '1'";
        Cursor cursor = writableDb.rawQuery(rawQuery, new String[]{String.valueOf(mainFormPKId)});
        return getDataFromDB(cursor);
    }

    public List<FormData> getAllOnlyCheckedInSubForms(int mainFormPKId) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_MAIN_FORM_PK_ID + "=? " + " AND " + COLUMN_IS_SUB_FORM + "='1'" + " AND " + COLUMN_FORM_CHECKIN_TIME + "> '0'" + " AND " + COLUMN_FORM_SUBMITTED + "='0'";
        Cursor cursor = writableDb.rawQuery(rawQuery, new String[]{String.valueOf(mainFormPKId)});
        return getDataFromDB(cursor);
    }
}
