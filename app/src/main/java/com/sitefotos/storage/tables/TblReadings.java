package com.sitefotos.storage.tables;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.storage.DBOpenHelper;

//Removed from app version 2.4.7
public class TblReadings{

    public static final String TABLE_NAME = "tblReadings";

    private static final String COLUMN_NAME_SENSOR_ID = "sensorid";
    private static final String COLUMN_NAME_METER_NAME = "meter_name";
    private static final String COLUMN_NAME_HOLE_DEPTH = "hole_depth";
    private static final String COLUMN_NAME_TARGET_RH = "target_rh";
    private static final String COLUMN_NAME_DESC = "desc";
    private static final String COLUMN_NAME_CREATEDAT = "createdAt";
    private static final String COLUMN_NAME_UPDATEDAT = "updatedAt";


    private final static String GET_READINGS_BY_SENSOR_ID = "SELECT * FROM " + TABLE_NAME + " WHERE " + COLUMN_NAME_SENSOR_ID + "= ?";

    private Context context;
    private SQLiteDatabase writableDb;

    public TblReadings(Context context) {
        this.context = context;
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }
    }


    public static String createTable() {
        return  "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_NAME_SENSOR_ID + "` VARCHAR PRIMARY KEY NOT NULL UNIQUE , " +
                " `" + COLUMN_NAME_METER_NAME + "` TEXT, " +
                " `" + COLUMN_NAME_HOLE_DEPTH + "` TEXT, " +
                " `" + COLUMN_NAME_TARGET_RH + "` TEXT, " +
                " `" + COLUMN_NAME_DESC + "` TEXT, " +
                " `" + COLUMN_NAME_UPDATEDAT + "` TEXT, " +
                " `" + COLUMN_NAME_CREATEDAT + "` TEXT " + " ) ";
    }

}
