package com.sitefotos.storage.tables;

import static com.sitefotos.util.DBUtils.dataExist;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.SQLException;
import android.database.sqlite.SQLiteDatabase;

import com.sitefotos.models.Equipments;
import com.sitefotos.models.Vehicles;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;

import java.util.ArrayList;
import java.util.List;

//Added this table in app version 2.4.7
public class TblEquipment {

    public static final String TABLE_NAME = "tblEquipment";
    private static final String COLUMN_PK_ID = "pkId";
    private static final String COLUMN_EQUIPMENT_ID = "equipmentId";
    private static final String COLUMN_EQUIPMENT_NAME = "equipmentName";
    private static final String COLUMN_CREATED = "created";
    private static final String COLUMN_UPDATED = "updated";
    private static final String COLUMN_UPDATE_STATUS = "c_update_status";

    private Context context;
    private SQLiteDatabase writableDb;
    private SQLiteDatabase readableDb;

    public TblEquipment(Context context) {
        if (writableDb == null) {
            writableDb = DBOpenHelper.getInstance(context).getWritableDatabase();
        }

        if (readableDb == null) {
            readableDb = DBOpenHelper.getInstance(context).getReadableDatabase();
        }
        this.context = context;
    }


    public static String createTable() {
        return "CREATE TABLE IF NOT EXISTS " + TABLE_NAME + " ( " +
                " `" + COLUMN_PK_ID + "` integer PRIMARY KEY AUTOINCREMENT, " +
                " `" + COLUMN_EQUIPMENT_ID + "` INTEGER , " +
                " `" + COLUMN_EQUIPMENT_NAME + "` TEXT, " +
                " `" + COLUMN_CREATED + "` INTEGER, " +
                " `" + COLUMN_UPDATED + "` INTEGER, " +
                " `" + COLUMN_UPDATE_STATUS + "` INTEGER " + " ) ";

    }


    private int checkIsColumnExist(String columnName) {
        Cursor cursor = null;
        int index = -1;
        try {
            String rawQuery = "SELECT * FROM " + TABLE_NAME;
            cursor = readableDb.rawQuery(rawQuery, null);
            index = cursor.getColumnIndex(columnName);
            return index;
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return index;
    }

    /**
     * Method to insert bulk data in the table
     *
     * @param lstEquipment list of Equipments
     */
    public synchronized void insertEquipmentBulkData(List<Equipments> lstEquipment) {
        try {
            writableDb.beginTransactionNonExclusive();
            for (Equipments equipment : lstEquipment) {
                insertData(equipment);
            }
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }finally {

            writableDb.endTransaction();
        }
    }

    /**
     * Method to insert data in table
     */
    public void insertData(Equipments data) {
        writableDb.beginTransactionNonExclusive();
        try {
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_EQUIPMENT_ID, data.getSeiId());
            initialValues.put(COLUMN_EQUIPMENT_NAME, data.getSeiEquipmentName());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATE_STATUS, 1);
            writableDb.insertOrThrow(TABLE_NAME, null, initialValues);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }

    public synchronized void updateData(long id, Equipments data) {
        writableDb.beginTransactionNonExclusive();
        ContentValues initialValues = new ContentValues();
        try {
            initialValues.put(COLUMN_EQUIPMENT_ID, data.getSeiId());
            initialValues.put(COLUMN_EQUIPMENT_NAME, data.getSeiEquipmentName());
            initialValues.put(COLUMN_CREATED, System.currentTimeMillis());
            initialValues.put(COLUMN_UPDATED, System.currentTimeMillis());
            writableDb.update(TABLE_NAME, initialValues, COLUMN_PK_ID + "=?",
                    new String[]{String.valueOf(id)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {

            writableDb.endTransaction();
        }
    }



    public Equipments getDataFromTableById(Long id) {
        String rawQuery = "SELECT * FROM " + TABLE_NAME + " WHERE "+ COLUMN_EQUIPMENT_ID +  "='" + id + "'" ;
        Cursor cursor = readableDb.rawQuery(rawQuery, null);
        return getSingleDataFromDB(cursor);
    }


    private Equipments getSingleDataFromDB(Cursor cursor) {
        Equipments data = new Equipments();
        try {
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int equipmentIdIndex = cursor.getColumnIndex(COLUMN_EQUIPMENT_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_EQUIPMENT_NAME);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setSeiId(cursor.getInt(equipmentIdIndex));
                    data.setSeiEquipmentName(cursor.getString(nameIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    cursor.moveToNext();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
            
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }

        return data;

    }

    public void updateDataStatus(int updateStatus) {
        try {
            writableDb.beginTransactionNonExclusive();
            ContentValues initialValues = new ContentValues();
            initialValues.put(COLUMN_UPDATE_STATUS, updateStatus);
            writableDb.update(TABLE_NAME, initialValues, COLUMN_UPDATE_STATUS + "=?", new String[]{String.valueOf(1)});
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }


    /**
     * Method to get all Equipment data
     *
     * @return list of Equipment
     */
    public List<Equipments> getAllEquipments() {
        Cursor cursor = null;
        ArrayList<Equipments> lstEquipment = new ArrayList<>();
        try {
            cursor = readableDb.rawQuery("SELECT * FROM " + TABLE_NAME, null);
            int pkIdIndex = cursor.getColumnIndex(COLUMN_PK_ID);
            int equipmentIdIndex = cursor.getColumnIndex(COLUMN_EQUIPMENT_ID);
            int nameIndex = cursor.getColumnIndex(COLUMN_EQUIPMENT_NAME);
            int createdIndex = cursor.getColumnIndex(COLUMN_CREATED);
            int updatedIndex = cursor.getColumnIndex(COLUMN_UPDATED);
            if (cursor.moveToFirst()) {
                while (!cursor.isAfterLast()) {
                    Equipments data = new Equipments();
                    data.setPkId(cursor.getInt(pkIdIndex));
                    data.setSeiId(cursor.getInt(equipmentIdIndex));
                    data.setSeiEquipmentName(cursor.getString(nameIndex));
                    data.setCreatedDate(cursor.getLong(createdIndex));
                    data.setUpdatedDate(cursor.getLong(updatedIndex));
                    lstEquipment.add(data);
                    cursor.moveToNext();
                }
            }
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return lstEquipment;
    }

    /**
     * Delete all data from table
     */
    public void deleteDataFromTable() {
        try {
            //writableDb.beginTransactionNonExclusive();
            writableDb.execSQL("delete from " + TABLE_NAME);
        } catch (SQLException e) {
            FirebaseEventUtils.logException(e);
            
        }
    }

    public int getDataCount() {
        String query = "SELECT count(*) FROM " + TABLE_NAME;
        int count = 0;
        Cursor cursor = null;
        try {
            cursor = readableDb.rawQuery(query, null);
            cursor.moveToFirst();
            count = cursor.getInt(0);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return count;
    }

    /**
     * Delete all old data from table
     */
    public void removeOldDataFromTable() {
        try {
            writableDb.beginTransactionNonExclusive();
            writableDb.delete(TABLE_NAME, COLUMN_UPDATE_STATUS + "=' 0 '", null);
            writableDb.setTransactionSuccessful();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            writableDb.endTransaction();
        }
    }

    public boolean isDataExist() {
        String query = "SELECT EXISTS (SELECT * FROM " + TABLE_NAME + " LIMIT 1) ";
        return dataExist(readableDb, query);
    }


}
