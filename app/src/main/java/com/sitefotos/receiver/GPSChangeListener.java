package com.sitefotos.receiver;

import static android.content.Context.LOCATION_SERVICE;
import static com.sitefotos.Constants.LOGGED_IN_PARAM_IS_LOCATION_REQUIRED;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.location.LocationManager;
import android.text.TextUtils;

import com.sitefotos.BaseApplication;
import com.sitefotos.PermissionOverlayActivity;
import com.sitefotos.event.GPSSettingEventEvent;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.logger.CustomLogKt;

import org.greenrobot.eventbus.EventBus;

public class GPSChangeListener extends BroadcastReceiver {

    private boolean isProviderEnabled = false;
    private boolean isProviderDisabled = false;
    private Context context;

    @Override
    public void onReceive(Context context, Intent intent) {
        this.context = context;
        if (intent.getAction() == null)
            return;
        if (intent.getAction().matches("android.location.PROVIDERS_CHANGED")) {
            LocationManager locationManager = (LocationManager) context.getSystemService(LOCATION_SERVICE);
            if (locationManager == null)
                return;
            if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                doTaskWhenGPSIsEnabled();
            } else {
                doTaskWhenGPSIsDisabled();
            }
        }
    }

    /**
     * When GPS is enabled from settings,
     * Here we used isProviderEnabled flag to prevent multiple times event call. when GPS settings change, onReceive method
     * calls multiple times.
     * Send GPS enable status to main screen and Permission screen if AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) != 0
     */
    private void doTaskWhenGPSIsEnabled() {
        if (!isProviderEnabled) {
            CustomLogKt.errorLog("About GPS", "GPS is Enabled in your device");
            isProviderEnabled = true;
            isProviderDisabled = false;
            EventBus.getDefault().post(new GPSSettingEventEvent(GPSSettingEventEvent.GPSStatus.Enabled));
        }
    }

    /**
     * When GPS is disabled from settings,
     * Here we used isProviderEnabled flag to prevent multiple times event call. when GPS settings change, onReceive method
     * calls multiple times.
     * Send GPS disable status to Permission screen if AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) != 0
     */
    private void doTaskWhenGPSIsDisabled() {
        if (!isProviderDisabled) {
            isProviderDisabled = true;
            isProviderEnabled = false;
            if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 0) {
                return;
            }
            if (AppPrefShared.getBoolean("isAppInBackground", true))
                return;

            if (!TextUtils.isEmpty(BaseApplication.getInstance().getTopActivityName()) && !TextUtils.isEmpty(PermissionOverlayActivity.class.getCanonicalName()) && BaseApplication.getInstance().getTopActivityName() != null && BaseApplication.getInstance().getTopActivityName().equalsIgnoreCase(PermissionOverlayActivity.class.getCanonicalName())) {
                EventBus.getDefault().post(new GPSSettingEventEvent(GPSSettingEventEvent.GPSStatus.Disabled));
            } else {
                navigateToOverlayScreen();
            }
        }
    }

    /**
     * Method to navigate user to permission screen if GPS is disable and  AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) != 0
     */
    private void navigateToOverlayScreen() {
        Intent intent = new Intent(context, PermissionOverlayActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }
}
