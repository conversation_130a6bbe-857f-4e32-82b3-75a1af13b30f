package com.sitefotos.form;

import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.PARAM_SUBMITTED_FOR;
import static com.sitefotos.Constants.SUB_FORM_CREATE_REQUEST_CODE;
import static com.sitefotos.util.ImageUtil.deleteImagesFromStorage;
import static com.sitefotos.util.StaticUtils.isDeviceLocked;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.ImageViewAdapter;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityFormDetailBinding;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.FormData;
import com.sitefotos.models.ImageData;
import com.sitefotos.models.Material;
import com.sitefotos.models.PanelData;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.models.ValidationData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.RejectedExecutionException;

import retrofit2.Response;

public class FormDetailActivity extends BaseFormDetailActivity implements OnAppDataApiResponse, View.OnClickListener {
    //    private boolean isLocationSettingsRequested = false;
    // Use GPS to get the location details here.
    private ActivityFormDetailBinding binding;

    private final BroadcastReceiver formUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (context != null) {
                if (intent.hasExtra(Constants.DELETED_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_FORM_ALL_DATA, false)) {
                        finish();
                        return;
                    }
                }
                if (intent.hasExtra(Constants.UPDATED_FORM_LIST)) {
                    ArrayList<Integer> lstUpdatedForm = intent.getIntegerArrayListExtra(Constants.UPDATED_FORM_LIST);
                    for (Integer formId : lstUpdatedForm != null ? lstUpdatedForm : new ArrayList<Integer>()) {
                        if (formId == formData.getFormId()) {
                            recreate();
                        }
                    }
                }
                if (intent.hasExtra(Constants.DELETED_FORM_LIST)) {
                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_FORM_LIST);
                    for (Integer formId : lstDeletedFormId != null ? lstDeletedFormId : new ArrayList<Integer>()) {
                        if (formId == formData.getFormId()) {
                            finish();
                        }
                    }
                }
            }
        }
    };
    private final BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            int formPkId = intent.getIntExtra("formPkId", -1);
            if (formPkId == mFormPkId) {
                concatImageData();
            }
        }
    };


    @Override
    boolean isTMForm() {
        return false;
    }

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        initBinding();
        setOnClickListener();
        setActionBarVisibility(false);
        startLocationServiceIfNotStarted();
        startProgress();
        new Handler().postDelayed(this::init, 300);
    }

    private void initBinding() {
        binding = ActivityFormDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void setOnClickListener() {
        binding.tlOther.imgBtnBack.setOnClickListener(this);
        binding.btnPrevious.setOnClickListener(this);
        binding.btnNext.setOnClickListener(this);
        binding.tlOther.ivSecondRight.setOnClickListener(this);
        binding.tlOther.ivPlotMap.setOnClickListener(this);
        binding.ivCamera.setOnClickListener(this);
    }

    private void init() {
        registerAppReceiver(mMessageReceiver, new IntentFilter(Constants.INTERNAL_IMAGE_BROADCAST));
        registerAppReceiver(formUpdateReceiver, new IntentFilter(Constants.INTERNAL_FORM_BROADCAST));
        vfPages = binding.vfPages;
        if (getBundleData()) {
            binding.vfPages.removeAllViews();
            setViewFlipperAnimation();
            readJsonFile(binding.vfPages);
            setData();
            binding.rlBottom.setVisibility(View.VISIBLE);
            if (returnFromSubFrom) {
                changePage(lastOpenedPage);
            }
        } else {
            stopProgressDialog();
            binding.tlOther.tvTitle.setText(formData.getFormName());
        }
        //setSiteAndFormBroadCrumbs(6, -2, formData.getFormId(), formData.getFormName());
        setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(this, binding.tlOther.llTitle);

        stopProgressDialog();
    }


    private void setViewFlipperAnimation() {
        leftOutAnimation = AnimationUtils.loadAnimation(this, R.anim.left_out);
        leftInAnimation = AnimationUtils.loadAnimation(this, R.anim.left_in);
        rightInAnimation = AnimationUtils.loadAnimation(this, R.anim.right_in);
        rightOutAnimation = AnimationUtils.loadAnimation(this, R.anim.right_out);

    }

    private void setFlipAnimation(boolean isNext) {
        if (isNext) {
            binding.vfPages.setInAnimation(rightInAnimation);
            binding.vfPages.setOutAnimation(leftOutAnimation);
        } else {
            binding.vfPages.setInAnimation(leftInAnimation);
            binding.vfPages.setOutAnimation(rightOutAnimation);
        }
    }

    private boolean getBundleData() {

        if (returnFromSubFrom) {
            formData = getFormData(formData.getFormPKId());
            return true;
        }
        if (getIntent() != null) {
            if (getIntent().hasExtra("buildingId")) {
                lastUpdatedBuildingId = String.valueOf(getIntent().getLongExtra("buildingId", 0L));
                lastBuildingId = lastUpdatedBuildingId;
            }
            mFormPkId = getIntent().getIntExtra("mFormPkId", 0);
            formData = getFormData(mFormPkId);
            if (formData != null) {
                formString = formData.getModifiedFormData();
                try {
                    if (getIntent().hasExtra("autoFill") && !TextUtils.isEmpty(getIntent().getStringExtra("autoFill"))) {
                        autoFillArray = new JSONArray(getIntent().getStringExtra("autoFill"));
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }

        if (TextUtils.isEmpty(formString)) {
            showForeGroundToastLong(getString(R.string.empty_data));
            return false;
        } else if (!isJSONValid(formString)) {
            showForeGroundToastLong(getString(R.string.invalid_data));
            return false;
        }
        return true;
    }


    private FormData getFormData(int formPkId) {
        TblForms tblForms = new TblForms(this);
        return tblForms.getFormDataByPKId(formPkId);
    }

    private void setData() {

        binding.tlOther.tvTitle.setText(formData.getFormName());
        if (!TextUtils.isEmpty(formString)) {
            if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_FORM_LOCK, 0) == 1) {
                binding.tlOther.imgBtnBack.setVisibility(View.GONE);
            } else {
                binding.tlOther.imgBtnBack.setVisibility(View.VISIBLE);
            }
            String pageDisplay = currentPage + "/" + totalPages;
            binding.tvPageNumber.setText(pageDisplay);
            binding.rlBottom.setVisibility(View.VISIBLE);
            showMapIcon(formData.isPlotOnMap());
            setBottomBtnText(binding.btnNext, binding.btnPrevious, binding.tlOther.imgBtnBack);
        }
    }

    private void changePage(int position, boolean isNext, boolean isButtonClick) {
        saveModifiedForm();
        hideSoftKeyboard(FormDetailActivity.this);
        if (!isNext) {

            setFlipAnimation(false);
            if (!isButtonClick) {
                for (int j = currentPage; j > position; j--) {
                    binding.vfPages.showPrevious();
                    currentPage--;
                    if (currentPage == 1)
                        break;
                }

               /* for (int j = 1; j <= currentPage - position; j++) {
                    vfPages.showPrevious();
                    currentPage--;
                    if (currentPage == 1)
                        break;
                }*/
            } else {
                binding.vfPages.showPrevious();
                currentPage--;
            }
        } else {
            setFlipAnimation(true);
            if (!isButtonClick) {

                for (int j = 1; j < position - currentPage; j++) {
                    binding.vfPages.showNext();
                    currentPage++;
                }
            } else {
                binding.vfPages.showNext();
                currentPage++;
            }
            if (!isButtonClick)
                currentPage = position;
        }

        pageNumber = currentPage;
        String pageDisplay = currentPage + "/" + totalPages;
        binding.tvPageNumber.setText(pageDisplay);
        setBottomBtnText(binding.btnNext, binding.btnPrevious, binding.tlOther.imgBtnBack);
    }

    private void changePage(int position) {
        hideSoftKeyboard(this);
        currentPage = pageNumber;
        if (lastOpenedPage < currentPage) {
            for (int j = currentPage; j > position; j--) {
                binding.vfPages.showPrevious();
                currentPage--;
                if (currentPage == 1)
                    break;
            }
        } else if (lastOpenedPage > currentPage) {
            for (int j = 1; j <= position - currentPage; j++) {
                binding.vfPages.showNext();
                currentPage++;
            }
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        startLocationServiceIfNotStarted();
    }


    /**
     * \
     * show map icon if form hase plotonmap enabled
     *
     * @param isPlotOnMap boolean
     */
    private void showMapIcon(Boolean isPlotOnMap) {
        if (isPlotOnMap) {
            binding.tlOther.ivPlotMap.setVisibility(View.VISIBLE);
        } else {
            binding.tlOther.ivPlotMap.setVisibility(View.GONE);
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SIGN_FOR_RESULT:
                if (data != null && data.getExtras() != null)
                    if (data.hasExtra("signaturePath") && data.hasExtra("tag") && data.hasExtra("isSign")) {
                        if (data.getIntExtra("tag", 0) == -1) {
                            finish();
                        } else
                            setBitmapOnSignatureView(data.getExtras());
                    } else if (data.hasExtra("tag") && data.hasExtra("isSign") && data.hasExtra("isSketch") && data.hasExtra("isEmpty")) {
                        clearSignature(data.getExtras());
                    }
                break;
            case Constants.MAP_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        doAfterComeBackFromNewPropertyScreen(data);
                    }
                }
                break;

            case Constants.ADD_ADDRESS_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        updateAddressData(data.getExtras(), binding.vfPages);
                    }
                }
                break;
            case REQUEST_CAMERA:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        if (data.getIntExtra("tag", 0) == -1) {
                            finish();
                        } else
                            setImageInImageView(data);
                    }
                }
                break;

            case LOCATION_REQUEST_CODE:
                //case Constants.MAP_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    isGpsEnableCalled = true;
                    startLocationServiceIfNotStarted();
                }
                break;

            case SUB_FORM_CREATE_REQUEST_CODE:
                returnFromSubFrom = true;
                init();
                break;
        }
    }

    private void clearSignature(Bundle extras) {
        int tagOfSign = extras.getInt("tag");
        boolean isSign = extras.getBoolean("isSign");
        String signaturePath = "";
        JSONObject jsonObject = (JSONObject) mapSignature.get(tagOfSign);
        if (jsonObject != null && jsonObject.has(Constants.VALUE)) {
            try {
                JSONArray array = jsonObject.getJSONArray(Constants.VALUE);
                if (array.length() > 0) {
                    if (array.getJSONObject(0).has(Constants.IMAGEPATHHIGH) && (!TextUtils.isEmpty(array.getJSONObject(0).getString(Constants.IMAGEPATHHIGH)))) {
                        signaturePath = array.getJSONObject(0).getString(Constants.IMAGEPATHHIGH);
                    } else {
                        if (array.getJSONObject(0).has(Constants.IMAGEPATHLOW) && (!TextUtils.isEmpty(array.getJSONObject(0).getString(Constants.IMAGEPATHLOW))))
                            signaturePath = array.getJSONObject(0).getString(Constants.IMAGEPATHLOW);
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

            View sign = binding.vfPages.findViewWithTag(tagOfSign);
            ImageView ivSign = sign.findViewById(R.id.ivSign);
            TextView tvClear = sign.findViewById(R.id.tvClear);
            TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);

            if (extras.getBoolean("isEmpty")) {
                if (!TextUtils.isEmpty(signaturePath))
                    ImageUtil.deleteImageFromSDCard(signaturePath);

                ivSign.setImageBitmap(null);
                tvClear.setVisibility(View.GONE);
                updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, false);
                String[] value = {};
                try {
                    jsonObject.put(Constants.VALUE, new JSONArray(value));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
                try {
                    JSONObject jsonImageData;
                    TblForms tblForms = new TblForms(context);
                    FormData data = getFormData(formData.getFormPKId());
                    jsonImageData = new JSONObject(data.getImageData());
                    if (isSign) {
                        if (StaticUtils.getPendingCount(formData.getFormPKId()) > 0) {
                            StaticUtils.setPendingCount(formData.getFormPKId(), StaticUtils.getPendingCount(formData.getFormPKId()) - 1);
                        }
                    }

                    JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                    int index = -1;
                    for (int i = 0; i < jsonImageArray.length(); i++) {
                        JSONObject object1 = jsonImageArray.getJSONObject(i);
                        if (tag == object1.getInt(Constants.TAGID)) {
                            index = i;
                        }
                    }
                    if (index != -1) {
                        jsonImageArray.remove(index);
                    }
                    tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }

            }
        }
    }

    private void plotImageDataOnMap(FormData formData) {
        JSONObject jsonImageData;
        try {
            int index = -1;
            jsonImageData = new JSONObject(formData.getImageData());
            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private void setBitmapOnSignatureView(Bundle extras) {
        saveDataInDB();
        int tag = extras.getInt("tag", -1);
        String signaturePath = extras.getString("signaturePath");
        boolean increaseCount = extras.getBoolean("increaseCount");
        JSONObject jsonObject = (JSONObject) mapSignature.get(tag);
        JSONArray jsonArray = new JSONArray();
        try {
            JSONObject signatureJsonObject = new JSONObject();
            signatureJsonObject.put(Constants.IMAGEID, imageId);
            signatureJsonObject.put(Constants.LRIMAGE, "");
            signatureJsonObject.put(Constants.HRIMAGE, "");
            signatureJsonObject.put(Constants.IMAGEPATHLOW, signaturePath);
            signatureJsonObject.put(Constants.IMAGEPATHHIGH, "");
            signatureJsonObject.put(Constants.IS_SIGNATURE, true);
            jsonArray.put(signatureJsonObject);
            if (jsonObject != null)
                jsonObject.put(Constants.VALUE, jsonArray);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        JSONObject signatureObject = null;
        try {
            JSONObject jsonImageData;
            TblForms tblForms = new TblForms(context);
            FormData data = getFormData(formData.getFormPKId());
            jsonImageData = new JSONObject(data.getImageData());
            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);


            if (increaseCount) {
                int pendingCount = Math.max(StaticUtils.getPendingCount(formData.getFormPKId()), 0);
                StaticUtils.setPendingCount(formData.getFormPKId(), pendingCount + 1);
            }

            for (int i = 0; i < jsonImageArray.length(); i++) {
                JSONObject object = jsonImageArray.getJSONObject(i);
                if (object.getInt(Constants.TAGID) == tag) {
                    signatureObject = object;
                    break;
                }
            }
            if (signatureObject == null) {
                signatureObject = new JSONObject();
                jsonImageArray.put(signatureObject);

            }
            signatureObject.put(Constants.IMAGEID, imageId);
            signatureObject.put(Constants.TAGID, tag);
            signatureObject.put(Constants.LRIMAGE, "");
            signatureObject.put(Constants.HRIMAGE, "");
            signatureObject.put(Constants.IMAGEPATHHIGH, "");
            signatureObject.put(Constants.IMAGEPATHLOW, signaturePath);
            signatureObject.put(Constants.IS_SIGNATURE, true);
            tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            saveModifiedForm();
            imageId++;

            tblForms.updateImageCountByPkId(formData.getFormPKId(), imageId);

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        View sign = binding.vfPages.findViewWithTag(tag);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        tvClear.setVisibility(View.VISIBLE);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);
        if (jsonObject != null)
            updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, true);

        tvClear.setOnClickListener(v -> {

            ImageUtil.deleteImageFromSDCard(signaturePath);
            ivSign.setImageBitmap(null);
            tvClear.setVisibility(View.GONE);
            if (jsonObject != null) {
                updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, false);
                String[] value = {};
                try {
                    jsonObject.put(Constants.VALUE, new JSONArray(value));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            try {
                JSONObject jsonImageData;
                TblForms tblForms = new TblForms(context);
                FormData data = getFormData(formData.getFormPKId());
                jsonImageData = new JSONObject(data.getImageData());
                if (StaticUtils.getPendingCount(formData.getFormPKId()) > 0) {
                    StaticUtils.setPendingCount(formData.getFormPKId(), StaticUtils.getPendingCount(formData.getFormPKId()) - 1);
                }


                JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                int index = -1;
                for (int i = 0; i < jsonImageArray.length(); i++) {
                    JSONObject object1 = jsonImageArray.getJSONObject(i);
                    if (tag == object1.getInt(Constants.TAGID)) {
                        index = i;
                    }
                }
                if (index != -1) {
                    jsonImageArray.remove(index);
                }
                tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        });

        ImageUtil.loadImageInGlide(ivSign, signaturePath);
    }


    private void doAfterComeBackFromNewPropertyScreen(Intent data) {
        if (data.getExtras() == null)
            return;
        try {
            if (!TextUtils.isEmpty(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                try {
                    switch (data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA)) {
                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN:
                            PopUtils.showAlertDialogPositiveButtonOnly(FormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN, getString(R.string.joinAlertMessage));
                            break;

                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX:
                            PopUtils.showAlertDialogPositiveButtonOnly(FormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX, getString(R.string.maxAlertMessage));
                            break;

                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST:
                            PopUtils.showAlertDialogPositiveButtonOnly(FormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST, getString(R.string.pastAlertMessage));
                            break;
                        default:
                            break;

                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void setImageInImageView(Intent data) {
        int tag;
        ImageViewAdapter imageViewAdapter;
        RecyclerView rvImages;
        String imagePath;
        TblForms tblForms = new TblForms(FormDetailActivity.this);
        if (data.getExtras() != null && data.hasExtra("tag")) {
            tag = data.getExtras().getInt("tag", -1);
            View view = null;
            try {
                view = binding.vfPages.findViewWithTag(tag);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

            if (view == null)
                return;
            TextView tvUploadPhoto = view.findViewById(R.id.tvUploadPhoto);

            JSONObject mainJson = (JSONObject) mapImageUpload.get(tag);

            if (mainJson != null) {
                updateValidationInMap(mainJson, tvUploadPhoto, "", false, true, true);

                JSONArray mainJsonArray = null;
                try {
                    mainJsonArray = mainJson.getJSONArray(Constants.VALUE);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }

                rvImages = view.findViewById(R.id.rlImages);
                imageViewAdapter = (ImageViewAdapter) rvImages.getAdapter();
                ArrayList<ImageData> lstImage = imageViewAdapter.getImageList();
                String imagePathData = "";
                TblForms tblForms1 = new TblForms(context);
                FormData formData1 = tblForms1.getFormDataByPKId(formData.getFormPKId());

                if (data.hasExtra("imagePath")) {

                    imagePath = data.getExtras().getString("imagePath");
                    JSONObject jsonImageData = null;
                    FormData formData2 = getFormData(formData.getFormPKId());
                    try {
                        jsonImageData = new JSONObject(formData2.getImageData());

                        JSONArray jsonArray = new JSONArray(imagePath);
                        JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                        int pendingCount = Math.max(StaticUtils.getPendingCount(formData.getFormPKId()), 0);
                        StaticUtils.setPendingCount(formData1.getFormPKId(), pendingCount + jsonArray.length());
                        for (int i = 0; i < jsonArray.length(); i++) {
                            JSONObject jsonImage = new JSONObject();
                            JSONObject mainImageJson = new JSONObject();
                            mainJsonArray.put(mainImageJson);
                            jsonImageArray.put(jsonImage);
                            JSONObject jsonObjects = jsonArray.getJSONObject(i);
                            String imagePathHigh = "";
                            if (jsonObjects.has(Constants.IMAGEPATHHIGH)) {
                                imagePathHigh = jsonObjects.getString(Constants.IMAGEPATHHIGH);
                                jsonImage.put(Constants.IMAGEPATHHIGH, imagePathHigh);
                                mainImageJson.put(Constants.IMAGEPATHHIGH, imagePathHigh);
                            }
                            String imagePathLow = "";
                            jsonImage.put(Constants.TAGID, tag);
                            jsonImage.put(Constants.LRIMAGE, "");
                            jsonImage.put(Constants.HRIMAGE, "");
                            jsonImage.put(Constants.IS_SIGNATURE, false);
                            if (jsonObjects.has(Constants.IMAGEPATHLOW)) {
                                imagePathLow = jsonObjects.getString(Constants.IMAGEPATHLOW);
                                jsonImage.put(Constants.IMAGEPATHLOW, imagePathLow);
                                mainImageJson.put(Constants.IMAGEPATHLOW, imagePathLow);
                            }
                            if (!TextUtils.isEmpty(imagePathHigh)) {
                                imagePathData = imagePathHigh;
                            } else {
                                imagePathData = imagePathLow;
                            }
                            if (!TextUtils.isEmpty(imagePathData)) {
                                ImageData imageData = new ImageData();
                                imageData.setImagePath(imagePathData);
                                imageData.setImageId(imageId);
                                lstImage.add(imageData);
                                jsonImage.put(Constants.IMAGEID, imageId);
                                mainImageJson.put(Constants.IMAGEID, imageId);
                                imageId++;
                            }
                        }
                        tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                        saveModifiedForm();
                        tblForms.updateImageCountByPkId(formData.getFormPKId(), imageId);

                        imageViewAdapter.updateList(lstImage);
                        if (lstImage.size() < 1) {
                            rvImages.setVisibility(View.GONE);
                        } else {
                            rvImages.setVisibility(View.VISIBLE);
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
        }
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            onBackPressed();
        } else if (viewId == R.id.btnPrevious) {
            if (currentPage > 1) {
                changePage(currentPage - 1, false, true);
            }
        } else if (viewId == R.id.btnNext) {
            if (totalPages == currentPage) {
                saveDataInDB();
                if (isFormValidate() && isMaterialNotEmapty()) {
                    submitForm();
                }
            } else if (currentPage < totalPages) {
                changePage(currentPage - 1, true, true);
                checkAndShowCrewSelectionIfRequired(currentPage);
                checkAndShowGeoSelectionIfRequired(currentPage);
            }
        } else if (viewId == R.id.ivSecondRight) {
            navigateToUploadActivityScreen(binding.tlOther.ivSecondRight);
        } else if (viewId == R.id.ivPlotMap) {
            isShowFormDetailView(false);
        } else if (viewId == R.id.ivCamera) {
            showImageComponentList();
        } else if (viewId == R.id.ivFormDetail) {
            isShowFormDetailView(true);
        }
    }

    private void isShowFormDetailView(boolean shouldShowFormView) {
        if (shouldShowFormView) {
            binding.rlFormDetails.setVisibility(View.VISIBLE);
            binding.rlMap.setVisibility(View.GONE);
            binding.tlOther.ivPlotMap.setVisibility(View.VISIBLE);
            binding.tlOther.ivFormDetail.setVisibility(View.GONE);
            clearGoogleMap();
        } else {
            binding.rlFormDetails.setVisibility(View.GONE);
            binding.rlMap.setVisibility(View.VISIBLE);
            binding.tlOther.ivPlotMap.setVisibility(View.GONE);
            binding.tlOther.ivFormDetail.setVisibility(View.VISIBLE);
            binding.ivCamera.setVisibility(View.VISIBLE);
            loadMap();
        }
    }


    private boolean isMaterialNotEmapty() {
        if (lstChildMaterial.isEmpty()) {
            return true;
        } else {
            for (Material material : lstChildMaterial) {
                if (TextUtils.isEmpty(material.getMaterialName())) {
                    showForeGroundToast(getString(R.string.you_have_not_select_any_material));
                    return false;
                }
            }
        }
        return true;
    }

    private void submitForm() {
        if (isFinishing())
            return;
        try {
            startProgress();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            // Re initiate data executor service if its been shutdown or terminated
            BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
            BaseApplication.getInstance().dataExecutorService.submit(() -> {
                boolean shouldExecuteSignaturePart = false;
                try {
                    shouldExecuteSignaturePart = submitFormDataInSafeThread(mFormPkId);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                } finally {
                    if (shouldExecuteSignaturePart) {
                        addSignatureAndUploadData();
                    } else {
                        stopProgressDialog();
                        goBackToListingScreen();
                    }
                }
            });
        } catch (RejectedExecutionException e) {
            FirebaseEventUtils.logException(e);
        } finally {
            stopProgressDialog();
        }
        //new SubmitFormAsyncTask().execute();

    }

    private void addSignatureAndUploadData() {
        runOnUiThread(this::stopProgressDialog);
        addSignaturInUploadingQueue(false, formData);
        BaseApplication.getInstance().startImageUpload();
        saveDataInDbAtFormSubmission();
        insertCopyData(isTMForm(), formData);
        addDataInUploadQueue(false);
        runOnUiThread(() -> {
            goBackToListingScreen();
            stopProgressDialog();
        });

       /* AddSignatureInUploadQueue addSignatureInUploadQueue = new AddSignatureInUploadQueue(this, formData, isTMForm(), result -> {
            if (isFinishing()) {
                return;
            }
            AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());
            saveDataInDbAtFormSubmission();
            insertCopyData(isTMForm(), formData);
            addDataInUploadQueue(false);
            goBackToListingScreen();
            stopProgressDialog();
        });
        addSignatureInUploadQueue.execute();*/

    }

    private void addDataInUploadQueue(boolean isImageUploaded) {

        TblUploadData tblUploadData = new TblUploadData(FormDetailActivity.this);
        UploadOtherData uploadOtherData = new UploadOtherData();

        long currentTime = System.currentTimeMillis();
        uploadOtherData.setDataType(Constants.FORM_DATA);
        uploadOtherData.setCreatedAt(currentTime);
        uploadOtherData.setUpdatedAt(currentTime);
        uploadOtherData.setProcessStartTime(currentTime);
        FormData tempFormData = getFormData(formData.getFormPKId());
        String data = tempFormData.getModifiedFormData();
        JSONObject dataJson = new JSONObject();
        try {
            dataJson.put(Constants.PARAM_FORM_ID, tempFormData.getFormId());
            dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            if (tempFormData.getLastBuildingId() == 0) {
                if (Integer.parseInt(lastUpdatedBuildingId) > 0) {
                    tempFormData.setLastBuildingId(Integer.parseInt(lastUpdatedBuildingId));
                } else if (Integer.parseInt(lastBuildingId) > 0) {
                    tempFormData.setLastBuildingId(Integer.parseInt(lastBuildingId));
                }
            }

            dataJson.put(Constants.PARAM_BUIL, tempFormData.getLastBuildingId());
            dataJson.put(Constants.PARAMS_FORM_DATA_APP, data);
            dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
            //dataJson.put(Constants.PARAM_FORM_KEY_DATA, StaticUtils.getValueJson(data, tempFormData.getImageData()));
            dataJson.put(Constants.PARAM_LON, MainActivity.currentLongitude);
            dataJson.put(Constants.PARAM_LAT, MainActivity.currentLatitude);

            dataJson.put(Constants.PARAM_SOURCE, "Forms");
            dataJson.put(Constants.PARAM_LANG, AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
            long submittedTime;
            if (tempFormData.getSf_submited() <= 0)
                submittedTime = System.currentTimeMillis() / 1000;
            else
                submittedTime = tempFormData.getSf_submited();


            if (formData.isSubForm() && formData.getSubFormOtherData() != null) {
                dataJson.put(PARAM_SUBMITTED_FOR, formData.getSubFormOtherData().getCrewId());
            }
            dataJson.put(Constants.PARAM_DT, submittedTime);
            dataJson.put(Constants.PARAM_FORM_NAME, tempFormData.getFormName());
            dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        uploadOtherData.setTitle(getString(R.string.form_, formData.getFormName()));
        uploadOtherData.setImageUploaded(isImageUploaded);
        uploadOtherData.setRequestedData(dataJson.toString());
        uploadOtherData.setData(data);
        uploadOtherData.setFormPKId(mFormPkId);
        uploadOtherData.setFormSubmissionId(formData.getFormSubmissionId());
        uploadOtherData.setHasSubForm(formData.hasSubForm());
        uploadOtherData.setSubForm(formData.isSubForm());
        tblUploadData.insertFormData(uploadOtherData, isTMForm());
        if (isImageUploaded) {
            removeDataFromTableAfterFormSubmitted();
            insertCopyData(isTMForm(), formData);
            sendBroadCastForStartOtherDataUpload();
        }

    }

    private void removeDataFromTableAfterFormSubmitted() {
        if (!formData.isSubForm()) {
            TblForms tblForms = new TblForms(this);
            if (!formData.hasSubForm() || (formData.hasSubForm() && formData.isFormSubmitted() && StaticUtils.getPendingCount(formData.getFormPKId()) == 0)) {
                //Delete All SubForm of the form
                tblForms.deleteDataByMainFormPKId(TblForms.TABLE_NAME, formData.getFormPKId());
                tblForms.deleteDataByPKId(formData.getFormPKId());
                deleteImagesFromStorage(formData.getImageData());
            }
        }
    }

    private void saveDataInDbAtFormSubmission() {
        saveDataInDB();
        TblForms tblForms = new TblForms(FormDetailActivity.this);
        formData.setFormSubmitted(true);
        formData.setSf_submited(System.currentTimeMillis() / 1000);
        formData.setLatitude(MainActivity.currentLatitude);
        formData.setLongitude(MainActivity.currentLongitude);
        tblForms.updateFormSubmittedStatus(formData.getFormPKId(), MainActivity.currentLatitude, MainActivity.currentLongitude);
        sendBroadCastForStartOtherDataUpload();
    }

    private void saveDataInDB() {
        TblForms tblForms = new TblForms(FormDetailActivity.this);
        tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastBuildingId));
        saveModifiedForm();
    }

    public boolean isFormValidate() {
        boolean isValidData = true;
        int counter = 0;
        if (formData.hasSubForm()) {
            List<FormData> lstForm = getSubFormData();
            for (FormData subFormData : lstForm) {
                if (subFormData.getCheckin_time() > 0 && subFormData.getCheckout_time() <= 0) {
                    showForeGroundToastLong(getString(R.string.you_need_to_checkout_crew_item_for_crew, subFormData.getSubFormOtherData().getCrewName()));
                    return false;
                }
            }
        }
        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    if (validationData.getPageNumber() < currentPage) {
                        changePage(validationData.getPageNumber(), false, false);
                    }
                }
            }
        }
        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                    if (view instanceof EditText) {
                        isValidData = false;

                        if (validationData.isEmail()) {
                            ((EditText) view).setError(getString(R.string.msg_valid_email_address));
                            ((EditText) view).setSelection(((EditText) view).getText().toString().trim().length());
                        } else {
                            ((EditText) view).setError(getString(R.string.msg_field_can_not_be_an_empty));
                        }
                        if (binding.vfPages.getDisplayedChild() == validationData.getPageNumber() - 1) {
                            counter++;
                            if (counter == 1) {
                                showForeGroundToast(getString(R.string.required, validationData.getTitle()));
                                focusOnView(binding.llMain, scrollView, view);
                            }
                        }
                    } else if (view instanceof TextView) {
                        isValidData = false;
                        ((TextView) view).setError(getString(R.string.msg_field_can_not_be_an_empty));
                        if (binding.vfPages.getDisplayedChild() == validationData.getPageNumber() - 1) {
                            counter++;
                            if (counter == 1) {
                                showForeGroundToast(getString(R.string.required, validationData.getTitle()));
                                focusOnView(binding.llMain, scrollView, view);
                            }
                        }
                    }
                }
            }
        }
        if (isValidData) {
            isValidData = checkPanelValidation();
        }
        return isValidData;
    }


    private boolean checkPanelValidation() {
        formData = getFormData(mFormPkId);
        if (panelValidationData.isEmpty())
            return true;
        StaticUtils.scanPanelDataInJSON(formData.getModifiedFormData(), panelValidationData);
        Iterator panelIterator = panelValidationData.keySet().iterator();
        while (panelIterator.hasNext()) {
            int key = (int) panelIterator.next();
            PanelData panelData = panelValidationData.get(key);
            if (panelData != null && !panelData.isValidated()) {
                NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                changePage(panelData.getPageNumber(), false, false);
                showForeGroundToast(getString(R.string.required, panelData.getTitle()));
                focusOnView(binding.llMain, scrollView, panelData.getPanelView());
                return false;
            }
        }
        return true;
    }

    private void concatImageData() {
        TblForms tblForms = new TblForms(context);
        try {
            JSONArray jsonArray = updatedJsonObject.getJSONArray(Constants.PAGES);
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            jsonArrayReadToUpdateImageUrl(array);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            saveModifiedForm();

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void jsonArrayReadToUpdateImageUrl(JSONArray jsonArray) {
        formData = getFormData(formData.getFormPKId());
        String imagePathString = formData.getImageData();
        for (int i = 0; i < jsonArray.length(); i++) {
            try {

                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayReadToUpdateImageUrl(array);
                } else if (object.has(Constants.CHOICES)) {
                    try {
                        JSONArray array = object.getJSONArray(Constants.CHOICES);
                        for (int j = 0; j < array.length(); j++) {
                            JSONObject jsonObject = null;
                            try {
                                if (array.get(j) instanceof JSONObject) {
                                    jsonObject = array.getJSONObject(j);
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                            if (jsonObject != null) {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    try {
                                        JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                        if (jsonArray1.length() > 0) {
                                            jsonArrayReadToUpdateImageUrl(jsonArray1);
                                        }
                                    } catch (JSONException e) {
                                        e.getStackTrace();

                                    }
                                }
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                } else {
                    if (!TextUtils.isEmpty(imagePathString)) {
                        JSONObject jsonObject = new JSONObject(imagePathString);
                        JSONArray array = jsonObject.getJSONArray(Constants.DATA);
                        if (object.getString(Constants.TYPE).equals(Constants.IMAGE_UPLOAD)) {
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);
                                        for (int k = 0; k < array.length(); k++) {
                                            JSONObject object2 = array.getJSONObject(k);
                                            if (object1.has(Constants.IMAGEID) && object2.has(Constants.IMAGEID) && object2.getInt(Constants.IMAGEID) == object1.getInt(Constants.IMAGEID)) {
                                                object1.put(Constants.LRIMAGE, object2.getString(Constants.LRIMAGE));
                                                object1.put(Constants.HRIMAGE, object2.getString(Constants.HRIMAGE));
                                                if (object2.has(Constants.IMAGE_UUID)) {
                                                    object1.put(Constants.IMAGE_UUID, object2.getString(Constants.IMAGE_UUID));
                                                }
                                                object1.put(Constants.IMAGEPATHHIGH, object2.getString(Constants.IMAGEPATHHIGH));
                                                object1.put(Constants.IMAGEPATHLOW, object2.getString(Constants.IMAGEPATHLOW));
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        } else if (object.getString(Constants.TYPE).equals(Constants.ISSUES)) {
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);

                                        if (object1.has(Constants.VALUE)) {
                                            JSONArray jsonArray2 = object1.getJSONArray(Constants.VALUE);
                                            for (int k = 0; k < jsonArray2.length(); k++) {
                                                JSONObject object2 = jsonArray2.getJSONObject(k);
                                                for (int l = 0; l < array.length(); l++) {
                                                    JSONObject object3 = array.getJSONObject(l);
                                                    if (object2.getInt(Constants.IMAGEID) == object3.getInt(Constants.IMAGEID)) {
                                                        object2.put(Constants.LRIMAGE, object3.getString(Constants.LRIMAGE));
                                                        object2.put(Constants.HRIMAGE, object3.getString(Constants.HRIMAGE));
                                                        if (object3.has(Constants.IMAGE_UUID)) {
                                                            object2.put(Constants.IMAGE_UUID, object3.getString(Constants.IMAGE_UUID));
                                                        }
                                                        object2.put(Constants.IMAGEPATHHIGH, object3.getString(Constants.IMAGEPATHHIGH));
                                                        object2.put(Constants.IMAGEPATHLOW, object3.getString(Constants.IMAGEPATHLOW));
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        }
                    }
                }

            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }


    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Constants.LOCATION_PERMISSION_REQUEST:
                doAfterLocationPermissionResult(permissions, grantResults);
                break;
        }
    }

    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    private void goBackToListingScreen() {
        //Added this condition to prevent null data in uploading queue because of multiple button click when device is too slow
        if(formData.getFormId()>0 && !TextUtils.isEmpty(formData.getFormSubmissionId())) {
            setSiteAndFormBroadCrumbs(8, -2, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
        }else{
            //Added event to understand empty form data
            FirebaseEventUtils.EmptyDataSubmissionEvent(this);
        }
        //setSiteAndFormBroadCrumbs(7, -2, formData.getFormId(), formData.getFormName());
        Intent intent = new Intent();
        showForeGroundToast(getString(R.string.txt_form_submitted));
        setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    public void onBackPressed() {
        if (currentPage != 1) {
            return;
        }

        //setSiteAndFormBroadCrumbs(7, -2, formData.getFormId(), formData.getFormName());
        saveDataInDB();
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_FORM_LOCK, 0) == 1) {
            killApp();
        }

        super.onBackPressed();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(this, binding.tlOther.llTitle);
    }

    @Override
    public void onSuccessResponse(Response<AppDataResponse> response) {

    }

    @Override
    public void onFailureResponse(Throwable t) {

    }

    @Override
    public void onNoInternetConnection() {

    }

    @Override
    public void onUpdateAppVersion(int versionCode, boolean isForceUpdate) {
        checkAndShowAppUpdateDialog(versionCode, isForceUpdate);

    }

    private boolean submitFormDataInSafeThread(int mFormPkId) {
        TblForms tblForms = new TblForms(context);
        formData = getFormData(formData.getFormPKId());
        String imageData = formData.getImageData();
        submitIssueAtFormSubmissionTime(context, false, null, formData);
        updateStopServiceTimeAtSubmissionTime(updatedJsonObject);
        //addTMDataInUploadQueue(isTMForm(), null, formData);
        int pendingMediaCount = 0;
        try {
            JSONObject jsonObject = null;
            if (!TextUtils.isEmpty(imageData)) {
                jsonObject = new JSONObject(imageData);
                if (StaticUtils.getPendingCount(formData.getFormPKId()) > -1) {
                    pendingMediaCount = StaticUtils.getPendingCount(mFormPkId);
                }
            }

            if (pendingMediaCount == 0) {
                saveDataInDbAtFormSubmission();
                addDataInUploadQueue(true);
            } else {
                if (jsonObject == null) {
                    saveDataInDbAtFormSubmission();
                    addDataInUploadQueue(true);
                } else {
                    JSONArray imageArray = jsonObject.getJSONArray(Constants.DATA);
                    if (imageArray.length() > 0) {
                        List<String> lstSignaturePath = new ArrayList<>();
                        for (int i = 0; i < imageArray.length(); i++) {
                            if (imageArray.getJSONObject(i).getBoolean(Constants.IS_SIGNATURE)) {
                                String imagePath = imageArray.getJSONObject(i).getString(IMAGEPATHLOW);
                                lstSignaturePath.add(imagePath);
                            }
                        }
                        saveModifiedForm();
                        if (!lstSignaturePath.isEmpty()) {
                            if (Integer.parseInt(lastUpdatedBuildingId) > 0) {
                                tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastUpdatedBuildingId));
                            } else {
                                tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastBuildingId));
                            }
                            return true;


                        } else {
                            saveDataInDbAtFormSubmission();
                            insertCopyData(isTMForm(), formData);
                            addDataInUploadQueue(false);
                        }
                    } else {
                        saveDataInDbAtFormSubmission();
                        addDataInUploadQueue(true);
                    }
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        return false;
    }

    @Override
    protected void onStop() {
        super.onStop();
        try {
            if (!isDeviceLocked(this)) {
                saveModifiedForm();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    @Override
    protected void onDestroy() {
        try {
            unregisterReceiver(mMessageReceiver);
            unregisterReceiver(formUpdateReceiver);
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        super.onDestroy();
    }
}