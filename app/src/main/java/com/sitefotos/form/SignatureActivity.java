package com.sitefotos.form;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.view.View;

import com.sitefotos.BaseActivity;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivitySignBinding;
import com.sitefotos.util.ImageUtil;

import java.util.ArrayList;

public class SignatureActivity extends BaseActivity implements View.OnClickListener {

    int tag;
    int formIdData;
    boolean isSign;
    private boolean increaseCount;
    public ActivitySignBinding binding;


    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        setOnClickListener();
        setActionBarVisibility(false);
        registerAppReceiver(formUpdateReceiver, new IntentFilter(Constants.INTERNAL_FORM_BROADCAST));

        binding.drawing.setDrawingCacheEnabled(true);
        if (getIntent().getExtras() != null) {
            isSign = getIntent().getExtras().getBoolean("isSign", false);
            tag = getIntent().getExtras().getInt("tag", -1);
            formIdData = (int) getIntent().getExtras().getLong("formId", -1);
            increaseCount = getIntent().getExtras().getBoolean("increaseCount", false);
        }
    }


    private void initBinding() {
        binding = ActivitySignBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void setOnClickListener() {
        binding.imgBtnDone.setOnClickListener(this);
        binding.ivClear.setOnClickListener(this);
        binding.ivUndo.setOnClickListener(this);
    }


    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnDone) {
            if (binding.drawing.isDrawnAnything()) {
                String path = ImageUtil.saveImage(SignatureActivity.this, binding.drawing.getDrawingCache(), false);
                Intent intent = new Intent();
                intent.putExtra("signaturePath", path);
                intent.putExtra("tag", tag);
                intent.putExtra("isSign", isSign);
                intent.putExtra("isSketch", false);
                intent.putExtra("increaseCount", increaseCount);
                setResult(RESULT_OK, intent);
            }
            finish();
        } else if (viewId == R.id.ivClear) {
            finish();
        } else if (viewId == R.id.ivUndo) {
            binding.drawing.undo();
            if (binding.drawing.getPaths().isEmpty()) {
                binding.ivUndo.setVisibility(View.GONE);
            }
        }
    }

    private void undoDrawing() {

    }

    private BroadcastReceiver formUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (context != null) {
                if (intent.hasExtra(Constants.DELETED_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_FORM_ALL_DATA, false)) {
                        setResultForDeletedForm();
                    }
                }
                if (intent.hasExtra(Constants.DELETED_FORM_LIST)) {
                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_FORM_LIST);
                    for (Integer formId : lstDeletedFormId) {
                        if (formId == formIdData) {
                            setResultForDeletedForm();
                        }
                    }
                }
            }
        }
    };

    private void setResultForDeletedForm() {
        Intent intent = new Intent();
        intent.putExtra("signaturePath", "");
        intent.putExtra("tag", -1);
        intent.putExtra("isSign", isSign);
        intent.putExtra("increaseCount", increaseCount);
        this.setResult(RESULT_OK, intent);
        finish();
    }

    @Override
    protected void onDestroy() {
        unregisterReceiver(formUpdateReceiver);
        super.onDestroy();
    }

}
