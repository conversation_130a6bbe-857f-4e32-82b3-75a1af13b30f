package com.sitefotos.form;

import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.TITLE;
import static com.sitefotos.Constants.VALUE;
import static com.sitefotos.util.StaticUtils.isDeviceLocked;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.sitefotos.BaseApplication;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.ImageViewAdapter;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivitySubFormDetailBinding;
import com.sitefotos.event.TMFormCheckInOutEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.FormData;
import com.sitefotos.models.ImageData;
import com.sitefotos.models.PanelData;
import com.sitefotos.models.ValidationData;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.logger.CustomLogKt;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.RejectedExecutionException;

import retrofit2.Response;

public class SubFormDetailActivity extends BaseSubFormDetailActivity implements OnAppDataApiResponse, View.OnClickListener {
    private ActivitySubFormDetailBinding binding;
    private long mLastCheckInClickTime = 0;
    int calenderYear, month, day, hour, min;
    int mainFromPKId;
    Calendar calendar = Calendar.getInstance();
    boolean clearFormData;

    boolean isTMForm;


    boolean didCheckInOrCheckOut;
    private final BroadcastReceiver formUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (context != null) {
                if (intent.hasExtra(Constants.DELETED_WP_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_WP_FORM_ALL_DATA, false)) {
                        finish();
                        return;
                    }
                }
                if (intent.hasExtra(Constants.UPDATED_WP_LIST)) {
                    ArrayList<Integer> lstUpdatedForm = intent.getIntegerArrayListExtra(Constants.UPDATED_WP_LIST);
                    for (Integer formId : lstUpdatedForm != null ? lstUpdatedForm : new ArrayList<Integer>()) {
                        if (formId == formData.getFormId()) {
                            recreate();
                        }
                    }
                }
                if (intent.hasExtra(Constants.DELETED_WP_LIST)) {
                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_WP_LIST);
                    for (Integer formId : lstDeletedFormId != null ? lstDeletedFormId : new ArrayList<Integer>()) {
                        if (formId == formData.getFormId()) {
                            finish();
                        }
                    }
                }
            }
        }
    };

    private final BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            int formPkId = intent.getIntExtra("formPkId", -1);
            if (formPkId == mFormPkId) {
                concatImageData();
            }
        }
    };


    @Override
    boolean isTMForm() {
        return isTMForm;
    }

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        initBinding();
        setOnClickListener();
        setActionBarVisibility(false);
        startLocationServiceIfNotStarted();
        startProgress();
        new Handler().postDelayed(this::init, 300);
    }

    private void initBinding() {
        binding = ActivitySubFormDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void setOnClickListener() {
        binding.tlOther.imgBtnBack.setOnClickListener(this);
        binding.tvDate.setOnClickListener(this);
        binding.tvTime.setOnClickListener(this);
        binding.tlOther.ivSecondRight.setOnClickListener(this);
        binding.btnCheckIn.setOnClickListener(this);
        binding.btnMapCheckIn.setOnClickListener(this);
        binding.tlOther.ivPlotMap.setOnClickListener(this);
        binding.tlOther.ivFormDetail.setOnClickListener(this);
        binding.ivCamera.setOnClickListener(this);
        binding.tlOther.tvSubmit.setOnClickListener(this);
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        startLocationServiceIfNotStarted();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(this, binding.tlOther.llTitle);
    }

    private void init() {
        registerAppReceiver(mMessageReceiver, new IntentFilter(Constants.INTERNAL_IMAGE_BROADCAST));
        registerAppReceiver(formUpdateReceiver, new IntentFilter(Constants.INTERNAL_FORM_BROADCAST));
        vfPages = binding.vfPages;
        if (getBundleData()) {
            setViewFlipperAnimation();
            setData(false);
            if (formData.getIsCheckInOut()) {
                checkAndSetBottomButtonVisibility();
                showCheckInOrOpenFormView();
            }
        } else {
            stopProgressDialog();
            setFormTitle();
        }
    }

    private void setFormTitle() {
        binding.tlOther.tvMainTitle.setVisibility(View.GONE);
        binding.tlOther.tvTitle.setText(formData.getFormName());
        if (formData.getCheckin_time() > 0 && !formData.isFormSubmitted()) {
            binding.tlOther.tvSubmit.setText(getString(R.string.txt_cancel));
            binding.tlOther.tvSubmit.setVisibility(View.VISIBLE);
        }
    }


    private void setViewFlipperAnimation() {
        leftOutAnimation = AnimationUtils.loadAnimation(this, R.anim.left_out);
        leftInAnimation = AnimationUtils.loadAnimation(this, R.anim.left_in);
        rightInAnimation = AnimationUtils.loadAnimation(this, R.anim.right_in);
        rightOutAnimation = AnimationUtils.loadAnimation(this, R.anim.right_out);

    }

    private void setFlipAnimation(boolean isNext) {
        if (isNext) {
            binding.vfPages.setInAnimation(rightInAnimation);
            binding.vfPages.setOutAnimation(leftOutAnimation);
        } else {
            binding.vfPages.setInAnimation(leftInAnimation);
            binding.vfPages.setOutAnimation(rightOutAnimation);
        }
    }


    private boolean getBundleData() {
        if (getIntent() != null) {
            if (getIntent().hasExtra("siteData")) {
                siteData = getIntent().getParcelableExtra("siteData");
                if (siteData != null) {
                    lastUpdatedBuildingId = String.valueOf(siteData.getSiteId());
                }
                lastBuildingId = lastUpdatedBuildingId;
            } else {
                if (getIntent().hasExtra("buildingId")) {
                    lastUpdatedBuildingId = String.valueOf(getIntent().getLongExtra("buildingId", 0L));
                    lastBuildingId = lastUpdatedBuildingId;
                }
            }

            isTMForm = getIntent().getBooleanExtra("isTMForm", false);

            mainFromPKId = getIntent().getIntExtra("mainFromPKId", 0);

            mFormPkId = getIntent().getIntExtra("formPkId", 0);
            formData = getDataFromTable(mFormPkId);
            if (formData != null) {
                formString = formData.getModifiedFormData();
                try {
                    if (getIntent().hasExtra("autoFill") && !TextUtils.isEmpty(getIntent().getStringExtra("autoFill"))) {
                        autoFillArray = new JSONArray(getIntent().getStringExtra("autoFill"));
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        if (TextUtils.isEmpty(formString)) {
            showForeGroundToastLong(getString(R.string.empty_data));
            return false;
        } else if (!isJSONValid(formString)) {
            showForeGroundToastLong(getString(R.string.invalid_data));
            return false;
        }
        return true;
    }

    private FormData getDataFromTable(int formPKId) {
        if (isTMForm()) {
            TblTMForms tblTNMForms = new TblTMForms(this);
            return tblTNMForms.getFormDataByPKId(formPKId);
        } else {
            TblForms tblForms = new TblForms(this);
            return tblForms.getFormDataByPKId(formPKId);
        }
    }


    private void updateImageCountByPkId(int formPKId, int imageId) {
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.updateImageCountByPkId(formPKId, imageId);
        } else {
            TblForms tblForms = new TblForms(this);
            tblForms.updateImageCountByPkId(formPKId, imageId);
        }
    }

    private void showCheckInOrOpenFormView() {
        if (formData.getCheckin_time() == 0) {
            binding.btnCheckIn.setText(R.string.check_in);


        }
    }

    private void setData(boolean skipOpenBreadCrumb) {
        binding.vfPages.removeAllViews();
        if (!TextUtils.isEmpty(formString)) {
            readJsonFile(binding.vfPages);
            setFormTitle();
            showMapIcon(formData.isPlotOnMap());
            checkAndSetBottomButtonVisibility();
            binding.rlBottom.setVisibility(View.VISIBLE);
            getDataFromCalendar();
            binding.tvDate.setText(DateUtil.getDatInDDMMMYYYY(calendar.getTimeInMillis()));
            binding.tvTime.setText(DateUtil.getTimeInHHMM(calendar.getTimeInMillis()));
            if (!skipOpenBreadCrumb) {
                setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
                updateMarginOfTitleView(this, binding.tlOther.llTitle);
            }
        }
        stopProgressDialog();
    }

    private void getDataFromCalendar() {
        calenderYear = calendar.get(Calendar.YEAR);
        month = calendar.get(Calendar.MONTH);
        day = calendar.get(Calendar.DAY_OF_MONTH);
        hour = calendar.get(Calendar.HOUR_OF_DAY);
        min = calendar.get(Calendar.MINUTE);
    }

    /**
     * show map icon if form hase plot on map enabled
     *
     * @param isPlotOnMap boolean
     **/
    private void showMapIcon(Boolean isPlotOnMap) {
        if (isPlotOnMap) {
            binding.tlOther.ivPlotMap.setVisibility(View.VISIBLE);
        } else {
            binding.tlOther.ivPlotMap.setVisibility(View.GONE);
        }
    }


    private void checkAndSetBottomButtonVisibility() {
        if (formData.getIsCheckInOut() && formData.getCheckin_time() > 0 && formData.getCheckout_time() > 0) {
            binding.btnCheckIn.setVisibility(View.GONE);
            binding.tvDate.setVisibility(View.GONE);
            binding.tvTime.setVisibility(View.GONE);
            binding.tvTimeDifference.setVisibility(View.VISIBLE);
            binding.viewSeparator.setVisibility(View.INVISIBLE);
            binding.rlBottom.setBackgroundColor(ContextCompat.getColor((this), R.color.transparent));
            enableOrDisableView(false);
            disableServiceView();

            String displayText = DateUtil.getCheckInAndCheckOutDateWithDifference(formData.getCheckin_time(), formData.getCheckout_time());
            binding.tvTimeDifference.setText(displayText);
        } else if (formData.getIsCheckInOut() && formData.getCheckin_time() > 0) {
            binding.btnCheckIn.setText(getString(R.string.check_out));
            enableOrDisableView(true);
            disableServiceView();
        } else if (formData.getIsCheckInOut()) {
            binding.btnCheckIn.setText(getString(R.string.check_in));
            enableOrDisableView(true);
        } else {
            binding.btnCheckIn.setText(getString(R.string.submit));
            enableOrDisableView(true);
        }
        /*if (fromMap && formData.isPlotOnMap()) {
            if (shouldShowFormView) {
                binding.rlMapBottom.setVisibility(View.GONE);
            } else {
                if (formData.getIsCheckInOut() && formData.getCheckin_time() <= 0) {
                    binding.ivCamera.setVisibility(View.GONE);
                    binding.btnMapCheckIn.setVisibility(View.VISIBLE);
                } else {
                    binding.ivCamera.setVisibility(View.VISIBLE);
                    binding.btnMapCheckIn.setVisibility(View.GONE);
                }
            }
        }*/
    }

    private void changePage(int position, boolean isNext, boolean isButtonClick) {
        updateModifiedDataInTable();
        hideSoftKeyboard(SubFormDetailActivity.this);
        if (!isNext) {
            setFlipAnimation(false);
            if (!isButtonClick) {
                for (int j = currentPage; j > position; j--) {
                    binding.vfPages.showPrevious();
                    currentPage--;
                    if (currentPage == 1)
                        break;
                }
            } else {
                binding.vfPages.showPrevious();
                currentPage--;
            }
        } else {
            setFlipAnimation(true);
            if (!isButtonClick) {

                for (int j = 1; j < position - currentPage; j++) {
                    binding.vfPages.showNext();
                    currentPage++;
                }
            } else {
                binding.vfPages.showNext();
                currentPage++;
            }
            if (!isButtonClick)
                currentPage = position;
        }

        pageNumber = currentPage;
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SIGN_FOR_RESULT:
                if (data != null && data.getExtras() != null)
                    if (data.hasExtra("signaturePath") && data.hasExtra("tag") && data.hasExtra("isSign")) {
                        if (data.getIntExtra("tag", 0) == -1) {
                            finish();
                        } else
                            setBitmapOnSignatureView(data.getExtras());
                    } else if (data.hasExtra("tag") && data.hasExtra("isSign") && data.hasExtra("isSketch") && data.hasExtra("isEmpty")) {
                        clearSignature(data.getExtras());
                    }
                break;
            case Constants.MAP_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        doAfterComeBackFromNewPropertyScreen(data);
                    }
                }
                break;
            case Constants.ADD_ADDRESS_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        updateAddressData(data.getExtras(), binding.vfPages);
                    }
                }
                break;
            case REQUEST_CAMERA:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        if (data.getIntExtra("tag", 0) == -1) {
                            finish();
                        } else
                            setImageInImageView(data);
                    }
                }
                break;

            case LOCATION_REQUEST_CODE:
                if (resultCode == RESULT_OK) {
                    isGpsEnableCalled = true;
                    startLocationServiceIfNotStarted();
                }
                break;
        }
    }

    private void clearSignature(Bundle extras) {
        int tagOfSign = extras.getInt("tag");
        boolean isSign = extras.getBoolean("isSign");
        String signaturePath = "";
        JSONObject jsonObject = (JSONObject) mapSignature.get(tagOfSign);
        if (jsonObject != null && jsonObject.has(Constants.VALUE)) {
            try {
                JSONArray array = jsonObject.getJSONArray(Constants.VALUE);
                if (array.length() > 0) {
                    if (array.getJSONObject(0).has(Constants.IMAGEPATHHIGH) && (!TextUtils.isEmpty(array.getJSONObject(0).getString(Constants.IMAGEPATHHIGH)))) {
                        signaturePath = array.getJSONObject(0).getString(Constants.IMAGEPATHHIGH);
                    } else {
                        if (array.getJSONObject(0).has(Constants.IMAGEPATHLOW) && (!TextUtils.isEmpty(array.getJSONObject(0).getString(Constants.IMAGEPATHLOW))))
                            signaturePath = array.getJSONObject(0).getString(Constants.IMAGEPATHLOW);
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
        View sign = binding.vfPages.findViewWithTag(tagOfSign);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);

        if (extras.getBoolean("isEmpty")) {
            if (!TextUtils.isEmpty(signaturePath))
                ImageUtil.deleteImageFromSDCard(signaturePath);

            ivSign.setImageBitmap(null);
            tvClear.setVisibility(View.GONE);
            if (jsonObject != null) {
                updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, false);
                String[] value = {};
                try {
                    jsonObject.put(Constants.VALUE, new JSONArray(value));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }

            try {
                JSONObject jsonImageData;
                FormData data = getDataFromTable(formData.getFormPKId());
                jsonImageData = new JSONObject(data.getImageData());
                if (isSign) {
                    if (StaticUtils.getTMFormPendingCount(formData.getFormPKId()) > 0)
                        StaticUtils.setTMFormPendingCount(formData.getFormPKId(), StaticUtils.getTMFormPendingCount(formData.getFormPKId()) - 1);
                }

                JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                int index = -1;
                for (int i = 0; i < jsonImageArray.length(); i++) {
                    JSONObject object1 = jsonImageArray.getJSONObject(i);
                    if (tag == object1.getInt(Constants.TAGID)) {
                        index = i;
                    }
                }
                if (index != -1) {
                    jsonImageArray.remove(index);
                }
                updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    private void updateImageDataByPkId(int formPKId, String jsonImageData) {
        if (isTMForm()) {
            TblTMForms tblTNMForms = new TblTMForms(this);
            tblTNMForms.updateImageDataByPkId(formPKId, jsonImageData);
        } else {
            TblForms tblForms = new TblForms(this);
            tblForms.updateImageDataByPkId(formPKId, jsonImageData);
        }
    }

    private void setBitmapOnSignatureView(Bundle extras) {

        saveDataInDB(formData);
        int tag = extras.getInt("tag", -1);
        String signaturePath = extras.getString("signaturePath");
        boolean increaseCount = extras.getBoolean("increaseCount");

        JSONObject jsonObject = (JSONObject) mapSignature.get(tag);
        JSONArray jsonArray = new JSONArray();
        try {
            JSONObject signatureJsonObject = new JSONObject();
            signatureJsonObject.put(Constants.IMAGEID, imageId);
            signatureJsonObject.put(Constants.LRIMAGE, "");
            signatureJsonObject.put(Constants.HRIMAGE, "");
            signatureJsonObject.put(Constants.IMAGEPATHLOW, "");
            signatureJsonObject.put(Constants.IMAGEPATHHIGH, signaturePath);
            signatureJsonObject.put(Constants.IS_SIGNATURE, true);
            jsonArray.put(signatureJsonObject);
            if (jsonObject != null) {
                jsonObject.put(Constants.VALUE, jsonArray);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        JSONObject signatureObject = null;
        try {
            JSONObject jsonImageData;
            FormData data = getDataFromTable(formData.getFormPKId());
            jsonImageData = new JSONObject(data.getImageData());
            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
            if (increaseCount) {
                int tmFormPendingCount = Math.max(StaticUtils.getTMFormPendingCount(formData.getFormPKId()), 0);
                StaticUtils.setTMFormPendingCount(formData.getFormPKId(), tmFormPendingCount + 1);
            }

            for (int i = 0; i < jsonImageArray.length(); i++) {
                JSONObject object = jsonImageArray.getJSONObject(i);
                if (object.getInt(Constants.TAGID) == tag) {
                    signatureObject = object;
                    break;
                }
            }
            if (signatureObject == null) {
                signatureObject = new JSONObject();
                jsonImageArray.put(signatureObject);

            }
            signatureObject.put(Constants.IMAGEID, imageId);
            signatureObject.put(Constants.TAGID, tag);
            signatureObject.put(Constants.LRIMAGE, "");
            signatureObject.put(Constants.HRIMAGE, "");
            signatureObject.put(Constants.IMAGEPATHHIGH, "");
            signatureObject.put(Constants.IMAGEPATHLOW, signaturePath);
            signatureObject.put(Constants.IS_SIGNATURE, true);
            updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            updateModifiedDataInTable();
            imageId++;
            updateImageCountByPkId(formData.getFormPKId(), imageId);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        View sign = binding.vfPages.findViewWithTag(tag);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        tvClear.setVisibility(View.VISIBLE);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);
        if (jsonObject != null) {
            updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, true);
        }

        tvClear.setOnClickListener(v -> {

            ImageUtil.deleteImageFromSDCard(signaturePath);
            ivSign.setImageBitmap(null);
            tvClear.setVisibility(View.GONE);
            if (jsonObject != null) {
                updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, false);
            }
            String[] value = {};
            try {
                if (jsonObject != null) {
                    jsonObject.put(Constants.VALUE, new JSONArray(value));
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
            try {
                JSONObject jsonImageData;
                FormData data = getDataFromTable(formData.getFormPKId());
                jsonImageData = new JSONObject(data.getImageData());
                if (StaticUtils.getTMFormPendingCount(formData.getFormPKId()) > 0) {
                    StaticUtils.setTMFormPendingCount(formData.getFormPKId(), StaticUtils.getTMFormPendingCount(formData.getFormPKId()) - 1);
                }

                JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                int index = -1;
                for (int i = 0; i < jsonImageArray.length(); i++) {
                    JSONObject object1 = jsonImageArray.getJSONObject(i);
                    if (tag == object1.getInt(Constants.TAGID)) {
                        index = i;
                    }
                }
                if (index != -1) {
                    jsonImageArray.remove(index);
                }
                updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        });
        ImageUtil.loadImageInGlide(ivSign, signaturePath);
    }


    private void doAfterComeBackFromNewPropertyScreen(Intent data) {
        if (data.getExtras() == null)
            return;
        try {
            if (!TextUtils.isEmpty(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                try {
                    switch (Objects.requireNonNull(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN:
                            PopUtils.showAlertDialogPositiveButtonOnly(SubFormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN, getString(R.string.joinAlertMessage));
                            break;

                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX:
                            PopUtils.showAlertDialogPositiveButtonOnly(SubFormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX, getString(R.string.maxAlertMessage));
                            break;

                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST:
                            PopUtils.showAlertDialogPositiveButtonOnly(SubFormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST, getString(R.string.pastAlertMessage));
                            break;
                        default:
                            break;

                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void setImageInImageView(Intent data) {
        int tag;
        ImageViewAdapter imageViewAdapter;
        RecyclerView rvImages;
        String imagePath;
        boolean isFromMap;
        if (data.getExtras() != null && data.hasExtra("tag")) {
            tag = data.getExtras().getInt("tag", -1);
            isFromMap = data.getExtras().getBoolean("isFromMap", false);

            View view = null;
            try {
                view = binding.vfPages.findViewWithTag(tag);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

            if (view == null)
                return;
            TextView tvUploadPhoto = view.findViewById(R.id.tvUploadPhoto);

            JSONObject mainJson = (JSONObject) mapImageUpload.get(tag);

            if (mainJson != null) {
                updateValidationInMap(mainJson, tvUploadPhoto, "", false, true, true);
                JSONArray mainJsonArray = null;
                try {
                    mainJsonArray = mainJson.getJSONArray(Constants.VALUE);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
                rvImages = view.findViewById(R.id.rlImages);
                imageViewAdapter = (ImageViewAdapter) rvImages.getAdapter();
                ArrayList<ImageData> lstImage = new ArrayList<>();
                if (imageViewAdapter != null) {
                    lstImage = imageViewAdapter.getImageList();
                }
                String imagePathData = "";
                FormData formData1 = getDataFromTable(formData.getFormPKId());

                if (data.hasExtra("imagePath")) {

                    imagePath = data.getExtras().getString("imagePath");
                    JSONObject jsonImageData;
                    FormData formData2 = getDataFromTable(formData.getFormPKId());
                    try {
                        jsonImageData = new JSONObject(formData2.getImageData());
                        JSONArray jsonArray = new JSONArray(imagePath);
                        JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                        int tmFormPendingCount = Math.max(StaticUtils.getTMFormPendingCount(formData1.getFormPKId()), 0);
                        StaticUtils.setTMFormPendingCount(formData1.getFormPKId(), tmFormPendingCount + jsonArray.length());
                        for (int i = 0; i < jsonArray.length(); i++) {
                            JSONObject jsonImage = new JSONObject();
                            JSONObject mainImageJson = new JSONObject();

                            JSONObject jsonObjects = jsonArray.getJSONObject(i);
                            String imagePathHigh = "";
                            if (jsonObjects.has(Constants.IMAGEPATHHIGH)) {
                                imagePathHigh = jsonObjects.getString(Constants.IMAGEPATHHIGH);
                                jsonImage.put(Constants.IMAGEPATHHIGH, imagePathHigh);
                                mainImageJson.put(Constants.IMAGEPATHHIGH, imagePathHigh);
                            }

                            addPinData(jsonObjects, jsonImage);

                            String imagePathLow = "";
                            jsonImage.put(Constants.TAGID, tag);
                            jsonImage.put(Constants.LRIMAGE, "");
                            jsonImage.put(Constants.HRIMAGE, "");
                            jsonImage.put(Constants.IS_SIGNATURE, false);
                            if (jsonObjects.has(Constants.IMAGEPATHLOW)) {
                                imagePathLow = jsonObjects.getString(Constants.IMAGEPATHLOW);
                                jsonImage.put(Constants.IMAGEPATHLOW, imagePathLow);
                                mainImageJson.put(Constants.IMAGEPATHLOW, imagePathLow);
                            }
                            if (!TextUtils.isEmpty(imagePathHigh)) {
                                imagePathData = imagePathHigh;
                            } else {
                                imagePathData = imagePathLow;
                            }
                            if (!TextUtils.isEmpty(imagePathData)) {
                                ImageData imageData = new ImageData();
                                imageData.setImagePath(imagePathData);
                                imageData.setImageId(imageId);
                                lstImage.add(imageData);
                                jsonImage.put(Constants.IMAGEID, imageId);
                                mainImageJson.put(Constants.IMAGEID, imageId);
                                imageId++;
                            }

                            mainJsonArray.put(mainImageJson);
                            jsonImageArray.put(jsonImage);
                        }
                        updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                        updateModifiedDataInTable();
                        updateImageCountByPkId(formData.getFormPKId(), imageId);
                        formData.setImageData(jsonImageData.toString());
                        if (imageViewAdapter != null) {
                            imageViewAdapter.updateList(lstImage);
                        }
                        if (lstImage.isEmpty()) {
                            rvImages.setVisibility(View.GONE);
                        } else {
                            rvImages.setVisibility(View.VISIBLE);
                        }

                        if (isFromMap) {
                            showMapView(false);
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
        }
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            onBackPressed();
        } else if (viewId == R.id.tvDate) {
            showDatePickerForCheckInOrOut();
        } else if (viewId == R.id.tvTime) {
            showTimePickerForCheckInOrOut();
        } else if (viewId == R.id.btnCheckIn) {
            checkAndDoCheckIn();
        } else if (viewId == R.id.ivSecondRight) {
            navigateToUploadActivityScreen(binding.tlOther.ivSecondRight);
        } else if (viewId == R.id.ivPlotMap) {
            showMapView(false);
        } else if (viewId == R.id.ivCamera) {
            showImageComponentList();
        } else if (viewId == R.id.ivFormDetail) {
            showMapView(true);
        } else if (viewId == R.id.tvSubmit) {

            showCancelCheckedInForm();

        }
    }

    private void showCancelCheckedInForm() {
        PopUtils.showCustomTwoButtonAlertDialog(this, null, getString(R.string.cancel_checkin_form_alert_message),
                getString(R.string.yes), getString(R.string.no), false,
                (dialog, which) -> {
                    deleteFormAndSendCheckoutBreadcrumbs();
                }, (dialog, which) -> {

                });
    }

    private void deleteFormAndSendCheckoutBreadcrumbs() {
        if (isTMForm()) {
            //Send checkout breadcrumb with type 3 along with formSubmittedFor key
            prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), true, true, formData.getSubFormOtherData().getCrewId());
            //Delete canceled form data from DB
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.deleteDataByPKId(formData.getFormPKId());

        } else {
            //Send checkout breadcrumb with type 3 along with formSubmittedFor key
            prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude, -1, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), true, true, formData.getSubFormOtherData().getCrewId());
            //Delete canceled form data from DB
            TblForms tblForms = new TblForms(this);
            tblForms.deleteDataByPKId(formData.getFormPKId());
        }
        //Back user to main form screen
        onBackPressed();

    }

    private void showDatePickerForCheckInOrOut() {
        DatePickerDialog datePickerDialog = getDatePickerDialog();
        datePickerDialog.getDatePicker().setMaxDate(System.currentTimeMillis());
        if (formData.getCheckin_time() > 0) {
            datePickerDialog.getDatePicker().setMinDate(formData.getCheckin_time());
        }
    }

    @NonNull
    private DatePickerDialog getDatePickerDialog() {
        DatePickerDialog datePickerDialog = new DatePickerDialog(this,
                (view, year, monthOfYear, dayOfMonth) -> {
                    calenderYear = year;
                    month = monthOfYear;
                    day = dayOfMonth;
                    updateCalender();
                    binding.tvDate.setText(DateUtil.getDatInDDMMMYYYY(calendar.getTimeInMillis()));
                }, calenderYear, month, day);
        datePickerDialog.show();
        return datePickerDialog;
    }

    private void showTimePickerForCheckInOrOut() {
        calenderYear = calendar.get(Calendar.YEAR);
        month = calendar.get(Calendar.MONTH);
        day = calendar.get(Calendar.DAY_OF_MONTH);
        hour = calendar.get(Calendar.HOUR_OF_DAY);
        min = calendar.get(Calendar.MINUTE);
        TimePickerDialog timePickerDialog = getTimePickerDialog();
        timePickerDialog.updateTime(hour, min);
    }

    /**
     * function to launch time picker for sub form check in and check out
     *
     * @return timePickerDialog
     */
    private TimePickerDialog getTimePickerDialog() {
        TimePickerDialog timePickerDialog = new TimePickerDialog(this,
                (view, hourOfDay, minute) -> {
                    hour = hourOfDay;
                    min = minute;
                    updateCalender();
                    binding.tvTime.setText(DateUtil.getTimeInHHMM(calendar.getTimeInMillis()));
                }, hour, min, false);
        timePickerDialog.show();
        return timePickerDialog;
    }

    private void showMapView(boolean shouldShowFormView) {
        if (shouldShowFormView) {
            binding.rlFormDetails.setVisibility(View.VISIBLE);
            binding.rlMap.setVisibility(View.GONE);
            binding.tlOther.ivPlotMap.setVisibility(View.VISIBLE);
            binding.tlOther.ivFormDetail.setVisibility(View.GONE);
            clearGoogleMap();
        } else {
            binding.rlFormDetails.setVisibility(View.GONE);
            binding.rlMap.setVisibility(View.VISIBLE);
            binding.tlOther.ivPlotMap.setVisibility(View.GONE);
            binding.tlOther.ivFormDetail.setVisibility(View.VISIBLE);
            if (formData.getIsCheckInOut() && formData.getCheckin_time() <= 0) {
                binding.ivCamera.setVisibility(View.GONE);
                binding.btnMapCheckIn.setVisibility(View.VISIBLE);
            } else {
                binding.ivCamera.setVisibility(View.VISIBLE);
                binding.btnMapCheckIn.setVisibility(View.GONE);
            }
            loadMap();
        }
    }

    private void checkAndDoCheckIn() {
        //Added Code to prevent double click
        if (SystemClock.elapsedRealtime() - mLastCheckInClickTime < 1000) {
            return;
        }
        mLastCheckInClickTime = SystemClock.elapsedRealtime();
        if (formData.getCheckin_time() > 0) {
            if (calendar.getTimeInMillis() / 60000 > System.currentTimeMillis() / 60000) {
                showForeGroundToast(getString(R.string.selected_date_must_be_less_than_current_time));
                return;
            }
            if (calendar.getTimeInMillis() >= formData.getCheckin_time()) {
                if (isFormValidate(false)) {
                    didCheckInOrCheckOut = true;
                    new SaveDataAndValidateData(formData).execute();
                }
            } else {
                showForeGroundToast(getString(R.string.selected_date_and_time_must_be_greater_than_checkin_time));
            }
        } else {
            if (calendar.getTimeInMillis() / 60000 > System.currentTimeMillis() / 60000) {
                showForeGroundToast(getString(R.string.selected_date_must_be_less_than_current_time));
            } else {
                doCheckIn();
            }
        }
    }

    private void updateCalender() {
        calendar.set(Calendar.YEAR, calenderYear);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, day);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, min);
    }


    private void setCurrentData() {
        resetGlobalData();
        mFormPkId = currentFormPkId;
        initializeData(mFormPkId, currentSiteData.getSiteId());
        setData(true);
        enableDisableCrewView();
    }

    private void initializeData(int formPkId, long siteId) {
        clearListData();
        if (isTMForm()) {
            TblSites tblSites = new TblSites(this);
            siteData = tblSites.getDataFromSiteId(siteId);
            lastUpdatedBuildingId = String.valueOf(siteData.getSiteId());
            lastBuildingId = lastUpdatedBuildingId;
        }
        mFormPkId = formPkId;
        formData = getDataFromTable(mFormPkId);
        if (formData == null || TextUtils.isEmpty(formData.getModifiedFormData())) {
            finish();
        }
        formString = formData.getModifiedFormData();
    }

    /**
     * Clear Date when user open already checked in form from another form detail screen
     */
    private void clearListData() {
        mapImageUpload.clear();
        mapSignature.clear();
        mapTagObject.clear();
        panelValidationData.clear();
        lstChildMaterial.clear();
        if (validationMap != null) {
            validationMap.clear();
        }
        lstCrewComponent.clear();
        lstGeoComponent.clear();
        lstView.clear();
    }

    private void enableDisableCrewView() {
        try {
            View crewView = getViewForCrewComponent();
            if (crewView != null) {
                LinearLayout llMultiSelect = crewView.findViewById(R.id.llMultiSelect);
                llMultiSelect.setEnabled(false);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }


    private void doCheckIn() {

        if (formData.getIsCheckInOut() && validateManageCrewSelectionAtCheckInTime() && checkRequiredServiceValidationAtCheckinTime()) {
            updateCalender();
            formData.setCheckin_time(calendar.getTime().getTime());
            updateFormDataAndCheckInTime(formData.getFormPKId());
            updateMainFormHasSubForm();
            checkAndSetBottomButtonVisibility();
            didCheckInOrCheckOut = true;
            checkInFormAndInitialSetup();
        }
    }

    private boolean checkRequiredServiceValidationAtCheckinTime() {
        int counter = 0;
        boolean isValidData = true;
        for (View view : lstServiceValidationView) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                    if (view instanceof EditText) {
                        isValidData = false;
                        showValidateMessageForEditText(validationData, view, scrollView, counter, false);
                    } else if (view instanceof TextView) {
                        isValidData = false;
                        showValidateMessageForTextView(validationData, view, scrollView, counter, false);
                    }
                }
            }
        }
        return isValidData;
    }

    private void updateFormDataAndCheckInTime(int formPKId) {
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.updateFormDataAndCheckInTime(TblTMForms.TABLE_NAME, formData.getSubFormOtherData(), formData.getCheckin_time(), formPKId);
        } else {
            TblForms tblForms = new TblForms(this);
            tblForms.updateFormDataAndCheckInTime(TblForms.TABLE_NAME, formData.getSubFormOtherData(), formData.getCheckin_time(), formPKId);
        }
    }

    private void updateMainFormHasSubForm() {
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.updateMainFormHasSubForm(TblTMForms.TABLE_NAME, mainFromPKId);
        } else {
            TblForms tblForms = new TblForms(this);
            tblForms.updateMainFormHasSubForm(TblForms.TABLE_NAME, mainFromPKId);
        }
    }

    private void checkInFormAndInitialSetup() {
        // First we need to clone all Json Object in new array and then remove unnecessary data from cloned Array
        removeNotSelectedServices();
        JSONArray selectedServices = StaticUtils.getCloneOfJSONArray(selectedServiceArray);
        String selectedServiceData = StaticUtils.filterPreSelectServiceJSON(selectedServices);
        if (isTMForm()) {
            setSiteAndFormBroadCrumbs(2, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), selectedServiceData);
        } else {
            setSiteAndFormBroadCrumbs(2, -1, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), selectedServiceData);
        }
        enableOrDisableView(true);
        disableServiceView();
        //Post event to update form data in form listing screen
        EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
        onBackPressed();
    }


    private void resetGlobalData() {
        currentPage = 1;
        imageId = 1;
        updateImageCountByPkId(formData.getFormPKId(), imageId);
    }

    private boolean validateManageCrewSelectionAtCheckInTime() {
        return readJsonFileForManagedCrew(updatedJsonObject);
    }

    protected boolean readJsonFileForManagedCrew(JSONObject updatedJsonObject) {
        boolean shouldCheckIn = true;
        try {
            JSONArray jsonArray = updatedJsonObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            return jsonArrayReadForManagedCrew(array, shouldCheckIn);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return shouldCheckIn;
    }


    private boolean jsonArrayReadForManagedCrew(JSONArray jsonArray, boolean shouldCheckIn) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.getString(Constants.TYPE).equals(Constants.DYNAMIC_DROPDOWN) && object.has(Constants.IS_MANAGE_CREW_FIELD) && object.getBoolean(Constants.IS_MANAGE_CREW_FIELD)) {
                    shouldCheckIn = object.has(VALUE) && !TextUtils.isEmpty(object.getString(VALUE));
                    if (!shouldCheckIn) {
                        showForeGroundToast(getString(R.string.required, object.getString(TITLE)));
                    }
                    return shouldCheckIn;
                }
                // also check required type for service type
                if (object.getString(Constants.TYPE).equals(Constants.SERVICE) && object.getBoolean(Constants.ISREQUIRED)) {


                    shouldCheckIn = object.has(VALUE) && !TextUtils.isEmpty(object.getString(VALUE));
                    if (!shouldCheckIn) {
                        showForeGroundToast(getString(R.string.required, object.getString(TITLE)));
                    }
                    return shouldCheckIn;
                }

            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
                return false;
            }
        }
        return shouldCheckIn;
    }

    private void submitForm() {
        if (isFinishing())
            return;
        try {
            startProgress();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            // Re initiate data executor service if its been showtdown or terminated
            BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
            BaseApplication.getInstance().dataExecutorService.submit(() -> {
                boolean shouldExecuteSignaturePart = false;
                try {
                    // BaseApplication.getInstance().threadLock.lock();
                    shouldExecuteSignaturePart = submitFormDataInSafeThread();
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                } finally {
                    if (shouldExecuteSignaturePart) {
                        addSignatureAndUploadData(formData);
                    } else {
                        stopProgressDialog();
                        if (currentFormPkId > 0) {
                            if (!isTMForm()) {
                                setSiteAndFormBroadCrumbs(3, -1, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                            }
                            if (isTMForm()) {
                                //Call checkout and close breadcrumb (checkout =3 and close = 7) and then check-in current form
                                setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                                setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                            }
                            notifyAndSetCurrentData();
                        } else {
                            goBackToMainFormScreen();
                        }
                    }
                }
            });
        } catch (RejectedExecutionException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void addSignatureAndUploadData(FormData formData) {
        runOnUiThread(this::startProgress);
        addSignaturInUploadingQueue(isTMForm,formData);
        try {
            BaseApplication.getInstance().startImageUpload();
            saveDataInDbAtFormSubmission(formData);
            addDataInUploadQueue(isTMForm(), formData, false, lastUpdatedBuildingId, lastBuildingId);
            runOnUiThread(this::stopProgressDialog);
            if (currentFormPkId > 0) {
                //Call checkout and close breadcrumb (checkout =3 and close = 7) and then check-in current form
                if (isTMForm()) {
                    setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                } else {
                    setSiteAndFormBroadCrumbs(3, -1, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                }
                notifyAndSetCurrentData();
            } else {
                goBackToMainFormScreen();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
      /*  AddSignatureInUploadQueue addSignatureInUploadQueue = new AddSignatureInUploadQueue(this, formData, isTMForm(), result -> {
            try {
                stopProgressDialog();
                AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());
                saveDataInDbAtFormSubmission(formData);
                addDataInUploadQueue(isTMForm(), formData, false, lastUpdatedBuildingId, lastBuildingId);

                if (currentFormPkId > 0) {
                    //Call checkout and close breadcrumb (checkout =3 and close = 7) and then check-in current form
                    if (isTMForm()) {
                        setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                    } else {
                        setSiteAndFormBroadCrumbs(3, -1, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
                    }
                    notifyAndSetCurrentData();

                } else {
                    goBackToMainFormScreen();
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
        });
        addSignatureInUploadQueue.execute();*/

    }


    private void notifyAndSetCurrentData() {
        runOnUiThread(() -> {
            setCurrentData();
            currentFormPkId = 0;
            EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
        });
    }

    private void saveDataInDB(FormData formData) {
        if (isTMForm()) {
            TblTMForms tblTNMForms = new TblTMForms(SubFormDetailActivity.this);
            if (updatedJsonObject != null) {
                tblTNMForms.updateModifiedFormByPkId(formData.getFormPKId(), updatedJsonObject.toString());
                tblTNMForms.updateLastBuildingIdByPkId(formData.getFormPKId(), (int) siteData.getSiteId());
            }
        } else {
            TblForms tblForms = new TblForms(SubFormDetailActivity.this);
            if (updatedJsonObject != null) {
                tblForms.updateModifiedFormByPkId(formData.getFormPKId(), updatedJsonObject.toString());
                if (Integer.parseInt(lastUpdatedBuildingId) > 0) {
                    tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastUpdatedBuildingId));
                } else {
                    tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastBuildingId));
                }
            }
        }

    }

    private void saveDataInDbAtFormSubmission(FormData formData) {
        saveDataInDB(formData);
        formData.setFormSubmitted(true);
        formData.setSf_submited(System.currentTimeMillis() / 1000);
        formData.setLatitude(MainActivity.currentLatitude);
        formData.setLongitude(MainActivity.currentLongitude);
        if (isTMForm()) {
            TblTMForms tblTNMForms = new TblTMForms(SubFormDetailActivity.this);
            tblTNMForms.updateFormSubmittedStatus(formData.getFormPKId(), MainActivity.currentLatitude, MainActivity.currentLongitude);
            tblTNMForms.updateFormCheckOutTime(TblTMForms.TABLE_NAME, calendar.getTime().getTime(), formData.getFormPKId());
        } else {
            TblForms tblForms = new TblForms(SubFormDetailActivity.this);
            tblForms.updateFormSubmittedStatus(formData.getFormPKId(), MainActivity.currentLatitude, MainActivity.currentLongitude);
            tblForms.updateFormCheckOutTime(TblForms.TABLE_NAME, calendar.getTime().getTime(), formData.getFormPKId());
        }
    }

    public boolean isFormValidate(boolean isFromBackPress) {
        boolean isValidData = true;
        int counter = 0;

        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    if (validationData.getPageNumber() < currentPage) {
                        //if (validationData.getPageNumber() > 1)
                        //changePage(validationData.getPageNumber() - 1, false, false);
                        //else
                        changePage(validationData.getPageNumber(), false, false);
                    }
                }
            }
        }
        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                    if (view instanceof EditText) {
                        isValidData = false;
                        showValidateMessageForEditText(validationData, view, scrollView, counter, isFromBackPress);
                    } else if (view instanceof TextView) {
                        isValidData = false;
                        showValidateMessageForTextView(validationData, view, scrollView, counter, isFromBackPress);
                    }
                }
            }

        }
        if (isValidData) {
            isValidData = checkPanelValidation();
        }
        return isValidData;
    }

    private void showValidateMessageForTextView(ValidationData validationData, View view, NestedScrollView scrollView, int counter, boolean isFromBackPress) {
        if (!isFromBackPress) {
            ((TextView) view).setError(getString(R.string.msg_field_can_not_be_an_empty));
        }
        if (binding.vfPages.getDisplayedChild() == validationData.getPageNumber() - 1) {
            counter++;
            if (counter == 1) {
                if (!isFromBackPress) {
                    showForeGroundToast(getString(R.string.required, validationData.getTitle()));
                }
                focusOnView(binding.llMain, scrollView, view);
            }
        }
    }

    private void showValidateMessageForEditText(ValidationData validationData, View view, NestedScrollView scrollView, int counter, boolean isFromBackPress) {
        if (validationData.isEmail()) {
            if (!isFromBackPress) {
                ((EditText) view).setError(getString(R.string.msg_valid_email_address));
                ((EditText) view).setSelection(((EditText) view).getText().toString().trim().length());
            }
        } else {
            if (!isFromBackPress) {
                ((EditText) view).setError(getString(R.string.msg_field_can_not_be_an_empty));
            }
        }
        if (binding.vfPages.getDisplayedChild() == validationData.getPageNumber() - 1) {
            counter++;
            if (counter == 1) {
                if (!isFromBackPress) {
                    showForeGroundToast(getString(R.string.required, validationData.getTitle()));
                }
                focusOnView(binding.llMain, scrollView, view);
            }
        }
    }


    private boolean checkPanelValidation() {
        formData = getDataFromTable(mFormPkId);
        if (panelValidationData.isEmpty())
            return true;
        StaticUtils.scanPanelDataInJSON(formData.getModifiedFormData(), panelValidationData);
        Iterator<Integer> panelIterator = panelValidationData.keySet().iterator();
        while (panelIterator.hasNext()) {
            int key = (int) panelIterator.next();
            PanelData panelData = panelValidationData.get(key);
            if (panelData != null && !panelData.isValidated()) {
                NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                changePage(panelData.getPageNumber(), false, false);
                showForeGroundToastLong(getString(R.string.required_panel, panelData.getTitle()));
                focusOnView(binding.llMain, scrollView, panelData.getPanelView());
                return false;
            }
        }
        return true;
    }

    private void concatImageData() {
        try {
            JSONArray jsonArray = updatedJsonObject.getJSONArray(Constants.PAGES);
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            jsonArrayReadToUpdateImageUrl(array);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            updateModifiedDataInTable();
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void jsonArrayReadToUpdateImageUrl(JSONArray jsonArray) {
        formData = getDataFromTable(formData.getFormPKId());
        String imagePathString = formData.getImageData();
        for (int i = 0; i < jsonArray.length(); i++) {
            try {

                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayReadToUpdateImageUrl(array);
                } else if (object.has(Constants.CHOICES)) {
                    try {
                        JSONArray array = object.getJSONArray(Constants.CHOICES);
                        for (int j = 0; j < array.length(); j++) {
                            JSONObject jsonObject = null;
                            try {
                                if (array.get(j) instanceof JSONObject) {
                                    jsonObject = array.getJSONObject(j);
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                            if (jsonObject != null) {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    try {
                                        JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                        if (jsonArray1.length() > 0) {
                                            jsonArrayReadToUpdateImageUrl(jsonArray1);
                                        }
                                    } catch (JSONException e) {
                                        e.getStackTrace();

                                    }
                                }
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                } else {
                    if (!TextUtils.isEmpty(imagePathString)) {
                        JSONObject jsonObject = new JSONObject(imagePathString);
                        JSONArray array = jsonObject.getJSONArray(Constants.DATA);
                        if (object.getString(Constants.TYPE).equals(Constants.IMAGE_UPLOAD)) {
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);
                                        for (int k = 0; k < array.length(); k++) {
                                            JSONObject object2 = array.getJSONObject(k);
                                            if (object1.has(Constants.IMAGEID) && object2.has(Constants.IMAGEID) && object2.getInt(Constants.IMAGEID) == object1.getInt(Constants.IMAGEID)) {
                                                object1.put(Constants.LRIMAGE, object2.getString(Constants.LRIMAGE));
                                                object1.put(Constants.HRIMAGE, object2.getString(Constants.HRIMAGE));
                                                if (object2.has(Constants.IMAGE_UUID)) {
                                                    object1.put(Constants.IMAGE_UUID, object2.getString(Constants.IMAGE_UUID));
                                                }
                                                object1.put(Constants.IMAGEPATHHIGH, object2.getString(Constants.IMAGEPATHHIGH));
                                                object1.put(Constants.IMAGEPATHLOW, object2.getString(Constants.IMAGEPATHLOW));
                                                addPinData(object2, object1);
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        } else if (object.getString(Constants.TYPE).equals(Constants.ISSUES)) {
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);

                                        if (object1.has(Constants.VALUE)) {
                                            JSONArray jsonArray2 = object1.getJSONArray(Constants.VALUE);
                                            for (int k = 0; k < jsonArray2.length(); k++) {
                                                JSONObject object2 = jsonArray2.getJSONObject(k);
                                                for (int l = 0; l < array.length(); l++) {
                                                    JSONObject object3 = array.getJSONObject(l);
                                                    if (object2.getInt(Constants.IMAGEID) == object3.getInt(Constants.IMAGEID)) {
                                                        object2.put(Constants.LRIMAGE, object3.getString(Constants.LRIMAGE));
                                                        object2.put(Constants.HRIMAGE, object3.getString(Constants.HRIMAGE));
                                                        if (object3.has(Constants.IMAGE_UUID)) {
                                                            object2.put(Constants.IMAGE_UUID, object3.getString(Constants.IMAGE_UUID));
                                                        }
                                                        object2.put(Constants.IMAGEPATHHIGH, object3.getString(Constants.IMAGEPATHHIGH));
                                                        object2.put(Constants.IMAGEPATHLOW, object3.getString(Constants.IMAGEPATHLOW));
                                                        addPinData(object3, object2);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        }
                    }

                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    private void addPinData(JSONObject fromJson, JSONObject toJson) {
        try {
            if (formData.isPlotOnMap()) {
                if (fromJson.has(Constants.MAP_PIN_URL))
                    toJson.put(Constants.MAP_PIN_URL, fromJson.getString(Constants.MAP_PIN_URL));
                if (fromJson.has(Constants.PARAM_LAT))
                    toJson.put(Constants.PARAM_LAT, fromJson.getDouble(Constants.PARAM_LAT));
                if (fromJson.has(Constants.PARAM_LON))
                    toJson.put(Constants.PARAM_LON, fromJson.getDouble(Constants.PARAM_LON));
                if (fromJson.has(Constants.PIN_LABEL)) {
                    toJson.put(Constants.PIN_LABEL, fromJson.getString(Constants.PIN_LABEL));
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Constants.LOCATION_PERMISSION_REQUEST:
                doAfterLocationPermissionResult(permissions, grantResults);
                break;
        }
    }


    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    private void goBackToMainFormScreen() {
        if (formData.getIsCheckInOut()) {
            if (isTMForm()) {
                setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
            } else {
                setSiteAndFormBroadCrumbs(3, -1, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), false, true, formData.getSubFormOtherData().getCrewId(), null);
            }
        }

        EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
        stopProgressDialog();
        showForeGroundToast(getString(R.string.txt_form_submitted));
        finish();
    }


    @Override
    public void onBackPressed() {
        resetAllDataIfNotCheckedIn();
        if (currentFormPkId > 0) {
            setCurrentData();
            currentFormPkId = 0;
        } else {
            if (didCheckInOrCheckOut) {
                Intent intent = new Intent();
                setResult(RESULT_OK, intent);
            }
            super.onBackPressed();
        }
    }

    /**
     * Method to reset all data if user not check into the form and press back button
     */
    private void resetAllDataIfNotCheckedIn() {
        if (formData.getIsCheckInOut() && formData.getCheckin_time() == 0) {
            resetFormDataByPkId();

        } else {
            saveDataInDB(formData);
        }
    }

    private void resetFormDataByPkId() {
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.resetFormDataByPkId(formData.getFormPKId(), formData.getFormData(), formData.isCheckInOutComplete());
        } else {
            TblForms tblForms = new TblForms(this);
            tblForms.resetFormDataByPkId(formData.getFormPKId(), formData.getFormData());
        }
    }

    @Override
    public void onSuccessResponse(Response<AppDataResponse> response) {
    }

    @Override
    public void onFailureResponse(Throwable t) {
    }

    @Override
    public void onNoInternetConnection() {
    }

    @Override
    public void onUpdateAppVersion(int versionCode, boolean isForceUpdate) {
        checkAndShowAppUpdateDialog(versionCode, isForceUpdate);
    }


    private class SaveDataAndValidateData extends AsyncTask<Void, Void, Boolean> {
        FormData formData;

        SaveDataAndValidateData(FormData formData) {
            this.formData = formData;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            if (isFinishing())
                return;
            try {
                startProgress();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            saveDataInDB(formData);
            return false;
        }

        @Override
        protected void onPostExecute(Boolean shouldExecuteSignaturePart) {
            super.onPostExecute(shouldExecuteSignaturePart);
            stopProgressDialog();
            if (isFormValidate(false)) {
                submitForm();
            } else {
                binding.btnCheckIn.setClickable(true);
            }
        }
    }

    private boolean submitFormDataInSafeThread() {
        formData = getDataFromTable(mFormPkId);
        submitIssueAtFormSubmissionTime(context, isTMForm(), siteData, formData);
        updateStopServiceTimeAtSubmissionTime(updatedJsonObject);

        int pendingMediaCount = 0;
        try {
            JSONObject jsonObject = null;
            if (!TextUtils.isEmpty(formData.getImageData())) {
                jsonObject = new JSONObject(formData.getImageData());
                pendingMediaCount = Math.max(StaticUtils.getTMFormPendingCount(mFormPkId), 0);
            }
            if (pendingMediaCount == 0) {
                saveDataInDbAtFormSubmission(formData);
                addDataInUploadQueue(isTMForm(), formData, true, lastUpdatedBuildingId, lastBuildingId);
            } else {
                JSONArray imageArray = jsonObject.getJSONArray(Constants.DATA);
                if (imageArray.length() > 0) {
                    List<String> lstSignaturePath = new ArrayList<>();
                    for (int i = 0; i < imageArray.length(); i++) {
                        if (imageArray.getJSONObject(i).getBoolean(Constants.IS_SIGNATURE)) {
                            String imagePath = imageArray.getJSONObject(i).getString(IMAGEPATHLOW);
                            lstSignaturePath.add(imagePath);
                        }
                    }
                    if (!lstSignaturePath.isEmpty()) {
                        updateLastBuildingIdByPkId(formData.getFormPKId(), (int) siteData.getSiteId());
                        return true;
                    } else {
                        saveDataInDbAtFormSubmission(formData);
                        addDataInUploadQueue(isTMForm(), formData, false, lastUpdatedBuildingId, lastBuildingId);
                    }

                } else {
                    saveDataInDbAtFormSubmission(formData);
                    addDataInUploadQueue(isTMForm(), formData, true, lastUpdatedBuildingId, lastBuildingId);
                }
            }
        } catch (
                Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return false;
    }

    private void updateLastBuildingIdByPkId(int formPKId, int siteId) {
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(SubFormDetailActivity.this);
            tblTMForms.updateLastBuildingIdByPkId(formPKId, siteId);
        } else {
            TblForms tblForms = new TblForms(SubFormDetailActivity.this);
            if (Integer.parseInt(lastUpdatedBuildingId) > 0) {
                tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastUpdatedBuildingId));
            } else {
                tblForms.updateLastBuildingIdByPkId(formData.getFormPKId(), Integer.parseInt(lastBuildingId));
            }
        }
    }


    @Override
    protected void onStop() {
        super.onStop();
        try {
            if (!isDeviceLocked(this)) {
                if (updatedJsonObject != null) {
                    updateModifiedDataInTable();
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    @Override
    protected void onDestroy() {
        try {
            unregisterReceiver(mMessageReceiver);
            unregisterReceiver(formUpdateReceiver);
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        super.onDestroy();
    }
}
