package com.sitefotos.form;

import static android.app.Activity.RESULT_OK;
import static com.sitefotos.Constants.FORM_DETAIL_RESULT;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.Context;
import android.content.Intent;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BaseFragment;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.FormAdapter;
import com.sitefotos.databinding.FragmentFormListingBinding;
import com.sitefotos.event.AppDataCallBackEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.FormData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Response;


public class FormListingFragment extends BaseFragment implements SwipeRefreshLayout.OnRefreshListener, View.OnClickListener {
    private List<FormData> lstForms = new ArrayList<>();
    private final List<FormData> lstSearchForms = new ArrayList<>();
    private FormAdapter formAdapter;
    private Context context;

    private boolean isScreenVisible = false;
    private boolean detailEventCalled = false;
    private boolean shouldAutoNavigate = true;
    private FragmentFormListingBinding binding;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Inflate the layout for this fragment
        binding = FragmentFormListingBinding.inflate(inflater, container, false);
        initView();
        return binding.getRoot();
    }

    private void initView() {
        context = getContext();
        setupActionBar();
        setupAdapter();
        setEditTextChangeListener();
        setOnClickListener();
        manageSearchViewCloseTouchEvent(binding.edtSearch);
        binding.edtSearch.setImeOptions(EditorInfo.IME_ACTION_DONE);
        binding.edtSearch.setEnabled(false);
        getDataFromDatabase();


    }

    private void setupActionBar() {
        binding.tlOther.imgBtnBack.setVisibility(View.INVISIBLE);
        binding.tlOther.imgBtnBack.setEnabled(false);
        binding.tlOther.ivRight.setVisibility(View.GONE);
        binding.tlOther.tvTitle.setText(R.string.form_listing);
        if (getActivity() != null) {
            ((BaseActivity) getActivity()).setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
            updateMarginOfTitleView((BaseActivity) getActivity(), binding.tlOther.llTitle);

        }

    }


    private void setOnClickListener() {
        binding.tlOther.ivSecondRight.setOnClickListener(this);
    }

    private void getDataFromDatabase() {
        if (getActivity() != null) {
            ((MainActivity) getActivity()).dbExecutorService.execute(() -> {
                binding.progressBar.setVisibility(View.VISIBLE);
                TblForms tblForms = new TblForms(getContext());
                lstForms = tblForms.getAllForms();
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getActivity() != null && getActivity().isFinishing())
                        return;
                    if (!lstForms.isEmpty()) {
                        binding.progressBar.setVisibility(View.GONE);
                        if (isScreenVisible) {
                            navigateToDetailScreenIfOneDataInList();
                        }
                        formAdapter.updateList(lstForms, new ArrayList<>());
                        showSearchView(true);

                    } else {
                        showEmptyView(false);
                    }
                    binding.srlRefresh.setOnRefreshListener(this);
                });
            });
        }
    }

    private void setEditTextChangeListener() {
        binding.edtSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                searchText(charSequence.toString());
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        if (getActivity() == null)
            return;
        ((BaseActivity) getActivity()).visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView((BaseActivity) getActivity(), binding.tlOther.llTitle);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onAppDataCallBackEvent(AppDataCallBackEvent event) {
        if (event.response != null) {
            onSuccessResponse(event.response);
        }
        if (event.throwable != null) {
            onFailureResponse(event.throwable);
        }
        if (event.isNoInternetConnection) {
            onNoInternetConnection();
        }
    }

    private void searchText(String text) {
        if (!text.isEmpty()) {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
        } else {
            binding.edtSearch.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
        lstSearchForms.clear();
        if (formAdapter == null)
            return;
        if (!text.isEmpty()) {
            for (FormData data : lstForms) {
                String typedString = text.trim().toLowerCase();
                if (data.getFormName().toLowerCase().contains(typedString)) {
                    lstSearchForms.add(data);
                }
            }

            formAdapter.updateList(lstSearchForms, new ArrayList<>());
            if (lstSearchForms.isEmpty()) {
                showEmptyView(true);
            }
        } else {
            formAdapter.updateList(lstForms, new ArrayList<>());
        }
    }


    private List<FormData> doAfterResponseReceived() {
        TblForms tblForms = new TblForms(getContext());
        try {
            if (!tblForms.isDataExist()) {
                Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
                intent.putExtra(Constants.DELETED_FORM_ALL_DATA, true);
                context.sendBroadcast(intent);
                return new ArrayList<>();
            }
            ArrayList<Integer> lstFormId = tblForms.getFormDataWithColumnUpdateStatusZero();
            if (!lstFormId.isEmpty()) {
                Intent intent = new Intent(Constants.INTERNAL_FORM_BROADCAST);
                intent.putIntegerArrayListExtra(Constants.DELETED_FORM_LIST, lstFormId);
                context.sendBroadcast(intent);
            }

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return tblForms.getAllForms();
    }


    private void showEmptyView(boolean showSearchBar) {

        binding.progressBar.setVisibility(View.GONE);
        binding.rvForm.setEmptyView(binding.llEmptyView.llEmptyViewMain);
        showSearchView(showSearchBar);
        if (getActivity() != null)
            binding.rvForm.setEmptyData(getString(R.string.no_form_data_found));
    }


    private void navigateToDetailScreenIfUserLocked() {
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_FORM_LOCK, 0) == 1 && lstForms.size() == 1) {
            navigateToFormDetailScreen(0);
        }
    }


    private void navigateToDetailScreenIfOneDataInList() {
        if (lstForms.size() == 1 && shouldAutoNavigate) {
            shouldAutoNavigate = false;
            navigateToFormDetailScreen(0);

        }
    }

    private void navigateToFormDetailScreen(int position) {

        if (getActivity() != null) {
            ((BaseActivity) getActivity()).hideSoftKeyboard(getActivity());
        }
        try {
            if (detailEventCalled)
                return;
            FormData data;
            if (!lstSearchForms.isEmpty()) {
                data = lstSearchForms.get(position);
                position = lstForms.indexOf(data);
                if (position < 0) {
                    position = 0;
                }
            }
            data = lstForms.get(position);
            Intent intent = new Intent(getContext(), FormDetailActivity.class);
            intent.putExtra("mFormPkId", data.getFormPKId());
            startActivityForResult(intent, FORM_DETAIL_RESULT);
            detailEventCalled = true;
            if (getActivity() != null)
                getActivity().overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void setupAdapter() {
        formAdapter = new FormAdapter(lstForms, new ArrayList<>(), getActivity(), false, new FormAdapter.ViewClicked() {
            @Override
            public void onItemClicked(int position, View view, FormData dateItem) {
                navigateToFormDetailScreen(position);
            }

            @Override
            public void onItemLongClicked(int position, View view) {
                PopUtils.showDialogForClearFormData(getActivity(), getString(R.string.msg_clear_form_data), view1 -> clearFormData(position));
            }
        });
        binding.rvForm.setHasFixedSize(true);
        binding.rvForm.setAdapter(formAdapter);
        binding.rvForm.setEmptyView(binding.llEmptyView.llEmptyViewMain);
        binding.rvForm.setNestedScrollingEnabled(false);
        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.rvForm.getContext(), 1);
        binding.rvForm.addItemDecoration(dividerItemDecoration);

    }

    private void clearFormData(int position) {
        if (!lstSearchForms.isEmpty()) {
            FormData formData = lstSearchForms.get(position);
            position = lstForms.indexOf(formData);
        }
        clearDataFromDatabase(position);
        if(getActivity() != null) {
            showForeGroundToast(getString(R.string.msg_form_reset));
        }
    }


    private void deleteFormData(final int position) {
        PopUtils.showDialogForDeleteView(context, getString(R.string.are_you_sure_you_want_to_delete_selected_form), v -> {

            deleteDataFromDatabase(position);
        });

    }

    private void clearDataFromDatabase(int position) {
        FormData formData = lstForms.get(position);
        TblForms tblForms = new TblForms(getContext());

        if (formData.hasSubForm()) {
            List<FormData> lstSubForms = new TblForms(getContext()).getAllOnlyCheckedInSubForms(formData.getFormPKId());
            for (FormData subFormData : lstSubForms) {
                if (getActivity() != null) {
                    //send cancel breadcrumbs from all sub forms that are not yet completed
                    if (subFormData.getSubFormOtherData() != null && subFormData.getSubFormOtherData().getCrewId() > 0) {
                        ((BaseActivity) getActivity()).prepareDataForBreadCrumb(3, MainActivity.currentLatitude, MainActivity.currentLongitude, -1,
                                subFormData.getFormId(), subFormData.getFormName(), subFormData.getFormSubmissionId(), true, true, subFormData.getSubFormOtherData().getCrewId());
                    }
                }
            }

            tblForms.deleteDataByMainFormPKId(TblForms.TABLE_NAME,formData.getFormPKId());
        }

        tblForms.resetFormDataByPkId(formData.getFormPKId(), formData.getFormData());
        formData.setModifiedFormData(formData.getFormData());
        lstForms.set(position, formData);
    }


    private void deleteDataFromDatabase(int position) {
        if (lstSearchForms.size() > 0) {
            FormData formData = lstSearchForms.get(position);
            position = lstForms.indexOf(formData);
        }
        FormData formData = lstForms.get(position);
        TblForms tblForms = new TblForms(getContext());
        tblForms.deleteDataByPKId(formData.getFormPKId());
        lstForms.remove(position);
        if (lstSearchForms.size() > 0) {
            if (binding.edtSearch.getText() != null) {
                binding.edtSearch.setText(binding.edtSearch.getText().toString().trim());
                binding.edtSearch.setSelection(binding.edtSearch.getText().length());
            }
        }
        formAdapter.notifyDataSetChanged();
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case FORM_DETAIL_RESULT:
                detailEventCalled = false;
                if (resultCode == RESULT_OK) {
                    if (lstForms != null && !lstForms.isEmpty()) {
                        lstForms.clear();
                        TblForms tblForms = new TblForms(getContext());
                        lstForms = tblForms.getAllForms();
                        formAdapter.updateList(lstForms, new ArrayList<>());
                        navigateToDetailScreenIfUserLocked();
                        if (lstForms.isEmpty()) {
                            showEmptyView(false);
                        }
                        if (binding.edtSearch.getText() != null && !TextUtils.isEmpty(binding.edtSearch.getText())) {
                            binding.edtSearch.setText(binding.edtSearch.getText());
                            binding.edtSearch.setSelection(binding.edtSearch.getText().length());
                        }
                    }
                }
                break;
        }

    }

    public void updateData() {
        if (lstForms != null && !lstForms.isEmpty()) {
            lstForms.clear();
            getDataFromDatabase();
        } else {
            showEmptyView(false);
        }
    }

    @Override
    public void onRefresh() {
        shouldAutoNavigate = true;
        if (getActivity() == null)
            return;
        if (BaseApplication.getInstance().isOnline(getActivity())) {
            ((MainActivity) getActivity()).callApiForAppData(false, true);
        } else {
            binding.srlRefresh.setRefreshing(false);
        }

    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.ivSecondRight) {
            ((BaseActivity) requireActivity()).navigateToUploadActivityScreen(binding.tlOther.ivSecondRight);
        }
    }

    public void checkAndNavigateToDetailScreen() {
        isScreenVisible = true;
        navigateToDetailScreenIfOneDataInList();
    }


    protected class GetDataFromDatabase extends AsyncTask<Void, Void, List<FormData>> {

        @Override
        protected List<FormData> doInBackground(Void... params) {
            return doAfterResponseReceived();
        }

        @Override
        protected void onPostExecute(List<FormData> lstFormData) {
            if (getActivity() != null && getActivity().isFinishing())
                return;
            binding.srlRefresh.setRefreshing(false);
            lstForms = lstFormData;
            if (lstForms != null && !lstForms.isEmpty()) {
                if (isScreenVisible) {
                    navigateToDetailScreenIfOneDataInList();
                }
                if (formAdapter != null)
                    formAdapter.updateList(lstForms, new ArrayList<>());
                showSearchView(true);
                setText();
            } else {
                if (formAdapter != null)
                    formAdapter.updateList(new ArrayList<>(), new ArrayList<>());
                showEmptyView(false);
            }
            showDataUpdateMessage();

        }
    }

    private void showDataUpdateMessage() {
        if (getActivity() != null && ((MainActivity) getActivity()).activeFragment instanceof FormListingFragment) {
            showForeGroundToast(getString(R.string.msg_form_data_updated));
        }
    }

    private void showSearchView(boolean enable) {
        if (enable) {
            binding.edtSearch.setVisibility(View.VISIBLE);
            binding.edtSearch.setEnabled(true);

        } else {
            binding.edtSearch.setVisibility(View.GONE);
            binding.edtSearch.setEnabled(false);
        }

    }

    private void onSuccessResponse(Response<AppDataResponse> response) {
        if (response != null && response.code() == 304) {
            binding.srlRefresh.setRefreshing(false);
            return;
        }
        new GetDataFromDatabase().execute();

    }

    public void onFailureResponse(Throwable throwable) {
        if (getActivity() == null)
            return;
        if (getActivity().isFinishing())
            return;
        binding.srlRefresh.setRefreshing(false);
        binding.edtSearch.setEnabled(true);
        binding.progressBar.setVisibility(View.GONE);
    }

    public void onNoInternetConnection() {
        if (getActivity() == null)
            return;
        if (getActivity().isFinishing())
            return;
        binding.srlRefresh.setRefreshing(false);
        binding.edtSearch.setEnabled(true);
        binding.progressBar.setVisibility(View.GONE);
    }

    private void setText() {
        if (binding.edtSearch.getText() != null && !TextUtils.isEmpty(binding.edtSearch.getText())) {
            searchText(binding.edtSearch.getText().toString());
            binding.edtSearch.setSelection(binding.edtSearch.getText().length());
            if (getActivity() != null) {
                ((BaseActivity) getActivity()).showKeyboard(binding.edtSearch);
            }
        }
    }


    @Override
    public void onDestroyView() {
        super.onDestroyView();
        if (EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().unregister(this);
        }
    }
}
