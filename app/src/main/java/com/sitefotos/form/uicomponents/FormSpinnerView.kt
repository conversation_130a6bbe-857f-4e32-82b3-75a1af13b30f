package com.sitefotos.form.uicomponents

import android.annotation.SuppressLint
import android.content.Context
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.CompoundButton
import android.widget.LinearLayout
import android.widget.RadioButton
import android.widget.TextView
import com.sitefotos.R

class FormSpinnerView(context: Context, adsLayout: ViewGroup?,private val listener:CallBackListener) : LinearLayout(context) {
    var llMultiSelect: LinearLayout
    var tvMultiSelected: TextView
    var rbSelection: RadioButton
    var tvMultiSelectTitle: TextView

    init {
        val view = inflate(context, R.layout.layout_spinner, adsLayout)
        llMultiSelect = view.findViewById(R.id.llMultiSelect)
        tvMultiSelected = view.findViewById(R.id.tvMultiSelected)
        rbSelection = view.findViewById(R.id.rbSelection)
        tvMultiSelectTitle = view.findViewById(R.id.tvMultiSelectTitle)
        setTouchListener()
        setClickListener()
        checkChangeListener()
        listener.onViewInitialised(this)
    }

    private fun checkChangeListener() {

        rbSelection.setOnCheckedChangeListener { buttonView, isChecked ->
            rbSelection.error = null
            clearFocus()
            listener.onRbCheckedChangeListener(tvMultiSelected,buttonView, isChecked)

        }
    }


    @SuppressLint("ClickableViewAccessibility")
    private fun setTouchListener() {
        tvMultiSelected.setOnTouchListener { view: View?, motionEvent: MotionEvent ->
            if (motionEvent.action == MotionEvent.ACTION_DOWN) {
                val drawableRight = tvMultiSelected.compoundDrawables[2]
                if (drawableRight != null) {
                    if (motionEvent.rawX >= tvMultiSelected.right - drawableRight.bounds.width()) {
                        tvMultiSelected.text = ""
                        listener.onTouchListener(tvMultiSelected)
                        tvMultiSelected.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0)
                        return@setOnTouchListener true
                    }
                }
            }
            false
        }
    }

    private fun setClickListener() {
        tvMultiSelected.setOnClickListener { view: View? ->
            clearFocus()
            listener.onTextClickListener(view,tvMultiSelected)
            tvMultiSelected.error = null

        }
    }

    fun getView(): View {
        return this
    }

    fun setRightSideDrawable(){
        updateCompoundDrawables(right= R.drawable.icn_close)
    }

    @JvmName("getLlMultiSelect1")
    fun getLlMultiSelect():LinearLayout{
        return llMultiSelect
    }

    fun setText(text:String){
        tvMultiSelected.text = text
    }

    fun updateCompoundDrawables(left:Int = 0,top:Int = 0,right:Int = 0,bottom:Int =0){
        tvMultiSelected.setCompoundDrawablesWithIntrinsicBounds(left, top, right, bottom)
    }

    interface CallBackListener {
        fun onViewInitialised(formSpinnerView: FormSpinnerView)
        fun onTouchListener( tvMultiSelected: TextView)
        fun onTextClickListener(view:View?,  tvMultiSelected:TextView)
        fun onRbCheckedChangeListener(llMultiSelect:TextView,buttonView: CompoundButton, isChecked: Boolean)
    }
}