package com.sitefotos.form;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.sitefotos.BaseActivity;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivitySketchBinding;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.views.DrawingView;
import com.sitefotos.util.views.VerticalColorPicker;

import java.util.ArrayList;

public class SketchActivity extends BaseActivity implements DrawingView.DrawComplete, View.OnClickListener {

    private int tag;
    private long formIdData;
    public int colorSelected;
    private boolean isSign;
    private String drawingPath;
    private long pkId;
    private String colorKey = "";
    private boolean increaseCount;
    public ActivitySketchBinding binding;

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return null;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initBinding();
        setOnClickListener();
        setActionBarVisibility(false);

        if (getSupportActionBar() != null)
            getSupportActionBar().hide();
        binding.ivUndo.setBackgroundResource(R.drawable.ic_pencil_white);
        registerAppReceiver(formUpdateReceiver, new IntentFilter(Constants.INTERNAL_FORM_BROADCAST));

        setColorPickerInSeekBar();
        binding.drawing.setSketch(true);
        binding.drawing.setDrawingCacheEnabled(true);
        if (getIntent().getExtras() != null) {
            tag = getIntent().getExtras().getInt("tag", -1);
            formIdData = getIntent().getExtras().getLong("formId", -1);
            isSign = getIntent().getExtras().getBoolean("isSign", false);
            pkId = getIntent().getExtras().getInt("pkId", 0);
            increaseCount = getIntent().getExtras().getBoolean("increaseCount", false);
            String key = String.valueOf(pkId).concat(",").concat(String.valueOf(tag));

            drawingPath = AppPrefShared.getString(key, "");
            colorKey = "color" + pkId + tag;
            binding.verticalColorPicker.setKey(colorKey);
            getDrawingDataFromString(drawingPath);
        }

        if (binding.drawing.getPaths().size() == 0) {
            binding.ivUndo.setVisibility(View.GONE);
        } else {
            binding.ivUndo.setVisibility(View.VISIBLE);
        }

    }


    private void initBinding() {
        binding = ActivitySketchBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void setOnClickListener() {
        binding.ivImageDone.setOnClickListener(this);
        binding.ivClear.setOnClickListener(this);
        binding.ivUndo.setOnClickListener(this);
    }


    private void setColorPickerInSeekBar() {
        colorSelected = AppPrefShared.getInt(Constants.DRAWING_PAINT_STROKE_COLOR, Constants.DRAWING_PAINT_DEFAULT_STROKE_COLOR);
        binding.ivUndo.setBackgroundColor(colorSelected);
        binding.drawing.setPencilColor(colorSelected);
        binding.verticalColorPicker.setOnColorChangeListener(new VerticalColorPicker.OnColorChangeListener() {
            @Override
            public void onColorChange(int selectedColor) {
                if (selectedColor != 0) {
                    colorSelected = selectedColor;
                    binding.drawing.setPencilColor(colorSelected);
                    AppPrefShared.putValue(Constants.DRAWING_PAINT_STROKE_COLOR, colorSelected);
                }
            }
        });
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.ivImageDone) {
            onDoneClick();
        } else if (viewId == R.id.ivClear) {
            binding.drawing.clearDrawingData();
            finish();
        } else if (viewId == R.id.ivUndo) {
            binding.drawing.undo();
            if (binding.drawing.getPaths().isEmpty()) {
                binding.ivUndo.setVisibility(View.GONE);
            }
        }
    }

    private void onDoneClick() {
        if (binding.drawing.isDrawnAnything()) {
            new saveDrawings().execute();
        } else {
            Intent intent = new Intent();
            intent.putExtra("tag", tag);
            intent.putExtra("isSign", isSign);
            intent.putExtra("isSketch", true);
            intent.putExtra("isEmpty", true);
            intent.putExtra("increaseCount", increaseCount);
            setResult(RESULT_OK, intent);
            AppPrefShared.putValue(colorKey, (float) 52.00);
            finish();
        }
    }

    private void saveDrawingWork() {
        String drawingData = binding.drawing.getDrawingData();
        if (!TextUtils.isEmpty(drawingData)) {
            String key = String.valueOf(pkId).concat(",").concat(String.valueOf(tag));
            AppPrefShared.putValue(key, drawingData);
        }
    }

    //TODO Need to check from where this was getting called.
    private void clearDrawingFromPref() {
        String key = String.valueOf(pkId).concat(",").concat(String.valueOf(tag));
        AppPrefShared.putValue(key, "");
    }

    private BroadcastReceiver formUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (context != null) {
                if (intent.hasExtra(Constants.DELETED_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_FORM_ALL_DATA, false)) {
                        setResultForDeletedForm();
                    }
                }
                if (intent.hasExtra(Constants.DELETED_FORM_LIST)) {

                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_FORM_LIST);
                    for (Integer formId : lstDeletedFormId) {
                        if (formId == formIdData) {
                            setResultForDeletedForm();
                        }
                    }
                }

                if (intent.hasExtra(Constants.DELETED_WP_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_WP_FORM_ALL_DATA, false)) {
                        setResultForDeletedForm();
                    }
                }
                if (intent.hasExtra(Constants.DELETED_WP_LIST)) {

                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_WP_LIST);
                    for (Integer formId : lstDeletedFormId) {
                        if (formId == formIdData) {
                            setResultForDeletedForm();
                        }
                    }
                }
            }
        }
    };

    private void setResultForDeletedForm() {
        Intent intent = new Intent();
        intent.putExtra("signaturePath", "");
        intent.putExtra("isSign", isSign);
        intent.putExtra("tag", -1);
        this.setResult(RESULT_OK, intent);
        finish();
    }

    private void getDrawingDataFromString(String rawPath) {
        if (TextUtils.isEmpty(rawPath)) {
            AppPrefShared.putValue(colorKey, (float) 52.0);
            return;
        }
        binding.drawing.setDrawView(this);
        binding.drawing.setAllDataInAList(rawPath);
    }

    @Override
    protected void onDestroy() {
        unregisterReceiver(formUpdateReceiver);
        super.onDestroy();
    }

    @Override
    public void onDrawComplete() {
//        verticalColorPicker.setProgress();
    }


    /**
     * Class to save drawing in preference.
     */
    public class saveDrawings extends AsyncTask<Void, Void, String> {
        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            if (isFinishing())
                return;
            try {
                startProgress();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        }

        @Override
        protected String doInBackground(Void... voids) {
            saveDrawingWork();
            return ImageUtil.saveImage(SketchActivity.this, binding.drawing.getDrawingCache(), false);
        }

        @Override
        protected void onPostExecute(String path) {
            super.onPostExecute(path);
            Intent intent = new Intent();
            intent.putExtra("signaturePath", path);
            intent.putExtra("tag", tag);
            intent.putExtra("isSign", isSign);
            intent.putExtra("isSketch", true);
            intent.putExtra("increaseCount", increaseCount);
            setResult(RESULT_OK, intent);
            finish();
        }
    }
}
