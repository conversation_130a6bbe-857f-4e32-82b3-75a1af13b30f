package com.sitefotos.form;

import com.sitefotos.models.CrewSelectionData;

import org.json.JSONArray;

import java.util.List;

/**
 *
 */

public interface OnCrewAndServiceActionTapped {
    void onSelected(List<CrewSelectionData> lstEmployee, List<CrewSelectionData> lstSelectedEmployee, List<Integer> lstDeselectedEmployee, List<Integer> lstNewSelected,JSONArray arrayModifiedServices);
    void onCancel();
}
