package com.sitefotos.form;

import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.LOGGED_IN_PARAM_IS_LOCATION_REQUIRED;
import static com.sitefotos.Constants.SUB_FORM_CREATE_REQUEST_CODE;
import static com.sitefotos.util.StaticUtils.isDeviceLocked;
import static com.sitefotos.util.StaticUtils.updateMarginOfTitleView;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.AsyncTask;
import android.os.Bundle;
import android.os.Handler;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;
import android.view.animation.AnimationUtils;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.sitefotos.BaseApplication;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.ImageViewAdapter;
import com.sitefotos.appinterface.OnAppDataApiResponse;
import com.sitefotos.databinding.ActivityFormDetailBinding;
import com.sitefotos.event.CrewSelectionEvent;
import com.sitefotos.event.PreSelectedServiceEvent;
import com.sitefotos.event.SiteDataUpdateEvent;
import com.sitefotos.event.TMFormCheckInOutEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.AppDataResponse;
import com.sitefotos.models.CheckInMap;
import com.sitefotos.models.Employees;
import com.sitefotos.models.FormData;
import com.sitefotos.models.ImageData;
import com.sitefotos.models.PanelData;
import com.sitefotos.models.TMService;
import com.sitefotos.models.ValidationData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.storage.tables.TblSites;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.PropertyUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.RejectedExecutionException;

import retrofit2.Response;

public class TMFormDetailActivity extends BaseFormDetailActivity implements OnAppDataApiResponse, View.OnClickListener {
    private ActivityFormDetailBinding binding;
    private long mLastCheckInClickTime = 0;


    private String lastBtnName;
    private final BroadcastReceiver formUpdateReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (context != null) {
                if (intent.hasExtra(Constants.DELETED_WP_FORM_ALL_DATA)) {
                    if (intent.getBooleanExtra(Constants.DELETED_WP_FORM_ALL_DATA, false)) {
                        finish();
                        return;
                    }
                }
                if (intent.hasExtra(Constants.UPDATED_WP_LIST)) {
                    ArrayList<Integer> lstUpdatedForm = intent.getIntegerArrayListExtra(Constants.UPDATED_WP_LIST);
                    for (Integer formId : lstUpdatedForm != null ? lstUpdatedForm : new ArrayList<Integer>()) {
                        if (formId == formData.getFormId()) {
                            recreate();
                        }
                    }
                }
                if (intent.hasExtra(Constants.DELETED_WP_LIST)) {
                    ArrayList<Integer> lstDeletedFormId = intent.getIntegerArrayListExtra(Constants.DELETED_WP_LIST);
                    for (Integer formId : lstDeletedFormId != null ? lstDeletedFormId : new ArrayList<Integer>()) {
                        if (formId == formData.getFormId()) {
                            finish();
                        }
                    }
                }
            }
        }
    };

    private final BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            int formPkId = intent.getIntExtra("formPkId", -1);
            if (formPkId == mFormPkId) {
                concatImageData();
            }
        }
    };


    @Override
    boolean isTMForm() {
        return true;
    }

    @Override
    protected OnAppDataApiResponse getApiCallBack() {
        return this;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        context = this;
        initBinding();
        setOnClickListener();
        setActionBarVisibility(false);
        startLocationServiceIfNotStarted();
        startProgress();
        new Handler().postDelayed(this::init, 300);
    }

    private void initBinding() {
        binding = ActivityFormDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
    }

    private void setOnClickListener() {
        binding.tlOther.imgBtnBack.setOnClickListener(this);
        binding.btnPrevious.setOnClickListener(this);
        binding.btnNext.setOnClickListener(this);
        binding.tlOther.ivSecondRight.setOnClickListener(this);
        binding.btnCheckIn.setOnClickListener(this);
        binding.btnMapCheckIn.setOnClickListener(this);
        binding.tlOther.ivPlotMap.setOnClickListener(this);
        binding.tlOther.ivFormDetail.setOnClickListener(this);
        binding.ivCamera.setOnClickListener(this);
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this);
        }
        startLocationServiceIfNotStarted();
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUploadProcessStartEvent(UploadFileStatusEvent event) {
        visibleUploadImageViewInScreen(binding.tlOther.ivSecondRight);
        updateMarginOfTitleView(this, binding.tlOther.llTitle);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void OnPreSelectedServiceEvent(PreSelectedServiceEvent event) {
        if (event != null && event.getCrewView() != null) {
            setCheckedInDataAndStoreIt();
            updateCheckInCrewSelectionData(event.getLstSelectedCrew());
        }
        saveDataInDB(formData);
        setCheckedInDataAndStoreIt();
        checkInFormAndInitialSetup();
        //Clear all collection variable as it will again check and set the collection values
        clearListData();
        init();

        if (event != null && event.getCrewView() != null) {
            checkInformAndSubmitCrews(event.getObject(), event.getLstSelectedCrew(), event.getLstDeSelectedEmployee(), event.getLstNewSelected(), event.getCrewView());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onCrewSelectionEvent(CrewSelectionEvent event) {
        if (event != null) {
            setCheckedInDataAndStoreIt();
            checkInFormAndInitialSetup();
            updateCheckInCrewSelectionData(event.getLstSelectedCrew());
            checkInformAndSubmitCrews(event.getObject(), event.getLstSelectedCrew(), event.getLstDeSelectedEmployee(), event.getLstNewSelected(), event.getCrewView());
        }
    }

    private void init() {
        registerAppReceiver(mMessageReceiver, new IntentFilter(Constants.INTERNAL_IMAGE_BROADCAST));
        registerAppReceiver(formUpdateReceiver, new IntentFilter(Constants.INTERNAL_FORM_BROADCAST));
        vfPages = binding.vfPages;
        if (getBundleData()) {
            setViewFlipperAnimation();
            setData(false);
            if (formData.getIsCheckInOut()) {
                showCheckInOrOpenFormView();
            }
            if (returnFromSubFrom) {
                changePage(lastOpenedPage);
            }
        } else {
            stopProgressDialog();
            setFormTitle();
            if (TextUtils.isEmpty(formString)) {
                clearCheckMapDataAndFormData();
            }
        }
    }

    private void setFormTitle() {
        binding.tlOther.tvMainTitle.setVisibility(View.VISIBLE);
        binding.tlOther.tvTitle.setText(formData.getFormName());
        binding.tlOther.tvMainTitle.setText(siteData.getSiteName());
    }


    private void setViewFlipperAnimation() {
        leftOutAnimation = AnimationUtils.loadAnimation(this, R.anim.left_out);
        leftInAnimation = AnimationUtils.loadAnimation(this, R.anim.left_in);
        rightInAnimation = AnimationUtils.loadAnimation(this, R.anim.right_in);
        rightOutAnimation = AnimationUtils.loadAnimation(this, R.anim.right_out);

    }

    private void setFlipAnimation(boolean isNext) {
        if (isNext) {
            binding.vfPages.setInAnimation(rightInAnimation);
            binding.vfPages.setOutAnimation(leftOutAnimation);
        } else {
            binding.vfPages.setInAnimation(leftInAnimation);
            binding.vfPages.setOutAnimation(rightOutAnimation);
        }
    }


    private boolean getBundleData() {
        if (returnFromSubFrom) {
            formData = getFormData(formData.getFormPKId());
            return true;
        }
        if (getIntent() != null) {
            if (getIntent().hasExtra("siteData")) {
                siteData = getIntent().getParcelableExtra("siteData");
                if (siteData != null) {
                    lastUpdatedBuildingId = String.valueOf(siteData.getSiteId());
                }
                lastBuildingId = lastUpdatedBuildingId;
            }
            mFormPkId = getIntent().getIntExtra("formPkId", 0);
            formData = getFormData(mFormPkId);
            if (formData != null) {
                formString = formData.getModifiedFormData();
                try {
                    if (getIntent().hasExtra("autoFill") && !TextUtils.isEmpty(getIntent().getStringExtra("autoFill"))) {
                        autoFillArray = new JSONArray(getIntent().getStringExtra("autoFill"));
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        if (TextUtils.isEmpty(formString)) {
            showForeGroundToastLong(getString(R.string.empty_data));
            return false;
        } else if (!isJSONValid(formString)) {
            showForeGroundToastLong(getString(R.string.invalid_data));
            return false;
        }
        return true;
    }

    /**
     * Delete Data from check in map and form data in Pk id is exist.
     */
    private void clearCheckMapDataAndFormData() {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        CheckInMap mCheckInMap = tblCheckInMap.getDataByFormId(mFormPkId);
        if (mCheckInMap != null && mCheckInMap.getPkId() > 0) {
            deleteCheckInMapData(mCheckInMap.getPkId());
            finish();
        }
    }

    private void showCheckInOrOpenFormView() {
        if (formData.getCheckin_time() == 0) {
            //check for migration data from db version 13 to 14
            TblCheckInMap tblCheckInMap = new TblCheckInMap(context);
            CheckInMap checkInMap = tblCheckInMap.getDataByFormId(formData.getFormPKId());
            // comparision is true is data is migrated of false
            if (checkInMap.getFormPkId() == formData.getFormPKId()) {
                setCheckedInDataAndStoreIt();
                return;
            }
            // process further for db version 14 and onward
            try {
                PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name), getString(R.string.checkin_instruction_message),
                        getString(R.string.check_in)
                        , getString(R.string.view), false, false, (dialog, which) -> {
                            checkAndDoCheckIn();
                        }, (dialog, which) -> {
                            dialog.dismiss();
                            enableDisableCrewView();

                        });
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }

        }
    }

    private void setData(boolean skipOpenBreadCrumb) {
        binding.vfPages.removeAllViews();
        if (!TextUtils.isEmpty(formString)) {
            TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
            CheckInMap mCheckInMap = tblCheckInMap.getDataByFormId(formData.getFormPKId());
            readJsonFile(binding.vfPages);
            setFormTitle();
            showMapIcon(formData.isPlotOnMap());
            checkAndSetBottomButtonVisibility(mCheckInMap, false, false);
            binding.rlBottom.setVisibility(View.VISIBLE);
            if (!skipOpenBreadCrumb) {
                //setSiteAndFormBroadCrumbs(6, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
                setInVisibilityOfUploadView(binding.tlOther.ivSecondRight);
                updateMarginOfTitleView(this, binding.tlOther.llTitle);
            }
        }
        stopProgressDialog();
    }

    /**
     * \
     * show map icon if form hase plotonmap enabled
     *
     * @param isPlotOnMap boolean
     */
    private void showMapIcon(Boolean isPlotOnMap) {
        if (isPlotOnMap) {
            binding.tlOther.ivPlotMap.setVisibility(View.VISIBLE);
        } else {
            binding.tlOther.ivPlotMap.setVisibility(View.GONE);
        }
    }


    private void checkAndSetBottomButtonVisibility(CheckInMap mCheckInMap, boolean shouldShowFormView, boolean fromMap) {
        if (formData.getIsCheckInOut() && mCheckInMap != null && (mCheckInMap.getFormId() == formData.getFormId())) {
            binding.btnCheckIn.setVisibility(View.GONE);
            lastBtnName = getString(R.string.check_out);
            binding.btnNext.setVisibility(View.VISIBLE);
            enableOrDisableView(true);
            disablePreSelectServiceView();
        } else if (formData.getIsCheckInOut()) {
            binding.btnCheckIn.setVisibility(View.VISIBLE);
            binding.btnNext.setVisibility(View.GONE);
            lastBtnName = getString(R.string.check_out);
            enableOrDisableView(false);
        } else {
            binding.btnCheckIn.setVisibility(View.GONE);
            binding.btnNext.setVisibility(View.VISIBLE);
            lastBtnName = getString(R.string.submit);
            enableOrDisableView(true);
        }
        if (totalPages == currentPage) {
            binding.btnNext.setText(lastBtnName);
        } else {
            binding.btnNext.setText(getString(R.string.next));
        }
        if (currentPage == 1) {
            binding.btnPrevious.setVisibility(View.GONE);
            binding.tvPageNumber.setText(String.valueOf(currentPage).concat("/").concat(String.valueOf(totalPages)));
        } else {
            binding.btnPrevious.setVisibility(View.VISIBLE);
        }

        if (fromMap && formData.isPlotOnMap()) {
            if (shouldShowFormView) {
                binding.rlMapBottom.setVisibility(View.GONE);
            } else {
                if (formData.getIsCheckInOut() && formData.getCheckin_time() <= 0) {
                    binding.ivCamera.setVisibility(View.GONE);
                    binding.btnMapCheckIn.setVisibility(View.VISIBLE);
                } else {
                    binding.ivCamera.setVisibility(View.VISIBLE);
                    binding.btnMapCheckIn.setVisibility(View.GONE);
                }
            }
        }

    }

    private void changePage(int position, boolean isNext, boolean isButtonClick) {
        saveModifiedForm();
        hideSoftKeyboard(TMFormDetailActivity.this);
        if (!isNext) {
            setFlipAnimation(false);
            if (!isButtonClick) {
                for (int j = currentPage; j > position; j--) {
                    binding.vfPages.showPrevious();
                    currentPage--;
                    if (currentPage == 1)
                        break;
                }
            } else {
                binding.vfPages.showPrevious();
                currentPage--;
            }
        } else {
            setFlipAnimation(true);
            if (!isButtonClick) {

                for (int j = 1; j < position - currentPage; j++) {
                    binding.vfPages.showNext();
                    currentPage++;
                }
            } else {
                binding.vfPages.showNext();
                currentPage++;
            }
            if (!isButtonClick)
                currentPage = position;
        }

        pageNumber = currentPage;
        binding.tvPageNumber.setText(String.valueOf(currentPage).concat("/").concat(String.valueOf(totalPages)));
        if (totalPages == currentPage) {
            binding.btnNext.setText(lastBtnName);
        } else {
            binding.btnNext.setText(getString(R.string.next));
        }
        if (currentPage == 1) {
            binding.btnPrevious.setVisibility(View.GONE);
            binding.tlOther.imgBtnBack.setColorFilter(ContextCompat.getColor(context, R.color.white), android.graphics.PorterDuff.Mode.MULTIPLY);
        } else {
            binding.btnPrevious.setVisibility(View.VISIBLE);
            binding.tlOther.imgBtnBack.setColorFilter(ContextCompat.getColor(context, R.color.gray), android.graphics.PorterDuff.Mode.MULTIPLY);

        }
    }


    private void changePage(int position) {
        hideSoftKeyboard(TMFormDetailActivity.this);
        currentPage = pageNumber;
        if (lastOpenedPage < currentPage) {
            for (int j = currentPage; j > position; j--) {
                binding.vfPages.showPrevious();
                currentPage--;
                if (currentPage == 1)
                    break;
            }
        } else if (lastOpenedPage > currentPage) {
            for (int j = 1; j <= position - currentPage; j++) {
                binding.vfPages.showNext();
                currentPage++;
            }
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        switch (requestCode) {
            case SIGN_FOR_RESULT:
                if (data != null && data.getExtras() != null)
                    if (data.hasExtra("signaturePath") && data.hasExtra("tag") && data.hasExtra("isSign")) {
                        if (data.getIntExtra("tag", 0) == -1) {
                            finish();
                        } else
                            setBitmapOnSignatureView(data.getExtras());
                    } else if (data.hasExtra("tag") && data.hasExtra("isSign") && data.hasExtra("isSketch") && data.hasExtra("isEmpty")) {
                        clearSignature(data.getExtras());
                    }
                break;
            case Constants.MAP_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        doAfterComeBackFromNewPropertyScreen(data);
                    }
                }
                break;
            case Constants.ADD_ADDRESS_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        updateAddressData(data.getExtras(), binding.vfPages);
                    }
                }
                break;
            case REQUEST_CAMERA:
                if (resultCode == RESULT_OK) {
                    if (data != null && data.getExtras() != null) {
                        if (data.getIntExtra("tag", 0) == -1) {
                            finish();
                        } else
                            setImageInImageView(data);
                    }
                }
                break;

            case LOCATION_REQUEST_CODE:
                //case Constants.MAP_ACTIVITY_REQUEST:
                if (resultCode == RESULT_OK) {
                    isGpsEnableCalled = true;
                    startLocationServiceIfNotStarted();
                }
                break;

            case SUB_FORM_CREATE_REQUEST_CODE:
                returnFromSubFrom = true;
                init();
                break;

        }
    }

    private void clearSignature(Bundle extras) {
        int tagOfSign = extras.getInt("tag");
        boolean isSign = extras.getBoolean("isSign");
        String signaturePath = "";
        JSONObject jsonObject = (JSONObject) mapSignature.get(tagOfSign);
        if (jsonObject != null && jsonObject.has(Constants.VALUE)) {
            try {
                JSONArray array = jsonObject.getJSONArray(Constants.VALUE);
                if (array.length() > 0) {
                    if (array.getJSONObject(0).has(Constants.IMAGEPATHHIGH) && (!TextUtils.isEmpty(array.getJSONObject(0).getString(Constants.IMAGEPATHHIGH)))) {
                        signaturePath = array.getJSONObject(0).getString(Constants.IMAGEPATHHIGH);
                    } else {
                        if (array.getJSONObject(0).has(Constants.IMAGEPATHLOW) && (!TextUtils.isEmpty(array.getJSONObject(0).getString(Constants.IMAGEPATHLOW))))
                            signaturePath = array.getJSONObject(0).getString(Constants.IMAGEPATHLOW);
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
        View sign = binding.vfPages.findViewWithTag(tagOfSign);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);

        if (extras.getBoolean("isEmpty")) {
            if (!TextUtils.isEmpty(signaturePath))
                ImageUtil.deleteImageFromSDCard(signaturePath);

            ivSign.setImageBitmap(null);
            tvClear.setVisibility(View.GONE);
            if (jsonObject != null) {
                updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, false);
                String[] value = {};
                try {
                    jsonObject.put(Constants.VALUE, new JSONArray(value));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }

            try {
                JSONObject jsonImageData;
                FormData data = getFormData(formData.getFormPKId());
                jsonImageData = new JSONObject(data.getImageData());
                if (isSign) {
                    if (StaticUtils.getTMFormPendingCount(formData.getFormPKId()) > 0)
                        StaticUtils.setTMFormPendingCount(formData.getFormPKId(), StaticUtils.getTMFormPendingCount(formData.getFormPKId()) - 1);
                }

                JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                int index = -1;
                for (int i = 0; i < jsonImageArray.length(); i++) {
                    JSONObject object1 = jsonImageArray.getJSONObject(i);
                    if (tag == object1.getInt(Constants.TAGID)) {
                        index = i;
                    }
                }
                if (index != -1) {
                    jsonImageArray.remove(index);
                }
                TblTMForms tblTNMForms = new TblTMForms(context);
                tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        }
    }

    private void setBitmapOnSignatureView(Bundle extras) {

        saveDataInDB(formData);
        int tag = extras.getInt("tag", -1);
        String signaturePath = extras.getString("signaturePath");
        boolean increaseCount = extras.getBoolean("increaseCount");

        JSONObject jsonObject = (JSONObject) mapSignature.get(tag);
        JSONArray jsonArray = new JSONArray();
        try {
            JSONObject signatureJsonObject = new JSONObject();
            signatureJsonObject.put(Constants.IMAGEID, imageId);
            signatureJsonObject.put(Constants.LRIMAGE, "");
            signatureJsonObject.put(Constants.HRIMAGE, "");
            signatureJsonObject.put(Constants.IMAGEPATHLOW, "");
            signatureJsonObject.put(Constants.IMAGEPATHHIGH, signaturePath);
            signatureJsonObject.put(Constants.IS_SIGNATURE, true);
            jsonArray.put(signatureJsonObject);
            if (jsonObject != null) {
                jsonObject.put(Constants.VALUE, jsonArray);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        JSONObject signatureObject = null;
        try {
            JSONObject jsonImageData;
            FormData data = getFormData(formData.getFormPKId());
            jsonImageData = new JSONObject(data.getImageData());
            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
            if (increaseCount) {
                int tmFormPendingCount = Math.max(StaticUtils.getTMFormPendingCount(formData.getFormPKId()), 0);
                StaticUtils.setTMFormPendingCount(formData.getFormPKId(), tmFormPendingCount + 1);
            }

            for (int i = 0; i < jsonImageArray.length(); i++) {
                JSONObject object = jsonImageArray.getJSONObject(i);
                if (object.getInt(Constants.TAGID) == tag) {
                    signatureObject = object;
                    break;
                }
            }
            if (signatureObject == null) {
                signatureObject = new JSONObject();
                jsonImageArray.put(signatureObject);

            }
            signatureObject.put(Constants.IMAGEID, imageId);
            signatureObject.put(Constants.TAGID, tag);
            signatureObject.put(Constants.LRIMAGE, "");
            signatureObject.put(Constants.HRIMAGE, "");
            signatureObject.put(Constants.IMAGEPATHHIGH, "");
            signatureObject.put(Constants.IMAGEPATHLOW, signaturePath);
            signatureObject.put(Constants.IS_SIGNATURE, true);
            TblTMForms tblTNMForms = new TblTMForms(context);
            tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            saveModifiedForm();


            imageId++;
            tblTNMForms.updateImageCountByPkId(formData.getFormPKId(), imageId);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
        View sign = binding.vfPages.findViewWithTag(tag);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        tvClear.setVisibility(View.VISIBLE);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);
        if (jsonObject != null) {
            updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, true);
        }

        tvClear.setOnClickListener(v -> {

            ImageUtil.deleteImageFromSDCard(signaturePath);
            ivSign.setImageBitmap(null);
            tvClear.setVisibility(View.GONE);
            if (jsonObject != null) {
                updateValidationInMap(jsonObject, tvSignatureTitle, "", false, true, false);
            }
            String[] value = {};
            try {
                if (jsonObject != null) {
                    jsonObject.put(Constants.VALUE, new JSONArray(value));
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
            try {
                JSONObject jsonImageData;

                FormData data = getFormData(formData.getFormPKId());
                jsonImageData = new JSONObject(data.getImageData());
                if (StaticUtils.getTMFormPendingCount(formData.getFormPKId()) > 0) {
                    StaticUtils.setTMFormPendingCount(formData.getFormPKId(), StaticUtils.getTMFormPendingCount(formData.getFormPKId()) - 1);
                }

                JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                int index = -1;
                for (int i = 0; i < jsonImageArray.length(); i++) {
                    JSONObject object1 = jsonImageArray.getJSONObject(i);
                    if (tag == object1.getInt(Constants.TAGID)) {
                        index = i;
                    }
                }
                if (index != -1) {
                    jsonImageArray.remove(index);
                }
                TblTMForms tblTNMForms = new TblTMForms(context);
                tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }

        });
        ImageUtil.loadImageInGlide(ivSign, signaturePath);
    }


    private void doAfterComeBackFromNewPropertyScreen(Intent data) {
        if (data.getExtras() == null)
            return;
        try {
            if (!TextUtils.isEmpty(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                try {
                    switch (Objects.requireNonNull(data.getExtras().getString(Constants.KEY_INTENT_RETURN_DATA))) {
                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN:
                            PopUtils.showAlertDialogPositiveButtonOnly(TMFormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_JOIN, getString(R.string.joinAlertMessage));
                            break;

                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX:
                            PopUtils.showAlertDialogPositiveButtonOnly(TMFormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_MAX, getString(R.string.maxAlertMessage));
                            break;

                        case Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST:
                            PopUtils.showAlertDialogPositiveButtonOnly(TMFormDetailActivity.this, Constants.KEY_ADD_PROPERTY_API_RESPONSE_PAST, getString(R.string.pastAlertMessage));
                            break;
                        default:
                            break;

                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);

                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void setImageInImageView(Intent data) {
        int tag;
        ImageViewAdapter imageViewAdapter = null;
        RecyclerView rvImages;
        String imagePath;
        boolean isFromMap;
        TblTMForms tblTNMForms = new TblTMForms(TMFormDetailActivity.this);
        if (data.getExtras() != null && data.hasExtra("tag")) {
            tag = data.getExtras().getInt("tag", -1);
            isFromMap = data.getExtras().getBoolean("isFromMap", false);

            View view = null;
            try {
                view = binding.vfPages.findViewWithTag(tag);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }

            if (view == null)
                return;
            TextView tvUploadPhoto = view.findViewById(R.id.tvUploadPhoto);

            JSONObject mainJson = (JSONObject) mapImageUpload.get(tag);

            if (mainJson != null) {
                updateValidationInMap(mainJson, tvUploadPhoto, "", false, true, true);
                JSONArray mainJsonArray = null;
                try {
                    mainJsonArray = mainJson.getJSONArray(Constants.VALUE);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
                ArrayList<ImageData> lstImage = new ArrayList<>();
                rvImages = view.findViewById(R.id.rlImages);
                if (rvImages != null) {
                    imageViewAdapter = (ImageViewAdapter) rvImages.getAdapter();
                    if (imageViewAdapter != null) {
                        lstImage = imageViewAdapter.getImageList();
                    }
                }
                String imagePathData = "";
                FormData formData1 = getFormData(formData.getFormPKId());

                if (data.hasExtra("imagePath")) {

                    imagePath = data.getExtras().getString("imagePath");
                    JSONObject jsonImageData;
                    FormData formData2 = getFormData(formData.getFormPKId());
                    try {
                        jsonImageData = new JSONObject(formData2.getImageData());
                        JSONArray jsonArray = new JSONArray(imagePath);
                        JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                        int tmFormPendingCount = Math.max(StaticUtils.getTMFormPendingCount(formData1.getFormPKId()), 0);
                        StaticUtils.setTMFormPendingCount(formData1.getFormPKId(), tmFormPendingCount + jsonArray.length());
                        for (int i = 0; i < jsonArray.length(); i++) {
                            JSONObject jsonImage = new JSONObject();
                            JSONObject mainImageJson = new JSONObject();

                            JSONObject jsonObjects = jsonArray.getJSONObject(i);
                            String imagePathHigh = "";
                            if (jsonObjects.has(Constants.IMAGEPATHHIGH)) {
                                imagePathHigh = jsonObjects.getString(Constants.IMAGEPATHHIGH);
                                jsonImage.put(Constants.IMAGEPATHHIGH, imagePathHigh);
                                mainImageJson.put(Constants.IMAGEPATHHIGH, imagePathHigh);
                            }

                            addPinData(jsonObjects, jsonImage);

                            String imagePathLow = "";
                            jsonImage.put(Constants.TAGID, tag);
                            jsonImage.put(Constants.LRIMAGE, "");
                            jsonImage.put(Constants.HRIMAGE, "");
                            jsonImage.put(Constants.IS_SIGNATURE, false);
                            if (jsonObjects.has(Constants.IMAGEPATHLOW)) {
                                imagePathLow = jsonObjects.getString(Constants.IMAGEPATHLOW);
                                jsonImage.put(Constants.IMAGEPATHLOW, imagePathLow);
                                mainImageJson.put(Constants.IMAGEPATHLOW, imagePathLow);
                            }
                            if (!TextUtils.isEmpty(imagePathHigh)) {
                                imagePathData = imagePathHigh;
                            } else {
                                imagePathData = imagePathLow;
                            }
                            if (!TextUtils.isEmpty(imagePathData)) {
                                ImageData imageData = new ImageData();
                                imageData.setImagePath(imagePathData);
                                imageData.setImageId(imageId);
                                lstImage.add(imageData);
                                jsonImage.put(Constants.IMAGEID, imageId);
                                mainImageJson.put(Constants.IMAGEID, imageId);
                                imageId++;
                            }

                            mainJsonArray.put(mainImageJson);
                            jsonImageArray.put(jsonImage);
                        }
                        tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                        saveModifiedForm();
                        tblTNMForms.updateImageCountByPkId(formData.getFormPKId(), imageId);
                        formData.setImageData(jsonImageData.toString());
                        if (imageViewAdapter != null) {
                            imageViewAdapter.updateList(lstImage);
                        }
                        if (rvImages != null) {
                            if (lstImage.isEmpty()) {
                                rvImages.setVisibility(View.GONE);
                            } else {
                                rvImages.setVisibility(View.VISIBLE);
                            }
                        }

                        if (isFromMap) {
                            showMapView(false);
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                }
            }
        }
    }

    @Override
    public void onClick(View view) {
        int viewId = view.getId();
        if (viewId == R.id.imgBtnBack) {
            onBackPressed();
        } else if (viewId == R.id.btnPrevious) {
            if (currentPage > 1) {
                changePage(currentPage - 1, false, true);
            }
        } else if (viewId == R.id.btnNext) {
            if (currentPage == totalPages) {
                binding.btnNext.setClickable(false);
                new SaveDataAndValidateData(formData).execute();
            } else if (currentPage < totalPages) {
                changePage(currentPage - 1, true, true);
                checkAndShowCrewSelectionIfRequired(currentPage);
            }
        } else if (viewId == R.id.btnCheckIn || viewId == R.id.btnMapCheckIn) {
            checkAndDoCheckIn();
        } else if (viewId == R.id.ivSecondRight) {
            navigateToUploadActivityScreen(binding.tlOther.ivSecondRight);
        } else if (viewId == R.id.ivPlotMap) {
            showMapView(false);
        } else if (viewId == R.id.ivCamera) {
            showImageComponentList();
        } else if (viewId == R.id.ivFormDetail) {
            showMapView(true);
        }
    }


    private void showMapView(boolean shouldShowFormView) {
        if (shouldShowFormView) {
            binding.rlFormDetails.setVisibility(View.VISIBLE);
            binding.rlMap.setVisibility(View.GONE);
            binding.tlOther.ivPlotMap.setVisibility(View.VISIBLE);
            binding.tlOther.ivFormDetail.setVisibility(View.GONE);
            clearGoogleMap();
        } else {
            binding.rlFormDetails.setVisibility(View.GONE);
            binding.rlMap.setVisibility(View.VISIBLE);
            binding.tlOther.ivPlotMap.setVisibility(View.GONE);
            binding.tlOther.ivFormDetail.setVisibility(View.VISIBLE);
            if (formData.getIsCheckInOut() && formData.getCheckin_time() <= 0) {
                binding.ivCamera.setVisibility(View.GONE);
                binding.btnMapCheckIn.setVisibility(View.VISIBLE);
            } else {
                binding.ivCamera.setVisibility(View.VISIBLE);
                binding.btnMapCheckIn.setVisibility(View.GONE);
            }
            loadMap();
        }
        /*TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        CheckInMap mCheckInMap = tblCheckInMap.getDataByFormId(formData.getFormPKId());
        checkAndSetBottomButtonVisibility(mCheckInMap,shouldShowFormView,true);*/
    }

    private void checkAndDoCheckIn() {
        //Added Code to prevent double click
        if (SystemClock.elapsedRealtime() - mLastCheckInClickTime < 1000) {
            return;
        }
        mLastCheckInClickTime = SystemClock.elapsedRealtime();

        if (!checkLocationPermission()) {
            //Do not allow user to check in the form without location permission
            PopUtils.showLocationPermissionRequiredMessage(this);
            return;
        }

        if (!checkGPSEnableForNotGPSRequired()) {
            //Do not allow user to check in if gps is disable for and app allows user to use app without location permission
            PopUtils.showGPSIsOFF(context);
            return;
        }
        // Check user distance from site location and compare with siteRadius key
        String currentDistanceFromSite = PropertyUtils.getDistanceInString(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(), MainActivity.currentLatitude, MainActivity.currentLongitude);
        //Do not allow Check in as user is not inside site location area.
        if (siteData.getSiteConfig() != null && !PropertyUtils.userInsideSiteRadius(false, siteData.getSiteConfig().getSiteRadius(), currentDistanceFromSite, siteData.getSiteConfig().isForceRadiusOnCheckout())) {
            PopUtils.showYouAreOutsideSiteRadius(context, siteData.getSiteConfig().getSiteRadius(), currentDistanceFromSite);
            return;
        }

        TblClockCrew tblClockCrew = new TblClockCrew(this);
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            if (tblClockCrew.getAllResumedWorkCrewData().isEmpty()) {
                showClockInRequiredView();
            } else if (tblCheckInMap.isDataExist()) {
                CheckInMap checkInMap = tblCheckInMap.getAllData().get(0);
                if (formData.getFormPKId() != checkInMap.getPkId()) {
                    checkAndGetFormDataFromCheckInMap(checkInMap);
                } else {
                    checkForFormCheckInOut();
                }
            } else {
                checkForFormCheckInOut();
            }
        } else if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
            if (tblClockCrew.getAllResumedWorkCrewData().isEmpty()) {
                showClockInRequiredView();
            } else if (!hasCrewInCheckInForm && !checkSelfClockIn()) {
                showClockInRequiredView();
            } else if (!hasCrewInCheckInForm && checkSelfClockIn() && !isSelfAssignedWithForm()) {
                checkForFormCheckInOut();
            } else if (!hasCrewInCheckInForm && checkSelfClockIn() && isSelfAssignedWithForm()) {
                CheckInMap checkInMap = tblCheckInMap.getDataByCrewId(StaticUtils.getEmployeeIdInInt());
                showAlreadyCheckedInView(checkInMap);
            } else if (!checkAvailableCrewsForForm()) {
                String formNames = StaticUtils.getStringFromCheckMapList(tblCheckInMap.getAllData());
                PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name), getString(R.string.msg_checkin_more_crew, formNames), getString(R.string.ok)
                        , null, false, false, (dialog, which) -> {
                            dialog.dismiss();
                            enableDisableCrewView();
                        }, null);
            } else {
                checkForFormCheckInOut();
            }
        } else {
            if (tblCheckInMap.isDataExist()) {
                CheckInMap checkInMap = tblCheckInMap.getAllData().get(0);
                if (formData.getFormPKId() != checkInMap.getPkId()) {
                    checkAndGetFormDataFromCheckInMap(checkInMap);
                } else {
                    checkForFormCheckInOut();
                }
            } else {
                checkForFormCheckInOut();
            }
        }
    }

    private boolean checkLocationPermission() {
        //Check GPS for isGPSRequired = 0
        if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 0) {
            return checkApproxPermissionLocationGranted();
        }
        return true;
    }

    private boolean checkGPSEnableForNotGPSRequired() {
        //Check GPS for isGPSRequired = 0
        if (AppPrefShared.getInt(LOGGED_IN_PARAM_IS_LOCATION_REQUIRED, 0) == 0) {
            return canGetLocation(this);
        }
        return true;
    }

    private void checkAndGetFormDataFromCheckInMap(CheckInMap checkInMap) {
        if (checkIsFormAvailable(checkInMap.getFormPkId())) {
            showSelfCheckInView(checkInMap);
        } else {
            deleteCheckInMapDataByPK(checkInMap.getPkId());
            checkForFormCheckInOut();
        }
    }

    private boolean checkIsFormAvailable(int formPkId) {
        return getFormData(formPkId).getFormPKId() > 0;
    }

    private FormData getFormData(int formPkId) {
        TblTMForms tblTMForms = new TblTMForms(this);
        return tblTMForms.getFormDataByPKId(formPkId);
    }

    private void showSelfCheckInView(CheckInMap checkInMap) {
        PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name),
                context.getString(R.string.msg_already_self_checkin, checkInMap.getFormName(), checkInMap.getSiteName()),
                getString(R.string.open)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    setOldFormData(checkInMap.getFormPkId(), checkInMap.getSiteId());
                }, (dialog, which) -> {
                    dialog.dismiss();
                    enableDisableCrewView();
                });
    }

    private void showClockInRequiredView() {
        PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name), getString(R.string.clock_in_required_message), getString(R.string.open)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    goToMainActivityForClockIn();
                    //setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
                }, (dialog, which) -> {
                    dialog.dismiss();
                    enableDisableCrewView();
                });
    }

    private void showAlreadyCheckedInView(CheckInMap checkInMap) {
        PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name),
                context.getString(R.string.title_msg_checkout_checkin_dialog, checkInMap.getFormName(), checkInMap.getSiteName()),
                getString(R.string.open)
                , getString(R.string.txt_cancel), false, false, (dialog, which) -> {
                    setOldFormData(checkInMap.getFormPkId(), checkInMap.getSiteId());
                }, (dialog, which) -> {
                    dialog.dismiss();
                    enableDisableCrewView();
                });
    }


    /**
     * Method to check self clock in.
     * return true if login user is clocked in else return false.
     *
     * @return boolean
     */
    private boolean checkSelfClockIn() {
        TblClockCrew tblClockCrew = new TblClockCrew(this);
        return tblClockCrew.checkLoginUserClockedIn();
    }

    private boolean isSelfAssignedWithForm() {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        CheckInMap checkInMap = tblCheckInMap.getDataByCrewId(StaticUtils.getEmployeeIdInInt());
        return checkInMap.getPkId() > 0;
    }

    private void checkForFormCheckInOut() {
        if (formData.getIsCheckInOut() && formData.isCheckInOutComplete() && formData.isCheckInOutFormComplete()) {
            PopUtils.showCustomTwoButtonAlertDialog(context, getString(R.string.app_name), getString(R.string.msg_reopen_completed_form),
                    getString(R.string.yes), getString(R.string.no), true,
                    (dialog, which) -> {
                        updateStatusOfSelectedForm();
                        doCheckIn();
                    }, (dialog, which) -> {
                        dialog.dismiss();
                        enableDisableCrewView();
                    });
        } else {
            doCheckIn();
        }
    }

    private boolean checkAvailableCrewsForForm() {
        return getNotAssignedCrews().size() > 0;

    }


    private void updateStatusOfSelectedForm() {
        TblTMForms tblTMForms = new TblTMForms(this);
        formData.setCheckInOutFormComplete(false);
        tblTMForms.updateCheckInOutFormByPkId(formData.getFormPKId(), false);
        EventBus.getDefault().post(new SiteDataUpdateEvent(formData.getSiteId()));
    }

    private void setOldFormData(int formPkId, long siteId) {
        resetGlobalData();
        currentSiteData = siteData;
        currentFormPkId = mFormPkId;
        initializeData(formPkId, siteId);
        setData(false);
        enableDisableCrewView();
    }

    private void setCurrentData() {
        resetGlobalData();
        mFormPkId = currentFormPkId;
        initializeData(mFormPkId, currentSiteData.getSiteId());
        setData(true);
        enableDisableCrewView();
    }

    private void initializeData(int formPkId, long siteId) {
        clearListData();
        TblSites tblSites = new TblSites(this);
        siteData = tblSites.getDataFromSiteId(siteId);
        lastUpdatedBuildingId = String.valueOf(siteData.getSiteId());
        lastBuildingId = lastUpdatedBuildingId;
        mFormPkId = formPkId;
        formData = getFormData(mFormPkId);
        if (formData == null || TextUtils.isEmpty(formData.getModifiedFormData())) {
            finish();
        }
        formString = formData.getModifiedFormData();
    }

    /**
     * Clear Date when user open already checked in form from another form detail screen
     */
    private void clearListData() {
        mapImageUpload.clear();
        mapSignature.clear();
        mapTagObject.clear();
        panelValidationData.clear();
        lstChildMaterial.clear();
        if (validationMap != null) {
            validationMap.clear();
        }
        lstCrewComponent.clear();
        lstGeoComponent.clear();
        lstView.clear();
        preSelectServiceArray = new JSONArray();
        lstPreSelectServiceView.clear();
        hasCrewInCheckInForm = false;
    }

    private void enableDisableCrewView() {
        try {
            View crewView = getViewForCrewComponent();
            if (crewView != null) {
                LinearLayout llMultiSelect = crewView.findViewById(R.id.llMultiSelect);
                llMultiSelect.setEnabled(false);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void goToMainActivityForClockIn() {
        TblTMForms tblTMForms = new TblTMForms(this);
        tblTMForms.resetFormDataByPkId(formData.getFormPKId(), formData.getFormData(), formData.isCheckInOutComplete());
        try {
            updatedJsonObject = new JSONObject(formData.getFormData());
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }

        Intent intent = new Intent();
        intent.putExtra("ResultType", 2);
        setResult(RESULT_OK, intent);
        finish();
    }

    private void doCheckIn() {
        if (hasCrewInCheckInForm && checkIsFormHasPreSelectServices()) {
            showCrewSelectionAndServiceSelectionViewWithoutCheckInForm();
        } else if (checkIsFormHasPreSelectServices()) {
            showPreSelectServiceViewForCheckIn();
        } else if (hasCrewInCheckInForm) {
            showCrewSelectionViewWithoutCheckInForm();
        } else {
            setCheckedInDataAndStoreIt();
            checkInFormAndInitialSetup();
        }
    }

    /**
     * Check if any service has perSelect variable and it is set true from server.
     *
     * @return true if form is check in out and pre select service true.
     */
    private boolean checkIsFormHasPreSelectServices() {
        return formData.getIsCheckInOut() && formData.isPreSelectServices() && preSelectServiceArray.length() > 0;
    }

    private void showCrewSelectionAndServiceSelectionViewWithoutCheckInForm() {
        View crewView = getViewForCrewComponent();
        if (crewView == null) {
            return;
        }
        TextView tvMultiSelected = crewView.findViewById(R.id.tvMultiSelected);
        if (tvMultiSelected.getParent() instanceof LinearLayout) {
            ((LinearLayout) tvMultiSelected.getParent()).setTag(crewView.getTag() + "@@" + "Service");
            ((LinearLayout) tvMultiSelected.getParent()).callOnClick();
        }
    }

    private void showCrewSelectionViewWithoutCheckInForm() {
        View crewView = getViewForCrewComponent();

        if (crewView == null) {
            return;
        }
        TextView tvMultiSelected = crewView.findViewById(R.id.tvMultiSelected);

        if (tvMultiSelected.getParent() instanceof LinearLayout) {
            ((LinearLayout) tvMultiSelected.getParent()).callOnClick();
        }
    }


    private void checkInformAndSubmitCrews(JSONObject object, List<Employees> lstResult,
                                           List<Integer> lstDeselectedEmployee, List<Integer> lstNewSelected, View crewView) {
        setCrewInViewAndSendBreadcrumb(object, lstResult, lstDeselectedEmployee, lstNewSelected, crewView);
    }

    private void checkInFormAndInitialSetup() {
        if (formData.isPreSelectServices() && preSelectServiceArray.length() > 0) {
            removeNotCompletedPreSelectServices();
            String preSelectServiceData = StaticUtils.filterPreSelectServiceJSON(preSelectServiceArray);
            setSiteAndFormBroadCrumbs(2, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), preSelectServiceData);
        } else {
            setSiteAndFormBroadCrumbs(2, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
        }

        EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
        enableOrDisableView(true);
        disablePreSelectServiceView();
        binding.btnCheckIn.setVisibility(View.GONE);
        binding.btnMapCheckIn.setVisibility(View.GONE);
        binding.btnNext.setVisibility(View.VISIBLE);
        //enable button click event specially when when user checkout from old from and check in to the current form.
        binding.btnNext.setClickable(true);
        binding.btnPrevious.setVisibility(View.VISIBLE);
        //showDialogForCrewAtCheckedIn(showCrewView);

        if (currentPage == 1) {
            binding.btnPrevious.setVisibility(View.GONE);
            binding.tlOther.imgBtnBack.setColorFilter(ContextCompat.getColor(context, R.color.white), android.graphics.PorterDuff.Mode.MULTIPLY);
        } else {
            binding.btnPrevious.setVisibility(View.VISIBLE);
            binding.tlOther.imgBtnBack.setColorFilter(ContextCompat.getColor(context, R.color.gray), android.graphics.PorterDuff.Mode.MULTIPLY);
        }

        if (formData.isPlotOnMap() && binding.tlOther.ivFormDetail.getVisibility() == View.VISIBLE) {
            showMapView(false);
        }
    }

    /*
     * Removed not completed services from array
     */
    private void removeNotCompletedPreSelectServices() {
        for (int i = 0; i < preSelectServiceArray.length(); i++) {
            try {
                JSONObject jsonObject = preSelectServiceArray.getJSONObject(i);
                TMService tMService = new Gson().fromJson(jsonObject.getString(Constants.TnMService), TMService.class);
                if (!tMService.isCompleted()) {
                    preSelectServiceArray.remove(i);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    private void resetGlobalData() {
        currentPage = 1;
        imageId = 1;
        TblTMForms tblTMForms = new TblTMForms(this);
        tblTMForms.updateImageCountByPkId(formData.getFormPKId(), imageId);
    }

    /**
     * Method to store check in data in  CheckInMap table.
     */
    private void addDataInCheckInTable() {
        CheckInMap checkInMap = new CheckInMap();
        checkInMap.setSiteId(siteData.getSiteId());
        checkInMap.setFormId(formData.getFormId());
        checkInMap.setSiteName(siteData.getSiteName());
        checkInMap.setFormName(formData.getFormName());
        checkInMap.setFormPkId(formData.getFormPKId());
        checkInMap.setFormSubmissionId(formData.getFormSubmissionId());
        if (isTMForm() && !hasCrewInCheckInForm && checkSelfClockIn() && !isSelfAssignedWithForm()) {
            List<Integer> lstSelfId = new ArrayList<>();
            lstSelfId.add(StaticUtils.getEmployeeIdInInt());
            checkInMap.setCrewIds(lstSelfId);
        }
        checkInMap.setCreatedDate(System.currentTimeMillis());
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        tblCheckInMap.insertSingleData(checkInMap);
    }

    private void setCheckedInDataAndStoreIt() {
        if (formData.getIsCheckInOut()) {
            addDataInCheckInTable();
            TblTMForms tblTMForms = new TblTMForms(this);
            formData.setCheckin_time(System.currentTimeMillis());
            tblTMForms.updateFormCheckInTime(TblTMForms.TABLE_NAME, formData.getCheckin_time(), formData.getFormPKId());
        }
    }

    private void submitForm() {
        if (isFinishing())
            return;
        try {
            startProgress();
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            // Re initiate data executor service if its been shutdown or terminated
            BaseApplication.getInstance().reInitiateDataExecutorServiceIfShutDown();
            BaseApplication.getInstance().dataExecutorService.submit(() -> {
                boolean shouldExecuteSignaturePart = false;
                try {
                    shouldExecuteSignaturePart = submitFormDataInSafeThread(mFormPkId);
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                } finally {
                    if (shouldExecuteSignaturePart) {
                        addSignatureAndUploadData(formData);
                    } else {
                        runOnUiThread(this::stopProgressDialog);
                        checkAndDeleteCheckInMapData(formData);
                        if (currentFormPkId > 0) {
                            //Call checkout and close breadcrumb (checkout =3 and close = 7) and then check-in current form
                            setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
                            setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
                            notifyAndSetCurrentData();
                        } else {
                            goBackToListingScreen();
                        }
                    }

                }
            });
        } catch (RejectedExecutionException e) {
            FirebaseEventUtils.logException(e);
        } finally {
            stopProgressDialog();
        }
    }

    private void addSignatureAndUploadData(FormData formData) {
        startProgress();
        addSignaturInUploadingQueue(isTMForm(), formData);
        try {
            checkAndDeleteCheckInMapData(formData);
            BaseApplication.getInstance().startImageUpload();
            saveDataInDbAtFormSubmission(formData);
            insertCopyData(isTMForm(), formData);
            addDataInUploadQueue(isTMForm(), formData, false, lastUpdatedBuildingId, lastBuildingId);
            runOnUiThread(this::stopProgressDialog);
            if (currentFormPkId > 0) {
                //Call checkout and close breadcrumb (checkout =3 and close = 7) and then check-in current form
                setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
                //setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
                notifyAndSetCurrentData();
            } else {
                goBackToListingScreen();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
       /* AddSignatureInUploadQueue addSignatureInUploadQueue = new AddSignatureInUploadQueue(this, formData, isTMForm(), result -> {
            try {
                stopProgressDialog();
                checkAndDeleteCheckInMapData(formData);
                AsyncTask.execute(() -> BaseApplication.getInstance().startImageUpload());
                saveDataInDbAtFormSubmission(formData);
                insertCopyData(isTMForm(), formData);
                addDataInUploadQueue(isTMForm(), formData, false, lastUpdatedBuildingId, lastBuildingId);

                if (currentFormPkId > 0) {
                    //Call checkout and close breadcrumb (checkout =3 and close = 7) and then check-in current form
                    setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
                    //setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
                    setCurrentData();
                    currentFormPkId = 0;
                } else {
                    goBackToListingScreen();
                }
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
        });
        addSignatureInUploadQueue.execute();*/

    }

    private void notifyAndSetCurrentData() {
        runOnUiThread(() -> {
            setCurrentData();
            currentFormPkId = 0;
            EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
        });
    }


    private void saveDataInDB(FormData formData) {
        TblTMForms tblTNMForms = new TblTMForms(TMFormDetailActivity.this);
        if (updatedJsonObject != null) {
            saveModifiedForm();
            tblTNMForms.updateLastBuildingIdByPkId(formData.getFormPKId(), (int) siteData.getSiteId());
            //tblTNMForms.updateCheckInOutFormByPkId(formData.getFormPKId(),formData.isCheckInOutFormComplete());
        }
        if (formData.getCheckin_time() > 0 && formData.getIsCheckInOut()) {
            TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
            tblCheckInMap.updateFormValidation(formData.getFormPKId(), true);
        }
    }

    private void saveDataInDbAtFormSubmission(FormData formData) {
        saveDataInDB(formData);
        TblTMForms tblTNMForms = new TblTMForms(TMFormDetailActivity.this);
        formData.setFormSubmitted(true);
        formData.setSf_submited(System.currentTimeMillis() / 1000);
        formData.setLatitude(MainActivity.currentLatitude);
        formData.setLongitude(MainActivity.currentLongitude);
        tblTNMForms.updateFormSubmittedStatus(formData.getFormPKId(), MainActivity.currentLatitude, MainActivity.currentLongitude);
        if (formData.getIsCheckInOut())
            tblTNMForms.updateFormCheckOutTime(TblTMForms.TABLE_NAME, System.currentTimeMillis(), formData.getFormPKId());

        checkAndDeleteCheckInMapData(formData);
    }

    public boolean isFormValidate(boolean isFromBackPress) {
        boolean isValidData = true;
        int counter = 0;

        if (formData.hasSubForm()) {
            List<FormData> lstForm = getSubFormData();
            for (FormData subFormData : lstForm) {
                if (subFormData.getCheckin_time() > 0 && subFormData.getCheckout_time() <= 0) {
                    showForeGroundToastLong(getString(R.string.you_need_to_checkout_crew_item_for_crew, subFormData.getSubFormOtherData().getCrewName()));
                    return false;
                }
            }
        }

        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    if (validationData.getPageNumber() < currentPage) {
                        //if (validationData.getPageNumber() > 1)
                        //changePage(validationData.getPageNumber() - 1, false, false);
                        //else
                        changePage(validationData.getPageNumber(), false, false);
                    }
                }
            }
        }
        for (View view : validationMap.keySet()) {
            ValidationData validationData = (ValidationData) validationMap.get(view);
            if (validationData != null) {
                if (!validationData.isValidate() && ((View) view.getParent()).getVisibility() == View.VISIBLE) {
                    NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                    if (view instanceof EditText) {
                        isValidData = false;

                        if (validationData.isEmail()) {
                            if (!isFromBackPress) {
                                ((EditText) view).setError(getString(R.string.msg_valid_email_address));
                                ((EditText) view).setSelection(((EditText) view).getText().toString().trim().length());
                            }
                        } else {
                            if (!isFromBackPress) {
                                ((EditText) view).setError(getString(R.string.msg_field_can_not_be_an_empty));
                            }
                        }
                        if (binding.vfPages.getDisplayedChild() == validationData.getPageNumber() - 1) {
                            counter++;
                            if (counter == 1) {
                                if (!isFromBackPress) {
                                    showForeGroundToast(getString(R.string.required, validationData.getTitle()));
                                }
                                focusOnView(binding.llMain, scrollView, view);
                            }
                        }
                    } else if (view instanceof TextView) {
                        isValidData = false;
                        if (!isFromBackPress) {
                            ((TextView) view).setError(getString(R.string.msg_field_can_not_be_an_empty));
                        }
                        if (binding.vfPages.getDisplayedChild() == validationData.getPageNumber() - 1) {
                            counter++;
                            if (counter == 1) {
                                if (!isFromBackPress) {
                                    showForeGroundToast(getString(R.string.required, validationData.getTitle()));
                                }
                                focusOnView(binding.llMain, scrollView, view);
                            }
                        }
                    }
                }
            }
        }
        if (isValidData) {
            isValidData = checkPanelValidation();
        }
        return isValidData;
    }


    private boolean checkPanelValidation() {
        formData = getFormData(mFormPkId);
        if (panelValidationData.isEmpty())
            return true;
        StaticUtils.scanPanelDataInJSON(formData.getModifiedFormData(), panelValidationData);
        Iterator panelIterator = panelValidationData.keySet().iterator();
        while (panelIterator.hasNext()) {
            int key = (int) panelIterator.next();
            PanelData panelData = panelValidationData.get(key);
            if (panelData != null && !panelData.isValidated()) {
                NestedScrollView scrollView = binding.vfPages.getCurrentView().findViewById(R.id.svScroll);
                changePage(panelData.getPageNumber(), false, false);
                showForeGroundToastLong(getString(R.string.required_panel, panelData.getTitle()));
                focusOnView(binding.llMain, scrollView, panelData.getPanelView());
                return false;
            }
        }
        return true;
    }

    private void concatImageData() {
        TblTMForms tblTNMForms = new TblTMForms(context);
        try {
            JSONArray jsonArray = updatedJsonObject.getJSONArray(Constants.PAGES);
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            jsonArrayReadToUpdateImageUrl(array);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);

                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);

                }
            }
            saveModifiedForm();
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);

        }
    }

    private void jsonArrayReadToUpdateImageUrl(JSONArray jsonArray) {
        formData = getFormData(formData.getFormPKId());
        String imagePathString = formData.getImageData();
        for (int i = 0; i < jsonArray.length(); i++) {
            try {

                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    if (array != null) {
                        jsonArrayReadToUpdateImageUrl(array);
                    }
                } else if (object.has(Constants.CHOICES)) {
                    try {
                        JSONArray array = object.getJSONArray(Constants.CHOICES);
                        for (int j = 0; j < array.length(); j++) {
                            JSONObject jsonObject = null;
                            try {
                                if (array.get(j) instanceof JSONObject) {
                                    jsonObject = array.getJSONObject(j);
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                            if (jsonObject != null) {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    try {
                                        JSONArray jsonArray1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                        if (jsonArray1.length() > 0) {
                                            jsonArrayReadToUpdateImageUrl(jsonArray1);
                                        }
                                    } catch (JSONException e) {
                                        e.getStackTrace();

                                    }
                                }
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);

                    }
                } else {
                    if (!TextUtils.isEmpty(imagePathString)) {
                        JSONObject jsonObject = new JSONObject(imagePathString);
                        JSONArray array = jsonObject.getJSONArray(Constants.DATA);
                        if (object.getString(Constants.TYPE).equals(Constants.IMAGE_UPLOAD)) {
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);
                                        for (int k = 0; k < array.length(); k++) {
                                            JSONObject object2 = array.getJSONObject(k);
                                            if (object1.has(Constants.IMAGEID) && object2.has(Constants.IMAGEID) && object2.getInt(Constants.IMAGEID) == object1.getInt(Constants.IMAGEID)) {
                                                object1.put(Constants.LRIMAGE, object2.getString(Constants.LRIMAGE));
                                                object1.put(Constants.HRIMAGE, object2.getString(Constants.HRIMAGE));
                                                if (object2.has(Constants.IMAGE_UUID)) {
                                                    object1.put(Constants.IMAGE_UUID, object2.getString(Constants.IMAGE_UUID));
                                                }
                                                object1.put(Constants.IMAGEPATHHIGH, object2.getString(Constants.IMAGEPATHHIGH));
                                                object1.put(Constants.IMAGEPATHLOW, object2.getString(Constants.IMAGEPATHLOW));
                                                addPinData(object2, object1);
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        } else if (object.getString(Constants.TYPE).equals(Constants.ISSUES)) {
                            try {
                                if (object.has(Constants.VALUE)) {
                                    JSONArray jsonArray1 = object.getJSONArray(Constants.VALUE);
                                    for (int j = 0; j < jsonArray1.length(); j++) {
                                        JSONObject object1 = jsonArray1.getJSONObject(j);

                                        if (object1.has(Constants.VALUE)) {
                                            JSONArray jsonArray2 = object1.getJSONArray(Constants.VALUE);
                                            for (int k = 0; k < jsonArray2.length(); k++) {
                                                JSONObject object2 = jsonArray2.getJSONObject(k);
                                                for (int l = 0; l < array.length(); l++) {
                                                    JSONObject object3 = array.getJSONObject(l);
                                                    if (object2.getInt(Constants.IMAGEID) == object3.getInt(Constants.IMAGEID)) {
                                                        object2.put(Constants.LRIMAGE, object3.getString(Constants.LRIMAGE));
                                                        object2.put(Constants.HRIMAGE, object3.getString(Constants.HRIMAGE));
                                                        if (object3.has(Constants.IMAGE_UUID)) {
                                                            object2.put(Constants.IMAGE_UUID, object3.getString(Constants.IMAGE_UUID));
                                                        }
                                                        object2.put(Constants.IMAGEPATHHIGH, object3.getString(Constants.IMAGEPATHHIGH));
                                                        object2.put(Constants.IMAGEPATHLOW, object3.getString(Constants.IMAGEPATHLOW));
                                                        addPinData(object3, object2);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);

                            }
                        }
                    }

                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);

            }
        }
    }

    private void addPinData(JSONObject fromJson, JSONObject toJson) {
        try {
            if (formData.isPlotOnMap()) {
                if (fromJson.has(Constants.MAP_PIN_URL))
                    toJson.put(Constants.MAP_PIN_URL, fromJson.getString(Constants.MAP_PIN_URL));
                if (fromJson.has(Constants.PARAM_LAT))
                    toJson.put(Constants.PARAM_LAT, fromJson.getDouble(Constants.PARAM_LAT));
                if (fromJson.has(Constants.PARAM_LON))
                    toJson.put(Constants.PARAM_LON, fromJson.getDouble(Constants.PARAM_LON));
                if (fromJson.has(Constants.PIN_LABEL)) {
                    toJson.put(Constants.PIN_LABEL, fromJson.getString(Constants.PIN_LABEL));
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode,
                                           @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case Constants.LOCATION_PERMISSION_REQUEST:
                doAfterLocationPermissionResult(permissions, grantResults);
                break;
        }
    }


    @Override
    public void onPointerCaptureChanged(boolean hasCapture) {

    }

    private void goBackToListingScreen() {

        runOnUiThread(() -> {
            if (formData.getIsCheckInOut()) {
                setSiteAndFormBroadCrumbs(3, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
            } else {
                //Added this condition to prevent null data in uploading queue because of multiple button click when device is too slow
                if(formData.getFormPKId()>0 && !TextUtils.isEmpty(formData.getFormSubmissionId())) {
                    setSiteAndFormBroadCrumbs(8, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId());
                }else{
                    //Added event to understand empty form data
                    FirebaseEventUtils.EmptyDataSubmissionEvent(this);
                }
            }
            //setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
            EventBus.getDefault().post(new TMFormCheckInOutEvent(formData.getSiteId(), formData.getFormId()));
            stopProgressDialog();
            showForeGroundToast(getString(R.string.txt_form_submitted));
        });
        finish();
    }


    @Override
    public void onBackPressed() {
        if (currentPage != 1) {
            return;
        }
        resetAllDataIfNotCheckedIn();
        //setSiteAndFormBroadCrumbs(7, siteData.getSiteId(), formData.getFormId(), formData.getFormName());
        if (currentFormPkId > 0) {
            setCurrentData();
            currentFormPkId = 0;
        } else {
            super.onBackPressed();
        }

    }

    /**
     * Method to reset all data if user not check into the form and press back button
     */
    private void resetAllDataIfNotCheckedIn() {
        if (formData.getIsCheckInOut() && formData.getCheckin_time() == 0) {
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.resetFormDataByPkId(formData.getFormPKId(), formData.getFormData(), formData.isCheckInOutComplete());
        } else {
            saveDataInDB(formData);
        }
    }

    @Override
    public void onSuccessResponse(Response<AppDataResponse> response) {

    }

    @Override
    public void onFailureResponse(Throwable t) {

    }

    @Override
    public void onNoInternetConnection() {

    }

    @Override
    public void onUpdateAppVersion(int versionCode, boolean isForceUpdate) {
        checkAndShowAppUpdateDialog(versionCode, isForceUpdate);
    }


    private class SaveDataAndValidateData extends AsyncTask<Void, Void, Boolean> {
        FormData formData;

        SaveDataAndValidateData(FormData formData) {
            this.formData = formData;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            if (isFinishing())
                return;
            try {
                startProgress();
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);

            }
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            saveDataInDB(formData);
            return false;
        }

        @Override
        protected void onPostExecute(Boolean shouldExecuteSignaturePart) {
            super.onPostExecute(shouldExecuteSignaturePart);
            stopProgressDialog();

            if (!checkLocationPermission()) {
                //Do not allow user to check in the form without location permission
                PopUtils.showLocationPermissionRequiredMessage(TMFormDetailActivity.this);
                binding.btnNext.setClickable(true);
                return;
            }

            if (!checkGPSEnableForNotGPSRequired()) {
                //Do not allow user to check in if gps is disable for and app allows user to use app without location permission
                PopUtils.showGPSIsOFF(context);
                binding.btnNext.setClickable(true);
                return;
            }
            // Check user distance from site location and compare with siteRadius key
            String currentDistanceFromSite = PropertyUtils.getDistanceInString(siteData.getPropertyLatitude(), siteData.getPropertyLongitude(), MainActivity.currentLatitude, MainActivity.currentLongitude);
            // Do not allow Check out as user is not inside site location area.
            if (siteData.getSiteConfig() != null && !PropertyUtils.userInsideSiteRadius(true, siteData.getSiteConfig().getSiteRadius(), currentDistanceFromSite, siteData.getSiteConfig().isForceRadiusOnCheckout())) {
                PopUtils.showYouAreOutsideSiteRadius(context, siteData.getSiteConfig().getSiteRadius(), currentDistanceFromSite);
                binding.btnNext.setClickable(true);
                return;
            }
            if (isFormValidate(false)) {
                submitForm();
            } else {
                binding.btnNext.setClickable(true);
            }
        }
    }


    @Override
    protected void onStop() {
        super.onStop();
        try {
            if (!isDeviceLocked(this)) {
                if (formData != null && formData.getIsCheckInOut() && formData.getCheckin_time() == 0) {
                    updatedJsonObject = new JSONObject(formData.getFormData());
                }
                saveModifiedForm();

            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    @Override
    protected void onDestroy() {
        try {
            unregisterReceiver(mMessageReceiver);
            unregisterReceiver(formUpdateReceiver);
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);

        }
        super.onDestroy();
    }


    private boolean submitFormDataInSafeThread(int mFormPkId) {
        formData = getFormData(mFormPkId);
        submitIssueAtFormSubmissionTime(context, isTMForm(), siteData, formData);
        updateStopServiceTimeAtSubmissionTime(updatedJsonObject);
        int pendingMediaCount = 0;
        try {
            JSONObject jsonObject = null;
            if (!TextUtils.isEmpty(formData.getImageData())) {
                jsonObject = new JSONObject(formData.getImageData());
                pendingMediaCount = Math.max(StaticUtils.getTMFormPendingCount(mFormPkId), 0);
            }
            if (pendingMediaCount == 0) {
                saveDataInDbAtFormSubmission(formData);
                addDataInUploadQueue(isTMForm(), formData, true, lastUpdatedBuildingId, lastBuildingId);
            } else {
                JSONArray imageArray = jsonObject.getJSONArray(Constants.DATA);
                if (imageArray.length() > 0) {
                    List<String> lstSignaturePath = new ArrayList<>();
                    for (int i = 0; i < imageArray.length(); i++) {
                        if (imageArray.getJSONObject(i).getBoolean(Constants.IS_SIGNATURE)) {
                            String imagePath = imageArray.getJSONObject(i).getString(IMAGEPATHLOW);
                            lstSignaturePath.add(imagePath);
                        }
                    }
                    if (!lstSignaturePath.isEmpty()) {
                        TblTMForms tblTNMForms = new TblTMForms(context);
                        tblTNMForms.updateLastBuildingIdByPkId(formData.getFormPKId(), (int) siteData.getSiteId());
                        return true;

                    } else {
                        saveDataInDbAtFormSubmission(formData);
                        insertCopyData(isTMForm(), formData);
                        addDataInUploadQueue(isTMForm(), formData, false, lastUpdatedBuildingId, lastBuildingId);
                    }

                } else {
                    saveDataInDbAtFormSubmission(formData);
                    addDataInUploadQueue(isTMForm(), formData, true, lastUpdatedBuildingId, lastBuildingId);
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return false;
    }
}
