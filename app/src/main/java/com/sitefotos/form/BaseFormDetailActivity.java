package com.sitefotos.form;

import static com.sitefotos.Constants.AUTO_FILL_LOGGED_USERID;
import static com.sitefotos.Constants.CHOICES;
import static com.sitefotos.Constants.CHOICEVALUE;
import static com.sitefotos.Constants.COMPLETED;
import static com.sitefotos.Constants.DROPDOWN_VALUE_TYPE;
import static com.sitefotos.Constants.DYNAMIC_DROPDOWN;
import static com.sitefotos.Constants.FORM_SUBMITTED_BY;
import static com.sitefotos.Constants.ID;
import static com.sitefotos.Constants.IMAGEPATHHIGH;
import static com.sitefotos.Constants.IMAGEPATHLOW;
import static com.sitefotos.Constants.IMAGE_UPLOAD;
import static com.sitefotos.Constants.IS_FLEET_EQUIPMENTLIST;
import static com.sitefotos.Constants.IS_FLEET_VEHICLELIST;
import static com.sitefotos.Constants.LOCATION_PERMISSION_REQUEST;
import static com.sitefotos.Constants.LOCATION_REQUEST_CODE;
import static com.sitefotos.Constants.LOGGED_IN_USER_PARAM_SKIP_GEO;
import static com.sitefotos.Constants.SELECTED_EQUIPMENTS;
import static com.sitefotos.Constants.SELECTED_VEHICLES;
import static com.sitefotos.Constants.SPINNER;
import static com.sitefotos.Constants.TYPE;
import static com.sitefotos.Constants.VALUE;
import static com.sitefotos.util.PopUtils.showBottomViewForMaterialSelect;
import static com.sitefotos.util.StaticUtils.addCompoundDrawableWithView;
import static com.sitefotos.util.StaticUtils.addSeparatorView;
import static com.sitefotos.util.StaticUtils.changeVisibilityOfCurrentTime;
import static com.sitefotos.util.StaticUtils.changeVisibilityOfTotalTime;
import static com.sitefotos.util.StaticUtils.clearHourData;
import static com.sitefotos.util.StaticUtils.clearPeopleData;
import static com.sitefotos.util.StaticUtils.getArrayFromJSonArrayForAutoFill;
import static com.sitefotos.util.StaticUtils.getEmployeeDataFromCrewList;
import static com.sitefotos.util.StaticUtils.getStringFromDynamicDropDownSelectedData;
import static com.sitefotos.util.StaticUtils.getStringFromDynamicDropDownSelectedDataID;
import static com.sitefotos.util.StaticUtils.setDefaultVehicleAndEquipmentValue;
import static com.sitefotos.util.StaticUtils.setTitleOfComponent;
import static com.sitefotos.util.StaticUtils.setViewAndChildrenEnabled;
import static com.sitefotos.util.StaticUtils.setVisibilityOfServiceSubView;
import static com.sitefotos.util.StaticUtils.updateServiceValidationInMap;

import android.annotation.SuppressLint;
import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.res.ColorStateList;
import android.graphics.Point;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.location.Location;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Patterns;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.animation.Animation;
import android.webkit.WebSettings;
import android.widget.CheckBox;
import android.widget.Chronometer;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.ViewFlipper;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageButton;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.core.content.res.ResourcesCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.BitmapDescriptor;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.gson.Gson;
import com.google.gson.JsonParser;
import com.sitefotos.BaseActivity;
import com.sitefotos.BaseApplication;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.R;
import com.sitefotos.adapter.ImageViewAdapter;
import com.sitefotos.adapter.SubFormAdapter;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.camera.CameraActivity;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.event.CrewSelectionEvent;
import com.sitefotos.event.PreSelectedServiceEvent;
import com.sitefotos.interfaces.OnImageComponentSelected;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.CrewSelectionData;
import com.sitefotos.models.DynamicDropDownItem;
import com.sitefotos.models.Employees;
import com.sitefotos.models.Equipments;
import com.sitefotos.models.FormComponentData;
import com.sitefotos.models.FormData;
import com.sitefotos.models.ImageComponent;
import com.sitefotos.models.ImageData;
import com.sitefotos.models.ItemModel;
import com.sitefotos.models.MapDetail;
import com.sitefotos.models.Material;
import com.sitefotos.models.PanelData;
import com.sitefotos.models.SegmentData;
import com.sitefotos.models.SiteData;
import com.sitefotos.models.TMService;
import com.sitefotos.models.TimeLog;
import com.sitefotos.models.UploadOtherData;
import com.sitefotos.models.ValidationData;
import com.sitefotos.models.Vehicles;
import com.sitefotos.site.detail.OnMultiCrewSelected;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCheckInMap;
import com.sitefotos.storage.tables.TblClockCrew;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblDynamicDropdownItems;
import com.sitefotos.storage.tables.TblEmployees;
import com.sitefotos.storage.tables.TblEquipment;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblMaterials;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.storage.tables.TblServices;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadData;
import com.sitefotos.storage.tables.TblVehicles;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PermissionUtils;
import com.sitefotos.util.PolygonCalculation;
import com.sitefotos.util.PopUtils;
import com.sitefotos.util.StaticUtils;
import com.sitefotos.util.views.CustomEditText;
import com.sitefotos.util.views.CustomInfoWindowAdapter;
import com.thefinestartist.finestwebview.FinestWebView;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public abstract class BaseFormDetailActivity extends BaseActivity implements OnMapReadyCallback {
    HashMap<Integer, Object> mapImageUpload = new HashMap<>();
    HashMap<Integer, Object> mapSignature = new HashMap<>();
    HashMap<Integer, Object> mapTagObject = new HashMap<>();
    HashMap<Integer, PanelData> panelValidationData = new HashMap<>();
    JSONArray autoFillArray = new JSONArray();
    ArrayList<Material> lstChildMaterial = new ArrayList<>();
    int tag = 0;
    String formString;
    FormData formData;
    int mFormPkId;
    HashMap<View, Object> validationMap;
    Animation leftOutAnimation, leftInAnimation, rightInAnimation, rightOutAnimation;
    Context context;
    String lastBuildingId = "0";
    String lastUpdatedBuildingId = "0";
    JSONObject updatedJsonObject;
    int totalPages = 0;
    int pageNumber = 0;
    public final int SIGN_FOR_RESULT = 1010;
    public boolean isGpsEnableCalled = false;
    public List<PropertiesVo> lstProperty;
    int currentPage = 1;
    int lastOpenedPage = 1;

    boolean returnFromSubFrom;
    int imageId = 1;
    public static final int REQUEST_CAMERA = 1011;

    SiteData siteData, currentSiteData;
    int currentFormPkId;
    List<View> lstView = new ArrayList<>();
    HashMap<View, JSONObject> lstPreSelectServiceView = new HashMap<>();
    HashMap<Integer, FormComponentData> lstCrewComponent = new HashMap<>();
    HashMap<Integer, FormComponentData> lstGeoComponent = new HashMap<>();
    AppCompatEditText lastFocusedView;
    ViewFlipper vfPages;
    private GoogleMap googleMap;
    JSONArray preSelectServiceArray = new JSONArray();
    boolean hasCrewInCheckInForm;
    public ExecutorService dbExecutorService = Executors.newSingleThreadExecutor();

    abstract boolean isTMForm();

    /**
     * Check if json is valid.
     *
     * @param modified modified json data
     * @return true if data is valid else false
     */
    public static boolean isJSONValid(String modified) {
        try {
            JsonParser.parseString(modified);
            return true;
        } catch (com.google.gson.JsonSyntaxException ex) {
            return false;
        }
    }

    public void updatePanelValidationInMap(View view, boolean isValidate) {
        ValidationData validationData = (ValidationData) validationMap.get(view);
        if (validationData != null) {
            validationData.setValidate(isValidate);
            validationMap.put(view, validationData);
        }
    }

    public void updateValidationInMap(JSONObject object, View view, String enteredData,
                                      boolean isEmail, boolean isOther, boolean isValidate) {
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (object.getBoolean(Constants.ISREQUIRED)) {
                    ValidationData validationData = (ValidationData) validationMap.get(view);
                    if (validationData != null) {
                        if (isEmail) {
                            if (!TextUtils.isEmpty(enteredData)) {
                                validationData.setValidate(Patterns.EMAIL_ADDRESS.matcher(enteredData).matches());
                            } else {
                                validationData.setValidate(false);
                            }
                        } else {
                            if (isOther) {
                                validationData.setValidate(isValidate);
                            } else {
                                validationData.setValidate(!TextUtils.isEmpty(enteredData));
                            }
                        }
                        validationData.setValue(enteredData);
                        validationMap.put(view, validationData);
                    }
                } else if (isEmail) {
                    ValidationData validationData = (ValidationData) validationMap.get(view);
                    if (validationData != null) {
                        if (!TextUtils.isEmpty(enteredData)) {
                            validationData.setValidate(Patterns.EMAIL_ADDRESS.matcher(enteredData).matches());
                        } else {
                            validationData.setValidate(true);
                        }
                        validationData.setValue(enteredData);
                        validationMap.put(view, validationData);
                    } else {
                        validationData = setValidationInMap(object, true, view, "");
                        validationData.setValidate(Patterns.EMAIL_ADDRESS.matcher(enteredData).matches());
                        validationMap.put(view, validationData);
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    public void updateValidationInMap(JSONObject object, View view, String enteredData) {
        updateValidationInMap(object, view, enteredData, false, false, false);
    }


    public void updateValidationInMap(JSONObject object, View view, String enteredData,
                                      boolean isEmail) {
        updateValidationInMap(object, view, enteredData, isEmail, false, false);
    }

    public void setValidationInMap(JSONObject object, View view, String customTitle) {
        setValidationInMap(object, false, view, customTitle);
    }

    public void setValidationInMap(JSONObject object, View view) {
        setValidationInMap(object, false, view, "");
    }

   /* public ValidationData setValidationInMapForPanel(View keyView, String customTitle, boolean isValidated) {
        ValidationData validationData = new ValidationData();
        validationData.setPageNumber(pageNumber);
        validationData.setValidate(isValidated);
        validationData.setEmail(false);
        validationData.setTitle(customTitle);
        validationMap.put(keyView, validationData);
        return validationData;
    }*/


    public ValidationData setValidationInMap(JSONObject object, boolean isEmail, View keyView, String customTitle) {
        ValidationData validationData = null;
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (object.getBoolean(Constants.ISREQUIRED) && object.has("auto") && object.getBoolean("auto")) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    if (object.has(Constants.VALUE)) {
                        validationData.setValidate(true);
                    }
                } else if (object.getBoolean(Constants.ISREQUIRED)) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    if (object.has(Constants.VALUE)) {
                        if (object.getString(Constants.TYPE).equalsIgnoreCase(Constants.IMAGE_UPLOAD) || object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SIGNATUREPAD) || object.getString(Constants.TYPE).equalsIgnoreCase(Constants.SKETCH)) {
                            JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                            validationData.setValidate(jsonArray.length() > 0);
                        } else {
                            validationData.setValidate(!TextUtils.isEmpty(object.getString(Constants.VALUE)) && !object.getString(Constants.VALUE).equals("[]"));
                        }
                    }else if(object.has(Constants.COMPLETED) ){
                        //Check if service as a task is attempted. if it is completed then validate it.
                        validationData.setValidate(object.getBoolean(Constants.COMPLETED));
                    }
                    validationData.setEmail(isEmail);
                    if (TextUtils.isEmpty(customTitle) && object.has(Constants.TITLE)) {
                        validationData.setTitle(object.getString(Constants.TITLE));
                    } else {
                        validationData.setTitle(customTitle);
                    }
                } else if (isEmail) {
                    validationData = new ValidationData();
                    validationData.setValidate(true);
                    validationData.setPageNumber(pageNumber);
                    validationData.setEmail(true);
                    validationData.setTitle(object.getString(Constants.TITLE));
                }
                validationMap.put(keyView, validationData);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return validationData;
    }

    public void setValidationForServiceInMap(JSONObject object, View view, TMService tMService) {
        setValidationForServiceInMap(object, false, view, tMService);
    }

    public ValidationData setValidationForServiceInMap(JSONObject object, boolean setFalse, View keyView, TMService tMService) {
        ValidationData validationData = null;
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (setFalse) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(true);
                    validationData.setTitle(tMService.getServiceName());
                } else if (object.getBoolean(Constants.ISREQUIRED)) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(false);
                    validationData.setTitle(tMService.getServiceName());
                }
                validationMap.put(keyView, validationData);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return validationData;
    }

    public ValidationData setValidationForMaterial(JSONObject object, boolean setFalse, View keyView) {
        ValidationData validationData = null;
        if (object.has(Constants.ISREQUIRED)) {
            try {
                if (setFalse) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(true);
                    validationData.setTitle(getString(R.string.material));
                } else if (object.getBoolean(Constants.ISREQUIRED)) {
                    validationData = new ValidationData();
                    validationData.setPageNumber(pageNumber);
                    validationData.setValidate(false);
                    validationData.setTitle(getString(R.string.material));
                }
                validationMap.put(keyView, validationData);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return validationData;
    }

    /**
     * readJsonFile method is work as json reader
     */
    protected void readJsonFile(ViewFlipper vfPages) {
        try {
            JSONObject jsonImageData;

            validationMap = new HashMap<>();
            if (TextUtils.isEmpty(formData.getImageData())) {
                jsonImageData = new JSONObject();
            } else {
                jsonImageData = new JSONObject(formData.getImageData());
                //If to manage Previous app version(<=2.0.3)
                if (jsonImageData.getJSONArray(Constants.DATA).length() > 0 && formData.getImageCount() == 0) {
                    if (isTMForm()) {
                        TblTMForms tblTMForms = new TblTMForms(this);
                        tblTMForms.updateImageCountByPkId(formData.getFormPKId(), jsonImageData.getJSONArray(Constants.DATA).length());
                    } else {
                        TblForms tblForms = new TblForms(this);
                        tblForms.updateImageCountByPkId(formData.getFormPKId(), jsonImageData.getJSONArray(Constants.DATA).length());

                    }
                    imageId = jsonImageData.getJSONArray(Constants.DATA).length() + 1;
                } else {
                    //From the app version 2.0.4
                    imageId = formData.getImageCount() + 1;
                }
            }
            if (!jsonImageData.has(Constants.FORMID))
                jsonImageData.put(Constants.FORMID, mFormPkId);
            if (!jsonImageData.has(Constants.DATA)) {
                String[] data = {};
                jsonImageData.put(Constants.DATA, new JSONArray(data));
            }
            if (isTMForm()) {
                TblTMForms tblTMForms = new TblTMForms(context);
                tblTMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            } else {
                TblForms tblForms = new TblForms(context);
                tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            }
            if (!TextUtils.isEmpty(formString)) {
                updatedJsonObject = new JSONObject(formString);

                JSONArray jsonArray = updatedJsonObject.getJSONArray("pages");
                totalPages = jsonArray.length();
                for (int i = 0; i < jsonArray.length(); i++) {
                    try {
                        pageNumber = i + 1;
                        View page = getLayoutInflater().inflate(R.layout.layout_page, vfPages, false);
                        LinearLayout llMain = page.findViewById(R.id.llMain);
                        JSONObject object = jsonArray.getJSONObject(i);
                        if (object.has(Constants.ELEMENTS)) {
                            try {
                                JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                                jsonArrayRead(array, llMain, i + 1, -1, "");
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }

                        if (isTMForm() && pageNumber == 1) {
                            enableOrDisableView(false);
                        }
                        vfPages.addView(page);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
                pageNumber = 1;
            }
        } catch (JSONException | NullPointerException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    protected void setBottomBtnText(AppCompatButton btnNext, AppCompatButton
            btnPrevious, AppCompatImageButton imgBtnBack) {
        if (totalPages == currentPage) {
            btnNext.setText(getString(R.string.submit));
        } else {
            btnNext.setText(getString(R.string.next));
        }

        if (currentPage == 1) {
            btnPrevious.setVisibility(View.GONE);
            imgBtnBack.setColorFilter(ContextCompat.getColor(context, R.color.white), android.graphics.PorterDuff.Mode.MULTIPLY);
        } else {
            btnPrevious.setVisibility(View.VISIBLE);
            imgBtnBack.setColorFilter(ContextCompat.getColor(context, R.color.gray), android.graphics.PorterDuff.Mode.MULTIPLY);
        }
    }


    /**
     * jsonArrayRead method is use for read json array and get single object
     * this method is call when there is child view is there.
     *
     * @param jsonArray is json array is use for get single from array
     */
    public boolean jsonArrayRead(JSONArray jsonArray) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (objectReadAndCheckCrewComponent(object)) {
                    return true;
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        return false;
    }

    /**
     * jsonArrayRead method is use for read json array and get single object
     * this method is call when there is child view is there.
     *
     * @param jsonArray    is json array is use for get single from array
     * @param parentLayout is view group and work as parent layout
     * @param pageNumber   int current Page number
     */
    private void jsonArrayRead(JSONArray jsonArray, ViewGroup parentLayout, int pageNumber, int parentPanelTag, boolean shouldAddToImageList, String parentValue) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                objectRead(object, parentLayout, jsonArray, i, pageNumber, parentPanelTag, shouldAddToImageList, parentValue);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    /**
     * jsonArrayRead method is use for read json array and get single object
     * this method is call when there is child view is there.
     *
     * @param jsonArray    is json array is use for get single from array
     * @param parentLayout is view group and work as parent layout
     * @param pageNumber   int current Page number
     */
    private void jsonArrayRead(JSONArray jsonArray, ViewGroup parentLayout, int pageNumber, int parentPanelTag, String parentValue) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                objectRead(object, parentLayout, jsonArray, i, pageNumber, parentPanelTag, true, parentValue);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    /**
     * objectRead is use for read single object from object array
     *
     * @param object is json object use for get data from object
     */
    boolean objectReadAndCheckCrewComponent(JSONObject object) {
        try {
            String type = (String) object.get(Constants.TYPE);
            return type.equalsIgnoreCase(Constants.CREW);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return false;
    }


    /**
     * objectRead is use for read single object from object array
     *
     * @param object       is json object use for get data from object
     * @param parentLayout is view group and work as parent layout
     * @param pageNumber   int current page
     */
    private void objectRead(JSONObject object, ViewGroup parentLayout, JSONArray
            jsonArray, int objectPosition, int pageNumber, int parentPanelTag, boolean shouldAdd, String parentValue) {
        try {
            String type = (String) object.get(Constants.TYPE);

            switch (type) {
                case Constants.CHECKBOX:
                    inputTypeCheckBox(object, parentLayout, parentPanelTag, shouldAdd, parentValue);
                    break;
                case Constants.DROPDOWNMULTIPLE:
                    inputTypeDropdownMultiple(object, parentLayout);
                    break;
                case Constants.IMAGE_UPLOAD:
                    inputTypeImageUpload(object, parentLayout, shouldAdd);
                    break;
                case Constants.TEXT:
                    inputTypeCheck(object, parentLayout);
                    break;
                case Constants.INPUTTYPE_URL:
                    inputTypeUrl(object, parentLayout);
                    break;
                case Constants.SPINNER:
                    inputTypeSpinner(object, parentLayout, shouldAdd, parentValue);
                    break;
                case DYNAMIC_DROPDOWN:
                    inputTypeDynamicDropdown(object, parentLayout);
                    break;
                case Constants.SKETCH:
                    inputTypeSketch(object, parentLayout);
                    break;
                case Constants.SUB_HEADER:
                    inputTypeSubHeader(object, parentLayout);
                    break;
                case Constants.SIGNATUREPAD:
                    inputTypeSignaturePad(object, parentLayout);
                    break;
                case Constants.SEGMENT_INPUT:
                case Constants.RADIOGROUP:
                    inputTypeRadioGroup(object, parentLayout, shouldAdd, parentValue);
                    break;
                case Constants.PANEL:
                    addPanelView(object, parentLayout, -1, jsonArray, objectPosition, pageNumber, parentPanelTag);
                    break;
                case Constants.COMMENT:
                    if (object.has(Constants.ISWEATHERINPUT) && object.getBoolean(Constants.ISWEATHERINPUT)) {
                        inputTypeCommentWeather(object, parentLayout, parentPanelTag);
                    } else {
                        inputTypeComment(object, parentLayout);
                    }
                    break;
                case Constants.GEO:
                    inputTypeGeo(object, parentLayout, pageNumber);
                    break;

                case Constants.TEXTDISPLAY:
                    inputTypeTextDisplay(object, parentLayout);
                    break;
                case Constants.CHECKINOUT:
                    inputTypeCheckInOut(object, parentLayout);
                    break;
                case Constants.CREW:
                    inputTypeCrew(object, parentLayout, pageNumber);
                    break;

                case Constants.MANAGE_CREW:
                    inputTypeManagedCrew(object, parentLayout, pageNumber);
                    break;
                case Constants.SERVICE:
                    inputTypeService(object, parentLayout);
                    break;
                case Constants.MATERIAL:
                    inputTypeMaterial(object, parentLayout);
                    break;
                case Constants.ADDRESS:
                    inputTypeAddress(object, parentLayout, pageNumber);
                    break;
                case Constants.SITEINFO:
                    inputTypeSiteInfo(object, parentLayout);
                    break;
                case Constants.ISSUES:
                    inputTypeIssue(object, parentLayout, shouldAdd);
                    break;


            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void inputTypeIssue(JSONObject object, ViewGroup adsLayout, boolean shouldAdd) {
        View commentView = getLayoutInflater().inflate(R.layout.layout_issue_main, adsLayout, false);
        commentView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        LinearLayout llParent = commentView.findViewById(R.id.llParent);
        AppCompatImageView ivNewIssue = commentView.findViewById(R.id.ivNewIssue);
        TextView tvIssueTitle = commentView.findViewById(R.id.tvIssueTitle);
        try {
            tvIssueTitle.setText(object.getString(Constants.TITLE));
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
            tvIssueTitle.setText(getString(R.string.new_issue));
        }
        try {
            if (isTMForm()) {
                object.put(Constants.SITEID, String.valueOf(siteData.getSiteId()));
                object.put(Constants.SF_ID, String.valueOf(formData.getFormId()));
            } else {
                object.put(Constants.SITEID, "-2");
                object.put(Constants.SF_ID, String.valueOf(formData.getFormId()));
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            if (object.has(Constants.ISSUE_VALUE)) {
                JSONArray jsonArray = object.getJSONArray(Constants.ISSUE_VALUE);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject object1 = jsonArray.getJSONObject(i);
                    addChildIssueView(llParent, object1, object, i, shouldAdd);
                }
            } else {
                String[] value = {};
                object.put(Constants.ISSUE_VALUE, new JSONArray(value));
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        ivNewIssue.setOnClickListener(v -> {
            clearFocus();
            try {
                JSONArray jsonArray = object.getJSONArray(Constants.ISSUE_VALUE);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put(Constants.TITLE, "");
                if (isTMForm())
                    jsonObject.put(Constants.SITEID, String.valueOf(siteData.getSiteId()));
                else
                    jsonObject.put(Constants.SITEID, "-2");
                jsonObject.put(Constants.SF_ID, String.valueOf(formData.getFormId()));
                jsonObject.put(Constants.TYPE, object.getString(Constants.TYPE));
                jsonObject.put(Constants.NAME, object.getString(Constants.NAME));
                jsonArray.put(jsonObject);
                addChildIssueView(llParent, jsonObject, object, jsonArray.length() - 1, true);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        });
        adsLayout.addView(commentView);
        addViewInList(commentView);
    }

    private void addChildIssueView(ViewGroup llParent, JSONObject object, JSONObject parent, int position, boolean shouldAdd) {
        View issuePhotoView = getLayoutInflater().inflate(R.layout.layout_issue_photo_view, llParent, false);
        RecyclerView rlImages = issuePhotoView.findViewById(R.id.rlImages);
        AppCompatTextView tvSubmit = issuePhotoView.findViewById(R.id.tvSubmit);
        LinearLayout llUploadPhoto = issuePhotoView.findViewById(R.id.llUploadPhoto);
        CustomEditText edtText = issuePhotoView.findViewById(R.id.edtText);
        ImageView ivAddPhoto = issuePhotoView.findViewById(R.id.ivAddPhoto);
        llUploadPhoto.setTag(tag);
        LinearLayout llButtonView = issuePhotoView.findViewById(R.id.llButtonView);
        ImageViewAdapter imageViewAdapter;
        setEditTextListeners(edtText, false);
        if (shouldAdd)
            mapImageUpload.put(tag, object);

        ArrayList<ImageData> lstImages = new ArrayList<>();
        addTagInMainObjectAndIncreaseTagId(object);
        boolean isSubmitted = false;
        try {
            if (object.has(Constants.ISSUES_ISSUBMITTED)) {
                isSubmitted = object.getBoolean(Constants.ISSUES_ISSUBMITTED);
                if (isSubmitted) {
                    llButtonView.setVisibility(View.GONE);
                    edtText.setEnabled(false);
                    edtText.setFocusable(false);
                    ivAddPhoto.setVisibility(View.GONE);
                }
            } else {
                object.put(Constants.ISSUES_ISSUBMITTED, false);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            if (object.has(Constants.ISSUES_TEXE)) {
                edtText.setText(object.getString(Constants.ISSUES_TEXE));
                object.put(Constants.TITLE, object.getString(Constants.ISSUES_TEXE));
            } else {
                object.put(Constants.ISSUES_TEXE, "");
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setFocusChangeListenerForEditText(edtText);
        edtText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    if (edtText.getText() != null && edtText.getText().length() > 0 && TextUtils.isEmpty(edtText.getText().toString().trim())) {
                        edtText.setText("");
                        return;
                    }
                    object.put(Constants.ISSUES_TEXE, s.toString().trim());
                    object.put(Constants.TITLE, s.toString().trim());
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });
        try {
            if (object.has(Constants.VALUE)) {
                JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject object1 = jsonArray.getJSONObject(i);
                    ImageData imageData = new ImageData();
                    imageData.setImageId(object1.getInt(Constants.IMAGEID));
                    if (!TextUtils.isEmpty(object1.getString(IMAGEPATHHIGH)))
                        imageData.setImagePath(object1.getString(IMAGEPATHHIGH));
                    else
                        imageData.setImagePath(object1.getString(IMAGEPATHLOW));
                    lstImages.add(imageData);
                }
            } else {
                String[] value = {};
                object.put(Constants.VALUE, new JSONArray(value));
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        initializeRecyclerView(rlImages, true);

        imageViewAdapter = new ImageViewAdapter(this, lstImages, isSubmitted) {
            @Override
            public void deleteItem(int position, int imageId) {
                PopUtils.showDialogForDeleteView(BaseFormDetailActivity.this, getString(R.string.msg_delete_image), view -> {
                    ImageViewAdapter imageViewAdapter1 = (ImageViewAdapter) rlImages.getAdapter();
                    if (imageViewAdapter1 != null) {
                        imageViewAdapter1.removeItem(position);
                        deleteDataFromImageJson(imageId, object);
                    }
                });
            }

            @Override
            public void itemClick(int position, ImageData imageData) {
                PopUtils.showDialogImageView(context, imageData.getImagePath());
            }
        };
        tvSubmit.setOnClickListener(v -> {
            submitIssueButtonClicked(object, edtText, imageViewAdapter, llButtonView, ivAddPhoto, llUploadPhoto);
        });
        rlImages.setAdapter(imageViewAdapter);
        ivAddPhoto.setOnClickListener(view -> {
            openCameraToGetImage((int) llUploadPhoto.getTag(), true, false);
        });
        AppCompatTextView tvRemove = issuePhotoView.findViewById(R.id.tvRemove);
        tvRemove.setOnClickListener(v -> {
            removeIssueComponent(parent, object, llParent, imageViewAdapter, issuePhotoView);
        });
        llParent.addView(issuePhotoView);
    }

    public void submitIssueButtonClicked(JSONObject object, CustomEditText
            edtText, ImageViewAdapter imageViewAdapter, LinearLayout
                                                 llButtonView, ImageView ivAddPhoto, LinearLayout llUploadPhoto) {
        if (edtText.getText() == null)
            return;
        if (TextUtils.isEmpty(edtText.getText().toString().trim())) {
            PopUtils.showAlertDialogPositiveButtonOnly(this, getString(R.string.app_name), getString(R.string.please_enter_issue_detail));
            return;
        }
        StaticUtils.submitIssueData(context, isTMForm(), siteData, formData, edtText.getText().toString().trim(), object, (Integer) llUploadPhoto.getTag());
        imageViewAdapter.setSubmitted(true);
        llButtonView.setVisibility(View.GONE);
        imageViewAdapter.notifyDataSetChanged();
        ivAddPhoto.setVisibility(View.GONE);
        edtText.setEnabled(false);
        edtText.setFocusable(false);
        try {
            object.put(Constants.ISSUES_ISSUBMITTED, true);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void removeIssueComponent(JSONObject parent, JSONObject
            object, ViewGroup llParent, ImageViewAdapter imageViewAdapter, View
                                             issuePhotoView) {
        if (!imageViewAdapter.getImageList().isEmpty()) {
            PopUtils.showDialogForRemoveIssue(this, v1 -> {
                removeIssueComponentFromJson(parent, object, llParent, issuePhotoView);
            });
        } else {
            removeIssueComponentFromJson(parent, object, llParent, issuePhotoView);
        }
    }

    private void removeIssueComponentFromJson(JSONObject parent, JSONObject
            object, ViewGroup llParent, View issuePhotoView) {
        llParent.removeView(issuePhotoView);
        try {
            JSONArray jsonArray = parent.getJSONArray(Constants.ISSUE_VALUE);
            int indexOfObject = -1;
            for (int i = 0; i < jsonArray.length(); i++) {
                if (jsonArray.get(i).equals(object)) {
                    indexOfObject = i;
                    break;
                }
            }
            if (indexOfObject == -1)
                return;
            jsonArray.remove(indexOfObject);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }


    }

    private void inputTypeCrew(JSONObject object, ViewGroup adsLayout, int pageNumber) {
        View crewView = getLayoutInflater().inflate(R.layout.layout_crew, adsLayout, false);
        crewView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        TextView tvMultiSelected = crewView.findViewById(R.id.tvMultiSelected);
        TextView tvMultiSelectTitle = crewView.findViewById(R.id.tvMultiSelectTitle);
        setTitleOfComponent(tvMultiSelectTitle, object);
        setValidationInMap(object, tvMultiSelected);

        LinearLayout llMultiSelect = crewView.findViewById(R.id.llMultiSelect);

        llMultiSelect.setOnClickListener(view -> {
            clearFocus();
            llMultiSelect.setEnabled(false);
            AtomicBoolean enableSelection = new AtomicBoolean(llMultiSelect.isEnabled());
            TblEmployees tblEmployees = new TblEmployees(this);
           dbExecutorService.execute(() -> {
               List<Employees> lstEmployee = new ArrayList<>();
               List<Employees> lstSelectedEmployee = new ArrayList<>();
               if ((view.getTag() != null) && !(view.getTag() instanceof Integer) && !(view.getTag() instanceof String)) {
                   try {
                       lstSelectedEmployee = (List<Employees>) view.getTag();
                       if (isTMForm() && formData.getCheckin_time() > 0 && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
                           lstEmployee = getNotCheckedInEmployees();
                       } else {
                           List<Integer> lstSelectedCrews = new ArrayList<>();
                           for (Employees employees : lstSelectedEmployee) {
                               lstSelectedCrews.add(employees.getEmployeeID());
                           }
                           lstEmployee = tblEmployees.getAllDataWithoutSelected(lstSelectedCrews);
                       }
                   } catch (Exception ce) {

                       FirebaseEventUtils.logException(ce);
                   }
               } else {
                   try {
                       if (object.has(Constants.VALUE) && object.getJSONArray(Constants.VALUE).length() > 0) {
                           List<Integer> lstIds = new ArrayList<>();

                           if (isTMForm() && formData.getCheckin_time() > 0) {
                               lstIds = formData.getCrewIds();
                           } else {
                               for (int i = 0; i < object.getJSONArray(Constants.VALUE).length(); i++) {
                                   lstIds.add(object.getJSONArray(Constants.VALUE).getInt(i));
                               }
                           }
                           lstSelectedEmployee = tblEmployees.getAllDataByIds(lstIds);
                           if (isTMForm() && formData.getIsCheckInOut() && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
                               lstEmployee = getNotCheckedInEmployees();
                           } else {
                               lstEmployee = tblEmployees.getAllDataWithoutSelected(lstIds);
                           }
                       } else {
                           if (isTMForm() && formData.getIsCheckInOut() && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
                               lstEmployee = getNotAssignedCrews();
                           } else {
                               lstEmployee = tblEmployees.getCrewData();
                           }
                           if (isTMForm() && formData.getIsCheckInOut() && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 0) {
                               lstSelectedEmployee = tblEmployees.getSelectedCrewData();
                               List<Integer> lstSelectedCrews = new ArrayList<>();
                               for (Employees employees : lstSelectedEmployee) {
                                   lstSelectedCrews.add(employees.getEmployeeID());
                               }
                               lstEmployee = tblEmployees.getAllDataWithoutSelected(lstSelectedCrews);
                           }
                           enableSelection.set(true);

                       }
                   } catch (JSONException e) {
                       FirebaseEventUtils.logException(e);
                       enableSelection.set(true);
                   }
               }
               List<Employees> finalLstSelectedEmployee = lstSelectedEmployee;
               List<Employees> finalLstEmployee = lstEmployee;
               new Handler(Looper.getMainLooper()).post(() -> {
                    if (isFinishing())
                        return;
                   llMultiSelect.setEnabled(enableSelection.get());
                    if (StaticUtils.getEmployeeIdInInt() > 0 && isTMForm() && formData.getIsCheckInOut() && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
                        if ((view.getTag() != null) && (view.getTag() instanceof String) && isPreSelectServiceTag((String) view.getTag())) {
                            showCrewAndPreSelectServiceViewForCheckInForm(object, finalLstSelectedEmployee, finalLstEmployee, llMultiSelect, crewView, (String) view.getTag());
                        } else {
                            showCheckInCrewMemberView(object, finalLstSelectedEmployee, finalLstEmployee, llMultiSelect, crewView);
                        }
                    } else {
                        if (StaticUtils.getEmployeeIdInInt() > 0) {
                            if ((view.getTag() != null) && (view.getTag() instanceof String) && isPreSelectServiceTag((String) view.getTag())) {
                                showCrewAndPreSelectServiceViewForCheckInForm(object, finalLstSelectedEmployee, finalLstEmployee, llMultiSelect, crewView, (String) view.getTag());
                            } else {
                                showCrewMemberView(object, finalLstSelectedEmployee, finalLstEmployee, llMultiSelect, crewView, TextUtils.isEmpty(tvMultiSelected.getText()));
                            }
                        } else {
                            if ((view.getTag() != null) && (view.getTag() instanceof String) && isPreSelectServiceTag((String) view.getTag())) {
                                showCrewAndPreSelectServiceViewForCheckInForm(object, finalLstSelectedEmployee, finalLstEmployee, llMultiSelect, crewView, (String) view.getTag());
                            }
                        }
                    }
                    try {
                        if ((view.getTag() != null) && (view.getTag() instanceof Integer)) {
                            view.setTag(view.getTag());
                        } else if ((view.getTag() != null) && (view.getTag() instanceof String)) {
                            view.setTag(view.getTag());
                        } else {
                            view.setTag(null);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                        view.setTag(null);
                    }
                });
            });
        });

        if (object.has(Constants.VALUE_NAME)) {
            try {
                if (isTMForm() && formData.getCheckin_time() > 0) {
                    List<Integer> lstIds = formData.getCrewIds();
                    TblEmployees tblEmployees = new TblEmployees(this);
                    List<Employees> lstSelectedEmployee = tblEmployees.getAllDataByIds(lstIds);
                    String selectedItem = StaticUtils.getEmployeesNames(lstSelectedEmployee);
                    tvMultiSelected.setText(selectedItem);
                    updateValidationInMap(object, tvMultiSelected, tvMultiSelected.getText().toString());
                    try {
                        JSONArray dataIdArray = new JSONArray();
                        for (Employees employees : lstSelectedEmployee) {
                            dataIdArray.put(employees.getEmployeeID());
                        }
                        object.put(Constants.VALUE, dataIdArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                } else {
                    tvMultiSelected.setText(object.getString(Constants.VALUE_NAME));
                    updateValidationInMap(object, tvMultiSelected, tvMultiSelected.getText().toString());
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        } else {
            FormComponentData crewComponentData = new FormComponentData();
            crewComponentData.pageNumber = pageNumber;
            crewComponentData.object = object;
            crewComponentData.tagId = (Integer) crewView.getTag();
            crewComponentData.rootView = adsLayout;
            lstCrewComponent.put(pageNumber, crewComponentData);
            try {
                object.put(Constants.VALUE_NAME, "");
                object.put(Constants.VALUE, new JSONArray());
            } catch (JSONException e) {

                FirebaseEventUtils.logException(e);
            }
            if (pageNumber == 1) {
                showCrewSelectionView(crewView);
            }
        }
        if (formData.getIsCheckInOut()) {
            hasCrewInCheckInForm = true;
        }
        try {
            object.put(Constants.SF_ID, formData.getFormId());
            if (isTMForm())
                object.put(Constants.SITEID, String.valueOf(siteData.getSiteId()));
            else
                object.put(Constants.SITEID, "-2");
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        adsLayout.addView(crewView);
        addViewInList(crewView);

        if (StaticUtils.getEmployeeIdInInt() == 0) {
            crewView.setVisibility(View.GONE);
            tvMultiSelected.setText("  ");
            updateValidationInMap(object, tvMultiSelected, tvMultiSelected.getText().toString());
        }

    }


    private void inputTypeManagedCrew(JSONObject object, ViewGroup adsLayout, int pageNumber) {
        View managedCrewView = getLayoutInflater().inflate(R.layout.layout_managed_crew, adsLayout, false);
        managedCrewView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        TextView tvTitle = managedCrewView.findViewById(R.id.tvTitle);
        AppCompatImageView ivAddSubForm = managedCrewView.findViewById(R.id.ivAddSubForm);
        LinearLayout llAddSubForm = managedCrewView.findViewById(R.id.llAddSubForm);
        setTitleOfComponent(tvTitle, object);
        setValidationInMap(object, tvTitle);
        RecyclerView rvSubForm = managedCrewView.findViewById(R.id.rvSubForm);
        initializeRecyclerView(rvSubForm, false);
        SubFormAdapter formAdapter = getSubFormAdapter();
        rvSubForm.setAdapter(formAdapter);
        llAddSubForm.setOnClickListener(view -> ivAddSubForm.performClick());
        ivAddSubForm.setOnClickListener(view -> {
            if (hasManagedDropDownValueInSubForm(object)) {
                createNewSubFormAndNavigateUserToSubForm(object);
            } else {
                showForeGroundToast(getString(R.string.form_does_not_have_manage_crew_field));
            }
        });
        adsLayout.addView(managedCrewView);
        addViewInList(managedCrewView);
    }

    private boolean hasManagedDropDownValueInSubForm(JSONObject object) {
        boolean isManageCrewField = false;
        try {
            if (object.has("elements") && object.getJSONArray("elements").length() > 0) {
                JSONArray elementSetArray = object.getJSONArray("elements");
                for (int i = 0; i < elementSetArray.length(); i++) {
                    JSONObject subFormObjectElement = elementSetArray.getJSONObject(i);
                    if (subFormObjectElement.getString(TYPE).equalsIgnoreCase(DYNAMIC_DROPDOWN)
                            && subFormObjectElement.has(Constants.IS_MANAGE_CREW_FIELD)
                            && subFormObjectElement.getBoolean(Constants.IS_MANAGE_CREW_FIELD)) {
                        isManageCrewField = true;
                    }

                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
            return isManageCrewField;
        }
        return isManageCrewField;
    }

    @NonNull
    private SubFormAdapter getSubFormAdapter() {
        List<FormData> lstForm = getSubFormData();
        return new SubFormAdapter(lstForm, new SubFormAdapter.ViewClicked() {
            @Override
            public void onItemClicked(int position, View view, FormData dataItem) {
                navigateUserToSubFormScreen(position, dataItem);
            }

            @Override
            public void onItemLongClicked(int position, View view) {
            }
        });
    }

    public List<FormData> getSubFormData() {
        List<FormData> lstForm;
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(this);
            lstForm = tblTMForms.getAllSubFormsByFormPKId(formData.getFormPKId());
        } else {
            TblForms tblForms = new TblForms(this);
            lstForm = tblForms.getAllSubFormsByFormPKId(formData.getFormPKId());
        }
        return lstForm;
    }

    private void navigateUserToSubFormScreen(int position, FormData dataItem) {
        navigateUserToSubFormScreen(dataItem.getFormPKId());
    }

    private void createNewSubFormAndNavigateUserToSubForm(JSONObject object) {
        //Create new sub form and add in db
        String subFormDataObject = createFromDataJsonForNewSubForm(object);
        deleteBlankSubForm();
        createSubFormInFormTable(subFormDataObject);
        int subFormPkId = getSubFormPkId();

        navigateUserToSubFormScreen(subFormPkId);
    }

    private void navigateUserToSubFormScreen(int subFormPkId) {
        saveModifiedForm();
        lastOpenedPage = currentPage;
        Intent intent = new Intent(this, SubFormDetailActivity.class);
        intent.putExtra("formPkId", subFormPkId);
        intent.putExtra("siteData", siteData);
        intent.putExtra("mainFromPKId", formData.getFormPKId());
        intent.putExtra("isTMForm", isTMForm());
        startActivityForResult(intent, Constants.SUB_FORM_CREATE_REQUEST_CODE);
        overridePendingTransition(R.anim.enter_from_right, R.anim.exit_to_left);
    }


    private int getSubFormPkId() {
        if (isTMForm()) {
            return new TblTMForms(this).getPkIdOfNotCheckedInSubForm(TblTMForms.TABLE_NAME, formData.getFormPKId());
        } else {
            return new TblForms(this).getPkIdOfNotCheckedInSubForm(TblForms.TABLE_NAME, formData.getFormPKId());
        }
    }

    private void deleteBlankSubForm() {
        if (getSubFormPkId() <= 0)
            return;
        if (isTMForm()) {
            new TblTMForms(this).deleteBlankSubFormDataByPKId(TblTMForms.TABLE_NAME, formData.getMainFormPkId());
        } else {
            new TblForms(this).deleteBlankSubFormDataByPKId(TblForms.TABLE_NAME, formData.getMainFormPkId());
        }
    }

    private void createSubFormInFormTable(String subFormDataObject) {
        FormData subFormData = new FormData();
        subFormData.setFormId(formData.getFormId());
        subFormData.setSiteId(formData.getSiteId());
        subFormData.setFormName(formData.getFormName());
        subFormData.setFormData(subFormDataObject);
        subFormData.setModifiedFormData(subFormDataObject);
        subFormData.setIsCheckInOut(true);
        subFormData.setCheckInOutComplete(formData.isCheckInOutComplete());
        subFormData.setPlotOnMap(formData.isPlotOnMap());
        subFormData.setSubForm(true);
        subFormData.setMainFormPkId(formData.getFormPKId());
        subFormData.setPreSelectServices(formData.isPreSelectServices());
        subFormData.setFormCreated(System.currentTimeMillis());
        subFormData.setFormUpdated(System.currentTimeMillis());
        if (isTMForm()) {
            TblTMForms tblTMForms = new TblTMForms(this);
            tblTMForms.insertSingleFormData(subFormData);
        } else {
            TblForms tblForms = new TblForms(this);
            tblForms.insertSingleFormData(subFormData);
        }
    }

    private String createFromDataJsonForNewSubForm(JSONObject object) {
        try {
            JSONObject mainObject = new JSONObject();
            JSONArray pageArray = new JSONArray();
            JSONObject pageObject = new JSONObject();
            JSONArray elementArray = new JSONArray();
            if (object.has("elements") && object.getJSONArray("elements").length() > 0) {
                JSONArray elementSetArray = object.getJSONArray("elements");
                for (int i = 0; i < elementSetArray.length(); i++) {
                    elementArray.put(elementSetArray.get(i));
                }
            }
            pageObject.put("title", "Page");
            pageObject.put(FORM_SUBMITTED_BY, formData.getFormSubmissionId());
            pageObject.put("elements", elementArray);
            pageArray.put(pageObject);
            mainObject.put("pages", pageArray);
            return mainObject.toString();
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
            return null;
        }
    }

    private void initializeRecyclerView(RecyclerView recyclerView, boolean horizontal) {
        recyclerView.setHasFixedSize(false);
        LinearLayoutManager listLinearLayoutManager;
        if (horizontal) {
            listLinearLayoutManager = new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false);
        } else {
            listLinearLayoutManager = new LinearLayoutManager(this);
        }
        recyclerView.setLayoutManager(listLinearLayoutManager);
        if (!horizontal) {
            DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(recyclerView.getContext(), 1);
            recyclerView.addItemDecoration(dividerItemDecoration);
        }
    }

    private boolean isPreSelectServiceTag(String tag) {
        return tag.contains("Service");
    }

    private int getCrewTagFromAppendedData(String tag) {

        if (TextUtils.isEmpty(tag))
            return -1;

        //Sample 0@@Service
        String splitString = tag.split("@@")[0];

        if (TextUtils.isEmpty(splitString))
            return -1;
        try {
            int crewTag = Integer.parseInt(splitString);
            return crewTag;
        } catch (NumberFormatException e) {
            e.printStackTrace();
            return -1;
        }
    }

    /**
     * Method to show pre selected service true component along with crew selection view in bottomsheet dialog.
     */
    private void showCrewAndPreSelectServiceViewForCheckInForm(JSONObject object, List<Employees> lstSelectedEmployee, List<Employees> lstEmployee, LinearLayout llMultiSelect, View crewView, String viewTag) {
        PopUtils.showCrewAndPreSelectServiceViewForCheckIn(this, isTMForm(), siteData, formData, preSelectServiceArray, lstSelectedEmployee, lstEmployee, llMultiSelect, new OnCrewAndServiceActionTapped() {
                    @Override
                    public void onSelected(List<CrewSelectionData> lstResult, List<CrewSelectionData> lstSelectedEmployee, List<Integer> lstDeselectedEmployee, List<Integer> lstNewSelected, JSONArray arrayModifiedServices) {
                        llMultiSelect.setEnabled(true);

                        List<Employees> lstEmpData = getEmployeeDataFromCrewList(lstResult);
                        if (shouldContinueIfFormIsCheckedIn(lstEmpData, llMultiSelect, object, lstDeselectedEmployee, lstNewSelected, false)) {
                            updateCheckInCrewSelectionData(lstEmpData);
                            setCrewInViewAndSendBreadcrumb(object, lstEmpData, lstDeselectedEmployee, lstNewSelected, crewView);
                        }
                        updatePreSelectServiceInMainJson(arrayModifiedServices);
                        EventBus.getDefault().post(new PreSelectedServiceEvent(object, lstEmpData, lstDeselectedEmployee, lstNewSelected, llMultiSelect));
                    }

                    @Override
                    public void onCancel() {
                        llMultiSelect.setEnabled(!isTMForm() || formData.getCheckin_time() != 0 || !formData.getIsCheckInOut());
                    }
                }
        );

        int crewTag = getCrewTagFromAppendedData(viewTag);
        crewView.setTag(crewTag);

    }

    /**
     * Method to show only pre selected service true component in bottomsheet dialog.
     */
    protected void showPreSelectServiceViewForCheckIn() {
        PopUtils.showPreSelectServiceView(this, isTMForm(), siteData, formData, preSelectServiceArray, new OnPreSelectServiceActionTapped() {
            @Override
            public void onSelected(JSONArray updatedServiceArray) {
                updatePreSelectServiceInMainJson(updatedServiceArray);
                EventBus.getDefault().post(new PreSelectedServiceEvent());
            }

            @Override
            public void onCancel() {
            }
        });
    }

    private List<Employees> getNotCheckedInEmployees() {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        List<List<Integer>> crewData = tblCheckInMap.getCrewsOfForm(formData.getSiteId(), formData.getFormId());
        List<Integer> lstCrewIds = new ArrayList<>();
        for (List<Integer> lstData : crewData) {
            lstCrewIds.addAll(lstData);
        }
        List<Employees> lstEmployee = getNotAssignedCrews();
        for (Iterator<Employees> it = lstEmployee.iterator(); it.hasNext(); ) {
            Employees employees = it.next();
            if (lstCrewIds.contains(employees.getEmployeeID())) {
                it.remove();
            }
        }
        return lstEmployee;
    }

    public List<Employees> getNotAssignedCrews() {
        List<Employees> lstEmployee;
        TblClockCrew tblClockCrew = new TblClockCrew(this);
        List<Integer> lstResumedIds = tblClockCrew.getAllResumedWorkCrewId();

        TblCheckInMap tblCheckInMap = new TblCheckInMap(this);
        List<Integer> lstOccupiedCrew = tblCheckInMap.getAllCrewIdsInList();
        TblEmployees tblEmployees = new TblEmployees(this);

        if (lstOccupiedCrew.isEmpty()) {
            lstEmployee = tblEmployees.getAllDataByIds(lstResumedIds);
        } else {
            for (Integer id : lstOccupiedCrew) {
                lstResumedIds.remove(id);
            }
            lstEmployee = tblEmployees.getAllDataByIds(lstResumedIds);
        }
        return lstEmployee;
    }

    private void showCrewMemberView(JSONObject
                                            object, List<Employees> lstPreSelectedItems, List<Employees> lstItems, LinearLayout
                                            viewClick, View commentView, boolean isEmptyView) {
        PopUtils.showCrewSelectionView(BaseFormDetailActivity.this, formData.getFormName(), viewClick, lstPreSelectedItems, lstItems, isEmptyView, new OnMultiCrewSelected() {
            @Override
            public void onSelected(List<CrewSelectionData> lstResult, List<CrewSelectionData> lstSelectedEmployee,
                                   List<Integer> lstDeselectedEmployee, List<Integer> lstNewSelected) {

                List<Employees> lstEmpData = getEmployeeDataFromCrewList(lstResult);
                if (shouldContinueIfFormIsCheckedIn(lstEmpData, viewClick, object, lstDeselectedEmployee, lstNewSelected, true)) {
                    viewClick.setEnabled(true);
                    updateGlobalEmployeeData(false, true, lstEmpData, lstDeselectedEmployee);
                    setCrewInViewAndSendBreadcrumb(object, getEmployeeDataFromCrewList(lstSelectedEmployee), lstDeselectedEmployee, lstNewSelected, commentView);
                }
                if (AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 0) {
                    updateGlobalEmployeeData(false, false, lstEmpData, lstDeselectedEmployee);
                }
            }

            @Override
            public void onCancel() {
                viewClick.setEnabled(!isTMForm() || formData.getCheckin_time() != 0 || !formData.getIsCheckInOut());
            }
        });
    }


    private void showCheckInCrewMemberView(JSONObject
                                                   object, List<Employees> lstSelectedEmployee, List<Employees> lstExtendedCrew, LinearLayout
                                                   llMultiSelect, View crewView) {
        llMultiSelect.setEnabled(true);
        PopUtils.showNewCrewSelectionView(this, lstSelectedEmployee, lstExtendedCrew, llMultiSelect, new OnMultiCrewSelected() {
            @Override
            public void onSelected(List<CrewSelectionData> lstResult, List<CrewSelectionData> lstSelectedEmployee,
                                   List<Integer> lstDeselectedEmployee, List<Integer> lstNewSelected) {
                List<Employees> lstEmpData = getEmployeeDataFromCrewList(lstResult);
                if (shouldContinueIfFormIsCheckedIn(lstEmpData, llMultiSelect, object, lstDeselectedEmployee, lstNewSelected, true)) {
                    updateCheckInCrewSelectionData(lstEmpData);
                    setCrewInViewAndSendBreadcrumb(object, lstEmpData, lstDeselectedEmployee, lstNewSelected, crewView);
                }
            }

            @Override
            public void onCancel() {
                llMultiSelect.setEnabled(!isTMForm() || formData.getCheckin_time() != 0 || !formData.getIsCheckInOut());
            }
        });
    }

    private boolean shouldContinueIfFormIsCheckedIn(List<Employees> lstResult, LinearLayout viewClick, JSONObject
            object, List<Integer> lstDeselectedEmployee, List<Integer> lstNewSelected, boolean postData) {
        if (isTMForm() && formData.getIsCheckInOut() && formData.getCheckin_time() == 0) {
            if (lstResult.size() == 0) {
                viewClick.setEnabled(false);
            } else {
                if (postData) {
                    EventBus.getDefault().post(new CrewSelectionEvent(object, lstResult, lstDeselectedEmployee, lstNewSelected, viewClick));
                }
            }
            return false;
        }
        return true;
    }

    /**
     * Method to update crew data in employee table
     */
    private void updateGlobalEmployeeData(boolean updateCheckInMap, boolean updateCheckInData, List<Employees> lstData, List<Integer> lstDeselectedData) {
        TblEmployees tblEmployees = new TblEmployees(BaseFormDetailActivity.this);
        tblEmployees.updateBulkSelectionData(lstData);
        tblEmployees.updateBulkDeSelectionData(lstDeselectedData);
        if (updateCheckInData) {
            updateDataInCheckInMap(lstData, updateCheckInMap);
        }
    }

    private void updateDataInCheckInMap(List<Employees> lstData, boolean updateCheckInMap) {
        List<Integer> lstIds = new ArrayList<>();
        if (lstData != null) {
            TblTMForms tblTMForms = new TblTMForms(BaseFormDetailActivity.this);
            for (Employees employees : lstData) {
                lstIds.add(employees.getEmployeeID());
            }
            tblTMForms.updateCrewInForm(formData.getFormPKId(), lstIds);
        }
        formData.setCrewIds(lstIds);
        if (updateCheckInMap && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 1) {
            TblCheckInMap tblCheckInMap = new TblCheckInMap(BaseFormDetailActivity.this);
            tblCheckInMap.updateCrewInForm(formData.getFormPKId(), lstIds);
        }
    }


    /**
     * Method to update crew data in Tbl check in Map Table
     */
    void updateCheckInCrewSelectionData(List<Employees> lstData) {
        TblCheckInMap tblCheckInMap = new TblCheckInMap(BaseFormDetailActivity.this);
        TblTMForms tblTMForms = new TblTMForms(BaseFormDetailActivity.this);
        List<Integer> lstIds = new ArrayList<>();
        if (lstData != null) {
            for (Employees employees : lstData) {
                lstIds.add(employees.getEmployeeID());
            }
            tblCheckInMap.updateCrewInForm(formData.getFormPKId(), lstIds);
            tblTMForms.updateCrewInForm(formData.getFormPKId(), lstIds);
            formData.setCrewIds(tblTMForms.getFormCrewData(formData.getFormPKId()));
        }
    }

    public View getViewForCrewComponent() {

        //Here we check till size + 1 because we are stating loop from 1(As page number starts from 1)
        for (int i = 1; i <= totalPages; i++) {
            FormComponentData crewComponentData = lstCrewComponent.get(i);
            if (crewComponentData != null) {
                return crewComponentData.rootView.findViewWithTag(crewComponentData.tagId);
            }
        }
        return null;
    }


    void setCrewInViewAndSendBreadcrumb(JSONObject object, List<Employees> lstSelectedEmployee,
                                        List<Integer> lstDeSelectedEmployee, List<Integer> lstNewSelected, View crewView) {

        prepareForCrewSubmissionData(siteData, formData, lstDeSelectedEmployee, lstNewSelected);
        String selectedItem = StaticUtils.getEmployeesNames(lstSelectedEmployee);
        TextView tvMultiSelected = crewView.findViewById(R.id.tvMultiSelected);
        if (!TextUtils.isEmpty(selectedItem)) {
            tvMultiSelected.setText(selectedItem);
        } else {
            tvMultiSelected.setText("");
            tvMultiSelected.setHint(R.string.tap_to_select_item);
        }

        try {
            object.put(Constants.VALUE_NAME, tvMultiSelected.getText().toString());
            JSONArray dataIdArray = new JSONArray();
            for (Employees employees : lstSelectedEmployee) {
                dataIdArray.put(employees.getEmployeeID());
            }
            object.put(Constants.VALUE, dataIdArray);
            removeDataFromCrewComponentList();
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        updateValidationInMap(object, tvMultiSelected, tvMultiSelected.getText().toString());
    }

    private void removeDataFromCrewComponentList() {
        if (!lstCrewComponent.isEmpty()) {
            if (lstCrewComponent.containsKey(pageNumber)) {
                lstCrewComponent.remove(pageNumber);
            }
        }
    }

    private void showAutoCrewSelectionDialogIfRequired(View commentView) {
        if (commentView == null)
            return;
        if (formData.getIsCheckInOut() && siteData != null && formData.getCheckin_time() == 0) {
            return;
        }
        TblEmployees tblEmployees = new TblEmployees(this);
        List<Employees> lstEmployee = tblEmployees.getSelectedCrewData();
        TextView tvMultiSelected = commentView.findViewById(R.id.tvMultiSelected);
        if (tvMultiSelected.getParent() instanceof LinearLayout) {
            ((LinearLayout) tvMultiSelected.getParent()).setTag(lstEmployee);
            removeDataFromCrewComponentList();
            ((LinearLayout) tvMultiSelected.getParent()).callOnClick();
        }

    }

    private void showAutoCrewSelectionDialogForCheckInFormIfRequired(View commentView) {
        if (commentView == null)
            return;

        if (formData.getIsCheckInOut() && siteData != null && formData.getCheckin_time() == 0) {
            return;
        }
        //List<Employees> lstEmployee = getNotAssignedCrews();
        TextView tvMultiSelected = commentView.findViewById(R.id.tvMultiSelected);

        if (tvMultiSelected.getParent() instanceof LinearLayout) {
            //((LinearLayout) tvMultiSelected.getParent()).setTag(lstEmployee);
            removeDataFromCrewComponentList();
            ((LinearLayout) tvMultiSelected.getParent()).callOnClick();
        }
    }


    public void checkAndShowCrewSelectionIfRequired(int pageNumber) {
        FormComponentData crewComponentData = lstCrewComponent.get(pageNumber);
        if (crewComponentData == null)
            return;
        View commentView = crewComponentData.rootView.findViewWithTag(crewComponentData.tagId);
        showCrewSelectionView(commentView);

    }

    private void showCrewSelectionView(View commentView) {
        if (isTMForm() && formData.getIsCheckInOut() && AppPrefShared.getInt(Constants.LOGGED_IN_USER_PARAM_CLOCKINOUT, 0) == 2) {
            showAutoCrewSelectionDialogForCheckInFormIfRequired(commentView);
        } else {
            showAutoCrewSelectionDialogIfRequired(commentView);
        }
    }

    public void prepareForCrewSubmissionData(SiteData siteData, FormData formData, List<Integer> lstDeSelected, List<Integer> lstNewSelected) {
        if (siteData == null) {
            crewSelectionData(MainActivity.currentLatitude, MainActivity.currentLongitude, -2, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), lstNewSelected, lstDeSelected);
        } else {
            crewSelectionData(MainActivity.currentLatitude, MainActivity.currentLongitude, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), lstNewSelected, lstDeSelected);
        }
    }


    private void crewSelectionData(double currentLatitude, double currentLongitude,
                                   long siteId, long formId, String
                                           formName, String formSubmissionId, List<Integer> lstCrewIds, List<Integer> lstDeCrewIds) {
        try {
            if (lstCrewIds.isEmpty() && lstDeCrewIds.isEmpty())
                return;
            TblUploadData tblUploadData = new TblUploadData(this);
            UploadOtherData uploadOtherData = new UploadOtherData();
            long currentTime = System.currentTimeMillis();
            uploadOtherData.setDataType(Constants.CREW_DATA);
            uploadOtherData.setCreatedAt(currentTime);
            uploadOtherData.setUpdatedAt(currentTime);
            uploadOtherData.setProcessStartTime(currentTime);
            uploadOtherData.setFormSubmissionId(formSubmissionId);

            JSONObject dataJson = new JSONObject();
            try {
                dataJson.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
                dataJson.put(Constants.PARAM_EMAIL, AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                dataJson.put(Constants.PARAM_DT, currentTime / 1000);
                dataJson.put(Constants.PARAM_LON, currentLongitude);
                dataJson.put(Constants.PARAM_LAT, currentLatitude);
                dataJson.put(Constants.PARAM_SITEID, siteId);
                dataJson.put(Constants.PARAM_PROFILEID, formId);
                dataJson.put(Constants.PARAM_UUID, StaticUtils.getUuid());
                dataJson.put(Constants.PARAM_TYPE, 13);
                if (!lstCrewIds.isEmpty()) {
                    dataJson.put(Constants.PARAM_CREW, lstCrewIds);
                }
                if (lstDeCrewIds != null && !lstDeCrewIds.isEmpty()) {
                    dataJson.put(Constants.PARAM_DE_CREW, lstDeCrewIds);
                }
                dataJson.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            if (!TextUtils.isEmpty(formName)) {
                uploadOtherData.setTitle(getString(R.string.breadcrumb_, getString(R.string.txt_crew_selection), formName));
            } else {
                uploadOtherData.setTitle(getString(R.string.breadcrumb_single, getString(R.string.txt_crew_selection)));
            }
            uploadOtherData.setRequestedData(dataJson.toString());
            uploadOtherData.setUuid(StaticUtils.getUuid());
            uploadOtherData.setImageUploaded(true);
            tblUploadData.insertData(uploadOtherData);
            sendBroadCastForStartOtherDataUpload();

        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void inputTypeCommentWeather(JSONObject object, ViewGroup adsLayout, int parentPanelTag) {
        View commentView = getLayoutInflater().inflate(R.layout.layout_comment_weather, adsLayout, false);
        commentView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        ImageView ivWeather = commentView.findViewById(R.id.ivWeather);
        AppPrefShared.putValue(String.format("%s%s", tag, formData.getFormPKId()), "");
        CustomEditText edtWeather = commentView.findViewById(R.id.edtComment);
        TextView tvCommentTitle = commentView.findViewById(R.id.tvCommentTitle);
        setValidationInMap(object, edtWeather);
        ivWeather.setEnabled(true);
        ivWeather.setOnClickListener(view -> {
            if (BaseApplication.getInstance().isOnline(context)) {
                if (canGetLocation(this)) {
                    showProgress(getString(R.string.getting_weather_info));
                    hideSoftKeyboard(BaseFormDetailActivity.this);
                    apiCallForWeather(ivWeather, edtWeather, tag);
                }
            } else {
                showForeGroundToast(getString(R.string.enableMobileData));
            }
        });
        setFocusChangeListenerForEditText(edtWeather);
        setEditTextListeners(edtWeather, false);

        setTitleOfComponent(tvCommentTitle, object);
        try {
            if (object.has(Constants.VALUE)) {
                edtWeather.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, edtWeather, edtWeather.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
                setHintInComponent(edtWeather, object);
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setEditTextChangeListener(object, edtWeather);

        edtWeather.setOnTouchListener((v, event) -> {
            if (edtWeather.hasFocus()) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                switch (event.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_SCROLL:
                        v.getParent().requestDisallowInterceptTouchEvent(false);
                        return true;
                }
            }
            return false;
        });
        adsLayout.addView(commentView);
        addViewInList(commentView);
        addSeparatorView(this, adsLayout);
    }


    private void apiCallForWeather(ImageView ivWeather, EditText edtWeather, int tag) {
        ApiInterface apiService = RetrofitProvider.createServiceString(ApiInterface.class);
        HashMap<String, Object> params = new HashMap<>();
        params.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
        params.put(Constants.PARAM_LAT, String.valueOf(MainActivity.currentLatitude));
        params.put(Constants.PARAM_LNG, String.valueOf(MainActivity.currentLongitude));
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        StaticUtils.addCommonData(params);
        Call<String> call = apiService.getWeatherApi(params);
        call.enqueue(new Callback<String>() {
            @Override
            public void onResponse(Call<String> call, Response<String> response) {
                if (response.body() != null) {
                    stopProgress();
                    try {
                        if (!TextUtils.isEmpty(response.body())) {
                            String strWeather = response.body();
                            edtWeather.setError(null);

                            edtWeather.setText("");
                            edtWeather.setText(edtWeather.getText().toString().trim().replace(AppPrefShared.getString(String.format("%s%s", tag, formData.getFormPKId()), ""), "") + strWeather.trim());
                            AppPrefShared.putValue(String.format("%s%s", tag, formData.getFormPKId()), strWeather.trim());
                            ivWeather.setEnabled(false);
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    } finally {
                        stopProgress();
                    }
                } else {
                    stopProgress();
                    ivWeather.setSelected(false);
                }
            }

            @Override
            public void onFailure(Call<String> call, Throwable t) {
                stopProgress();
                ivWeather.setSelected(false);
            }
        });
    }

    /**
     * inputTypeRadioGroup is use for add Radio in parent view
     * this method is execute when type is radio group. and it is set data in radio group.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeRadioGroup(JSONObject object, ViewGroup adsLayout, boolean shouldAdd, String parentValue) {
        try {
            if (object.has(Constants.SEGMENT) && object.getBoolean(Constants.SEGMENT)) {
                View radioGroup = getLayoutInflater().inflate(R.layout.layout_radio_segment, adsLayout, false);
                radioGroup.setTag(tag);
                addTagInMainObjectAndIncreaseTagId(object);
                LinearLayout llRadioSegmentView = radioGroup.findViewById(R.id.llRadioSegmentView);
                AppCompatTextView tvRadioGroupTitle = radioGroup.findViewById(R.id.tvRadioGroupTitle);
                String selectedValue = "";

                setValidationInMap(object, tvRadioGroupTitle);
                updateAutoFillDataInView(object);
                if (object.has(Constants.VALUE)) {
                    selectedValue = object.getString(Constants.VALUE);
                    updateValidationInMap(object, tvRadioGroupTitle, selectedValue);
                } else {
                    object.put(Constants.VALUE, "");
                }
                //object.put(Constants.PARENT_TAG, parentPanelTag);
                setTitleOfComponent(tvRadioGroupTitle, object);
                adsLayout.addView(radioGroup);
                addViewInList(radioGroup);
                addSeparatorView(this, adsLayout);
                ArrayList<SegmentData> lstSegmentData = new ArrayList<>();
                if (object.has(Constants.CHOICES)) {
                    try {
                        JSONArray array = object.getJSONArray(Constants.CHOICES);
                        for (int i = 0; i < array.length(); i++) {
                            JSONObject jsonObject = null;
                            try {
                                if (array.get(i) instanceof JSONObject) {
                                    jsonObject = array.getJSONObject(i);
                                }
                            } catch (JSONException ex) {
                                FirebaseEventUtils.logException(ex);
                            }
                            if (jsonObject != null) {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    View textView = getLayoutInflater().inflate(R.layout.item_radiogroup_segment, adsLayout, false);
                                    View view = getLayoutInflater().inflate(R.layout.item_saprator_segment, adsLayout, false);
                                    View linear = getLayoutInflater().inflate(R.layout.layout_linear, adsLayout, false);
                                    LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                                    clearValue(llSubview, object);
                                    JSONArray arrayElement = jsonObject.getJSONArray(Constants.ELEMENTS);
                                    if (jsonObject.has(CHOICEVALUE)) {
                                        jsonArrayRead(arrayElement, llSubview, i, -1, selectedValue.equals(jsonObject.getString(CHOICEVALUE)), parentValue);
                                    } else {
                                        jsonArrayRead(arrayElement, llSubview, i, -1, true, parentValue);
                                    }
                                    clearValue(llSubview, object);
                                    TextView tvSelection = textView.findViewById(R.id.cbSelection);
                                    if (jsonObject.has(Constants.CHOICEVALUE)) {
                                        tvSelection.setText(jsonObject.getString(Constants.CHOICEVALUE));
                                    }

                                    if (selectedValue.equals(tvSelection.getText().toString())) {
                                        llSubview.setVisibility(View.VISIBLE);
                                        tvSelection.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
                                        tvSelection.setTextColor(getResources().getColor(R.color.white));
                                        readAndAddValidation(llSubview, jsonObject);
                                    }
                                    SegmentData segmentData = new SegmentData();
                                    segmentData.setLinearLayout(llSubview);
                                    segmentData.setTextView(tvSelection);
                                    segmentData.setJsonObject(jsonObject);
                                    lstSegmentData.add(segmentData);
                                    llRadioSegmentView.addView(tvSelection);
                                    adsLayout.addView(linear);
                                    addViewInList(linear);
                                    llRadioSegmentView.addView(view);
                                    JSONObject finalJsonObject = jsonObject;
                                    tvSelection.setOnClickListener(view12 -> {
                                        tvRadioGroupTitle.setError(null);
                                        clearFocus();
                                        updateValidationInMap(object, tvRadioGroupTitle, "", false, true, true);
                                        for (SegmentData segmentData1 : lstSegmentData) {
                                            if (tvSelection == segmentData1.getTextView()) {
                                                tvSelection.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
                                                tvSelection.setTextColor(getResources().getColor(R.color.white));
                                                segmentData1.getLinearLayout().setVisibility(View.VISIBLE);
                                                readAndAddValidation(llSubview, finalJsonObject);
                                                try {
                                                    object.put(Constants.VALUE, tvSelection.getText().toString());
                                                    updateImageUploadList(segmentData1.getJsonObject(), tvSelection.getText().toString());
                                                } catch (JSONException e) {
                                                    FirebaseEventUtils.logException(e);
                                                }

                                            } else {
                                                segmentData1.getTextView().setBackgroundColor(getResources().getColor(R.color.transparent));
                                                segmentData1.getTextView().setTextColor(getResources().getColor(R.color.black_font));
                                                try {
                                                    jsonArrayRead(segmentData1.getJsonObject().getJSONArray(Constants.ELEMENTS), segmentData1.getLinearLayout(), false);
                                                    updateImageUploadList(segmentData1.getJsonObject(), tvSelection.getText().toString());
                                                } catch (JSONException e) {
                                                    FirebaseEventUtils.logException(e);
                                                }
                                                segmentData1.getLinearLayout().setVisibility(View.GONE);
                                            }
                                        }
                                    });
                                }
                            } else {
                                View mainView = getLayoutInflater().inflate(R.layout.item_radiogroup_segment, adsLayout, false);
                                View separatorView = getLayoutInflater().inflate(R.layout.item_saprator_segment, adsLayout, false);
                                TextView tvSelection = mainView.findViewById(R.id.cbSelection);
                                tvSelection.setText(array.getString(i));
                                llRadioSegmentView.addView(tvSelection);
                                llRadioSegmentView.addView(separatorView);
                                if (selectedValue.equals(tvSelection.getText().toString())) {
                                    tvSelection.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
                                    tvSelection.setTextColor(getResources().getColor(R.color.white));
                                }
                                tvSelection.setOnClickListener(view1 -> {
                                    clearFocus();
                                    tvRadioGroupTitle.setError(null);
                                    updateValidationInMap(object, tvRadioGroupTitle, "", false, true, true);
                                    for (int i1 = 0; i1 < llRadioSegmentView.getChildCount(); i1++) {
                                        if (llRadioSegmentView.getChildAt(i1) instanceof AppCompatTextView) {
                                            AppCompatTextView textView1 = (AppCompatTextView) llRadioSegmentView.getChildAt(i1);
                                            if (tvSelection == textView1) {
                                                tvSelection.setBackgroundColor(ContextCompat.getColor(this, R.color.colorPrimary));
                                                tvSelection.setTextColor(getResources().getColor(R.color.white));
                                                try {
                                                    object.put(Constants.VALUE, tvSelection.getText().toString());

                                                } catch (JSONException e) {
                                                    FirebaseEventUtils.logException(e);
                                                }
                                            } else {
                                                textView1.setBackgroundColor(ContextCompat.getColor(this, R.color.transparent));
                                                textView1.setTextColor(getResources().getColor(R.color.black_font));
                                            }
                                        }
                                    }
                                });
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            } else {
                View radioGroup = getLayoutInflater().inflate(R.layout.layout_radio_group, adsLayout, false);
                radioGroup.setTag(tag);
                addTagInMainObjectAndIncreaseTagId(object);
                TextView tvRadioGroupTitle = radioGroup.findViewById(R.id.tvRadioGroupTitle);

                setValidationInMap(object, tvRadioGroupTitle);

                setTitleOfComponent(tvRadioGroupTitle, object);
                String selectedValue = "";
                adsLayout.addView(radioGroup);
                addViewInList(radioGroup);
                addSeparatorView(this, adsLayout);
                RadioGroup rbRadioGroup = radioGroup.findViewById(R.id.rbRadioGroup);
                updateAutoFillDataInView(object);
                if (object.has(Constants.VALUE)) {
                    selectedValue = object.getString(Constants.VALUE);
                    updateValidationInMap(object, tvRadioGroupTitle, selectedValue);
                } else {
                    object.put(Constants.VALUE, "");

                }
                if (object.has(Constants.CHOICES)) {
                    try {
                        JSONArray array = object.getJSONArray(Constants.CHOICES);
                        for (int i = 0; i < array.length(); i++) {
                            JSONObject jsonObject = null;
                            try {
                                if (array.get(i) instanceof JSONObject) {
                                    jsonObject = array.getJSONObject(i);
                                }

                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                            if (jsonObject != null) {
                                if (jsonObject.has(Constants.ELEMENTS)) {
                                    try {
                                        JSONObject object1 = jsonObject;
                                        View linear = getLayoutInflater().inflate(R.layout.layout_linear, adsLayout, false);
                                        LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                                        JSONArray arrayElement = jsonObject.getJSONArray(Constants.ELEMENTS);
                                        if (jsonObject.has(CHOICEVALUE)) {
                                            jsonArrayRead(arrayElement, llSubview, i, -1, selectedValue.equals(jsonObject.getString(CHOICEVALUE)), parentValue);
                                        } else {
                                            jsonArrayRead(arrayElement, llSubview, i, -1, true, parentValue);
                                        }
                                        clearValue(llSubview, object);

                                        RadioButton rbChoice = prepareRadioButton(array.getString(i));
                                        rbRadioGroup.addView(rbChoice);
                                        rbChoice.setOnCheckedChangeListener((compoundButton, b) -> {
                                            tvRadioGroupTitle.setError(null);
                                            clearFocus();
                                            updateValidationInMap(object, tvRadioGroupTitle, "", false, true, true);
                                            if (b) {
                                                try {
                                                    readAndAddValidation(llSubview, object1);
//                                                    jsonArrayReadAndAdd(arrayElement, llSubview);
                                                    object.put(Constants.VALUE, rbChoice.getText().toString());
                                                    updateImageUploadList(object1, object.getString(VALUE));
                                                } catch (JSONException e) {
                                                    FirebaseEventUtils.logException(e);
                                                }

                                                llSubview.setVisibility(View.VISIBLE);

                                                try {
                                                    if (adsLayout.getParent() instanceof NestedScrollView) {
                                                        ((NestedScrollView) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, llSubview));
                                                    } else if (adsLayout.getParent() instanceof ScrollView) {
                                                        ((ScrollView) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, llSubview));
                                                    } else if (adsLayout.getParent() instanceof LinearLayout) {
                                                        ((LinearLayout) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, llSubview));
                                                    }
                                                } catch (Exception e) {
                                                    FirebaseEventUtils.logException(e);
                                                }

                                            } else {
                                                jsonArrayRead(arrayElement, llSubview, false);
                                                llSubview.setVisibility(View.GONE);
                                                try {
                                                    updateImageUploadList(object1, object.getString(VALUE));
                                                } catch (JSONException e) {
                                                    FirebaseEventUtils.logException(e);
                                                }
                                            }

                                        });
                                        adsLayout.addView(linear);
                                        addViewInList(linear);


                                        if (jsonObject.has(Constants.CHOICEVALUE)) {
                                            rbChoice.setText(jsonObject.getString(Constants.CHOICEVALUE));
                                        }

                                        if (selectedValue.equals(rbChoice.getText().toString())) {
                                            rbChoice.setChecked(true);
                                        }
                                        rbChoice.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.colorPrimary)));

                                    } catch (JSONException e) {
                                        FirebaseEventUtils.logException(e);
                                    }

                                }
                            } else {
                                RadioButton rbChoice = prepareRadioButton(array.getString(i));
                                rbRadioGroup.addView(rbChoice);

                                if (selectedValue.equals(rbChoice.getText().toString())) {
                                    rbChoice.setChecked(true);
                                }

                                rbRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
                                    tvRadioGroupTitle.setError(null);
                                    clearFocus();
                                    RadioButton checkedRadioButton = group.findViewById(checkedId);
                                    boolean isChecked = checkedRadioButton.isChecked();
                                    if (isChecked) {
                                        try {
                                            object.put(Constants.VALUE, checkedRadioButton.getText().toString());
                                            updateValidationInMap(object, tvRadioGroupTitle, checkedRadioButton.getText().toString());
                                        } catch (JSONException e) {
                                            FirebaseEventUtils.logException(e);
                                        }
                                    }
                                });
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * Function to update map for image date.
     * We keep update this list when any child file view changing its visibility.
     * This function help us to show only required image properties for plotOnMap Feature
     *
     * @param object        Parent jsonObject
     * @param selectedValue current selected value to compare with child object
     */
    private void updateImageUploadList(JSONObject object, String selectedValue) {
        try {
            if (object == null)
                return;
            JSONArray arrayElement = object.getJSONArray(Constants.ELEMENTS);
            if (arrayElement.length() > 0) {
                for (int i = 0; i < arrayElement.length(); i++) {
                    try {
                        JSONObject childObject = arrayElement.getJSONObject(i);
                        String type = (String) childObject.get(Constants.TYPE);
                        if (type.equals(IMAGE_UPLOAD)) {
                            if (selectedValue.equalsIgnoreCase(object.getString(CHOICEVALUE))) {
                                if (!mapImageUpload.containsKey(childObject.getInt(ID))) {
                                    mapImageUpload.put(childObject.getInt(ID), childObject);
                                }
                            } else {
                                mapImageUpload.remove(childObject.getInt(ID));
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private RadioButton prepareRadioButton(String title) {
        RadioButton rbChoice = new RadioButton(BaseFormDetailActivity.this);
        rbChoice.setGravity(Gravity.CENTER_VERTICAL);
        rbChoice.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
        rbChoice.setText(title.trim());
        rbChoice.setTextSize(15);
        rbChoice.setTypeface(ResourcesCompat.getFont(this, R.font.roboto_regular));
        return rbChoice;
    }

    /**
     * inputTypeGeo is use for add Geo in parent view
     * this method is execute when type is Geo.
     *
     * @param object     is json object use for get data from object
     * @param adsLayout  is view group and work as parent layout
     * @param pageNumber int current pageNumber
     */
    private void inputTypeGeo(JSONObject object, ViewGroup adsLayout, int pageNumber) {
        View geoView = getLayoutInflater().inflate(R.layout.layout_geo, adsLayout, false);
        geoView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        List<String> lstIgnoreList = new ArrayList<>();
        ImageView ivRefresh = geoView.findViewById(R.id.ivRefresh);

        TextView tvPropertyName = geoView.findViewById(R.id.tvPropertyName);
        TextView tvGeoTitle = geoView.findViewById(R.id.tvGeoTitle);
        setTitleOfComponent(tvGeoTitle, object);
        setValidationInMap(object, tvPropertyName);
        try {
            if (object.has(Constants.PARAM_LAT)) {
                object.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
                object.put(Constants.PARAM_LNG, MainActivity.currentLongitude);
            } else {
                object.put(Constants.PARAM_LAT, MainActivity.currentLatitude);
                object.put(Constants.PARAM_LNG, MainActivity.currentLongitude);
            }

            if (isTMForm()) {
                object.put(Constants.SITEID, String.valueOf(siteData.getSiteId()));
            } else {
                object.put(Constants.SITEID, "-2");
            }

            if (object.has(Constants.BUILDING_ID) && (Integer.parseInt(object.getString(Constants.BUILDING_ID)) != -1)) {
                try {
                    lastUpdatedBuildingId = object.getString(Constants.BUILDING_ID);
                    lastBuildingId = lastUpdatedBuildingId;
                    if (!TextUtils.isEmpty(lastUpdatedBuildingId) && Integer.parseInt(lastUpdatedBuildingId) > 0) {
                        TblProperties tblProperties = new TblProperties(BaseFormDetailActivity.this);
                        PropertiesVo propertiesVo = tblProperties.getPropertyDataFromId(Integer.parseInt(lastUpdatedBuildingId));
                        if (propertiesVo != null) {
                            lstIgnoreList.add(lastUpdatedBuildingId);
                            tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
                            tvPropertyName.setText(propertiesVo.getPropertyName());
                        }
                        updateValidationInMap(object, tvPropertyName, tvPropertyName.getText().toString());
                    } else {
                        tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                    tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
                }
            } else {

                FormComponentData geoComponentData = new FormComponentData();
                geoComponentData.pageNumber = pageNumber;
                geoComponentData.object = object;
                geoComponentData.tagId = (Integer) geoView.getTag();
                geoComponentData.rootView = adsLayout;
                lstGeoComponent.put(pageNumber, geoComponentData);
                try {
                    object.put(Constants.BUILDING_ID, "-1");
                    object.put(Constants.BUILDING_NAME, "");

                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }

                if (isTMForm()) {
                    try {
                        TblProperties tblProperties = new TblProperties(this);
                        PropertiesVo propertiesVo = tblProperties.getPropertyDataFromId(Integer.parseInt(lastUpdatedBuildingId));
                        if (propertiesVo.getPropertyId() > 0) {
                            if (!TextUtils.isEmpty(lastUpdatedBuildingId) && Integer.parseInt(lastUpdatedBuildingId) > 0) {
                                try {
                                    object.put(Constants.BUILDING_ID, String.valueOf(lastUpdatedBuildingId));
                                    object.put(Constants.BUILDING_NAME, propertiesVo.getPropertyName());
                                } catch (JSONException e) {
                                    FirebaseEventUtils.logException(e);
                                }
                                lstIgnoreList.add(lastBuildingId);
                                tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
                                tvPropertyName.setText(propertiesVo.getPropertyName());
                                updateValidationInMap(object, tvPropertyName, tvPropertyName.getText().toString());
                                removeDataFromGeoComponentList();
                            } else {
                                tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
                            }
                        }
                    } catch (NumberFormatException e) {
                        FirebaseEventUtils.logException(e);
                        tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
                    }
                } else {
                    if (pageNumber == 1) {
                        new Handler().postDelayed(() -> {
                            if (currentPage == 1) {
                                if (canGetLocation(this) && Integer.parseInt(lastUpdatedBuildingId) == 0 &&
                                        AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 0) {
                                    refreshGeoProperty(object, lstIgnoreList, tvPropertyName, ivRefresh);
                                }
                            }
                        }, 500);
                        tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        ivRefresh.setOnClickListener(view -> {
            clearFocus();
            if (PermissionUtils.hasPermissions(context, PermissionUtils.getLocationPermissions())) {
                if (!TextUtils.isEmpty(tvPropertyName.getText().toString().trim())) {
                    PopUtils.showConformAlertDialogForProperty(BaseFormDetailActivity.this, tvPropertyName.getText().toString(),
                            (dialogInterface, i) -> refreshGeoProperty(object, lstIgnoreList, tvPropertyName, ivRefresh), (dialogInterface, i) -> {

                            });
                } else {
                    refreshGeoProperty(object, lstIgnoreList, tvPropertyName, ivRefresh);
                }
            } else {
                requestLocationPermission();
            }
        });
        adsLayout.addView(geoView);
        addViewInList(geoView);
        addSeparatorView(this, adsLayout);

    }


    /**
     * temp
     * Check location permission. show required permission dialog if user set permanent denied
     */
    private void requestLocationPermission() {
        if (PermissionUtils.shouldShowRequestPermissions(BaseFormDetailActivity.this, PermissionUtils.getLocationPermissions())) {
            PermissionUtils.requestPermission(BaseFormDetailActivity.this, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
        } else {
            PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to),
                    getString(R.string.open), getString(R.string.txt_cancel), false,
                    (dialog, which) -> PermissionUtils.navigateUserToPermissionScreen(this), (dialog, which) -> {

                    });

        }
    }

    private void refreshGeoProperty(JSONObject object, List<String> lstIgnoreList, TextView tvPropertyName, ImageView ivRefresh) {
        String buildingId = "0";
        ivRefresh.setEnabled(false);
        if (object.has(Constants.BUILDING_ID)) {
            try {
                buildingId = object.getString(Constants.BUILDING_ID);
                lastUpdatedBuildingId = buildingId;
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        if (Integer.parseInt(buildingId) > 0) {
            lstIgnoreList.add(buildingId);
        }
        showPropertyDialog(lstIgnoreList, tvPropertyName, object, ivRefresh);
    }


    private void showPropertyDialog(List<String> lstIgnoreList, TextView
            tvPropertyName, JSONObject object, ImageView ivRefresh) {


        dbExecutorService.execute(() -> {
            PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
            TblProperties tblProperties = new TblProperties(context);
            TblCluster tblCluster = new TblCluster(context);
            List<PropertiesVo> lstProperties = tblProperties.getAllProperties();
            List<Cluster> lstClusters = tblCluster.getAllClusterData();
            lstProperty = polygonCalculation.getFilteredPropertyListFromPolygon(BaseFormDetailActivity.this, false, null, MainActivity.currentLatitude,
                    MainActivity.currentLongitude, MainActivity.distanceAccuracy, lstIgnoreList, lstClusters, lstProperties);
            new Handler(Looper.getMainLooper()).post(() -> {
                if (isFinishing())
                    return;
                if (ivRefresh != null)
                    ivRefresh.setEnabled(true);
                showDialogueOfPropertyUsingPolygon(tvPropertyName, object);
            });
        });

    }

    private void removeDataFromGeoComponentList() {
        if (!lstGeoComponent.isEmpty()) {
            if (lstGeoComponent.containsKey(pageNumber)) {
                lstGeoComponent.remove(pageNumber);
            }
        }
    }

    public void checkAndShowGeoSelectionIfRequired(int pageNumber) {
        FormComponentData geoComponentData = lstGeoComponent.get(pageNumber);
        if (geoComponentData == null)
            return;
        View componentView = geoComponentData.rootView.findViewWithTag(geoComponentData.tagId);

        if (componentView != null && canGetLocation(this) && AppPrefShared.getInt(LOGGED_IN_USER_PARAM_SKIP_GEO, 0) == 0) {
            AppCompatTextView tvPropertyName = componentView.findViewById(R.id.tvPropertyName);
            ImageView ivRefresh = componentView.findViewById(R.id.ivRefresh);
            refreshGeoProperty(geoComponentData.object, geoComponentData.lstIgnoreList, tvPropertyName, ivRefresh);
        }

    }


    public void showDialogueOfPropertyUsingPolygon(TextView tvPropertyName, JSONObject object) {

        String buildingName;
        String buildingId;
        PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();
        boolean isPrivilegedUser = AppPrefShared.getString(Constants.LOGGED_IN_USER_TYPE, Constants.LOGGED_IN_USER_TYPE).equalsIgnoreCase(Constants.LOGGED_IN_USER_TYPE_PRIVILEGED_USER);
        buildingName = polygonCalculation.buildingName;
        tvPropertyName.setText(polygonCalculation.propertyName);
        buildingId = polygonCalculation.buildingId;
        lastBuildingId = buildingId;
        lastUpdatedBuildingId = buildingId;
        if (!TextUtils.isEmpty(buildingName)) {
            tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);
            try {
                object.put(Constants.BUILDING_ID, String.valueOf(buildingId));
                object.put(Constants.BUILDING_NAME, buildingName);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            updateValidationInMap(object, tvPropertyName, tvPropertyName.getText().toString());
        } else {
            tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
        }

        if ((isPrivilegedUser || lstProperty.size() != 0) && TextUtils.isEmpty(buildingName)) {
            if (lstProperty.size() == 0 && !canGetLocation(BaseFormDetailActivity.this)) {
                PopUtils.showAlertDialogPositiveButtonOnlyEnableLocation(BaseFormDetailActivity.this,
                        (dialogInterface, i) -> PopUtils.displayLocationSettingsRequest(this, LOCATION_REQUEST_CODE), (dialogInterface, i) -> {

                        });
            } else {
                try {
                    PopUtils.showPropertyDialog(BaseFormDetailActivity.this, lstProperty,
                            canGetLocation(BaseFormDetailActivity.this),
                            position -> doAfterPropertySelected(position, tvPropertyName, object),
                            view -> doAfterNewPropertyOptionClicked(),
                            view -> doAfterNonOfAboveOptionClicked(tvPropertyName, object));
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } else {
            removeDataFromGeoComponentList();
        }
    }


    private void doAfterPropertySelected(int position, TextView tvPropertyName, JSONObject object) {
        if (lstProperty != null && lstProperty.size() > 0) {
            PropertiesVo propertiesVo = lstProperty.get(position);
            tvPropertyName.setVisibility(View.VISIBLE);
            tvPropertyName.setText(propertiesVo.getPropertyName().trim());
            tvPropertyName.setBackgroundResource(R.drawable.shape_transparent_bg_rounded_corner);

            try {
                lastUpdatedBuildingId = String.valueOf(propertiesVo.getPropertyId());
                object.put(Constants.BUILDING_ID, String.valueOf(propertiesVo.getPropertyId()));
                object.put(Constants.BUILDING_NAME, propertiesVo.getPropertyName());
                updateValidationInMap(object, tvPropertyName, tvPropertyName.getText().toString());
                tvPropertyName.setError(null);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            removeDataFromGeoComponentList();
        }
    }

    private void doAfterNewPropertyOptionClicked() {
        removeDataFromGeoComponentList();
        if (canGetLocation(BaseFormDetailActivity.this)) {
            navigateToPropertyScreen(Constants.MAP_ACTIVITY_REQUEST);

        } else {
            if (!isGpsEnableCalled) {
                showGPSSettingAlert();
            }
        }
    }

    private void showGPSSettingAlert() {
        String message;
        message = getString(R.string.gps_not_enabled);
        PopUtils.displayLocationSettingsRequest(this, LOCATION_REQUEST_CODE);
        /*PopUtils.showGPSSettingsAlert(this, message, true,
                (dialogInterface, i) -> navigateToGPSSettingScreen(),
                (dialogInterface, i) -> isGpsEnableCalled = true);*/
    }


    private void doAfterNonOfAboveOptionClicked(TextView tvPropertyName, JSONObject object) {
        isGpsEnableCalled = true;
        lastUpdatedBuildingId = "0";
        try {
            object.put(Constants.BUILDING_ID, "0");
            object.put(Constants.BUILDING_NAME, "");
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        tvPropertyName.setBackgroundResource(R.drawable.drawable_lines_sign);
        tvPropertyName.setText("");
        updateValidationInMap(object, tvPropertyName, tvPropertyName.getText().toString());
        removeDataFromGeoComponentList();
    }

    /**
     * addPanelView is use for add panel in parent view
     * this method is execute when type is Checkbox. and it is set data in checkbox.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void addPanelView(JSONObject object, ViewGroup adsLayout, int index, JSONArray
            jsonArray, int objectPosition, int pageNumber, int parentTag) {
        try {
            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
            View panel = getLayoutInflater().inflate(R.layout.layout_penel, adsLayout, false);
            LinearLayout llPanel = panel.findViewById(R.id.llPanel);
            AppCompatImageView ivNewGroup = panel.findViewById(R.id.ivNewGroup);
            AtomicInteger objPosition = new AtomicInteger(objectPosition);
            LinearLayout llSeparator = panel.findViewById(R.id.llSeparator);
            AppCompatImageView ivDelete = panel.findViewById(R.id.ivDelete);
            TextView tvPanelTitle = panel.findViewById(R.id.tvPanelTitle);
            ivNewGroup.setVisibility(View.GONE);
            panel.setTag(tag);
            addTagInMainObjectAndIncreaseTagId(object);
            if (object.has("repeatable")) {
                if (object.getBoolean("repeatable")) {
                    ivNewGroup.setVisibility(View.VISIBLE);
                }
            }
            tvPanelTitle.setTag(objectPosition);
            setTitleOfComponent(tvPanelTitle, object);
            object.put(Constants.PARENT_TAG, (int) panel.getTag());
            if (object.getBoolean(Constants.ISREQUIRED)) {
                if (isChildDataNotRequired(object, array, adsLayout)) {
                    tvPanelTitle.setText("* ".concat(object.getString(Constants.TITLE)));
                    //setValidationInMapForPanel(tvPanelTitle, object.getString(Constants.TITLE), false);
                    PanelData panelData = new PanelData();
                    panelData.setPanelView(tvPanelTitle);
                    panelData.setPanelData(object.toString());
                    panelData.setTitle(object.getString(Constants.TITLE));
                    panelData.setPageNumber(pageNumber);
                    panelValidationData.put((int) panel.getTag(), panelData);
                }
            }
            ivNewGroup.setOnClickListener(view -> {
                try {
                    JSONObject newJsonObject = new JSONObject(object.toString());
                    removeValueFromJson(newJsonObject);
                    int index1 = adsLayout.indexOfChild(panel);
                    JSONArray jsonArray1 = new JSONArray();
                    for (int i = 0; i < jsonArray.length(); i++) {
                        if (jsonArray.get(i).equals(object)) {
                            objPosition.set(i);
                        }
                    }
                    for (int i = 0; i < jsonArray.length(); i++) {
                        if (i > objPosition.get()) {
                            jsonArray1.put(jsonArray.get(i));
                        }
                    }

                    jsonArray.put(objPosition.get() + 1, newJsonObject);
                    for (int i = 0; i < jsonArray1.length(); i++) {
                        jsonArray.put(objPosition.get() + 2 + i, jsonArray1.get(i));
                    }
                    addPanelView(newJsonObject, adsLayout, index1 + 1, jsonArray, objPosition.get() + 1, pageNumber, (int) panel.getTag());
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }

                try {
                    if (adsLayout.getParent() instanceof NestedScrollView scrollView) {
                        scrollView.postDelayed(() -> scrollView.smoothScrollTo(0, (panel.getRootView().getBottom())), 150);
                    }
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            });


            ivDelete.setOnClickListener(view -> {
                clearFocus();
                PopUtils.showDialogForDeleteView(BaseFormDetailActivity.this, getString(R.string.msg_delete_group),
                        view1 -> {
                            try {
                                //Remove Validation
                                int indexOfObject = -1;
                                for (int i = 0; i < jsonArray.length(); i++) {
                                    if (jsonArray.get(i).equals(object)) {
                                        indexOfObject = i;
                                    }
                                }

                                if (indexOfObject == -1)
                                    return;
                                JSONArray array1 = ((JSONObject) jsonArray.get(indexOfObject)).getJSONArray(Constants.ELEMENTS);
                                jsonArrayRead(array1, llPanel, false);

                                jsonArray.remove(indexOfObject);
                                adsLayout.removeViewAt(adsLayout.indexOfChild(panel));

                                try {
                                    //View will scroll to removed view
                                    if (adsLayout.getParent() instanceof NestedScrollView) {
                                        NestedScrollView scrollView = (NestedScrollView) adsLayout.getParent();
                                        scrollView.postDelayed(() -> scrollView.smoothScrollTo(0, panel.getBottom()), 150);
                                    }
                                } catch (Exception e) {
                                    FirebaseEventUtils.logException(e);
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        });
            });
            if (index != -1 || object.has(Constants.IS_GROUP_CREATED)) {
                object.put(Constants.IS_GROUP_CREATED, Constants.IS_GROUP_CREATED);
                ivDelete.setVisibility(View.VISIBLE);
                adsLayout.addView(panel, index);
            } else {
                ivDelete.setVisibility(View.GONE);
                adsLayout.addView(panel);
            }
            addViewInList(panel);

            jsonArrayRead(array, llPanel, pageNumber, (int) panel.getTag(), "");

            if (array.length() > 0) {
                llSeparator.setVisibility(View.GONE);
            } else {
                llSeparator.setVisibility(View.VISIBLE);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

    }

    /**
     * Method to check required check in all childern of panel view.
     * if There is any required componenet is detected, we no need to set
     * required check of panel child is already checking it self.
     * if there no any required componenets found inside panel then
     * we need to put validation and ask user to fill any one components
     * to validate panel.
     */
    private boolean isChildDataNotRequired(JSONObject parentObject, JSONArray array, ViewGroup adsLayout) {
        if (array.length() > 0) {
            for (int i = 0; i < array.length(); i++) {
                try {
                    JSONObject object = array.getJSONObject(i);
                    if (object.has(Constants.ISREQUIRED) && object.getBoolean(Constants.ISREQUIRED)) {
                        return false;
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
            return true;
        }
        return false;

    }


    protected void updateCountOnClearSignatureOrSketch() {
        if (isTMForm()) {
            if (StaticUtils.getTMFormPendingCount(formData.getFormPKId()) > 0) {
                StaticUtils.setTMFormPendingCount(formData.getFormPKId(), StaticUtils.getTMFormPendingCount(formData.getFormPKId()) - 1);
            }

        } else {
            if (StaticUtils.getPendingCount(formData.getFormPKId()) > 0) {
                StaticUtils.setPendingCount(formData.getFormPKId(), StaticUtils.getPendingCount(formData.getFormPKId()) - 1);
            }
        }
    }

    /*
     * Method to navigate signature screen by tap on sign view.
     * */

    /**
     * inputTypeSignaturePad is use for add Signature View in parent view
     * this method is execute when type is signature pad. and it is set data in signature pad.
     * when you click on signature pad open view for draw signature and save it.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeSignaturePad(JSONObject object, ViewGroup adsLayout) {
        View sign = getLayoutInflater().inflate(R.layout.layout_signnature, adsLayout, false);
        LinearLayout llSign = sign.findViewById(R.id.llSign);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        setTitleOfComponent(tvSignatureTitle, object);
        String storePath = "";
        try {
            if (object.has(Constants.VALUE)) {
                JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                if (jsonArray.length() > 0) {
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    if (!TextUtils.isEmpty(jsonObject.getString(IMAGEPATHHIGH)))
                        storePath = jsonObject.getString(IMAGEPATHHIGH);
                    else
                        storePath = jsonObject.getString(IMAGEPATHLOW);

                    updateValidationInMap(object, tvSignatureTitle, "", false, true, true);
                    tvClear.setVisibility(View.VISIBLE);
                }
                String finalStorePath = storePath;
                tvClear.setOnClickListener(v -> {
                    clearFocus();
                    ImageUtil.deleteImageFromSDCard(finalStorePath);
                    updateValidationInMap(object, tvSignatureTitle, "", false, true, false);
                    String[] value = {};
                    try {
                        object.put(Constants.VALUE, new JSONArray(value));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    ivSign.setImageBitmap(null);
                    tvClear.setVisibility(View.GONE);
                    updateCountOnClearSignatureOrSketch();
                    try {
                        JSONObject jsonImageData;
                        FormData data;
                        TblTMForms tblTNMForms = new TblTMForms(context);
                        TblForms tblForms = new TblForms(context);
                        if (isTMForm()) {
                            data = tblTNMForms.getFormDataByPKId(formData.getFormPKId());
                        } else {
                            data = tblForms.getFormDataByPKId(formData.getFormPKId());
                        }
                        jsonImageData = new JSONObject(data.getImageData());

                        JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                        int index = -1;
                        for (int i = 0; i < jsonImageArray.length(); i++) {
                            JSONObject object1 = jsonImageArray.getJSONObject(i);
                            if ((int) llSign.getTag() == object1.getInt(Constants.TAGID)) {
                                index = i;
                            }
                        }
                        if (index != -1) {
                            jsonImageArray.remove(index);
                        }
                        if (isTMForm()) {
                            tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                        } else
                            tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
                ImageUtil.loadImageInGlide(ivSign, storePath);
            } else {
                String[] value = {};
                object.put(Constants.VALUE, new JSONArray(value));

            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setValidationInMap(object, tvSignatureTitle);
        mapSignature.put(tag, object);
        llSign.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        llSign.setOnClickListener(view -> {
            clearFocus();
            Drawable drawable = ivSign.getDrawable();
            int signTag = (int) llSign.getTag();
            NavigateToSignatureScreen(drawable, signTag, tvSignatureTitle);
        });
        adsLayout.addView(sign);
        addViewInList(sign);
        addSeparatorView(this, adsLayout);

    }

    private void NavigateToSignatureScreen(Drawable drawable, int signTag, TextView tvSignatureTitle) {
        boolean isSign = false;
        if (drawable != null && ((BitmapDrawable) drawable).getBitmap() != null) {
            isSign = true;
        }
        Intent intent = new Intent(BaseFormDetailActivity.this, SignatureActivity.class);
        intent.putExtra("tag", signTag);
        intent.putExtra("formId", formData.getFormId());
        intent.putExtra("isSign", isSign);
        intent.putExtra("isSketch", false);
        intent.putExtra("increaseCount", !isSign);
        tvSignatureTitle.setError(null);
        startActivityForResult(intent, SIGN_FOR_RESULT);
    }


    private void inputTypeSubHeader(JSONObject object, ViewGroup adsLayout) {
        View subHeader = getLayoutInflater().inflate(R.layout.layout_sub_header, adsLayout, false);
        subHeader.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        TextView tvSubHeader = subHeader.findViewById(R.id.tvSubHeader);
        setTitleOfComponent(tvSubHeader, object);
        adsLayout.addView(subHeader);
        addViewInList(subHeader);
        addSeparatorView(this, adsLayout);
    }

    /**
     * inputTypeDropdownMultiple is use for add Multichoice Dropdown in parent view
     * here you can click on list for select particular item.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeDropdownMultiple(JSONObject object, ViewGroup adsLayout) {
        View selectMultiple = getLayoutInflater().inflate(R.layout.layout_multiple_selection, adsLayout, false);
        LinearLayout llMultiSelect = selectMultiple.findViewById(R.id.llMultiSelect);
        llMultiSelect.setTag(tag);
        TextView tvMultiSelected = selectMultiple.findViewById(R.id.tvMultiSelected);
        addTagInMainObjectAndIncreaseTagId(object);
        setValidationInMap(object, tvMultiSelected);
        adsLayout.addView(llMultiSelect);
        addViewInList(llMultiSelect);
        addSeparatorView(this, adsLayout);
        ArrayList<ItemModel> lstItems = new ArrayList<>();
        TextView tvMultiSelectTitle = selectMultiple.findViewById(R.id.tvMultiSelectTitle);
        setTitleOfComponent(tvMultiSelectTitle, object);
        JSONArray valueJSONArray = new JSONArray();
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                Object data = object.get(Constants.VALUE);
                if (data instanceof String) {
                    valueJSONArray = StaticUtils.getArrayFromString((String) data);
                } else {
                    valueJSONArray = (JSONArray) data;
                }
                tvMultiSelected.setText(StaticUtils.getDataFromArray(valueJSONArray));
                updateValidationInMap(object, tvMultiSelected, tvMultiSelected.getText().toString());
            } else {
                object.put(Constants.VALUE, valueJSONArray);
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        llMultiSelect.setOnClickListener(view -> {
            clearFocus();
            if (!TextUtils.isEmpty(tvMultiSelected.getText().toString())) {
                for (ItemModel itemModel : lstItems) {
                    if (tvMultiSelected.getText().toString().contains(itemModel.getName())) {
                        itemModel.setSelected(true);
                    }
                }
            }
            tvMultiSelected.setError(null);
            PopUtils.showDialogForMultiSelectView(context, lstItems, (s, itemModel) -> {
                if (TextUtils.isEmpty(s))
                    tvMultiSelected.setText("");
                tvMultiSelected.setText(s);
                updateValidationInMap(object, tvMultiSelected, s);
                try {
                    object.put(Constants.VALUE, StaticUtils.getArrayFromString(s));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            });
        });

        if (object.has(Constants.CHOICES)) {
            try {
                JSONArray array = object.getJSONArray(Constants.CHOICES);
                for (int i = 0; i < array.length(); i++) {
                    ItemModel itemModel = new ItemModel();
                    itemModel.setName(array.getString(i));
                    lstItems.add(itemModel);
                }

            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }

    }

    /**
     * inputTypeSignaturePad is use for add Signature View in parent view
     * this method is execute when type is signature pad. and it is set data in signature pad.
     * when you click on signature pad open view for draw signature and save it.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeSketch(JSONObject object, ViewGroup adsLayout) {
        View sign = getLayoutInflater().inflate(R.layout.layout_signnature, adsLayout, false);
        LinearLayout llSign = sign.findViewById(R.id.llSign);
        ImageView ivSign = sign.findViewById(R.id.ivSign);
        TextView tvSignatureTitle = sign.findViewById(R.id.tvSignatureTitle);
        TextView tvClear = sign.findViewById(R.id.tvClear);
        setTitleOfComponent(tvSignatureTitle, object);
        String storePath = "";
        if (object.has(Constants.VALUE)) {
            try {
                JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                if (jsonArray.length() > 0) {
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    if (!TextUtils.isEmpty(jsonObject.getString(IMAGEPATHHIGH)))
                        storePath = jsonObject.getString(IMAGEPATHHIGH);
                    else
                        storePath = jsonObject.getString(IMAGEPATHLOW);

                    updateValidationInMap(object, tvSignatureTitle, "", false, true, true);
                    tvClear.setVisibility(View.VISIBLE);
                }
                String finalStorePath = storePath;
                tvClear.setOnClickListener(v -> {
                    clearFocus();
                    ImageUtil.deleteImageFromSDCard(finalStorePath);
                    updateValidationInMap(object, tvSignatureTitle, "", false, true, false);
                    String[] value = {};
                    try {
                        object.put(Constants.VALUE, new JSONArray(value));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    ivSign.setImageBitmap(null);
                    tvClear.setVisibility(View.GONE);
                    updateCountOnClearSignatureOrSketch();
                    try {
                        JSONObject jsonImageData;
                        TblForms tblForms = new TblForms(context);
                        TblTMForms tblTNMForms = new TblTMForms(context);
                        FormData data;
                        if (isTMForm()) {
                            data = tblTNMForms.getFormDataByPKId(formData.getFormPKId());
                        } else {
                            data = tblForms.getFormDataByPKId(formData.getFormPKId());
                        }
                        jsonImageData = new JSONObject(data.getImageData());

                        JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
                        int index = -1;
                        for (int i = 0; i < jsonImageArray.length(); i++) {
                            JSONObject object1 = jsonImageArray.getJSONObject(i);
                            if ((int) llSign.getTag() == object1.getInt(Constants.TAGID)) {
                                index = i;
                            }
                        }
                        if (index != -1) {
                            jsonImageArray.remove(index);
                        }
                        if (isTMForm()) {
                            tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                        } else
                            tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }

                });
                ImageUtil.loadImageInGlide(ivSign, storePath);
            } catch (JSONException e) {
            }
        } else {
            String[] value = {};
            try {
                object.put(Constants.VALUE, new JSONArray(value));
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        setValidationInMap(object, tvSignatureTitle);
        mapSignature.put(tag, object);
        llSign.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);

        llSign.setOnClickListener(view -> {
            clearFocus();
            Drawable drawable = ivSign.getDrawable();
            int signTag = (int) llSign.getTag();
            NavigateToSketchScreen(drawable, signTag, tvSignatureTitle, tvClear.getVisibility() == View.GONE);
        });

        adsLayout.addView(sign);
        addViewInList(sign);
        addSeparatorView(this, adsLayout);

    }  /*
     * Method to navigate sketch screen by tap on sign view.
     * */

    private void NavigateToSketchScreen(Drawable drawable, int signTag, TextView tvSignatureTitle, boolean increaseCount) {
        boolean isSign = false;
        if (drawable != null && ((BitmapDrawable) drawable).getBitmap() != null) {
            isSign = true;
        } else {
            AppPrefShared.putValue(String.valueOf(formData.getFormPKId()).concat(",").concat(String.valueOf(signTag)), "");
        }
        Intent intent = new Intent(BaseFormDetailActivity.this, SketchActivity.class);
        intent.putExtra("tag", signTag);
        intent.putExtra("isSign", isSign);
        intent.putExtra("formId", formData.getFormId());
        intent.putExtra("isSketch", true);
        intent.putExtra("increaseCount", increaseCount);

        intent.putExtra("pkId", formData.getFormPKId());
        tvSignatureTitle.setError(null);
        startActivityForResult(intent, SIGN_FOR_RESULT);
    }


    /**
     * inputTypeSpinner is used for to add Spinner in parent view
     * this method is executes when type is dropdown.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */

    @SuppressLint("ClickableViewAccessibility")
    private void inputTypeSpinner(JSONObject object, ViewGroup adsLayout, boolean shouldAdd, String parentValue) {
        View spinner = getLayoutInflater().inflate(R.layout.layout_spinner, adsLayout, false);
        LinearLayout llMultiSelect = spinner.findViewById(R.id.llMultiSelect);
        llMultiSelect.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);

        TextView tvMultiSelected = spinner.findViewById(R.id.tvMultiSelected);
        RadioButton rbSelection = spinner.findViewById(R.id.rbSelection);
        TextView tvMultiSelectTitle = spinner.findViewById(R.id.tvMultiSelectTitle);
        setValidationInMap(object, tvMultiSelected);
        try {
            if (object.has(CHOICEVALUE)) {
                if (!TextUtils.isEmpty(parentValue) && parentValue.equalsIgnoreCase(object.getString(CHOICEVALUE))) {
                    updateValidationInMap(object, tvMultiSelected, object.getString(VALUE));
                }
            }

        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
        ArrayList<ItemModel> lstItems = new ArrayList<>();
        setTitleOfComponent(tvMultiSelectTitle, object);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                tvMultiSelected.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, tvMultiSelected, tvMultiSelected.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
                setDefaultVehicleAndEquipmentValue(object);
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
            addCompoundDrawableWithView(tvMultiSelected, object.getString(Constants.VALUE));
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        tvMultiSelected.setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                Drawable drawableRight = tvMultiSelected.getCompoundDrawables()[2];
                if (drawableRight != null) {
                    if(shouldRemoveSelectedValue(motionEvent,tvMultiSelected,drawableRight)){
                        tvMultiSelected.setText("");
                        //Remove All sub view of selected spinner value
                        clearSubViewsOfRemovedSpinnerSelection(lstItems);
                        //Clear value and update validation
                        clearValue(adsLayout, object);
                        updateValidationInMap(object, tvMultiSelected, "");
                        try {
                            object.put(Constants.VALUE, "");
                            tvMultiSelected.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                            if (object.has(Constants.SELECTED_EQUIPMENTS)) {
                                object.put(Constants.SELECTED_EQUIPMENTS, "");
                            }
                            if (object.has(Constants.SELECTED_VEHICLES)) {
                                object.put(Constants.SELECTED_VEHICLES, "");
                            }
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                        return true;
                    }
                }
            }
            return false;
        });

        tvMultiSelected.setOnClickListener(view -> {
            clearFocus();
            try {
                // If  isFleetVehicleList is true then show vehicle selection separate view with search feature
                if (object.has(Constants.IS_FLEET_VEHICLELIST) && object.getBoolean(Constants.IS_FLEET_VEHICLELIST)) {
                    PopUtils.showDialogToSelectVehicle(this, object.getBoolean(Constants.IS_FLEET_VEHICLELIST), lstItems, (s, itemModel) -> {
                        doAfterValueIsSelectedForDropdownView(s, itemModel, lstItems, object, adsLayout, tvMultiSelected, 1);
                    });

                } else if (object.has(Constants.IS_FLEET_EQUIPMENTLIST) && object.getBoolean(Constants.IS_FLEET_EQUIPMENTLIST)) {
                    PopUtils.showDialogToSelectVehicle(this, object.getBoolean(Constants.IS_FLEET_EQUIPMENTLIST), lstItems, (s, itemModel) -> {
                        doAfterValueIsSelectedForDropdownView(s, itemModel, lstItems, object, adsLayout, tvMultiSelected, 2);
                    });
                } else {
                    PopUtils.showDialogFoSingleSelectView(context, lstItems, (s, itemModel) -> {
                        doAfterValueIsSelectedForDropdownView(s, itemModel, lstItems, object, adsLayout, tvMultiSelected, 0);
                    });
                }

                if (!TextUtils.isEmpty(tvMultiSelected.getText().toString())) {
                    for (ItemModel itemModel : lstItems) {
                        if (tvMultiSelected.getText().toString().equals(itemModel.getName())) {
                            itemModel.setSelected(true);
                        }
                    }
                }
                tvMultiSelected.setError(null);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }

        });


        if (object.has(Constants.CHOICES)) {
            try {
                JSONArray array = object.getJSONArray(Constants.CHOICES);
                //check if no choice for value or it is fleet components
                if (array.length() > 0 || object.has(Constants.IS_FLEET_VEHICLELIST) && object.getBoolean(Constants.IS_FLEET_VEHICLELIST)
                        || object.has(IS_FLEET_EQUIPMENTLIST) && object.getBoolean(Constants.IS_FLEET_EQUIPMENTLIST)) {
                    adsLayout.addView(spinner);
                    addViewInList(spinner);
                    addSeparatorView(this, adsLayout);
                }

                String value = null;
                if (object.has(VALUE)) {
                    value = object.getString(VALUE);
                }

                if (object.has(Constants.SELECT_FROM_ALL_ITEMS)) {
                    try {
                        if (object.has(Constants.IS_FLEET_VEHICLELIST) && object.getBoolean(Constants.IS_FLEET_VEHICLELIST)) {
                            TblVehicles tblVehicles = new TblVehicles(this);
                            List<Vehicles> lstVehicle = tblVehicles.getAllVehicles();
                            for (Vehicles vehicles : lstVehicle) {
                                ItemModel itemModel = new ItemModel();
                                itemModel.setName(vehicles.getSvVehicleLabel());
                                itemModel.setDataId(vehicles.getSvId());
                                lstItems.add(itemModel);
                            }
                        } else if (object.has(Constants.IS_FLEET_EQUIPMENTLIST) && object.getBoolean(Constants.IS_FLEET_EQUIPMENTLIST)) {
                            TblEquipment tblEquipment = new TblEquipment(this);
                            List<Equipments> lstEquipment = tblEquipment.getAllEquipments();
                            for (Equipments equipment : lstEquipment) {
                                ItemModel itemModel = new ItemModel();
                                itemModel.setName(equipment.getSeiEquipmentName());
                                itemModel.setDataId(equipment.getSeiId());
                                lstItems.add(itemModel);
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                } else {
                    for (int i = 0; i < array.length(); i++) {
                        JSONObject jsonObject = null;
                        try {
                            if (array.get(i) instanceof JSONObject) {
                                jsonObject = array.getJSONObject(i);
                            }
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                        if (jsonObject != null) {
                            if (jsonObject.has(Constants.ELEMENTS)) {
                                View linear = getLayoutInflater().inflate(R.layout.layout_linear, adsLayout, false);
                                LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                                JSONArray arrayElement = jsonObject.getJSONArray(Constants.ELEMENTS);
                                boolean shouldAddInValidation = false;
                                if (jsonObject.has(CHOICEVALUE)) {
                                    if (!TextUtils.isEmpty(value) && jsonObject.getString(CHOICEVALUE).equalsIgnoreCase(value)) {
                                        parentValue = value;
                                    }
                                    shouldAddInValidation = !TextUtils.isEmpty(tvMultiSelected.getText().toString()) && tvMultiSelected.getText().toString().equals(jsonObject.getString(CHOICEVALUE));
                                    jsonArrayRead(arrayElement, llSubview, i, -1, !TextUtils.isEmpty(tvMultiSelected.getText().toString()) && tvMultiSelected.getText().toString().equals(jsonObject.getString(CHOICEVALUE)), parentValue);
                                } else {
                                    jsonArrayRead(arrayElement, llSubview, i, -1, true, parentValue);
                                }
                                adsLayout.addView(linear);
                                addViewInList(linear);
                                jsonArrayRead(arrayElement, llSubview, shouldAddInValidation);

                                ItemModel itemModel = new ItemModel();
                                if (jsonObject.has(Constants.CHOICEVALUE)) {
                                    if (tvMultiSelected.getText().toString().equals(jsonObject.getString(Constants.CHOICEVALUE))) {
                                        llSubview.setVisibility(View.VISIBLE);
                                    }
                                    itemModel.setName(jsonObject.getString(Constants.CHOICEVALUE));
                                    itemModel.setJsonObject(jsonObject);
                                    updateImageUploadList(itemModel.getJsonObject(), object.getString(VALUE));

                                }
                                itemModel.setJsonObject(jsonObject);
                                itemModel.setLinearLayout(llSubview);
                                lstItems.add(itemModel);
                            }
                        } else {
                            ItemModel itemModel = new ItemModel();
                            itemModel.setName(array.getString(i));
                            lstItems.add(itemModel);
                        }
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        if (lstItems.size() == 1) {
            tvMultiSelected.setVisibility(View.GONE);
            rbSelection.setVisibility(View.VISIBLE);
            rbSelection.setText(lstItems.get(0).getName());
            try {
                if (!TextUtils.isEmpty(object.getString(Constants.VALUE))) {
                    rbSelection.setChecked(true);
                    updateValidationInMap(object, tvMultiSelected, rbSelection.getText().toString());
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            rbSelection.setOnCheckedChangeListener((buttonView, isChecked) -> {
                rbSelection.setError(null);
                clearFocus();
                if (isChecked) {
                    try {
                        object.put(Constants.VALUE, rbSelection.getText().toString());
                        updateValidationInMap(object, tvMultiSelected, rbSelection.getText().toString());
                        updateImageUploadList(lstItems.get(0).getJsonObject(), rbSelection.getText().toString());
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            });
        } else {
            tvMultiSelected.setVisibility(View.VISIBLE);
            rbSelection.setVisibility(View.GONE);
        }
    }

    private void clearSubViewsOfRemovedSpinnerSelection(List<ItemModel> lstItems) {
        for (ItemModel itemModel : lstItems) {
            if (itemModel.getLinearLayout() != null) {
                itemModel.getLinearLayout().setVisibility(View.GONE);
            }
        }
        for (ItemModel itemModel : lstItems) {
            if (itemModel.isSelected()) {
                if (itemModel.getLinearLayout() != null)
                    itemModel.getLinearLayout().setVisibility(View.VISIBLE);

            }
        }
    }


    private void updateDropdownItemSelectedValues(String selectedValue, DynamicDropDownItem itemModel, List<DynamicDropDownItem> lstItems, JSONObject object, ViewGroup adsLayout, TextView tvMultiSelected) {
        try {
            object.put(Constants.VALUE, itemModel.getId());
            object.put(Constants.VALUE_DISPLAY, selectedValue);
            updateValidationInMap(object, tvMultiSelected, selectedValue);
            tvMultiSelected.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        tvMultiSelected.setText(selectedValue);
    }


    private void doAfterValueIsSelectedForDropdownView(String selectedValue, ItemModel itemModel, List<ItemModel> lstItems, JSONObject object, ViewGroup adsLayout, TextView tvMultiSelected, int selectionType) {
        try {
            if (itemModel != null && itemModel.getJsonObject() != null) {
                for (ItemModel itemModel1 : lstItems) {
                    JSONArray array = itemModel1.getJsonObject().getJSONArray(Constants.ELEMENTS);
                    jsonArrayRead(array, itemModel.getLinearLayout(), false);
                    updateImageUploadList(itemModel1.getJsonObject(), selectedValue);
                }
                clearValue(adsLayout, object);
                readAndAddValidation(itemModel.getLinearLayout(), itemModel.getJsonObject());

            }
            object.put(Constants.VALUE, selectedValue);
            if (itemModel != null) {
                if (selectionType == 1) {
                    object.put(Constants.SELECTED_VEHICLES, itemModel.getDataId());
                } else if (selectionType == 2) {
                    object.put(Constants.SELECTED_EQUIPMENTS, itemModel.getDataId());
                }
            }
            updateValidationInMap(object, tvMultiSelected, selectedValue);
            tvMultiSelected.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        tvMultiSelected.setText(selectedValue);
    }

    private void clearValue(ViewGroup llSubview, JSONObject object) {
        readAndRemove(object, llSubview);
    }


    private void readAndRemove(JSONObject object, ViewGroup llSubview) {
        try {
            if (object.has(Constants.CHOICES)) {
                JSONArray array = object.getJSONArray(Constants.CHOICES);
                if (array.length() > 0) {
                    jsonArrayRead(array, llSubview, false);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private void inputTypeDynamicDropdown(JSONObject object, ViewGroup adsLayout) {
        View dynamicDropDown = getLayoutInflater().inflate(R.layout.layout_dynamic_dropdown, adsLayout, false);
        LinearLayout llDynamicDropDown = dynamicDropDown.findViewById(R.id.llDynamicDropDown);
        llDynamicDropDown.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        TextView tvSelected = dynamicDropDown.findViewById(R.id.tvSelected);
        TextView tvTitle = dynamicDropDown.findViewById(R.id.tvTitle);
        setValidationInMap(object, tvSelected);
        ArrayList<DynamicDropDownItem> lstItems = new ArrayList<>();
        setTitleOfComponent(tvTitle, object);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE_DISPLAY)) {
                tvSelected.setText(object.getString(Constants.VALUE_DISPLAY));
                updateValidationInMap(object, tvSelected, tvSelected.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
                object.put(Constants.VALUE_DISPLAY, "");
            }
            addCompoundDrawableWithView(tvSelected, object.getString(Constants.VALUE));
            lstItems = getDataForDynamicDropDownItem(lstItems, object);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        tvSelected.setOnTouchListener((view, motionEvent) -> removeSelectedValueFromSelectiveView(object, motionEvent, tvSelected));
        ArrayList<DynamicDropDownItem> finalLstItems = lstItems;
        tvSelected.setOnClickListener(view -> {
            clearFocus();
            PopUtils.showDialogToSelectDynamicDropDown(this, finalLstItems, (s, itemModel) -> {
                updateDropdownItemSelectedValues(s, itemModel, finalLstItems, object, adsLayout, tvSelected);
            });
            if (!TextUtils.isEmpty(tvSelected.getText().toString())) {
                for (DynamicDropDownItem itemModel : finalLstItems) {
                    if (tvSelected.getText().toString().equals(itemModel.getValue())) {
                        itemModel.setSelected(true);
                    }
                }
            }
            tvSelected.setError(null);

        });

        adsLayout.addView(dynamicDropDown);
        addViewInList(dynamicDropDown);
        addSeparatorView(this, adsLayout);

    }

    private ArrayList<DynamicDropDownItem> getDataForDynamicDropDownItem(ArrayList<DynamicDropDownItem> lstItems, JSONObject object) {
        try {
            String dropdownValueType = object.getString(DROPDOWN_VALUE_TYPE);
            TblDynamicDropdownItems tblDynamicDropdownItems = new TblDynamicDropdownItems(this);
            if (object.has(Constants.LIST_ITEMS)) {
                JSONArray array = object.getJSONArray(Constants.LIST_ITEMS);
                if (array.length() == 0) {
                    lstItems = tblDynamicDropdownItems.getDataByType(dropdownValueType);
                } else {
                    List<Long> lstIds = new ArrayList<>();
                    for (int i = 0; i < array.length(); i++) {
                        lstIds.add(array.getLong(i));
                    }
                    lstItems = tblDynamicDropdownItems.getDataByIdsType(lstIds, dropdownValueType);
                }
            } else {
                lstItems = tblDynamicDropdownItems.getDataByType(dropdownValueType);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return lstItems;
    }

    private boolean removeSelectedValueFromSelectiveView(JSONObject object, MotionEvent motionEvent, TextView tvSelected) {
        if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
            Drawable drawableRight = tvSelected.getCompoundDrawables()[2];
            if (drawableRight != null) {
                if ( shouldRemoveSelectedValue(motionEvent,tvSelected,drawableRight)) {
                    tvSelected.setText("");
                    updateValidationInMap(object, tvSelected, "");
                    try {
                        object.put(Constants.VALUE, "");
                        tvSelected.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    return true;
                }
            }
        }
        return false;

    }


    private void inputTypeService(JSONObject object, ViewGroup parentLayout) {
        View serviceView = getLayoutInflater().inflate(R.layout.layout_service, parentLayout, false);
        serviceView.setTag(tag);
        LinearLayout llServiceRoot = serviceView.findViewById(R.id.llServiceRoot);
        addTagInMainObjectAndIncreaseTagId(object);
        try {
            if (isTMForm()) {
                object.put(Constants.SITEID, String.valueOf(siteData.getSiteId()));
            } else {
                object.put(Constants.SITEID, "-2");
            }
            object.put(Constants.SF_ID, String.valueOf(formData.getFormId()));

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        String serviceType;
        parentLayout.addView(serviceView);
        addViewInList(serviceView);
        if (object.has("serviceType")) {
            TMService tMService;
            try {
                int serviceId = -1;
                if (object.has("service")) {
                    serviceId = object.getInt("service");
                }
                if (!object.has(Constants.TnMService)) {
                    TblServices tblServices = new TblServices(this);
                    tMService = tblServices.getAllServices(serviceId);
                    object.put(Constants.TnMService, new Gson().toJson(tMService));
                } else {
                    tMService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                }
                object.put(Constants.SERVICEID, tMService.getServiceID());
                object.put(Constants.TIMELOG, StaticUtils.getTimeLogsForServiceAsATimer(tMService));
                object.put(Constants.SERVICE_NAME, tMService.getServiceName());

                serviceType = object.getString("serviceType");
                if (serviceType.equalsIgnoreCase("AsDetail")) {
                    addServiceAsDetailView(parentLayout, object, llServiceRoot, tMService);
                } else if (serviceType.equalsIgnoreCase("AsTask")) {
                    addServiceAsTaskView(parentLayout, object, llServiceRoot, tMService);
                } else if (serviceType.equalsIgnoreCase("AsTimer")) {
                    addServiceAsTimerView(parentLayout, object, llServiceRoot, tMService);
                }
                if (object.has(Constants.ELEMENTS)) {
                    View linear = getLayoutInflater().inflate(R.layout.layout_linear, parentLayout, false);
                    LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                    JSONArray arrayElement = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayRead(arrayElement, llSubview, 0, -1, "");
                    jsonArrayRead(arrayElement, llSubview, false);
                    llServiceRoot.addView(llSubview);
                    addViewInList(llSubview);
                    addSeparatorView(this, llSubview);
                    setVisibilityOfServiceSubView(tMService, llSubview);
                }
                if (tMService.isCompleted()) {
                    setValidationMapForChild(true, object, llServiceRoot);
                }
                //We only set pre select feature for Detail and task for now
                if (formData.getIsCheckInOut() && isTMForm() && formData.isPreSelectServices() && object.has(Constants.PARAM_PRE_SELECT) & !serviceType.equalsIgnoreCase("AsTimer")) {
                    try {
                        if (object.getBoolean(Constants.PARAM_PRE_SELECT)) {
                            preSelectServiceArray.put(object);
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }

            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private void addServiceAsDetailView(ViewGroup parentLayout, JSONObject object, LinearLayout llServiceRoot, TMService tMService) {
        LinearLayout llSubview;
        View childView = null;
        LinearLayout viewPeople = (LinearLayout) getLayoutInflater().inflate(R.layout.layout_service_people, parentLayout, false);
        TextView tvTitle = viewPeople.findViewById(R.id.tvTitle);
        LinearLayout llPeopleView = viewPeople.findViewById(R.id.llPeopleHours);
        LinearLayout llSeparator = viewPeople.findViewById(R.id.llSeparator);
        if (!TextUtils.isEmpty(tMService.getServiceName()))
            tvTitle.setText(tMService.getServiceName());
        CheckBox cbItem = viewPeople.findViewById(R.id.cbItem);
        try {
            if (!TextUtils.isEmpty(tMService.getServiceName())) {
                setTitleOfComponent(tvTitle, object, tMService.getServiceName());
                setValidationForServiceInMap(object, !tMService.isCompleted(), tvTitle, tMService);
                object.put(Constants.TITLE, tMService.getServiceName());
            }
            cbItem.setChecked(tMService.isCompleted());
            object.put(Constants.PEOPLE, tMService.getPeople());
            object.put(Constants.HOUR, tMService.getHours());
            llSubview = llServiceRoot.findViewById(R.id.llSubView);
            setVisibilityOfServiceSubView(tMService, llSubview);
            setVisibilityOfServiceSubView(tMService, llPeopleView);
            if (llSubview != null)
                manageValidationForService(tMService, llSubview, object);
            if (!object.has(Constants.OPTIONS)) {
                object.put(Constants.OPTIONS, new JSONArray());
            }
            if (tMService.getServiceOptions() != null) {
                childView = setSegmentOrMultipleSelectionView(object, tMService, viewPeople, tvTitle);
                setVisibilityOfServiceSubView(tMService, childView);
                llSubview = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSubview);
                manageValidationForService(tMService, llSubview, object);

                //Show separator if view is visible else false
                if (cbItem.isChecked()) {
                    llSeparator.setVisibility(View.VISIBLE);
                } else {
                    llSeparator.setVisibility(View.GONE);
                }
            }

            View finalChildView = childView;
            updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
            cbItem.setOnCheckedChangeListener((compoundButton, b) -> {
                clearFocus();
                tMService.setCompleted(b);
                setVisibilityOfServiceSubView(tMService, llPeopleView);
                if (finalChildView != null) {
                    setVisibilityOfServiceSubView(tMService, finalChildView);

                }

                //Show separator if view is visible else false
                if (b) {
                    llSeparator.setVisibility(View.VISIBLE);
                } else {
                    llSeparator.setVisibility(View.GONE);
                }
                LinearLayout llSub = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSub);
                try {
                    object.put(Constants.TnMService, new Gson().toJson(tMService));
                    tvTitle.setError(null);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }

            });

            AppCompatTextView tvPeople = viewPeople.findViewById(R.id.tvPeople);
            AppCompatTextView tvHour = viewPeople.findViewById(R.id.tvHour);

            tvPeople.setText(tMService.getPeople());
            addCompoundDrawableWithView(tvPeople, tMService.getPeople());
            tvPeople.setOnTouchListener((view, motionEvent) -> {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    Drawable drawableRight = tvPeople.getCompoundDrawables()[2];
                    if (drawableRight != null) {
                        if (shouldRemoveSelectedValue(motionEvent, tvPeople, drawableRight)) {
                            clearPeopleData(validationMap, tMService, tvPeople, tvTitle, object);
                            return true;
                        }
                    }
                }
                return false;
            });

            addCompoundDrawableWithView(tvHour, tMService.getHours());

            tvHour.setOnTouchListener((view, motionEvent) -> {
                if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    Drawable drawableRight = tvHour.getCompoundDrawables()[2];
                    if (drawableRight != null) {
                        if ((motionEvent.getRawX() / 2 - StaticUtils.convertDpToPixels(20)) >= tvHour.getRight() - drawableRight.getBounds().width()) {
                            clearHourData(validationMap, tMService, tvHour, tvTitle, object);
                            return true;
                        }
                    }
                }
                return false;
            });


            tvHour.setText(String.valueOf(tMService.getHours()));

            tvPeople.setOnClickListener(v -> {
                ArrayList<ItemModel> lstData = new ArrayList<>();
                int selectedPosition = -1;
                for (int i = 0; i <= 50; i++) {
                    ItemModel itemModel = new ItemModel();
                    itemModel.setName(String.valueOf(i));
                    if (!TextUtils.isEmpty(tMService.getPeople()) && Integer.parseInt(tMService.getPeople()) == i) {
                        itemModel.setSelected(true);
                        selectedPosition = i;
                    }
                    lstData.add(itemModel);
                }
                PopUtils.showBottomViewForNumberSelection(BaseFormDetailActivity.this, lstData, selectedPosition, (s, itemModel) -> {
                    tvPeople.setText(s);
                    tvTitle.setError(null);
                    try {
                        addCompoundDrawableWithView(tvPeople, s);
                        tMService.setPeople(itemModel.getName());
                        object.put(Constants.TnMService, new Gson().toJson(tMService));
                        object.put(Constants.PEOPLE, tMService.getPeople());
                        updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
            });

            tvHour.setOnClickListener(v -> {
                ArrayList<ItemModel> lstData = new ArrayList<>();
                int selectedPosition = -1;
                for (int i = 0; i <= 96; i++) {
                    double d = 0.25 * i;
                    ItemModel itemModel = new ItemModel();
                    itemModel.setName(String.valueOf(d));
                    if (!TextUtils.isEmpty(tMService.getHours()) && Double.parseDouble(tMService.getHours()) == d) {
                        itemModel.setSelected(true);
                        selectedPosition = i;
                    }
                    lstData.add(itemModel);
                }
                PopUtils.showBottomViewForNumberSelection(BaseFormDetailActivity.this, lstData, selectedPosition, (s, itemModel) -> {
                    tvHour.setText(s);
                    tvTitle.setError(null);
                    try {
                        addCompoundDrawableWithView(tvHour, s);
                        tMService.setHours(itemModel.getName());
                        object.put(Constants.TnMService, new Gson().toJson(tMService));
                        object.put(Constants.HOUR, tMService.getHours());
                        updateServiceValidationInMap(validationMap, object, tvTitle, tMService);
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
            });
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        llServiceRoot.addView(viewPeople);
        addViewInList(viewPeople);
        addSeparatorView(this, viewPeople);
        try {
            if (formData.isPreSelectServices() && object.has(Constants.PARAM_PRE_SELECT) && object.getBoolean(Constants.PARAM_PRE_SELECT)) {
                addServiceViewInList(llServiceRoot, object);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void addServiceAsTaskView(ViewGroup adsLayout, JSONObject object, LinearLayout llServiceRoot, TMService tMService) {
        View childView = null;
        LinearLayout viewSingleCheckbox = (LinearLayout) getLayoutInflater().inflate(R.layout.layout_service_checkbox, adsLayout, false);
        TextView tvCheckboxItem = viewSingleCheckbox.findViewById(R.id.tvCheckboxItem);
        CheckBox cbItem = viewSingleCheckbox.findViewById(R.id.cbItem);
        cbItem.setChecked(tMService.isCompleted());
        if (tMService.getServiceOptions() != null) {
            childView = setSegmentOrMultipleSelectionView(object, tMService, viewSingleCheckbox, tvCheckboxItem);
        }
        try {
            if (!object.has(Constants.OPTIONS)) {
                object.put(Constants.OPTIONS, new JSONArray());
            }
            object.put(Constants.COMPLETED, tMService.isCompleted());
            object.put(Constants.SERVICEID, tMService.getServiceID());
            object.put(Constants.SERVICE_NAME, tMService.getServiceName());
            setVisibilityOfServiceSubView(tMService, childView);
            setValidationForServiceInMap(object, !tMService.isCompleted(), tvCheckboxItem, tMService);

            updateServiceValidationInMap(validationMap, object, tvCheckboxItem, tMService);
            manageValidationForService(tMService, viewSingleCheckbox, object);

            View finalChildView = childView;

            cbItem.setOnCheckedChangeListener((compoundButton, b) -> {
                tMService.setCompleted(b);
                try {
                    object.put(Constants.TnMService, new Gson().toJson(tMService));
                    object.put(Constants.COMPLETED, b);
                    tvCheckboxItem.setError(null);
                    updateServiceValidationInMap(validationMap, object, tvCheckboxItem, tMService);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
                setValidationMapForChild(b, object, llServiceRoot);
                setVisibilityOfServiceSubView(tMService, finalChildView);
                LinearLayout llSubview = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSubview);
                manageValidationForService(tMService, llSubview, object);
            });
            if (!TextUtils.isEmpty(tMService.getServiceName())) {
                tvCheckboxItem.setText(tMService.getServiceName());
                setTitleOfComponent(tvCheckboxItem, object, tMService.getServiceName());

                if (tMService.getServiceOptions() == null)
                    setValidationForServiceInMap(object, tMService.isCompleted(), tvCheckboxItem, tMService);
                else if (tMService.getServiceOptions().getOptionsValue() != null && !tMService.getServiceOptions().getOptionsValue().isEmpty()) {
                    setValidationForServiceInMap(object, tMService.isCompleted(), tvCheckboxItem, tMService);
                }
                object.put(Constants.TITLE, tMService.getServiceName());
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        llServiceRoot.addView(viewSingleCheckbox);
        addViewInList(viewSingleCheckbox);
        addSeparatorView(this, viewSingleCheckbox);
        try {
            if (formData.isPreSelectServices() && object.has(Constants.PARAM_PRE_SELECT) && object.getBoolean(Constants.PARAM_PRE_SELECT)) {
                addServiceViewInList(llServiceRoot, object);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void setValidationMapForChild(boolean isValidation, JSONObject object, LinearLayout llServiceRoot) {
        if (object.has(Constants.ELEMENTS)) {
            LinearLayout llSubview = llServiceRoot.findViewById(R.id.llSubView);
            JSONArray arrayElement;
            try {
                arrayElement = object.getJSONArray(Constants.ELEMENTS);
                jsonArrayReadService(arrayElement, llSubview, isValidation);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }

    }

    private void addServiceAsTimerView(ViewGroup adsLayout, JSONObject object, LinearLayout llServiceRoot, TMService tMService) {
        View childView = null;
        LinearLayout viewServiceTimer = (LinearLayout) getLayoutInflater().inflate(R.layout.layout_service_timer, adsLayout, false);
        Chronometer cmCurrentTime = viewServiceTimer.findViewById(R.id.cmCurrentTime);
        Chronometer cmTotalTime = viewServiceTimer.findViewById(R.id.cmTotalTime);
        TextView tvStartService = viewServiceTimer.findViewById(R.id.tvStartService);
        TextView tvServiceName = viewServiceTimer.findViewById(R.id.tvServiceName);
        LinearLayout llCurrentTime = viewServiceTimer.findViewById(R.id.llCurrentTime);
        tvServiceName.setText(tMService.getServiceName());
        try {
            object.put(Constants.SERVICEID, tMService.getServiceID());
            object.put(Constants.SERVICE_NAME, tMService.getServiceName());
            if (!object.has(Constants.OPTIONS)) {
                object.put(Constants.OPTIONS, new JSONArray());
            }
            if (tMService.getServiceOptions() != null) {
                childView = setSegmentOrMultipleSelectionView(object, tMService, viewServiceTimer, tvServiceName);
            }
            setTitleOfComponent(tvServiceName, object, tMService.getServiceName());
            object.put(Constants.TITLE, tMService.getServiceName());
            if (!object.has(Constants.START_TIME)) {
                object.put(Constants.START_TIME, 0);
                object.put(Constants.STOP_TIME, 0);
            }
            List<TimeLog> lstTimeLog = tMService.getLstTimeLog();
            if (tMService.isCompleted()) {
                if (tMService.getStartChronometerTime() > 0) {
                    cmCurrentTime.setBase(tMService.getStartChronometerTime());
                } else {
                    cmCurrentTime.setBase(SystemClock.elapsedRealtime());
                }
                changeVisibilityOfTotalTime(tMService, cmTotalTime, false);
                changeVisibilityOfCurrentTime(llCurrentTime, cmCurrentTime, false);
                tvStartService.setText(R.string.stop);
                setValidationForServiceInMap(object, !tMService.isCompleted(), tvServiceName, tMService);
            } else {
                tvStartService.setText(R.string.start);
                changeVisibilityOfCurrentTime(llCurrentTime, cmCurrentTime, true);
                changeVisibilityOfTotalTime(tMService, cmTotalTime, true);
            }

            setValidationForServiceInMap(object, lstTimeLog != null && lstTimeLog.size() >= 1, tvServiceName, tMService);
            setVisibilityOfServiceSubView(tMService, childView);
            View finalChildView = childView;
            tvStartService.setOnClickListener(v -> {
                clearFocus();
                List<TimeLog> lstTimeLog1 = tMService.getLstTimeLog();
                TimeLog timeLog = null;
                if (!lstTimeLog1.isEmpty()) {
                    timeLog = lstTimeLog1.get(lstTimeLog1.size() - 1);
                }
                //When you stop Timer
                if (tvStartService.getText().toString().equals(getString(R.string.stop))) {
                    tMService.setStopTime(System.currentTimeMillis() / 1000);
                    tMService.setUpdatedStopTime(System.currentTimeMillis() / 1000);
                    if (timeLog != null) {
                        timeLog.setStopTime(System.currentTimeMillis() / 1000);
                    }
                    changeVisibilityOfCurrentTime(llCurrentTime, cmCurrentTime, true);
                    changeVisibilityOfTotalTime(tMService, cmTotalTime, true);
                    if (siteData != null) {
                        setSiteAndFormBroadCrumbs(11, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService);
                    } else {
                        setSiteAndFormBroadCrumbs(11, -2, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService);
                    }
                    tMService.setCompleted(false);
                    tMService.setStartTime(0);
                    tMService.setStartChronometerTime(0);
                    tMService.setStopTime(0);
                    //Removed this when we add code to set validation for service component. Now we reset value when user start timer not at stop timer.
                   /* if (tMService.getServiceOptions() != null && tMService.getServiceOptions().getOptions().size() > 0) {
                        tMService.getServiceOptions().setOptionsValue(new ArrayList<>());
                        try {
                            object.put(Constants.OPTIONS, new JSONArray());
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }*/

                    tvStartService.setText(R.string.start);
                    try {
                        //object.put(Constants.START_TIME, 0);
                        object.put(Constants.STOP_TIME, System.currentTimeMillis() / 1000);
                        object.put(Constants.TnMService, new Gson().toJson(tMService));
                        setValidationForServiceInMap(object, true, tvServiceName, tMService);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                } else {
                    //When you start Timer
                    tMService.setCompleted(true);
                    cmCurrentTime.setBase(SystemClock.elapsedRealtime());
                    tMService.setStartChronometerTime(cmCurrentTime.getBase());
                    if (tMService.getInitialStartTime() == 0) {
                        tMService.setInitialStartTime(System.currentTimeMillis() / 1000);
                    }
                    tMService.setStartTime(System.currentTimeMillis() / 1000);
                    tvStartService.setText(R.string.stop);
                    timeLog = new TimeLog();
                    timeLog.setStartTime(System.currentTimeMillis() / 1000);
                    tMService.getLstTimeLog().add(timeLog);
                    changeVisibilityOfTotalTime(tMService, cmTotalTime, false);
                    changeVisibilityOfCurrentTime(llCurrentTime, cmCurrentTime, false);
                    if (tMService.getServiceOptions() != null && tMService.getServiceOptions().getOptions().size() > 0) {
                        tMService.getServiceOptions().setOptionsValue(new ArrayList<>());
                    }

                    if (siteData != null) {
                        setSiteAndFormBroadCrumbs(10, siteData.getSiteId(), formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService);
                    } else {
                        setSiteAndFormBroadCrumbs(10, -2, formData.getFormId(), formData.getFormName(), formData.getFormSubmissionId(), tMService);
                    }

                    tvServiceName.setError(null);
                    try {
                        object.put(Constants.START_TIME, tMService.getStartTime());
                        object.put(Constants.TnMService, new Gson().toJson(tMService));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    if (tMService.getServiceOptions() != null && tMService.getServiceOptions().getOptionsValue() != null && tMService.getServiceOptions().getOptionsValue().isEmpty()) {
                        resetSegmentOrMultipleSelectionView(tMService, viewServiceTimer);
                    }

                    setValidationForServiceInMap(object, true, tvServiceName, tMService);
                }
                setVisibilityOfServiceSubView(tMService, finalChildView);
                LinearLayout llSubview = llServiceRoot.findViewById(R.id.llSubView);
                setVisibilityOfServiceSubView(tMService, llSubview);
                manageValidationForService(tMService, llSubview, object);
            });
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        llServiceRoot.addView(viewServiceTimer);
        addViewInList(viewServiceTimer);
        addSeparatorView(this, viewServiceTimer);
    }

    private void manageValidationForService(TMService tMService, LinearLayout llSubview, JSONObject object) {
        if (tMService == null || llSubview == null)
            return;
        if (tMService.isCompleted()) {
            readAndAddValidation(llSubview, object);
        } else {
            try {
                if (object.has(Constants.ELEMENTS)) {
                    JSONArray arrayElement = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayRead(arrayElement, llSubview, false);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }


    /**
     * Method to manage visibility of people view based on checked parent option in ServiceType = AsDetail Type
     *
     * @param llPeopleView people view
     * @param divider      divider view
     * @param checked      true if checkbox is checked els false
     */
    private void setVisibilityOfChildOnCheckbox(LinearLayout llPeopleView, View divider, boolean checked) {
        if (checked) {
            divider.setVisibility(View.VISIBLE);
            llPeopleView.setVisibility(View.VISIBLE);

        } else {
            divider.setVisibility(View.GONE);
            llPeopleView.setVisibility(View.GONE);
        }
    }

    private void resetSegmentOrMultipleSelectionView(TMService service, LinearLayout parentView) {
        if (service.getServiceOptions().getMultiple().equalsIgnoreCase("true")) {
            LinearLayout llCheckbox = parentView.findViewById(R.id.llCheckbox);
            for (int i = 0; i < llCheckbox.getChildCount(); i++) {
                if ((llCheckbox.getChildAt(i)) instanceof CheckBox) {
                    ((CheckBox) llCheckbox.getChildAt(i)).setChecked(false);
                }
            }
            llCheckbox.invalidate();
        } else if (service.getServiceOptions().getMultiple().equalsIgnoreCase("false")) {
            LinearLayout llSegmentView = parentView.findViewById(R.id.llSegmentView);
            for (int i = 0; i < llSegmentView.getChildCount(); i++) {
                if ((llSegmentView.getChildAt(i)) instanceof AppCompatTextView) {
                    AppCompatTextView tvSelection = (AppCompatTextView) llSegmentView.getChildAt(i);
                    tvSelection.setBackgroundColor(getResources().getColor(R.color.transparent));
                    tvSelection.setTextColor(getResources().getColor(R.color.black_font));
                }
            }
            llSegmentView.invalidate();
        }
    }


    private View setSegmentOrMultipleSelectionView(JSONObject object, TMService
            service, LinearLayout parentView, TextView tvTitle) {

        View returnView = null;
        if (service.getServiceOptions().getMultiple().equalsIgnoreCase("true")) {
            View viewMultipleCheckbox = getLayoutInflater().inflate(R.layout.layout_service_multiple_checkbox, parentView, false);
            LinearLayout llCheckbox = viewMultipleCheckbox.findViewById(R.id.llCheckbox);
            List<String> lstValue = service.getServiceOptions().getOptionsValue();
            if (lstValue == null) {
                lstValue = new ArrayList<>();
            }
            JSONArray optionArray = new JSONArray();
            try {
                object.put(Constants.OPTIONS, optionArray);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            for (String option : service.getServiceOptions().getOptions()) {
                CheckBox checkBox = new CheckBox(this);
                checkBox.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.text_size_xlarge));
                checkBox.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.colorPrimary)));

                if (lstValue.contains(option)) {
                    checkBox.setChecked(true);
                    optionArray.put(option);
                }
                checkBox.setText(option);
                List<String> finalLstValue = lstValue;
                checkBox.setOnCheckedChangeListener((buttonView, isChecked) -> {
                    if (isChecked) {
                        finalLstValue.add(checkBox.getText().toString());
                        optionArray.put(option);

                    } else {
                        finalLstValue.remove(checkBox.getText().toString());
                        int length = optionArray.length();
                        for (int i = 0; i < length; i++) {
                            try {
                                if (optionArray.get(i).equals(checkBox.getText().toString())) {
                                    optionArray.remove(i);
                                }
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                        }
                    }
                    service.getServiceOptions().setOptionsValue(finalLstValue);
                    try {
                        updateServiceValidationInMap(validationMap, object, tvTitle, service);
                        tvTitle.setError(null);
                        object.put(Constants.TnMService, new Gson().toJson(service));
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }

                });
                llCheckbox.addView(checkBox);
            }
            parentView.addView(viewMultipleCheckbox);
            returnView = viewMultipleCheckbox;
        } else if (service.getServiceOptions().getMultiple().equalsIgnoreCase("false")) {
            View viewRadioGroup = getLayoutInflater().inflate(R.layout.layout_service_segment, parentView, false);

            LinearLayout llSegmentView = viewRadioGroup.findViewById(R.id.llSegmentView);

            List<String> lstValue;

            lstValue = service.getServiceOptions().getOptionsValue();

            if (lstValue == null) {
                lstValue = new ArrayList<>();
            }

            JSONArray optionArray = new JSONArray();
            try {
                object.put(Constants.OPTIONS, optionArray);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            ArrayList<SegmentData> lstSegmentData = new ArrayList<>();
            for (String option : service.getServiceOptions().getOptions()) {
                View textView = getLayoutInflater().inflate(R.layout.item_radiogroup_segment, llSegmentView, false);
                View view = getLayoutInflater().inflate(R.layout.item_saprator_segment, llSegmentView, false);
                View linear = getLayoutInflater().inflate(R.layout.layout_linear, llSegmentView, false);
                LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                TextView tvSelection = textView.findViewById(R.id.cbSelection);
                tvSelection.setText(option);
                SegmentData segmentData = new SegmentData();
                segmentData.setLinearLayout(llSubview);
                segmentData.setTextView(tvSelection);
                segmentData.setTvTag(option);
                tvSelection.setTag(option);
                lstSegmentData.add(segmentData);
                for (SegmentData segmentData1 : lstSegmentData) {
                    if (lstValue.contains(option)) {
                        StaticUtils.checkAndAddInArray(optionArray, option);
                        setVisibilityOfSegmentView(object, option, lstValue, tvSelection, segmentData1, optionArray, false);

                    }
                }

                List<String> finalLstValue = lstValue;
                tvSelection.setOnClickListener(v -> {
                    for (SegmentData segmentData1 : lstSegmentData) {
                        setVisibilityOfSegmentView(object, option, finalLstValue, tvSelection, segmentData1, optionArray, true);
                    }
                    service.getServiceOptions().setOptionsValue(finalLstValue);
                    try {
                        updateServiceValidationInMap(validationMap, object, tvTitle, service);
                        tvTitle.setError(null);
                        object.put(Constants.TnMService, new Gson().toJson(service));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                });
                llSegmentView.addView(tvSelection);
                llSegmentView.addView(view);
            }

            parentView.addView(viewRadioGroup);
            returnView = viewRadioGroup;
        }
        return returnView;
    }


    private void setVisibilityOfSegmentView(JSONObject object, String option, List<String> lstValue,
                                            TextView tvSelection, SegmentData segmentData1, JSONArray optionArray, boolean isSelect) {
        if (isSelect) {
            if (tvSelection == segmentData1.getTextView()) {
                tvSelection.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
                tvSelection.setTextColor(getResources().getColor(R.color.white));
                if (!lstValue.contains(tvSelection.getText().toString().trim())) {
                    lstValue.add(tvSelection.getText().toString().trim());
                    try {
                        StaticUtils.checkAndAddInArray(optionArray, tvSelection.getText().toString().trim());
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            } else {
                segmentData1.getTextView().setBackgroundColor(getResources().getColor(R.color.transparent));
                segmentData1.getTextView().setTextColor(getResources().getColor(R.color.black_font));
                segmentData1.getLinearLayout().setVisibility(View.GONE);
                lstValue.remove(segmentData1.getTvTag());
                try {
                    StaticUtils.removeSelectedDataFromArray(optionArray, segmentData1.getTvTag());
                    object.put(Constants.OPTIONS, optionArray);
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } else {
            if (tvSelection.getTag() == option) {
                tvSelection.setBackgroundColor(getResources().getColor(R.color.colorPrimary));
                tvSelection.setTextColor(getResources().getColor(R.color.white));
                if (!lstValue.contains(option)) {
                    lstValue.add(option);
                    try {
                        StaticUtils.checkAndAddInArray(optionArray, option);
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            } else {
                segmentData1.getTextView().setBackgroundColor(getResources().getColor(R.color.transparent));
                segmentData1.getTextView().setTextColor(getResources().getColor(R.color.black_font));
                segmentData1.getLinearLayout().setVisibility(View.GONE);
                lstValue.remove(option);
                if (lstValue.size() == 0) {
                    try {
                        StaticUtils.removeSelectedDataFromArray(optionArray, option);
                        object.put(Constants.OPTIONS, optionArray);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
            }
        }
    }

    /**
     * inputTypeAddress is use for add Address in parent view
     * this method is execute when type is Address.
     *
     * @param object     is json object use for get data from object
     * @param adsLayout  is view group and work as parent layout
     * @param pageNumber int current pageNumber
     */
    private void inputTypeAddress(JSONObject object, ViewGroup adsLayout, int pageNumber) {
        View componentView = getLayoutInflater().inflate(R.layout.layout_address, adsLayout, false);
        componentView.setTag(tag);
        mapTagObject.put(tag, object);
        addTagInMainObjectAndIncreaseTagId(object);

        TextView tvAddressTitle = componentView.findViewById(R.id.tvAddressTitle);
        AppCompatImageView ivAddAddress = componentView.findViewById(R.id.ivAddAddress);
        TextView tvAddress = componentView.findViewById(R.id.tvAddress);
        setTitleOfComponent(tvAddressTitle, object);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                if (!TextUtils.isEmpty(object.getString(Constants.VALUE))) {
                    tvAddress.setVisibility(View.VISIBLE);
                    tvAddress.setText(object.getString(Constants.VALUE));
                } else {
                    tvAddress.setVisibility(View.GONE);
                }
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setValidationInMap(object, tvAddressTitle);


        tvAddressTitle.setOnClickListener(v -> ivAddAddress.performClick());
        ivAddAddress.setOnClickListener(v -> navigateUserToAddPropertyScreen((int) componentView.getTag()));

        adsLayout.addView(componentView);
        addViewInList(componentView);
        addSeparatorView(this, adsLayout);

    }

    void updateAddressData(Bundle extraBundle, ViewFlipper vfPages) {
        int tagId = extraBundle.getInt(Constants.TAG_ID);
        String address = extraBundle.getString(Constants.KEY_INTENT_ADDRESS);
        String state = extraBundle.getString(Constants.PARAM_STATE);
        double lat = Double.parseDouble(extraBundle.getString(Constants.PARAM_LAT));
        double lon = Double.parseDouble(extraBundle.getString(Constants.PARAM_LON));
        String zip = extraBundle.getString(Constants.PARAM_ZIP);
        String streetAddress = extraBundle.getString(Constants.PARAM_STREET_ADDRESS);
        String city = extraBundle.getString(Constants.PARAM_CITY);
        JSONObject jsonObject = (JSONObject) mapTagObject.get(tagId);

        View componentView = vfPages.findViewWithTag(tagId);
        TextView tvAddressTitle = componentView.findViewById(R.id.tvAddressTitle);
        TextView tvAddress = componentView.findViewById(R.id.tvAddress);
        if (jsonObject != null && tvAddress != null && tvAddressTitle != null) {
            tvAddress.setVisibility(View.VISIBLE);
            tvAddress.setText(address);
            tvAddressTitle.setError(null);
            updateValidationInMap(jsonObject, tvAddressTitle, "", false, true, !TextUtils.isEmpty(address));
            try {
                clearAddressData(jsonObject);
                jsonObject.put(Constants.VALUE, address);
                jsonObject.put(Constants.PARAM_STATE, state);
                jsonObject.put(Constants.PARAM_LAT, lat);
                jsonObject.put(Constants.PARAM_LON, lon);
                jsonObject.put(Constants.PARAM_ADDRESS_NAME, "");
                jsonObject.put(Constants.PARAM_ZIP, zip);
                jsonObject.put(Constants.PARAM_STREET_ADDRESS, streetAddress);
                jsonObject.put(Constants.PARAM_CITY, city);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private void clearAddressData(JSONObject jsonObject) {
        try {
            jsonObject.put(Constants.VALUE, "");
            jsonObject.put(Constants.PARAM_STATE, "");
            jsonObject.put(Constants.PARAM_LAT, 0.0);
            jsonObject.put(Constants.PARAM_LON, 0.0);
            jsonObject.put(Constants.PARAM_ADDRESS_NAME, "");
            jsonObject.put(Constants.PARAM_ZIP, "");
            jsonObject.put(Constants.PARAM_STREET_ADDRESS, "");
            jsonObject.put(Constants.PARAM_CITY, "");
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void navigateUserToAddPropertyScreen(int tagId) {
        if (canGetLocation(BaseFormDetailActivity.this)) {
            navigateToPropertyScreenFromForm(tagId, Constants.ADD_ADDRESS_ACTIVITY_REQUEST);
        } else {
            if (!isGpsEnableCalled) {
                showGPSSettingAlert();
            }
        }

    }

    private void inputTypeMaterial(JSONObject object, ViewGroup adsLayout) {
        if (object.has(Constants.MATERIAL)) {
            try {
                if (object.get(Constants.MATERIAL) instanceof JSONArray) {
                    JSONArray materialObject = object.getJSONArray(Constants.MATERIAL);
                    addRepeatableInMaterial(object, adsLayout, materialObject);
                    return;
                } else if (object.get(Constants.MATERIAL) instanceof String && object.getString(Constants.MATERIAL).equalsIgnoreCase("[]")) {
                    JSONArray materialObject = new JSONArray();
                    addRepeatableInMaterial(object, adsLayout, materialObject);
                    return;
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            try {
                if (object.get(Constants.MATERIAL) instanceof JSONArray) {
                    JSONArray materialObject = object.getJSONArray(Constants.MATERIAL);
                    if (materialObject != null) {
                        addRepeatableInMaterial(object, adsLayout, materialObject);
                        return;
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        View commentView = getLayoutInflater().inflate(R.layout.layout_material, adsLayout, false);
        commentView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        Material material = null;
        TblMaterials tblMaterials = new TblMaterials(this);

        if (object.has(Constants.MATERIAL)) {
            try {
                if (object.has(Constants.MATERIAL_DATA)) {
                    material = new Gson().fromJson(object.getString(Constants.MATERIAL_DATA), Material.class);
                }
                if (material == null) {
                    try {
                        if (object.get(Constants.MATERIAL) instanceof String)
                            material = tblMaterials.getMaterialsByID(object.getInt(Constants.MATERIAL));
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    object.put(Constants.MATERIAL_DATA, new Gson().toJson(material));
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }

        TextView tvMaterialTitle = commentView.findViewById(R.id.tvMaterialTitle);
        TextView tvMaterialName = commentView.findViewById(R.id.tvMaterialName);
        TextView tvUnit = commentView.findViewById(R.id.tvUnit);
        CustomEditText edtQty = commentView.findViewById(R.id.edtQty);
        setEditTextListeners(edtQty, false);

        try {
            if (material != null) {
                setValidationInMap(object, tvMaterialName, material.getMaterialName());
                try {
                    object.put(Constants.MATERIAL_DATA, new Gson().toJson(material));

                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
                tvMaterialName.setText(material.getMaterialName());
                tvUnit.setText(material.getMaterialUnit());
                setTitleOfComponent(tvMaterialTitle, object, material.getMaterialName());
                object.put(Constants.UNIT, material.getMaterialUnit());
                object.put(Constants.TITLE, material.getMaterialName());
                if (material.getUsage() > 0.0)
                    object.put(Constants.QUANTITY, material.getUsage());
                else
                    object.put(Constants.QUANTITY, "");
            }
            if (object.has(Constants.ISREQUIRED)) {
                if (object.getBoolean(Constants.ISREQUIRED)) {
                    if (material != null)
                        tvMaterialName.setText("* ".concat(material.getMaterialName()));
                    else
                        tvMaterialName.setText("* ");
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            if (object.has(Constants.VALUE)) {
                edtQty.setText(object.getString(Constants.VALUE));
                if (edtQty.getText() != null)
                    updateValidationInMap(object, edtQty, edtQty.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        setFocusChangeListenerForEditText(edtQty);
        Material finalMaterial = material;
        edtQty.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {
                try {
                    if (edtQty.getText() != null) {
                        String qty = edtQty.getText().toString().trim();
                        if (edtQty.getText().length() > 0 && TextUtils.isEmpty(qty)) {
                            edtQty.setText("");
                            return;
                        }

                        updateValidationInMap(object, tvMaterialName, qty);
                        object.put(Constants.VALUE, edtQty.getText().toString());
                        if (finalMaterial != null) {
                            if (TextUtils.isEmpty(qty)) {
                                finalMaterial.setUsage(0.0);
                                object.put(Constants.QUANTITY, "");
                            } else {
                                try {
                                    finalMaterial.setUsage(Double.parseDouble(qty));
                                } catch (NumberFormatException e) {
                                    FirebaseEventUtils.logException(e);
                                }
                                object.put(Constants.QUANTITY, finalMaterial.getUsage());
                            }
                        }
                        object.put(Constants.MATERIAL_DATA, new Gson().toJson(finalMaterial));
                        tvMaterialName.setError(null);
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });
        adsLayout.addView(commentView);
        addViewInList(commentView);
    }

    private void addRepeatableInMaterial(JSONObject object, ViewGroup adsLayout, JSONArray materialObject) {
        JSONArray jsonArray;
        jsonArray = new JSONArray();

        View commentView = getLayoutInflater().inflate(R.layout.layout_material_new, adsLayout, false);
        commentView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        LinearLayout llMaterial = commentView.findViewById(R.id.llMeterial);
        addSeparatorView(this, llMaterial);
        try {
            if (object.has(Constants.VALUE)) {
                jsonArray = object.getJSONArray(Constants.VALUE);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    addChildInMaterial(jsonObject, llMaterial, materialObject, jsonArray);
                }
            } else {
                object.put(Constants.VALUE, jsonArray);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        AppCompatTextView tvMaterialTitle = commentView.findViewById(R.id.tvMaterialTitle);
        AppCompatImageView ivNewGroup = commentView.findViewById(R.id.ivNewGroup);

        setTitleOfComponent(tvMaterialTitle, object);
        JSONArray finalJsonArray = jsonArray;
        ivNewGroup.setOnClickListener(v ->
        {
            JSONObject jsonObject = new JSONObject();
            finalJsonArray.put(jsonObject);
            try {
                jsonObject.put(Constants.TYPE, object.getString(Constants.TYPE));
                jsonObject.put(Constants.ISREQUIRED, true);
                jsonObject.put(Constants.NAME, object.getString(Constants.NAME));
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            addChildInMaterial(jsonObject, llMaterial, materialObject, finalJsonArray);
        });

        adsLayout.addView(commentView);
        addViewInList(commentView);


    }

    private void addChildInMaterial(JSONObject object, LinearLayout llMaterial, JSONArray materialObject, JSONArray valueJsonArray) {

        View commentView = getLayoutInflater().inflate(R.layout.layout_material_child, llMaterial, false);
        commentView.setTag(tag);
        AppCompatImageView ivDelete = commentView.findViewById(R.id.ivDelete);
        Material materialMain = new Material();
        lstChildMaterial.add(materialMain);

        AppCompatImageView ivClear = commentView.findViewById(R.id.ivClear);
        CustomEditText edtQty = commentView.findViewById(R.id.edtQty);
        AppCompatTextView tvMaterialName = commentView.findViewById(R.id.tvMaterialName);
        setValidationForMaterial(object, false, tvMaterialName);
        setValidationInMap(object, tvMaterialName);
        AppCompatTextView tvUnit = commentView.findViewById(R.id.tvUnit);
        setEditTextListeners(edtQty, false);
        try {

            if (object.has(Constants.TITLE)) {
                tvMaterialName.setText(object.getString(Constants.TITLE));
                materialMain.setMaterialName(object.getString(Constants.TITLE));
                if (!TextUtils.isEmpty(materialMain.getMaterialName()))
                    ivClear.setVisibility(View.VISIBLE);
            }
            if (object.has(Constants.UNIT)) {
                tvUnit.setText(object.getString(Constants.UNIT));
                materialMain.setMaterialUnit(object.getString(Constants.UNIT));
            }
            if (object.has(Constants.QUANTITY)) {
                edtQty.setText(object.getString(Constants.QUANTITY));
            }
            if (!TextUtils.isEmpty(edtQty.getText().toString()) && !TextUtils.isEmpty(materialMain.getMaterialName())) {
                setValidationForMaterial(object, true, tvMaterialName);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        if (TextUtils.isEmpty(tvMaterialName.getText().toString())) {
            edtQty.setEnabled(false);
        } else {
            edtQty.setEnabled(true);
        }
        AtomicInteger selectedPosition = new AtomicInteger();
        selectedPosition.set(-1);
        edtQty.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                try {
                    if (!TextUtils.isEmpty(edtQty.getText().toString()) && !TextUtils.isEmpty(materialMain.getMaterialName())) {
                        tvMaterialName.setError(null);
                        setValidationForMaterial(object, true, tvMaterialName);
                    } else {
                        setValidationForMaterial(object, false, tvMaterialName);
                    }
                    object.put(Constants.QUANTITY, edtQty.getText().toString());

                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });
        TblMaterials tblMaterials = new TblMaterials(this);
        List<Material> lstItems;
        if (materialObject.length() == 0) {
            lstItems = tblMaterials.getAllMaterials();
        } else {
            lstItems = new ArrayList<>();
            for (int i = 0; i < materialObject.length(); i++) {
                try {
                    lstItems.add(tblMaterials.getMaterialsByID(materialObject.getInt(i)));
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
        ivClear.setOnClickListener(v -> {
            tvMaterialName.setText("");
            materialMain.setMaterialName("");
            ivClear.setVisibility(View.GONE);
            //clear qty data if any and enable it because we removed material here
            edtQty.setText("");
            tvUnit.setText("");
            hideSoftKeyboard(this);
            edtQty.setEnabled(false);
            selectedPosition.set(-1);
            setValidationForMaterial(object, false, tvMaterialName);
            try {
                object.put(Constants.TITLE, "");
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        });

        tvMaterialName.setOnClickListener(v -> {
            if (!TextUtils.isEmpty(edtQty.getText().toString().trim())) {
                edtQty.setText("");
                hideSoftKeyboard(this);
                edtQty.setEnabled(false);
                tvUnit.setText("");
            }

            if (!lstItems.isEmpty()) {
                showBottomViewForMaterialSelect(BaseFormDetailActivity.this, selectedPosition.get(), lstItems, position -> {
                    Material material = lstItems.get(position);
                    ivClear.setVisibility(View.VISIBLE);
                    materialMain.setMaterialUnit(material.getMaterialUnit());
                    materialMain.setMaterialName(material.getMaterialName());
                    materialMain.setMaterialID(material.getMaterialID());
                    tvMaterialName.setError(null);
                    setValidationForMaterial(object, !TextUtils.isEmpty(edtQty.getText().toString()) && !TextUtils.isEmpty(materialMain.getMaterialName()), tvMaterialName);
                    selectedPosition.set(position);
                    try {
                        object.put(Constants.TITLE, material.getMaterialName());
                        object.put(Constants.UNIT, material.getMaterialUnit());
                        object.put(Constants.MATERIAL, material.getMaterialID());
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    tvUnit.setText(material.getMaterialUnit());
                    tvMaterialName.setText(material.getMaterialName());
                    if (!TextUtils.isEmpty(tvMaterialName.getText().toString().trim())) {
                        edtQty.setEnabled(true);
                    }
                });
            }

        });
        ivDelete.setOnClickListener(v -> {
            setValidationForMaterial(object, true, tvMaterialName);

            if (TextUtils.isEmpty(tvMaterialName.getText())) {
                int position = -1;
                try {
                    for (int i = 0; i < valueJsonArray.length(); i++) {
                        JSONObject jsonObject = valueJsonArray.getJSONObject(i);
                        if (jsonObject == object) {
                            position = i;
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
                if (position != -1) {
                    valueJsonArray.remove(position);
                    llMaterial.removeView(commentView);
                    lstChildMaterial.remove(materialMain);
                }
            } else {
                PopUtils.showDialogForRemoveMaterial(this, v1 -> {
                    int position = -1;
                    try {
                        for (int i = 0; i < valueJsonArray.length(); i++) {
                            JSONObject jsonObject = valueJsonArray.getJSONObject(i);
                            if (jsonObject == object) {
                                position = i;
                            }
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    valueJsonArray.remove(position);
                    llMaterial.removeView(commentView);
                    lstChildMaterial.remove(materialMain);
                });
            }

        });
        llMaterial.addView(commentView);
    }

    /**
     * inputTypeCheckBox is use for add checkbox in parent view
     * this method is execute when type is Checkbox. and it is set data in checkbox.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeCheckBox(JSONObject object, ViewGroup adsLayout, int parentPanelTag, boolean shouldAdd, String parentValue) {

        View checkbox = getLayoutInflater().inflate(R.layout.layout_checkbox, adsLayout, false);
        LinearLayout llCheckbox = checkbox.findViewById(R.id.llCheckbox);
        llCheckbox.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);

        TextView tvCheckboxTitle = checkbox.findViewById(R.id.tvCheckboxTitle);
        setTitleOfComponent(tvCheckboxTitle, object);
        setValidationInMap(object, tvCheckboxTitle);
        adsLayout.addView(checkbox);
        addViewInList(checkbox);
        addSeparatorView(this, adsLayout);
        if (object.has(Constants.CHOICES)) {
            try {
                JSONArray array = object.getJSONArray(Constants.CHOICES);
                for (int i = 0; i < array.length(); i++) {
                    JSONObject jsonObject = null;
                    try {
                        if (array.get(i) instanceof JSONObject) {
                            jsonObject = array.getJSONObject(i);
                        }
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    if (jsonObject != null) {
                        if (jsonObject.has(Constants.ELEMENTS)) {
                            View linear = getLayoutInflater().inflate(R.layout.layout_linear, adsLayout, false);
                            LinearLayout llSubview = linear.findViewById(R.id.llSubView);
                            JSONArray arrayElement = jsonObject.getJSONArray(Constants.ELEMENTS);
                            String valueSelectedData = "";
                            JSONArray valueJSONArray = new JSONArray();
                            updateAutoFillDataInView(object);
                            if (object.has(Constants.VALUE)) {
                                Object data = object.get(Constants.VALUE);
                                if (data instanceof String) {
                                    valueJSONArray = StaticUtils.getArrayFromString((String) data);
                                } else {
                                    valueJSONArray = (JSONArray) data;
                                }
                                valueSelectedData = StaticUtils.getDataFromArray(valueJSONArray);

                                updateValidationInMap(object, tvCheckboxTitle, valueSelectedData);

                            } else {
                                object.put(Constants.VALUE, valueJSONArray);
                            }
                            if (jsonObject.has(CHOICEVALUE)) {
                                jsonArrayRead(arrayElement, llSubview, i, -1, checkSelectedValueInList(StaticUtils.getStringArrayFromString(valueSelectedData), jsonObject.getString(CHOICEVALUE)), parentValue);
                            } else {
                                jsonArrayRead(arrayElement, llSubview, i, -1, true, parentValue);
                            }
                            jsonArrayRead(arrayElement, llSubview, shouldAdd);
                            clearValue(llSubview, object);
                            CheckBox cbChoice = new CheckBox(BaseFormDetailActivity.this);
                            cbChoice.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.text_size_xlarge));
                            cbChoice.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.colorPrimary)));
                            llCheckbox.addView(cbChoice);
                            JSONObject finalJsonObject = jsonObject;
                            JSONArray finalValueJSONArray = valueJSONArray;
                            cbChoice.setOnCheckedChangeListener((compoundButton, b) -> {
                                tvCheckboxTitle.setError(null);
                                clearFocus();
                                if (b) {
                                    llSubview.setVisibility(View.VISIBLE);
                                    readAndAddValidation(llSubview, finalJsonObject);

                                } else {
                                    llSubview.setVisibility(View.GONE);
                                    try {
                                        jsonArrayRead(arrayElement, llSubview, false);
                                    } catch (Exception e) {
                                        FirebaseEventUtils.logException(e);
                                    }
                                }
                                tvCheckboxTitle.setError(null);
                                String valueData;
                               /* JSONArray valueJSONArray = new JSONArray();
                                if (object.has(Constants.VALUE)) {
                                    try {
                                        Object data = object.get(Constants.VALUE);
                                        if (data instanceof String) {
                                            valueJSONArray = StaticUtils.getArrayFromString((String) data);
                                        } else {
                                            valueJSONArray = (JSONArray) data;
                                        }
                                    } catch (JSONException e) {
                                        FirebaseEventUtils.logException(e);
                                    }
                                }*/
                                if (b) {
                                    //valueJSONArray.put(cbChoice.getText().toString());
                                    StaticUtils.checkAndAddInArray(finalValueJSONArray, cbChoice.getText().toString());
                                } else {
                                    StaticUtils.removeSelectedDataFromArray(finalValueJSONArray, cbChoice.getText().toString());
                                }
                                valueData = StaticUtils.getDataFromArray(finalValueJSONArray);
                                try {
                                    updateValidationInMap(object, tvCheckboxTitle, valueData);
                                    object.put(Constants.VALUE, finalValueJSONArray);
                                    if (checkSelectedValueInList(StaticUtils.getStringArrayFromString(valueData), finalJsonObject.getString(CHOICEVALUE))) {
                                        updateImageUploadList(finalJsonObject, finalJsonObject.getString(CHOICEVALUE));
                                    } else {
                                        updateImageUploadList(finalJsonObject, "");
                                    }

                                } catch (JSONException e) {
                                    FirebaseEventUtils.logException(e);
                                }
                            });

                            if (jsonObject.has(Constants.CHOICEVALUE)) {
                                cbChoice.setText(jsonObject.getString(Constants.CHOICEVALUE));
                            }
                            cbChoice.setChecked(valueSelectedData.contains(cbChoice.getText().toString()));
                            cbChoice.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.colorPrimary)));
                            adsLayout.addView(linear);
                            addViewInList(linear);
                        }
                    } else {
                        CheckBox cbChoice = new CheckBox(BaseFormDetailActivity.this);
                        cbChoice.setTextSize(TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.text_size_xlarge));
                        cbChoice.setButtonTintList(ColorStateList.valueOf(getResources().getColor(R.color.colorPrimary)));
                        String valueSelected = "";

                        JSONArray valueJSONArray = new JSONArray();
                        updateAutoFillDataInView(object);
                        if (object.has(Constants.VALUE)) {
                            try {
                                Object data = object.get(Constants.VALUE);
                                if (data instanceof String) {
                                    valueJSONArray = StaticUtils.getArrayFromString((String) data);
                                } else {
                                    valueJSONArray = (JSONArray) data;
                                }
                                valueSelected = StaticUtils.getDataFromArray(valueJSONArray);
                            } catch (JSONException e) {
                                FirebaseEventUtils.logException(e);
                            }
                            updateValidationInMap(object, tvCheckboxTitle, valueSelected);
                        } else {
                            object.put(Constants.VALUE, valueJSONArray);
                        }
                        cbChoice.setText(array.getString(i));
                        cbChoice.setChecked(valueSelected.contains(array.getString(i)));
                        cbChoice.setOnCheckedChangeListener((compoundButton, b) -> {
                            clearFocus();
                            String valueSelectedData;
                            JSONArray valueJSONArray1 = new JSONArray();
                            if (object.has(Constants.VALUE)) {
                                try {
                                    Object data = object.get(Constants.VALUE);
                                    if (data instanceof String) {
                                        valueJSONArray1 = StaticUtils.getArrayFromString((String) data);
                                    } else {
                                        valueJSONArray1 = (JSONArray) data;
                                    }
                                    valueSelectedData = StaticUtils.getDataFromArray(valueJSONArray1);
                                    updateValidationInMap(object, tvCheckboxTitle, valueSelectedData);
                                } catch (JSONException e) {
                                    FirebaseEventUtils.logException(e);
                                }
                            }
                            if (b) {
                                //valueJSONArray1.put(cbChoice.getText().toString());
                                StaticUtils.checkAndAddInArray(valueJSONArray1, cbChoice.getText().toString());
                            } else {
                                StaticUtils.removeSelectedDataFromArray(valueJSONArray1, cbChoice.getText().toString());
                            }
                            valueSelectedData = StaticUtils.getDataFromArray(valueJSONArray1);

                            tvCheckboxTitle.setError(null);
                            updateValidationInMap(object, tvCheckboxTitle, valueSelectedData);
                            try {
                                object.put(Constants.VALUE, valueJSONArray1);
                            } catch (JSONException e) {

                                FirebaseEventUtils.logException(e);
                            }
                        });
                        llCheckbox.addView(cbChoice);
                    }
                }

            } catch (JSONException e) {

                FirebaseEventUtils.logException(e);
            }
        }
    }

    private boolean checkSelectedValueInList(String[] lstData, String value) {
        for (String data : lstData) {
            if (data.equalsIgnoreCase(value)) {
                return true;
            }
        }
        return false;
    }

    private void jsonArrayReadService(JSONArray jsonArray, ViewGroup adsLayout, boolean isValidation) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                if (jsonArray.get(i) instanceof JSONObject) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    JSONArray array = null;
                    if (object.has(Constants.ELEMENTS)) {
                        array = object.getJSONArray(Constants.ELEMENTS);
                    } else if (object.has(Constants.CHOICES)) {
                        array = object.getJSONArray(Constants.CHOICES);
                    }
                    if (array != null && array.length() > 0) {
                        jsonArrayReadService(array, adsLayout, isValidation);
                    }
                    readViewAndJson(adsLayout, object, isValidation);
                }
            } catch (JSONException e) {

                FirebaseEventUtils.logException(e);
            }
        }
    }

    /**
     * Method to set place holder in all required edit fields.
     *
     * @param edtHint    instance of component of edit text
     * @param jsonObject JSONObject instance of component
     */
    public void setHintInComponent(EditText edtHint, JSONObject jsonObject) {
        try {
            if (jsonObject.has(Constants.TITLE)) {
                if (jsonObject.has(Constants.PLACEHOLDER)) {
                    edtHint.setHint(jsonObject.getString(Constants.PLACEHOLDER));
                }
            }
        } catch (JSONException e) {

            FirebaseEventUtils.logException(e);
        }
    }

    private void jsonArrayRead(JSONArray jsonArray, ViewGroup adsLayout, boolean isAdd) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                if (jsonArray.get(i) instanceof JSONObject) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    JSONArray array = null;
                    if (object.has(Constants.ELEMENTS)) {
                        array = object.getJSONArray(Constants.ELEMENTS);
                    } else if (object.has(Constants.CHOICES)) {
                        array = object.getJSONArray(Constants.CHOICES);
                    }
                    if (array != null && array.length() > 0) {
                        jsonArrayRead(array, adsLayout, isAdd);
                    }
                    readViewAndJson(adsLayout, object, isAdd);
                }
            } catch (JSONException e) {

                FirebaseEventUtils.logException(e);
            }
        }
    }

    public void inputTypeCheckInOut(JSONObject object, ViewGroup adsLayout) {
        View commentView = getLayoutInflater().inflate(R.layout.layout_comment_weather, adsLayout, false);
        commentView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
    }


    private void inputTypeSiteInfo(JSONObject object, ViewGroup adsLayout) {
        if (siteData != null) {
            View notesView = null;
            LinearLayout mapView = null;
            View operatingHoursView = null;
            View infoMapView = getLayoutInflater().inflate(R.layout.layout_site_info_maps_notes, adsLayout, false);
            AppCompatTextView tvInfo = infoMapView.findViewById(R.id.tvInfo);
            AppCompatTextView tvMap = infoMapView.findViewById(R.id.tvMap);
            AppCompatTextView tvOperatingHours = infoMapView.findViewById(R.id.tvOperatingHours);
            infoMapView.setTag(tag);
            addTagInMainObjectAndIncreaseTagId(object);
            adsLayout.addView(infoMapView);
            addViewInList(infoMapView);

            if (siteData.getSiteConfig() != null) {
                if (TextUtils.isEmpty(siteData.getSiteConfig().getNotes())
                        && TextUtils.isEmpty(siteData.getSiteConfig().getOperatingHours())
                        && siteData.getSiteConfig().getMapDetails().isEmpty()) {
                    infoMapView.setVisibility(View.GONE);
                } else {
                    infoMapView.setVisibility(View.VISIBLE);
                }
                if (!TextUtils.isEmpty(siteData.getSiteConfig().getNotes())) {
                    tvInfo.setVisibility(View.VISIBLE);
                    notesView = getLayoutInflater().inflate(R.layout.layout_simple_text, adsLayout, false);
                    AppCompatTextView tvText = notesView.findViewById(R.id.tvText);
                    tvText.setText(siteData.getSiteConfig().getNotes());
                    adsLayout.addView(notesView);
                    addViewInList(notesView);
                    notesView.setVisibility(View.GONE);
                } else {
                    tvInfo.setVisibility(View.GONE);
                }

                if (!TextUtils.isEmpty(siteData.getSiteConfig().getOperatingHours())) {
                    tvOperatingHours.setVisibility(View.VISIBLE);
                    operatingHoursView = getLayoutInflater().inflate(R.layout.layout_simple_text, adsLayout, false);
                    AppCompatTextView tvText = operatingHoursView.findViewById(R.id.tvText);
                    tvText.setText(siteData.getSiteConfig().getOperatingHours());
                    adsLayout.addView(operatingHoursView);
                    addViewInList(operatingHoursView);
                    operatingHoursView.setVisibility(View.GONE);
                } else {
                    tvOperatingHours.setVisibility(View.GONE);
                }

                if ((siteData.getSiteConfig().getMapDetails() != null && !siteData.getSiteConfig().getMapDetails().isEmpty())) {
                    mapView = (LinearLayout) getLayoutInflater().inflate(R.layout.layout_siteinfo_mapview, adsLayout, false);
                    setMapDetailData(mapView, tvMap, adsLayout);
                    mapView.setVisibility(View.GONE);
                } else {
                    tvMap.setVisibility(View.GONE);
                }
            } else {
                infoMapView.setVisibility(View.GONE);
            }


            View finalNotesView = notesView;
            LinearLayout finalMapView = mapView;
            View finalOperatingHoursView = operatingHoursView;
            tvInfo.setOnClickListener(v -> {
                clearFocus();
                tvInfo.setBackgroundResource(R.drawable.segment_square_selected);
                tvInfo.setTextColor(ContextCompat.getColor(context, R.color.white));
                tvMap.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                tvMap.setBackgroundResource(R.drawable.segment_square_unselected);
                tvOperatingHours.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                tvOperatingHours.setBackgroundResource(R.drawable.segment_square_unselected);
                if (finalNotesView != null) {
                    finalNotesView.setVisibility(View.VISIBLE);
                    try {
                        if (adsLayout.getParent() instanceof ViewGroup) {
                            ((NestedScrollView) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, finalNotesView));
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
                if (finalMapView != null) {
                    finalMapView.setVisibility(View.GONE);
                }
                if (finalOperatingHoursView != null) {
                    finalOperatingHoursView.setVisibility(View.GONE);
                }
            });


            tvOperatingHours.setOnClickListener(v -> {
                clearFocus();
                tvOperatingHours.setBackgroundResource(R.drawable.segment_square_selected);
                tvOperatingHours.setTextColor(ContextCompat.getColor(context, R.color.white));
                tvMap.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                tvMap.setBackgroundResource(R.drawable.segment_square_unselected);
                tvInfo.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                tvInfo.setBackgroundResource(R.drawable.segment_square_unselected);
                if (finalOperatingHoursView != null) {
                    finalOperatingHoursView.setVisibility(View.VISIBLE);
                    try {
                        if (adsLayout.getParent() instanceof ViewGroup) {
                            ((NestedScrollView) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, finalOperatingHoursView));
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
                if (finalMapView != null) {
                    finalMapView.setVisibility(View.GONE);
                }
                if (finalNotesView != null) {
                    finalNotesView.setVisibility(View.GONE);
                }
            });


            tvMap.setOnClickListener(v -> {
                clearFocus();
                tvMap.setBackgroundResource(R.drawable.segment_square_selected);
                tvMap.setTextColor(ContextCompat.getColor(context, R.color.white));
                tvInfo.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                tvInfo.setBackgroundResource(R.drawable.segment_square_unselected);
                tvOperatingHours.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
                tvOperatingHours.setBackgroundResource(R.drawable.segment_square_unselected);
                if (finalMapView != null) {
                    finalMapView.setVisibility(View.VISIBLE);
                    try {
                        if (adsLayout.getParent() instanceof ViewGroup) {
                            if (adsLayout.getParent() instanceof NestedScrollView) {
                                ((NestedScrollView) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, finalMapView));
                            }else if (adsLayout.getParent() instanceof LinearLayout){
                                ((LinearLayout) adsLayout.getParent()).post(() -> adsLayout.getParent().requestChildFocus(adsLayout, finalMapView));
                            }
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    }
                }
                if (finalNotesView != null) {
                    finalNotesView.setVisibility(View.GONE);
                }
                if (finalOperatingHoursView != null) {
                    finalOperatingHoursView.setVisibility(View.GONE);
                }
            });
            showPopupIfItIsTrue(object, siteData);

        }
    }

    private void showPopupIfItIsTrue(JSONObject object, SiteData siteData) {
        if (object.has("popup")) {
            try {
                if (object.getBoolean("popup")) {
                    if (siteData.getSiteConfig() != null && !TextUtils.isEmpty(siteData.getSiteConfig().getNotes())) {
                        PopUtils.showAlertDialogPositiveButtonOnly(this, getString(R.string.siteinfo), siteData.getSiteConfig().getNotes());
                    }
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private void setMapDetailData(LinearLayout mapView, AppCompatTextView tvMap, ViewGroup adsLayout) {
        tvMap.setVisibility(View.VISIBLE);

        for (int i = 0; i < siteData.getSiteConfig().getMapDetails().size(); i++) {
            MapDetail mapDetail = siteData.getSiteConfig().getMapDetails().get(i);
            View mapDataView = getLayoutInflater().inflate(R.layout.item_service_map, adsLayout, false);
            AppCompatTextView tvMapItemName = mapDataView.findViewById(R.id.tvMapItemName);
            tvMapItemName.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary));
            tvMapItemName.setText(mapDetail.getMapName());
            mapDataView.setBackgroundColor(ContextCompat.getColor(context, R.color.setting_activity_background));

            mapDataView.setOnClickListener(v -> {
                navigateUserToWebViewScreen(mapDetail.getMapName(), mapDetail.getMapURL(), mapDataView.getTag() != null && (boolean) mapDataView.getTag());
                mapDataView.setTag(true);
            });

            mapView.addView(mapDataView);
            if (i < siteData.getSiteConfig().getMapDetails().size() - 1) {
                View separator = getLayoutInflater().inflate(R.layout.layout_saparator, adsLayout, false);
                mapView.addView(separator);
            }
        }
        mapView.setVisibility(View.VISIBLE);
        adsLayout.addView(mapView);
        addViewInList(mapView);
    }


    /**
     * when Type is text that time check input type
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    protected void inputTypeCheck(JSONObject object, ViewGroup adsLayout) {
        try {
            String inoutType = (String) object.get(Constants.INPUTTYPE);
            switch (inoutType) {
                case Constants.INPUTTYPE_COMMENT:
                    inputTypeComment(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_DATE:
                    inputTypeDate(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_DATE_TIME:
                    inputTypeDateAndTime(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_EMAIL:
                    inputTypeEmail(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_NUMBER:
                    inputTypeNumber(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_PASSWORD:
                    inputTypePassword(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_TEL:
                    inputTypeTel(object, adsLayout);
                    break;
                case Constants.INPUTTYPE_URL:
                    inputTypeUrl(object, adsLayout);
                    break;
                case Constants.TEXT:
                    inputTypeText(object, adsLayout);
                    break;

            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    protected void readAndAddValidation(LinearLayout llSubview, JSONObject object) {
        try {
            // Check if object has elements key then only get array data from it.
            if (object.has(Constants.ELEMENTS)) {
                JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                jsonArrayReadAndAdd(array, llSubview);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void jsonArrayReadAndAdd(JSONArray jsonArray, LinearLayout llSubview) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.has(Constants.ELEMENTS)) {
                    //ViewGroup view = (ViewGroup) adsLayout.getChildAt(i);
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    jsonArrayReadAndAdd(array, llSubview);
                    readViewAndJson(llSubview, object, true);
                } else if (object.has(Constants.CHOICES)) {
                    JSONArray array = object.getJSONArray(Constants.CHOICES);
                    if (object.has(Constants.VALUE) && !TextUtils.isEmpty(object.getString(Constants.VALUE).trim()) && !object.getString(Constants.VALUE).equals("[]")) {
                        for (int j = 0; j < array.length(); j++) {
                            if (array.get(j) instanceof JSONObject) {
                                JSONObject jsonObject = array.getJSONObject(j);
                                if (jsonObject.has(Constants.CHOICEVALUE)
                                        && jsonObject.getString(Constants.CHOICEVALUE).trim().equalsIgnoreCase(object.getString(Constants.VALUE))
                                        && jsonObject.has(Constants.ELEMENTS)) {
                                    JSONArray array1 = jsonObject.getJSONArray(Constants.ELEMENTS);
                                    if (array1.length() > 0) {
                                        jsonArrayReadAndAdd(array1, llSubview);
                                    }
                                }
                            }
                        }
                    } else {
                        readViewAndJson(llSubview, object, true);
                    }
                } else {
                    readViewAndJson(llSubview, object, true);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }

        }

    }

    protected void readViewAndJson(View view, JSONObject object, boolean isAdd) {
        String type;
        try {
            if (!object.has(Constants.TYPE))
                return;
            type = (String) object.get(Constants.TYPE);
            switch (type) {
                case Constants.CHECKBOX:
                    textViewUpdateValidationMap(view, R.id.tvCheckboxTitle, object, isAdd);
                    break;
                case Constants.DROPDOWNMULTIPLE:
                case Constants.SPINNER:
                    textViewUpdateValidationMap(view, R.id.tvMultiSelected, object, isAdd);
                    break;

                case DYNAMIC_DROPDOWN:
                    textViewUpdateValidationMap(view, R.id.tvTitle, object, isAdd);
                    break;
                case Constants.IMAGE_UPLOAD:
                    textViewUpdateValidationMap(view, R.id.tvUploadPhoto, object, isAdd);
                    break;
                case Constants.TEXT:
                    textViewCheckTextType(view, object, isAdd);
                    break;
                case Constants.INPUTTYPE_URL:
                    //editTextUpdateValidationMap(view,R.id.edtUrl,object,isAdd);
                    break;

                case Constants.SIGNATUREPAD:
                    textViewUpdateValidationMap(view, R.id.tvSignatureTitle, object, isAdd);
                    break;
                case Constants.SEGMENT_INPUT:
                case Constants.RADIOGROUP:
                    textViewUpdateValidationMap(view, R.id.tvRadioGroupTitle, object, isAdd);

                    break;
                case Constants.INPUTTYPE_COMMENT:
                    textViewUpdateValidationMap(view, R.id.edtComment, object, isAdd);

                    break;
                case Constants.GEO:
                    break;
                case Constants.MATERIAL:
                    textViewUpdateValidationMap(view, R.id.tvMaterialName, object, isAdd);
                    break;
                case Constants.SERVICE:
                    String serviceType = object.getString("serviceType");
                    if (serviceType.equalsIgnoreCase("AsDetail")) {
                        textViewUpdateValidationMap(view, R.id.tvTitle, object, isAdd);
                    } else if (serviceType.equalsIgnoreCase("AsTask")) {
                        textViewUpdateValidationMap(view, R.id.tvCheckboxItem, object, isAdd);
                    } else if (serviceType.equalsIgnoreCase("AsTimer")) {
                        textViewUpdateValidationMap(view, R.id.tvServiceName, object, isAdd);
                    }

                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * inputTypeImageUpload is use for add Image upload option in parent view
     * this method is execute when type is file.
     * when you click on choose file open gallery or camera and take photo.
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeImageUpload(JSONObject object, ViewGroup adsLayout, boolean shouldAdd) {
        ImageViewAdapter imageViewAdapter;
        View photoUpload = getLayoutInflater().inflate(R.layout.layout_photo_upload, adsLayout, false);
        LinearLayout llUploadPhoto = photoUpload.findViewById(R.id.llUploadPhoto);
        llUploadPhoto.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object, false);
        if (shouldAdd)
            mapImageUpload.put(tag, object);
        tag++;
        RecyclerView rlImages = photoUpload.findViewById(R.id.rlImages);
        TextView tvUploadPhoto = photoUpload.findViewById(R.id.tvUploadPhoto);
        setTitleOfComponent(tvUploadPhoto, object);
        initializeRecyclerView(rlImages, true);
        ArrayList<ImageData> lstImages = new ArrayList<>();
        try {
            if (object.has(Constants.VALUE)) {
                JSONArray jsonArray = object.getJSONArray(Constants.VALUE);
                for (int i = 0; i < jsonArray.length(); i++) {
                    JSONObject object1 = jsonArray.getJSONObject(i);
                    ImageData imageData = new ImageData();
                    imageData.setImageId(object1.getInt(Constants.IMAGEID));
                    if (!TextUtils.isEmpty(object1.getString(IMAGEPATHHIGH)))
                        imageData.setImagePath(object1.getString(IMAGEPATHHIGH));
                    else
                        imageData.setImagePath(object1.getString(IMAGEPATHLOW));

                    lstImages.add(imageData);
                }
                if (lstImages.size() > 0) {
                    updateValidationInMap(object, tvUploadPhoto, "", false, true, true);
                }
            } else {
                String[] value = {};
                object.put(Constants.VALUE, new JSONArray(value));
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setValidationInMap(object, tvUploadPhoto);
        imageViewAdapter = new ImageViewAdapter(this, lstImages) {
            @Override
            public void deleteItem(int position, int imageId) {
                PopUtils.showDialogForDeleteView(BaseFormDetailActivity.this, getString(R.string.msg_delete_image), view -> {
                    ImageViewAdapter imageViewAdapter1 = (ImageViewAdapter) rlImages.getAdapter();
                    if (imageViewAdapter1 != null) {
                        imageViewAdapter1.removeItem(position);
                        if (imageViewAdapter1.getImageList().isEmpty()) {
                            updateValidationInMap(object, tvUploadPhoto, "", false, true, false);
                        }
                    }
                    deleteDataFromImageJson(imageId, object);
                });
            }

            @Override
            public void itemClick(int position, ImageData imageData) {
                PopUtils.showDialogImageView(context, imageData.getImagePath());
            }
        };
        rlImages.setAdapter(imageViewAdapter);

        ImageView ivAddPhoto = photoUpload.findViewById(R.id.ivAddPhoto);
        ivAddPhoto.setOnClickListener(view -> {
            clearFocus();
            openCameraToGetImage((int) llUploadPhoto.getTag(), false, false);
            tvUploadPhoto.setError(null);
        });
        if (lstImages.size() < 1) {
            rlImages.setVisibility(View.GONE);
        } else {
            rlImages.setVisibility(View.VISIBLE);
        }
        adsLayout.addView(llUploadPhoto);
        addViewInList(llUploadPhoto);
        addSeparatorView(this, adsLayout);
    }

    void openCameraToGetImage(int tag, boolean isIssue, boolean fromMapView) {
        Intent intent = new Intent(BaseFormDetailActivity.this, CameraActivity.class);
        intent.putExtra("tag", tag);
        intent.putExtra("mFormPkId", mFormPkId);
        intent.putExtra("isTnm", isTMForm());
        intent.putExtra("isIssue", isIssue);
        intent.putExtra("isFromMap", fromMapView);
        if (isTMForm()) {
            intent.putExtra("siteId", siteData.getSiteId());
            intent.putExtra("isPlotOnMap", formData.isPlotOnMap());
        } else
            intent.putExtra("siteId", -2L);
        startActivityForResult(intent, REQUEST_CAMERA);
    }

    private void deleteDataFromImageJson(int imageId, JSONObject mainJson) {
        try {
            JSONObject jsonImageData;
            TblForms tblForms = new TblForms(context);
            TblTMForms tblTNMForms = new TblTMForms(context);
            FormData data;
            if (isTMForm()) {
                data = tblTNMForms.getFormDataByPKId(formData.getFormPKId());
            } else {
                data = tblForms.getFormDataByPKId(formData.getFormPKId());
            }
            jsonImageData = new JSONObject(data.getImageData());

            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);
            int index = -1;
            for (int i = 0; i < jsonImageArray.length(); i++) {
                JSONObject object = jsonImageArray.getJSONObject(i);
                if (object.has(Constants.IMAGEID)) {
                    if (object.getInt(Constants.IMAGEID) == imageId) {
                        index = i;
                    }
                }
            }
            if (index != -1) {
                jsonImageArray.remove(index);
            }
            int indexMainJson = -1;
            JSONArray mainJsonArray = mainJson.getJSONArray(Constants.VALUE);
            for (int j = 0; j < mainJsonArray.length(); j++) {
                JSONObject object = mainJsonArray.getJSONObject(j);
                if (object.has(Constants.IMAGEID)) {
                    if (object.getInt(Constants.IMAGEID) == imageId) {
                        indexMainJson = j;
                    }
                }
            }
            if (indexMainJson != -1) {
                mainJsonArray.remove(indexMainJson);
            }
            if (isTMForm()) {
                tblTNMForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());

            } else {
                tblForms.updateImageDataByPkId(formData.getFormPKId(), jsonImageData.toString());
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

    }


    /**
     * inputTypeText is use for add Text view in parent view
     * this method is call when type is text and inputType is password
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    protected void inputTypeTextDisplay(JSONObject object, ViewGroup adsLayout) {
        View text = getLayoutInflater().inflate(R.layout.layout_static_text, adsLayout, false);
        text.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        TextView tvStaticTitle = text.findViewById(R.id.tvStaticTitle);
        TextView tvStaticValue = text.findViewById(R.id.tvStaticValue);

        setTitleOfComponent(tvStaticTitle, object);
        if (object.has(Constants.TEXT)) {
            try {
                tvStaticValue.setText(object.getString(Constants.TEXT));
                object.put(Constants.VALUE, object.getString(Constants.TEXT));
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        adsLayout.addView(text);
        addViewInList(text);
        addSeparatorView(this, adsLayout);

    }


    private void textViewCheckTextType(View view, JSONObject object, boolean isAdd) {
        try {
            String inoutType = (String) object.get(Constants.INPUTTYPE);
            switch (inoutType) {
                case Constants.INPUTTYPE_COMMENT:
                    editTextUpdateValidationMap(view, R.id.edtComment, object, isAdd);
                    break;
                case Constants.INPUTTYPE_DATE:
                    textViewUpdateValidationMap(view, R.id.tvDateDisplay, object, isAdd);
                    break;
                case Constants.INPUTTYPE_DATE_TIME:
                    textViewUpdateValidationMap(view, R.id.tvDateAndTime, object, isAdd);
                    break;
                case Constants.INPUTTYPE_EMAIL:
                    editTextUpdateValidationMap(view, R.id.edtEmail, object, isAdd);
                    break;
                case Constants.INPUTTYPE_NUMBER:
                    editTextUpdateValidationMap(view, R.id.edtNumber, object, isAdd);
                    break;
                case Constants.INPUTTYPE_PASSWORD:
                    editTextUpdateValidationMap(view, R.id.edtPassword, object, isAdd);
                    break;
                case Constants.INPUTTYPE_TEL:
                    editTextUpdateValidationMap(view, R.id.edtPhone, object, isAdd);
                    break;
                case Constants.INPUTTYPE_URL:
                    //editTextUpdateValidationMap(view,R.id.edtUrl,object,isAdd);
                    break;
                case Constants.TEXT:
                    editTextUpdateValidationMap(view, R.id.edtText, object, isAdd);
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void textViewUpdateValidationMap(View view, int textViewId, JSONObject object, boolean isAdd) {
        try {
            TextView tvKey = null;
            if (object.has(Constants.ID) && object.has(Constants.ISREQUIRED) && object.getBoolean(Constants.ISREQUIRED)) {
                if (view.findViewWithTag(object.getInt(Constants.ID)) instanceof LinearLayout) {
                    LinearLayout linearLayout = view.findViewWithTag(object.getInt(Constants.ID));
                    tvKey = linearLayout.findViewById(textViewId);
                } else if (view.findViewWithTag(object.getInt(Constants.ID)) instanceof ScrollView) {
                    ScrollView linearLayout = view.findViewWithTag(object.getInt(Constants.ID));
                    tvKey = linearLayout.findViewById(textViewId);
                }
                if (tvKey != null) {
                    if (!isAdd) {
                        validationMap.remove(tvKey);
                    } else {
                        setValidationInMap(object, tvKey);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void editTextUpdateValidationMap(View componentView, int edtTextId, JSONObject object, boolean isAdd) {
        try {
            if (object.has(Constants.ISREQUIRED) && object.getBoolean(Constants.ISREQUIRED)) {
                LinearLayout layout = null;
                EditText edtTextKey;
                if (object.has(Constants.ID)) {
                    layout = componentView.findViewWithTag(object.get(Constants.ID));
                }
                if (layout != null)
                    edtTextKey = layout.findViewById(edtTextId);
                else
                    edtTextKey = componentView.findViewById(edtTextId);
                if (edtTextKey != null) {
                    if (!isAdd) {
                        validationMap.remove(edtTextKey);
                    } else {
                        setValidationInMap(object, edtTextKey);
                    }
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    protected void removeValueFromJson(JSONObject newJsonObject) {
        try {
            JSONArray array = newJsonObject.getJSONArray(Constants.ELEMENTS);
            for (int i = 0; i < array.length(); i++) {
                JSONObject jsonObject = array.getJSONObject(i);
                if (jsonObject.has(Constants.ELEMENTS)) {
                    removeValueFromJson(jsonObject);
                } else if (jsonObject.has(Constants.CHOICES)) {
                    if (jsonObject.has(Constants.VALUE))
                        jsonObject.remove(Constants.VALUE);
                    JSONArray array1 = jsonObject.getJSONArray(Constants.CHOICES);
                    for (int j = 0; j < array1.length(); j++) {
                        JSONObject jsonObject1 = null;
                        try {
                            jsonObject1 = array1.getJSONObject(j);
                        } catch (JSONException ignored) {

                        }
                        if (jsonObject1 != null) {
                            if (jsonObject1.has(Constants.VALUE))
                                jsonObject1.remove(Constants.VALUE);

                            if (jsonObject1.has(Constants.ELEMENTS))
                                removeValueFromJson(jsonObject1);
                            if (jsonObject1.has(Constants.TYPE)) {
                                if (jsonObject1.getString(Constants.TYPE).equalsIgnoreCase(Constants.MATERIAL)) {
                                    if (jsonObject1.has(Constants.MATERIAL_DATA))
                                        jsonObject1.remove(Constants.MATERIAL_DATA);
                                } else if (jsonObject1.getString(Constants.TYPE).equalsIgnoreCase(Constants.GEO)) {
                                    removeGeoValues(jsonObject1);
                                } else if (jsonObject1.getString(Constants.TYPE).equalsIgnoreCase(Constants.SERVICE)) {
                                    removeServiceData(jsonObject1);
                                } else if (jsonObject1.getString(Constants.TYPE).equalsIgnoreCase(Constants.CREW)) {
                                    removeCrewData(jsonObject1);
                                }
                            }
                        }
                    }
                } else if (jsonObject.has(Constants.VALUE)) {
                    jsonObject.remove(Constants.VALUE);
                    if (jsonObject.getString(Constants.TYPE).equalsIgnoreCase(Constants.MATERIAL)) {
                        if (jsonObject.has(Constants.MATERIAL_DATA))
                            jsonObject.remove(Constants.MATERIAL_DATA);
                    }
                    if (jsonObject.getString(Constants.TYPE).equalsIgnoreCase(Constants.CREW)) {
                        removeCrewData(jsonObject);
                    }
                } else if (jsonObject.getString(Constants.TYPE).equalsIgnoreCase(Constants.GEO)) {
                    removeGeoValues(jsonObject);
                } else if (jsonObject.getString(Constants.TYPE).equalsIgnoreCase(Constants.SERVICE)) {
                    removeServiceData(jsonObject);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }


    }

    private void removeGeoValues(JSONObject jsonObject) {
        if (jsonObject.has(Constants.PARAM_LAT))
            jsonObject.remove(Constants.PARAM_LAT);
        if (jsonObject.has(Constants.PARAM_LNG))
            jsonObject.remove(Constants.PARAM_LNG);
        if (jsonObject.has(Constants.SITEID))
            jsonObject.remove(Constants.SITEID);
        if (jsonObject.has(Constants.BUILDING_ID))
            jsonObject.remove(Constants.BUILDING_ID);
        if (jsonObject.has(Constants.BUILDING_NAME))
            jsonObject.remove(Constants.BUILDING_NAME);
    }

    private void removeAddressValues(JSONObject jsonObject) {
        if (jsonObject.has(Constants.PARAM_LAT))
            jsonObject.remove(Constants.PARAM_LAT);
        if (jsonObject.has(Constants.PARAM_LNG))
            jsonObject.remove(Constants.PARAM_LNG);
        if (jsonObject.has(Constants.SITEID))
            jsonObject.remove(Constants.SITEID);
        if (jsonObject.has(Constants.BUILDING_ID))
            jsonObject.remove(Constants.BUILDING_ID);
        if (jsonObject.has(Constants.BUILDING_NAME))
            jsonObject.remove(Constants.BUILDING_NAME);
    }

    private void removeServiceData(JSONObject jsonObject) {
        try {
            if (jsonObject.has(Constants.SITEID))
                jsonObject.remove(Constants.SITEID);
            if (jsonObject.has(Constants.SF_ID))
                jsonObject.remove(Constants.SF_ID);
            if (jsonObject.has(Constants.TnMService))
                jsonObject.remove(Constants.TnMService);
            if (jsonObject.has(Constants.SERVICEID))
                jsonObject.remove(Constants.SERVICEID);
            if (jsonObject.has(Constants.SERVICE_NAME))
                jsonObject.remove(Constants.SERVICE_NAME);

            String serviceType = jsonObject.getString("serviceType");

            if (serviceType.equalsIgnoreCase("AsDetail")) {
                if (jsonObject.has(Constants.PEOPLE))
                    jsonObject.remove(Constants.PEOPLE);
                if (jsonObject.has(Constants.HOUR))
                    jsonObject.remove(Constants.HOUR);
                if (jsonObject.has(Constants.OPTIONS))
                    jsonObject.remove(Constants.OPTIONS);
            } else if (serviceType.equalsIgnoreCase("AsTask")) {
                if (jsonObject.has(Constants.COMPLETED))
                    jsonObject.remove(Constants.COMPLETED);
            } else if (serviceType.equalsIgnoreCase("AsTimer")) {
                if (jsonObject.has(Constants.OPTIONS))
                    jsonObject.remove(Constants.OPTIONS);
                if (jsonObject.has(Constants.START_TIME))
                    jsonObject.remove(Constants.START_TIME);
                if (jsonObject.has(Constants.STOP_TIME))
                    jsonObject.remove(Constants.STOP_TIME);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void removeCrewData(JSONObject jsonObject) {
        if (jsonObject.has(Constants.SITEID))
            jsonObject.remove(Constants.SITEID);
        if (jsonObject.has(Constants.SF_ID))
            jsonObject.remove(Constants.SF_ID);
        if (jsonObject.has(Constants.VALUE_NAME))
            jsonObject.remove(Constants.VALUE_NAME);
    }

    /**
     * inputTypeText is use for add Text view in parent view
     * this method is call when type is text and inputType is password
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeText(JSONObject object, ViewGroup adsLayout) {
        View text = getLayoutInflater().inflate(R.layout.layout_text, adsLayout, false);
        text.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        CustomEditText edtText = text.findViewById(R.id.edtText);
        TextView tvTitle = text.findViewById(R.id.tvTitle);
        setHintInComponent(edtText, object);
        setTitleOfComponent(tvTitle, object);
        setEditTextListeners(edtText, true);
        setValidationInMap(object, edtText);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                edtText.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, edtText, edtText.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setEditTextChangeListener(object, edtText);
        setFocusChangeListenerForEditText(edtText);
        adsLayout.addView(text);
        addViewInList(text);
        addSeparatorView(this, adsLayout);

    }

    private JSONObject getDataFromAutoFillMap(JSONObject componentJson) {
        try {
            if (autoFillArray == null || autoFillArray.length() == 0)
                return null;
            for (int i = 0; i < autoFillArray.length(); i++) {
                JSONObject dataObject = autoFillArray.getJSONObject(i);
                if (dataObject.getString(TYPE).equalsIgnoreCase(componentJson.getString(TYPE))) {
                    if (dataObject.getString(TYPE).equalsIgnoreCase(SPINNER)) {
                        //Check if dropdown type has value key. There should be value key for simple dropdown type
                        //For fleet and equipment we wont get value key in json
                        if (dataObject.has(VALUE)) {
                            return dataObject;
                        } else if (dataObject.has(IS_FLEET_VEHICLELIST) && dataObject.getBoolean(IS_FLEET_VEHICLELIST) && componentJson.has(IS_FLEET_VEHICLELIST) && componentJson.getBoolean(IS_FLEET_VEHICLELIST)) {
                            return dataObject;
                        } else if (dataObject.has(IS_FLEET_EQUIPMENTLIST) && dataObject.getBoolean(IS_FLEET_EQUIPMENTLIST) && componentJson.has(IS_FLEET_EQUIPMENTLIST) && componentJson.getBoolean(IS_FLEET_EQUIPMENTLIST)) {
                            return dataObject;
                        } else if (!dataObject.has(IS_FLEET_EQUIPMENTLIST) && !dataObject.has(IS_FLEET_VEHICLELIST) && !componentJson.has(IS_FLEET_EQUIPMENTLIST) && !componentJson.has(IS_FLEET_VEHICLELIST)) {
                            return dataObject;
                        }
                    } else {
                        return dataObject;
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return null;
    }


    private void removeDataFromAutoFillMap(JSONObject autoFileObject,JSONObject elementObject) {
        try {
            if (autoFillArray == null || autoFillArray.length() == 0)
                return;
            for (int i = 0; i < autoFillArray.length(); i++) {
                JSONObject dataObject = autoFillArray.getJSONObject(i);
                if (dataObject == autoFileObject) {
                    if (dataObject.getString(TYPE).equalsIgnoreCase(SPINNER)) {
                        if (dataObject.has(IS_FLEET_VEHICLELIST) && dataObject.getBoolean(IS_FLEET_VEHICLELIST) && elementObject.has(Constants.IS_FLEET_VEHICLELIST)) {
                            autoFillArray.remove(i);
                        } else if (dataObject.has(IS_FLEET_EQUIPMENTLIST) && dataObject.getBoolean(IS_FLEET_EQUIPMENTLIST)&& elementObject.has(Constants.IS_FLEET_EQUIPMENTLIST)) {
                            autoFillArray.remove(i);
                        } else {
                            if (!dataObject.has(IS_FLEET_EQUIPMENTLIST) && !dataObject.has(Constants.IS_FLEET_VEHICLELIST) &&
                                    !elementObject.has(IS_FLEET_EQUIPMENTLIST) && !elementObject.has(Constants.IS_FLEET_VEHICLELIST)) {
                                autoFillArray.remove(i);
                            }
                        }
                    } else {
                        autoFillArray.remove(i);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * Function to set Auto fill data in JSONObject
     *
     * @param object JSONObject
     */
    private void updateAutoFillDataInView(JSONObject object) {
        try {
            JSONObject autoFillJson = getDataFromAutoFillMap(object);
            if (autoFillJson != null) {
                String typeValue = "";
                if (autoFillJson.has(VALUE)) {
                    Object jsonElement = autoFillJson.get(VALUE);
                    if (jsonElement instanceof JSONArray) {
                        JSONArray valueArray = (JSONArray) jsonElement;
                        if (object.has(CHOICES)) {
                            JSONArray choiceData = object.getJSONArray(CHOICES);
                            typeValue = StaticUtils.getStringFromJSONArrayForAutoFill(valueArray, getArrayFromJSonArrayForAutoFill(choiceData));
                        }else if (object.getString(TYPE).equalsIgnoreCase(DYNAMIC_DROPDOWN)) {
                            // Check if value is in Array of long for dynamic dropdown selection
                            TblDynamicDropdownItems tblDynamicDropdownItems = new TblDynamicDropdownItems(this);
                            List<Long> lstIds = new ArrayList<>();
                            for (int i = 0; i < valueArray.length(); i++) {
                                lstIds.add(valueArray.getLong(i));
                            }
                            ArrayList<DynamicDropDownItem> lstItems = tblDynamicDropdownItems.getDataByIdsType(lstIds, autoFillJson.getString(DROPDOWN_VALUE_TYPE));
                            typeValue = getStringFromDynamicDropDownSelectedData(lstItems);
                            object.put(Constants.VALUE, getStringFromDynamicDropDownSelectedDataID(lstItems));
                            object.put(Constants.VALUE_DISPLAY, typeValue);
                        } else {
                            typeValue = StaticUtils.getStringFromJSONArrayForAutoFill(valueArray, new ArrayList<>());
                        }
                    } else if (jsonElement instanceof Integer) {
                        if (autoFillJson.getString(TYPE).equalsIgnoreCase(DYNAMIC_DROPDOWN)) {
                            // Check if value a long for dynamic dropdown selection
                            TblDynamicDropdownItems tblDynamicDropdownItems = new TblDynamicDropdownItems(this);
                            DynamicDropDownItem dynamicDropDownItem = tblDynamicDropdownItems.getDataByIdType(autoFillJson.getLong(VALUE), autoFillJson.getString(DROPDOWN_VALUE_TYPE));
                            typeValue = dynamicDropDownItem.getValue();
                            object.put(Constants.VALUE, dynamicDropDownItem.getId());
                            object.put(Constants.VALUE_DISPLAY, typeValue);
                        } else {
                            if (object.has(CHOICES)) {
                                JSONArray choiceData = object.getJSONArray(CHOICES);
                                if (StaticUtils.checkDataInJsonArray(choiceData, (String) jsonElement)) {
                                    typeValue = (String) jsonElement;
                                }
                            } else {
                                typeValue = String.valueOf(jsonElement);
                            }
                        }

                    } else {
                        if (object.has(CHOICES)) {
                            JSONArray choiceData = object.getJSONArray(CHOICES);
                            if (StaticUtils.checkDataInJsonArray(choiceData, (String) jsonElement)) {
                                typeValue = (String) jsonElement;
                            }
                        } else {
                            typeValue = (String) jsonElement;
                        }
                    }
                } else {
                    if (autoFillJson.has(IS_FLEET_VEHICLELIST) && autoFillJson.getBoolean(Constants.IS_FLEET_VEHICLELIST)) {
                        //autofill value for dropdown type vehicle data
                        TblVehicles tblVehicles = new TblVehicles(this);
                        if (autoFillJson.has(SELECTED_VEHICLES)) {
                            Vehicles vehicles = tblVehicles.getDataFromTableById(autoFillJson.getLong(SELECTED_VEHICLES));
                            if (vehicles.getPkId() > 0) {
                                typeValue = vehicles.getSvVehicleLabel();
                            }
                        }
                    } else if (autoFillJson.has(IS_FLEET_EQUIPMENTLIST) && autoFillJson.getBoolean(Constants.IS_FLEET_EQUIPMENTLIST)) {
                        //autofill value for dropdown type Equipment data
                        TblEquipment tblEquipment = new TblEquipment(this);
                        if (autoFillJson.has(SELECTED_EQUIPMENTS)) {
                            Equipments equipments = tblEquipment.getDataFromTableById(autoFillJson.getLong(SELECTED_EQUIPMENTS));
                            if (equipments.getPkId() > 0) {
                                typeValue = equipments.getSeiEquipmentName();
                            }
                        }
                    }
                    if (object.getString(TYPE).equalsIgnoreCase(DYNAMIC_DROPDOWN)) {
                        // Check if value is in Array of long for dynamic dropdown selection
                        TblDynamicDropdownItems tblDynamicDropdownItems = new TblDynamicDropdownItems(this);
                        List<Long> lstIds = new ArrayList<>();
                        if (autoFillJson.has(AUTO_FILL_LOGGED_USERID) && autoFillJson.getBoolean(AUTO_FILL_LOGGED_USERID)) {
                            lstIds.add(StaticUtils.getEmployeeIdInLong());
                        }
                        ArrayList<DynamicDropDownItem> lstItems = tblDynamicDropdownItems.getDataByIdsType(lstIds, autoFillJson.getString(DROPDOWN_VALUE_TYPE));
                        typeValue = getStringFromDynamicDropDownSelectedData(lstItems);
                        object.put(Constants.VALUE, StaticUtils.getEmployeeIdInInt());
                        object.put(Constants.VALUE_DISPLAY, typeValue);
                    }
                }
                if  (!object.getString(TYPE).equalsIgnoreCase(DYNAMIC_DROPDOWN)) {
                    object.put(Constants.VALUE, typeValue);
                }
                removeDataFromAutoFillMap(autoFillJson,object);
                //}
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

    }

    /**
     * If Component is email then this method will be execute.
     *
     * @param object    object of Email component
     * @param adsLayout parent view to add email component in it
     */
    private void inputTypeEmail(JSONObject object, ViewGroup adsLayout) {
        View email = getLayoutInflater().inflate(R.layout.layout_email, adsLayout, false);
        email.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        CustomEditText edtEmail = email.findViewById(R.id.edtEmail);
        setValidationInMap(object, true, edtEmail, "");
        edtEmail.setClickable(true);
        TextView tvEmailTitle = email.findViewById(R.id.tvEmailTitle);
        setHintInComponent(edtEmail, object);
        setTitleOfComponent(tvEmailTitle, object);
        setEditTextListeners(edtEmail, true);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                edtEmail.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, edtEmail, edtEmail.getText().toString(), true);
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setEditTextChangeListener(object, edtEmail, true);
        setFocusChangeListenerForEditText(edtEmail);
        adsLayout.addView(email);
        addViewInList(email);
        addSeparatorView(this, adsLayout);


    }

    /**
     * inputTypeTel is use for add phone view in parent view
     * this method is call when type is text and inputType is tel
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeTel(JSONObject object, ViewGroup adsLayout) {
        View phone = getLayoutInflater().inflate(R.layout.layout_phone, adsLayout, false);
        TextView tvPhoneTitle = phone.findViewById(R.id.tvPhoneTitle);
        CustomEditText edtPhone = phone.findViewById(R.id.edtPhone);
        setEditTextListeners(edtPhone, true);
        setValidationInMap(object, edtPhone);
        phone.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        setTitleOfComponent(tvPhoneTitle, object);
        setHintInComponent(edtPhone, object);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                edtPhone.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, edtPhone, edtPhone.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setEditTextChangeListener(object, edtPhone);
        adsLayout.addView(phone);
        addViewInList(phone);
        addSeparatorView(this, adsLayout);
    }


    /**
     * Common method to manage Text Change listener method
     *
     * @param object  JSONObject of component
     * @param edtText Object of CustomEditText
     */
    private void setEditTextChangeListener(JSONObject object, CustomEditText edtText) {
        setEditTextChangeListener(object, edtText, false);
    }


    /**
     * Common method to manage Text Change listener method
     *
     * @param object  JSONObject of component
     * @param edtText Object of CustomEditText
     * @param isEmail true if it is email component else false
     */
    private void setEditTextChangeListener(JSONObject object, CustomEditText edtText, boolean isEmail) {
        edtText.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
            }

            @Override
            public void afterTextChanged(Editable editable) {
                checkAndValidateEditText(object, edtText, isEmail);
            }
        });
    }

    /**
     * Common method to set updated data in its jsonObject and update its validation in map
     *
     * @param object  JSONObject of component
     * @param edtText Object of CustomEditText
     * @param isEmail true if it is email component else false
     */
    private void checkAndValidateEditText(JSONObject object, CustomEditText edtText, boolean isEmail) {
        try {
            if (edtText != null && edtText.getText() != null) {
                String data = edtText.getText().toString().trim();
                if (edtText.getText().length() > 0 && TextUtils.isEmpty(data)) {
                    edtText.setText("");
                }
                updateValidationInMap(object, edtText, data, isEmail);
                object.put(Constants.VALUE, data);

                checkAndUpdatePanelValidationInMap(object);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void checkAndUpdatePanelValidationInMap(JSONObject object) {
        try {
            if (object.has(Constants.VALUE) && !TextUtils.isEmpty(object.getString(Constants.VALUE))) {
                if (object.has(Constants.PARENT_TAG)) {
                    View view = getViewFromTag(object.getInt(Constants.PARENT_TAG));
                    if (view != null) {
                        updatePanelValidationInMap(view, true);
                    }
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    /**
     * inputTypeUrl is use for add url in parent view
     * this method is call when type is text and inputType is url
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    protected void inputTypeUrl(JSONObject object, ViewGroup adsLayout) {
        View urlView = getLayoutInflater().inflate(R.layout.layout_url, adsLayout, false);
        TextView tvUrlTitle = urlView.findViewById(R.id.tvUrlTitle);

        urlView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        try {
            setTitleOfComponent(tvUrlTitle, object);
            if (object.has(Constants.URL)) {
                tvUrlTitle.setOnClickListener(v -> {
                            clearFocus();
                            try {
                                String url = object.getString(Constants.URL);
                                if (!url.startsWith("http://") && !url.startsWith("https://"))
                                    url = "http://" + url;
                                navigateUserToWebViewScreen(object.getString(Constants.TITLE), url, tvUrlTitle.getTag() != null && (boolean) tvUrlTitle.getTag());
                            } catch (JSONException | ActivityNotFoundException e) {
                                FirebaseEventUtils.logException(e);
                            }
                            tvUrlTitle.setTag(true);
                        }
                );
                object.put(Constants.VALUE, object.getString(Constants.URL));
                //object.put(Constants.PARENT_TAG, parentPanelTag);
            } else {
                urlView.setVisibility(View.GONE);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        adsLayout.addView(urlView);
        addViewInList(urlView);
        addSeparatorView(this, adsLayout);

    }

    private void navigateUserToWebViewScreen(String title, String link, boolean useCached) {
        new FinestWebView(this)
                .titleDefault(title)
                .webViewBuiltInZoomControls(true)
                .webViewDisplayZoomControls(false)
                .dividerHeight(0)
                .gradientDivider(false)
                .webViewCacheMode(useCached ? WebSettings.LOAD_CACHE_ELSE_NETWORK : WebSettings.LOAD_NO_CACHE)
                .show(link);

    }


    /**
     * inputTypeDateAndTime is use for add Date and time in parent view
     * this method is call when type is text and inputType is date/time
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    @SuppressLint("ClickableViewAccessibility")
    private void inputTypeDateAndTime(JSONObject object, ViewGroup adsLayout) {
        View dateAndTime = getLayoutInflater().inflate(R.layout.layout_date_time, adsLayout, false);
        dateAndTime.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        Calendar c = Calendar.getInstance();
        mYear = c.get(Calendar.YEAR);
        mMonth = c.get(Calendar.MONTH);
        mDay = c.get(Calendar.DAY_OF_MONTH);
        mHour = c.get(Calendar.HOUR_OF_DAY);
        mMinute = c.get(Calendar.MINUTE);
        Date currentTime = c.getTime();

        TextView tvDateAndTimeTitle = dateAndTime.findViewById(R.id.tvDateAndTimeTitle);
        TextView tvDateAndTime = dateAndTime.findViewById(R.id.tvDateAndTime);
        setValidationInMap(object, tvDateAndTime);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                tvDateAndTime.setText(object.getString(Constants.VALUE));
                if (object.has("auto")) {
                    if (object.getBoolean("auto")) {
                        tvDateAndTime.setEnabled(false);
                    } else {
                        tvDateAndTime.setEnabled(true);
                    }
                }
            } else {
                if (object.has("auto")) {
                    if (object.getBoolean("auto")) {
                        object.put(Constants.VALUE, DateUtil.getDateAndTime(currentTime.getTime()));
                        tvDateAndTime.setText(DateUtil.getDateAndTime(currentTime.getTime()));
                        tvDateAndTime.setEnabled(false);
                    } else {
                        object.put(Constants.VALUE, "");
                        tvDateAndTime.setText("");
                        tvDateAndTime.setEnabled(true);
                    }
                } else {
                    object.put(Constants.VALUE, DateUtil.getDateAndTime(currentTime.getTime()));
                }
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        try {
            if (!object.has("auto") || !object.getBoolean("auto")) {
                addCompoundDrawableWithView(tvDateAndTime, object.getString(Constants.VALUE));
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        updateValidationInMap(object, tvDateAndTime, tvDateAndTime.getText().toString());
        tvDateAndTime.setOnClickListener(view -> {
            try {
                showDatePickerDialog(null, tvDateAndTime, true, object, object.getString(Constants.TITLE));
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        });

        tvDateAndTime.setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                Drawable drawableRight = tvDateAndTime.getCompoundDrawables()[2];
                if (drawableRight != null) {
                    if (shouldRemoveSelectedValue(motionEvent,tvDateAndTime,drawableRight)) {
                        try {
                            tvDateAndTime.setText("");
                            updateValidationInMap(object, tvDateAndTime, tvDateAndTime.getText().toString());
                            tvDateAndTime.setError(null);
                            object.put(Constants.VALUE, "");
                            tvDateAndTime.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                        return true;
                    }
                }
            }
            return false;
        });

        setTitleOfComponent(tvDateAndTimeTitle, object);
        try {
            if (object.has("auto") && object.getBoolean("auto") && object.has("visible") && !object.getBoolean("visible")) {
            } else {
                adsLayout.addView(dateAndTime);
                addViewInList(dateAndTime);
                addSeparatorView(this, adsLayout);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    protected int mYear, mMonth, mDay, mHour, mMinute;

    /**
     * inputTypeDate is use for add Date in parent view
     * this method is call when type is text and inputType is date
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    @SuppressLint("ClickableViewAccessibility")
    private void inputTypeDate(JSONObject object, ViewGroup adsLayout) {
        View date = getLayoutInflater().inflate(R.layout.layout_date, adsLayout, false);
        date.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);

        TextView tvDateTitle = date.findViewById(R.id.tvDateTitle);
        setTitleOfComponent(tvDateTitle, object);
        Calendar c = Calendar.getInstance();
        mYear = c.get(Calendar.YEAR);
        mMonth = c.get(Calendar.MONTH);
        mDay = c.get(Calendar.DAY_OF_MONTH);
        TextView tvDateDisplay = date.findViewById(R.id.tvDateDisplay);
        setValidationInMap(object, tvDateDisplay);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                tvDateDisplay.setText(object.getString(Constants.VALUE));
                if (object.has("auto")) {
                    tvDateDisplay.setEnabled(object.getBoolean("auto"));
                }
            } else {
                if (object.has("auto")) {
                    if (object.getBoolean("auto")) {
                        object.put(Constants.VALUE, DateUtil.getDate(c.getTime().getTime()));
                        tvDateDisplay.setText(DateUtil.getDate(c.getTime().getTime()));
                        tvDateDisplay.setEnabled(false);
                    } else {
                        object.put(Constants.VALUE, "");
                        tvDateDisplay.setText("");
                        tvDateDisplay.setEnabled(true);
                    }
                } else {
                    object.put(Constants.VALUE, DateUtil.getDate(c.getTime().getTime()));
                }
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        updateValidationInMap(object, tvDateDisplay, tvDateDisplay.getText().toString());

        try {
            if (object.has("auto") && object.getBoolean("auto")) {
                tvDateDisplay.setEnabled(false);
            } else {
                tvDateDisplay.setEnabled(true);
            }

        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        try {
            if (!object.has("auto") || !object.getBoolean("auto")) {
                addCompoundDrawableWithView(tvDateDisplay, object.getString(Constants.VALUE));
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        tvDateDisplay.setOnClickListener(view -> {
            clearFocus();
            try {
                showDatePickerDialog(tvDateDisplay, null, false, object, object.getString(Constants.TITLE));
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        });

        tvDateDisplay.setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                clearFocus();
                Drawable drawableRight = tvDateDisplay.getCompoundDrawables()[2];
                if (drawableRight != null) {
                    if (shouldRemoveSelectedValue(motionEvent,tvDateDisplay,drawableRight)) {
                        try {
                            tvDateDisplay.setText("");
                            updateValidationInMap(object, tvDateDisplay, tvDateDisplay.getText().toString());
                            tvDateDisplay.setError(null);
                            object.put(Constants.VALUE, "");
                            tvDateDisplay.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                        return true;
                    }
                }
            }
            return false;
        });


        try {
            if (object.has("auto") && object.getBoolean("auto") && object.has("visible") && !object.getBoolean("visible")) {
            } else {
                adsLayout.addView(date);
                addViewInList(date);
                addSeparatorView(this, adsLayout);
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }


    }

    /**
     * showDatePickerDialog is use for date dialog and select data from dialog
     * method is execute when user click on data or data&time
     *
     * @param tvDateDisplay text view is use for set date in this view
     * @param isTimeAlso    is use for is show dialog from date and time than after selecting the date show time dialog
     */
    private void showDatePickerDialog(TextView tvDateDisplay, TextView tvDateAndTime,
                                      boolean isTimeAlso, JSONObject object, String key) {
        DatePickerDialog datePickerDialog = new DatePickerDialog(this,
                (view, year, monthOfYear, dayOfMonth) -> {
                    mDay = dayOfMonth;
                    mMonth = monthOfYear;
                    mYear = year;
                    Date date = new Date();
                    date.setDate(mDay);
                    date.setMonth(mMonth);
                    date.setYear(mYear - 1900);
                    if (isTimeAlso) {
                        showTimePickerDialog(tvDateAndTime, object, key);
                    } else {
                        try {
                            tvDateDisplay.setText(DateUtil.getDate(date.getTime()));
                            updateValidationInMap(object, tvDateDisplay, tvDateDisplay.getText().toString());
                            tvDateDisplay.setError(null);
                            object.put(Constants.VALUE, DateUtil.getDate(date.getTime()));
                            tvDateDisplay.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                }, mYear, mMonth, mDay);
        datePickerDialog.show();
    }

    /**
     * showTimePickerDialog is use for show time selection dialog
     * this method is show when use click on data and time
     *
     * @param tvDateAndTime is use for set data and time
     */
    private void showTimePickerDialog(TextView tvDateAndTime, JSONObject object, String key) {
        // Launch Time Picker Dialog
        TimePickerDialog timePickerDialog = new TimePickerDialog(this,
                (view, hourOfDay, minute) -> {
                    Date date = new Date();
                    date.setHours(hourOfDay);
                    date.setMinutes(minute);
                    date.setDate(mDay);
                    date.setMonth(mMonth);
                    date.setYear(mYear - 1900);
                    tvDateAndTime.setText(DateUtil.getDateAndTime(date.getTime()));
                    tvDateAndTime.setError(null);
                    updateValidationInMap(object, tvDateAndTime, tvDateAndTime.getText().toString());
                    try {
                        object.put(Constants.VALUE, DateUtil.getDateAndTime(date.getTime()));
                        tvDateAndTime.setCompoundDrawablesWithIntrinsicBounds(0, 0, R.drawable.icn_close, 0);
                    } catch (JSONException e) {
                        FirebaseEventUtils.logException(e);
                    }
                    mHour = hourOfDay;
                    mMinute = minute;
                }, mHour, mMinute, false);
        timePickerDialog.show();
    }

    /**
     * inputTypeComment is use for add comment in parent view
     * this method is call when type is text and inputType is comment
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    @SuppressLint("ClickableViewAccessibility")
    protected void inputTypeComment(JSONObject object, ViewGroup adsLayout) {
        View commentView = getLayoutInflater().inflate(R.layout.layout_comment, adsLayout, false);
        commentView.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        CustomEditText edtComment = commentView.findViewById(R.id.edtComment);
        setValidationInMap(object, edtComment);

        edtComment.setOnTouchListener((v, event) -> {
            if (edtComment.hasFocus()) {
                v.getParent().requestDisallowInterceptTouchEvent(true);
                switch (event.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_SCROLL:
                        v.getParent().requestDisallowInterceptTouchEvent(false);
                        return true;
                }
            }
            return false;
        });


        if (!object.has(Constants.ISWEATHERINPUT)) {
            try {
                object.put(Constants.ISWEATHERINPUT, false);
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
        setEditTextListeners(edtComment, false);

        TextView tvCommentTitle = commentView.findViewById(R.id.tvCommentTitle);
        setTitleOfComponent(tvCommentTitle, object);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                edtComment.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, edtComment, edtComment.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
                setHintInComponent(edtComment, object);
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }

        setEditTextChangeListener(object, edtComment);
        adsLayout.addView(commentView);
        addViewInList(commentView);
        addSeparatorView(this, adsLayout);
    }

    private void setFocusChangeListenerForEditText(CustomEditText editText) {
        if (android.os.Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            editText.setOnFocusChangeListener((v, hasFocus) -> {
                if (hasFocus) {
                    lastFocusedView = (AppCompatEditText) v;
                }
            });
        }
    }

    /**
     * inputTypeNumber is use for add Number view in parent view
     * this method is call when type is text and inputType is number
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypeNumber(JSONObject object, ViewGroup adsLayout) {
        View number = getLayoutInflater().inflate(R.layout.layout_number, adsLayout, false);
        TextView tvNumberTitle = number.findViewById(R.id.tvNumberTitle);
        CustomEditText edtNumber = number.findViewById(R.id.edtNumber);
        number.setTag(tag);
        //edtNumber.setFocusable(false);
        addTagInMainObjectAndIncreaseTagId(object);
        setValidationInMap(object, edtNumber);


        setHintInComponent(edtNumber, object);
        setTitleOfComponent(tvNumberTitle, object);
        setEditTextListeners(edtNumber, true);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                edtNumber.setText(object.getString(Constants.VALUE));
                if (edtNumber.getText() != null) {
                    updateValidationInMap(object, edtNumber, edtNumber.getText().toString());
                }
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setEditTextChangeListener(object, edtNumber);
        setFocusChangeListenerForEditText(edtNumber);
        adsLayout.addView(number);
        addViewInList(number);
        addSeparatorView(this, adsLayout);

    }

    /**
     * inputTypePassword is use for add password view in parent view
     * this method is call when type is text and inputType is password
     *
     * @param object    is json object use for get data from object
     * @param adsLayout is view group and work as parent layout
     */
    private void inputTypePassword(JSONObject object, ViewGroup adsLayout) {
        View password = getLayoutInflater().inflate(R.layout.layout_password, adsLayout, false);
        password.setTag(tag);
        addTagInMainObjectAndIncreaseTagId(object);
        CustomEditText edtPassword = password.findViewById(R.id.edtPassword);
        setValidationInMap(object, edtPassword);
        TextView tvTitlePassword = password.findViewById(R.id.tvTitlePassword);

        setHintInComponent(edtPassword, object);
        setTitleOfComponent(tvTitlePassword, object);
        setEditTextListeners(edtPassword, true);
        try {
            updateAutoFillDataInView(object);
            if (object.has(Constants.VALUE)) {
                edtPassword.setText(object.getString(Constants.VALUE));
                updateValidationInMap(object, edtPassword, edtPassword.getText().toString());
            } else {
                object.put(Constants.VALUE, "");
            }
            //object.put(Constants.PARENT_TAG, parentPanelTag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        setEditTextChangeListener(object, edtPassword);
        setFocusChangeListenerForEditText(edtPassword);
        adsLayout.addView(password);
        addViewInList(password);
        addSeparatorView(this, adsLayout);

    }


    /**
     * Common method to add tag in main component json object and increase tag id
     *
     * @param object              component json object
     * @param shouldIncreaseTagId true if want to increase just after adding tag in json object or set false
     */
    private void addTagInMainObjectAndIncreaseTagId(JSONObject object, boolean shouldIncreaseTagId) {
        try {
            object.put(Constants.ID, tag);
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        if (shouldIncreaseTagId) {
            tag++;
        }
    }

    /**
     * Common method to add tag in main component json object and increase tag id
     *
     * @param object component json object
     */
    private void addTagInMainObjectAndIncreaseTagId(JSONObject object) {
        addTagInMainObjectAndIncreaseTagId(object, true);
    }

    public void setSiteAndFormBroadCrumbs(int type, long siteId, long formId, String formName, String formSubmissionId) {
        prepareDataForBreadCrumb(type, MainActivity.currentLatitude, MainActivity.currentLongitude, siteId, formId, formName, formSubmissionId, "");
    }

    public void setSiteAndFormBroadCrumbs(int type, long siteId, long formId, String formName, String formSubmissionId, String preSelectedServices) {
        prepareDataForBreadCrumb(type, MainActivity.currentLatitude, MainActivity.currentLongitude, siteId, formId, formName, formSubmissionId, preSelectedServices);
    }

    public void setSiteAndFormBroadCrumbs(int type, long siteId, long formId, String formName, String formSubmissionId, TMService service) {
        prepareDataForBreadCrumb(type, MainActivity.currentLatitude, MainActivity.currentLongitude, siteId, formId, formName, formSubmissionId, service);
    }


    /**
     * Method to add view in list to manage touch event to site info view in checked in Form
     *
     * @param view
     */
    private void addViewInList(View view) {
        if (isTMForm() && pageNumber == 1) {
            lstView.add(view);
        }
    }


    /**
     * Method to disable touch event of all view of page number 1 if form is check in out and once it is checked in
     * set all view to enable
     *
     * @param enable
     */
    void enableOrDisableView(boolean enable) {
        for (View view : lstView) {
            ViewGroup viewGroup = (ViewGroup) view;
            if (view.getId() == R.id.llSiteInfo || view.getId() == R.id.llNote || view.getId() == R.id.llMapView) {
                setViewAndChildrenEnabled(viewGroup, true);
            } else {
                setViewAndChildrenEnabled(viewGroup, enable);
            }
        }
    }

    private void addServiceViewInList(View view, JSONObject object) {
        if (isTMForm()) {
            lstPreSelectServiceView.put(view, object);
        }
    }

    void disablePreSelectServiceView() {
        if (formData.getIsCheckInOut() && isTMForm() && formData.isPreSelectServices()) {
            for (Map.Entry<View, JSONObject> hashSet : lstPreSelectServiceView.entrySet()) {
                try {
                    JSONObject object = hashSet.getValue();
                    View view = hashSet.getKey();
                    if (object != null) {
                        ViewGroup viewGroup = (ViewGroup) view;
                        if (object.getString(TYPE).equalsIgnoreCase(DYNAMIC_DROPDOWN)
                                && object.has(Constants.IS_MANAGE_CREW_FIELD) && object.getBoolean(Constants.IS_MANAGE_CREW_FIELD)) {
                            setViewAndChildrenEnabled(viewGroup, false);
                        } else if (object.has(Constants.SERVICETYPE) && (object.getString(Constants.SERVICETYPE).equalsIgnoreCase(Constants.AS_DETAIL) || object.getString(Constants.SERVICETYPE).equalsIgnoreCase(Constants.AS_TASK))) {
                            if (object.has(Constants.TnMService)) {
                                TMService tnMService = new Gson().fromJson(object.getString(Constants.TnMService), TMService.class);
                                if (!tnMService.isCompleted()) {
                                    setViewAndChildrenEnabled(viewGroup, false);
                                } else {
                                    //Do not allow user to select once it is completed for checkedin sub form
                                    CheckBox cbItem = view.findViewById(R.id.cbItem);
                                    cbItem.setEnabled(false);
                                }
                            }
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        }
    }

    /**
     * Called from child class to manage code after location result.
     *
     * @param permissions  permissions
     * @param grantResults grantResults
     */
    void doAfterLocationPermissionResult(String[] permissions, int[] grantResults) {
        if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startLocationServiceIfNotStarted();
        } else {

            PopUtils.showCustomTwoButtonAlertDialog(this, getString(R.string.app_name), getString(R.string.to_determine_position_sitefotos_requires_access_to),
                    getString(R.string.open), getString(R.string.txt_cancel), false,
                    (dialog, which) -> {
                        PermissionUtils.requestPermission(this, PermissionUtils.getLocationPermissions(), LOCATION_PERMISSION_REQUEST);
                    }, (dialog, which) -> {

                    });
        }
    }


    private void readJsonFileForService(JSONObject updatedJsonObject, JSONArray updatedPreSelectServiceArray) {
        try {
            JSONArray jsonArray = updatedJsonObject.getJSONArray("pages");
            for (int i = 0; i < jsonArray.length(); i++) {
                try {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (object.has(Constants.ELEMENTS)) {
                        try {
                            JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                            if (array != null) {
                                jsonArrayReadForService(array, updatedPreSelectServiceArray);
                            }
                        } catch (JSONException e) {
                            FirebaseEventUtils.logException(e);
                        }
                    }
                } catch (JSONException e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    protected void updatePreSelectServiceInMainJson(JSONArray updatedPreSelectServiceArray) {
        readJsonFileForService(updatedJsonObject, updatedPreSelectServiceArray);
    }


    private void jsonArrayReadForService(JSONArray jsonArray, JSONArray updatedPreSelectServiceArray) {
        for (int i = 0; i < jsonArray.length(); i++) {
            try {
                JSONObject object = jsonArray.getJSONObject(i);
                if (object.getString(Constants.TYPE).equals(Constants.PANEL)) {
                    JSONArray array = object.getJSONArray(Constants.ELEMENTS);
                    if (array != null) {
                        jsonArrayReadForService(array, updatedPreSelectServiceArray);
                    }
                } else {
                    objectReadForService(object, updatedPreSelectServiceArray);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
        }
    }

    private void objectReadForService(JSONObject serviceObject, JSONArray updatedPreSelectServiceArray) {
        try {
            String type = (String) serviceObject.get(Constants.TYPE);
            switch (type) {
                case Constants.SERVICE:
                    updateModifiedDataWithPreSelectService(serviceObject, updatedPreSelectServiceArray);
                    break;
            }
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
    }


    private void updateModifiedDataWithPreSelectService(JSONObject mainUpdatedObject, JSONArray updatedPreSelectServiceArray) {
        for (int i = 0; i < updatedPreSelectServiceArray.length(); i++) {
            try {
                JSONObject preSelectServiceJsonObject = updatedPreSelectServiceArray.getJSONObject(i);
                if (mainUpdatedObject.has(Constants.ID) && preSelectServiceJsonObject.has(Constants.TEMP_TAG_ID)) {
                    if (mainUpdatedObject.getInt(Constants.ID) == preSelectServiceJsonObject.getInt(Constants.TEMP_TAG_ID)) {
                        mainUpdatedObject = preSelectServiceJsonObject;
                        mainUpdatedObject.remove(Constants.TEMP_TAG_ID);
                        formData.setModifiedFormData(mainUpdatedObject.toString());
                    }
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    void focusOnView(LinearLayout llMain, NestedScrollView scrollView, View focusview) {
        llMain.post(() -> {
            try {
                Point childOffset = new Point();
                getDeepChildOffset(scrollView, focusview.getParent(), focusview, childOffset);
                // Scroll to child.
                scrollView.smoothScrollTo(0, childOffset.y);
            } catch (Exception e) {
                FirebaseEventUtils.logException(e);
            }
        });
    }

    /**
     * Used to get deep child offset.
     * <p/>
     * 1. We need to scroll to child in scrollview, but the child may not the direct child to scrollview.
     * 2. So to get correct child position to scroll, we need to iterate through all of its parent views till the main parent.
     *
     * @param mainParent        Main Top parent.
     * @param parent            Parent.
     * @param child             Child.
     * @param accumulatedOffset Accumulated Offset.
     */
    private void getDeepChildOffset(final ViewGroup mainParent, final ViewParent parent, final View child, final Point accumulatedOffset) {
        if (parent instanceof  ViewGroup) {
            ViewGroup parentGroup = (ViewGroup) parent;
            accumulatedOffset.x += child.getLeft();
            accumulatedOffset.y += child.getTop();
            if (parentGroup.equals(mainParent)) {
                return;
            }
            getDeepChildOffset(mainParent, parentGroup.getParent(), parentGroup, accumulatedOffset);
        }
    }


    private void clearFocus() {
        if (lastFocusedView != null)
            lastFocusedView.clearFocus();
    }

    private View getViewFromTag(int tagId) {
        View view = null;
        try {
            view = vfPages.findViewWithTag(tagId);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
        return view;
    }


    void loadMap() {
        SupportMapFragment mapFragment = (SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.plotOnMap);
        if (mapFragment != null)
            mapFragment.getMapAsync(this);
    }

    @Override
    public void onMapReady(@NonNull GoogleMap googleMap) {
        this.googleMap = googleMap;
        addMarkerOnMap(formData);
        googleMap.setMapType(GoogleMap.MAP_TYPE_SATELLITE);
    }


    /**
     * This method is add marker on google map
     * when user click on marker it will display image.
     *
     * @param formData form data object
     */
    private void addMarkerOnMap(FormData formData) {
        JSONObject jsonImageData;
        JSONArray jsonImageDataArray;
        try {
            if (!TextUtils.isEmpty(formData.getImageData())) {
                jsonImageData = new JSONObject(formData.getImageData());
                jsonImageDataArray = jsonImageData.getJSONArray(Constants.DATA);
                if (jsonImageDataArray.length() > 0) {
                    for (int i = 0; i < jsonImageDataArray.length(); i++) {
                        JSONObject object1 = jsonImageDataArray.getJSONObject(i);
                        Location location = ImageUtil.getLocationFromImage(object1.get(Constants.IMAGEPATHLOW).toString());
                        BitmapDescriptor bitmapDescriptor;
                        if (object1.has(Constants.MAP_PIN_URL)) {
                            bitmapDescriptor = ImageUtil.getMarkerBitmapFromPath(ImageUtil.getMapPinRootFolder(this).concat(new File(object1.getString(Constants.MAP_PIN_URL)).getName()));
                        } else {
                            bitmapDescriptor = BitmapDescriptorFactory.fromResource(R.drawable.icn_marker_blue);
                        }
                        if (location != null) {
                            googleMap.addMarker(new MarkerOptions()
                                    .position(new LatLng(location.getLatitude(), location.getLongitude()))
                                    .title(object1.get(Constants.IMAGEPATHLOW).toString())
                                    .icon(bitmapDescriptor));
                            googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(location.getLatitude(), location.getLongitude()), 16));
                            googleMap.setInfoWindowAdapter(new CustomInfoWindowAdapter(this));
                        }
                    }
                } else {
                    googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(MainActivity.currentLatitude, MainActivity.currentLongitude), 19));
                }
            } else {
                googleMap.animateCamera(CameraUpdateFactory.newLatLngZoom(new LatLng(MainActivity.currentLatitude, MainActivity.currentLongitude), 19));
            }


        } catch (JSONException e) {
            e.printStackTrace();
        }
    }


    void clearGoogleMap() {
        if (googleMap != null) {
            googleMap.clear();
            googleMap = null;
        }
    }

    public void showImageComponentList() {
        Iterator iterator = mapImageUpload.keySet().iterator();
        List<ImageComponent> lstImageComponent = new ArrayList<>();
        while (iterator.hasNext()) {
            int key = (int) iterator.next();
            ImageComponent imageComponent = new ImageComponent();
            imageComponent.setKey(key);
            JSONObject imageJsonObject = (JSONObject) mapImageUpload.get(key);
            String type = "";
            try {
                if (imageJsonObject != null) {
                    type = imageJsonObject.getString(Constants.TYPE);
                }
            } catch (JSONException e) {
                FirebaseEventUtils.logException(e);
            }
            if (!TextUtils.isEmpty(type) && type.equals(IMAGE_UPLOAD)) {
                imageComponent.setImageObject((JSONObject) mapImageUpload.get(key));
                lstImageComponent.add(imageComponent);
            }
        }
        PopUtils.showImageComponentsView(this, lstImageComponent, new OnImageComponentSelected() {
            @Override
            public void onItemSelected(ImageComponent imageComponents) {
                openCameraToGetImage(imageComponents.getKey(), false, true);
            }
        });


    }


    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.enter_from_left, R.anim.exit_to_right);
    }

    public void saveModifiedForm() {
        if (updatedJsonObject != null && formData != null) {
            if (isTMForm()) {
                TblTMForms tblTMForms = new TblTMForms(this);
                tblTMForms.updateModifiedFormByPkId(formData.getFormPKId(), updatedJsonObject.toString());
            } else {
                TblForms tblForms = new TblForms(this);
                tblForms.updateModifiedFormByPkId(formData.getFormPKId(), updatedJsonObject.toString());
            }
            formString = updatedJsonObject.toString();
        }
    }



}