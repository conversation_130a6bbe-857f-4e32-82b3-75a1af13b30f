package com.sitefotos;

import android.graphics.drawable.Drawable;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.widget.AppCompatEditText;
import androidx.fragment.app.Fragment;

import com.sitefotos.site.screens.WorkFragment;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

public class BaseFragment extends Fragment {


    public WorkFragment getInstanceOfWorkFragment() {
        WorkFragment workFragment = null;
        if (getFragmentManager() != null && getFragmentManager().findFragmentByTag("3") != null) {
            Fragment fragment = getFragmentManager().findFragmentByTag("3");
            if ((fragment instanceof WorkFragment)) {
                workFragment = (WorkFragment) fragment;
            }
        }
        return workFragment;
    }

    /**
     * ShowToast Message Notification
     *
     * @param msg Toast Title Message
     */
    public void showBackGroundToast(String msg) {
        Toast.makeText(getActivity(), msg, Toast.LENGTH_SHORT).show();
    }

    /**
     * ShowToast Message Notification for short time when app is on foreground
     *
     * @param msg Toast Title Message
     */
    public void showForeGroundToast(String msg) {
        StaticUtils.showForeGroundToast(getActivity(),msg,false);
    }

    /**
     * ShowToast Message Notification for long time when app is on foreground
     *
     * @param msg Toast Title Message
     */
    public void showForeGroundToastLong(String msg) {
        StaticUtils.showForeGroundToast(getActivity(),msg,true);
    }

    public void manageSearchViewCloseTouchEvent(AppCompatEditText edtSearch) {
        edtSearch.setOnTouchListener((view, motionEvent) -> {
            if (motionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                Drawable drawableRight = edtSearch.getCompoundDrawables()[2];
                if (drawableRight != null) {
                    if (motionEvent.getRawX() >= edtSearch.getRight() - edtSearch.getPaddingEnd() - drawableRight.getBounds().width() - 30) {
                        edtSearch.setText("");
                        return true;
                    }
                }
            }
            return false;
        });
    }
}
