package com.sitefotos.asynk;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;

import com.sitefotos.Constants;
import com.sitefotos.interfaces.OnSignatureGenerationComplete;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.StaticUtils;

public class SignatureLocalPathGenerator extends AsyncTask<Void, Void, Void> {
    private String imagePath;
    private Context context;
    OnSignatureGenerationComplete onSignatureGenerationComplete;
    int tagName;
    String imagePathLow = "";
    String imagePathHigh = "";

    public SignatureLocalPathGenerator(Context context, String imagePath , int tagName,OnSignatureGenerationComplete onSignatureGenerationComplete) {
        this.imagePath = imagePath;
        this.context = context;
        this.onSignatureGenerationComplete = onSignatureGenerationComplete;
        this.tagName = tagName;
    }

    @Override
    protected Void doInBackground(Void... voids) {
        Bitmap lowBitmap, highBitmap;

        int originalBitmapHeight, originalBitmapWidth;

        highBitmap = ImageUtil.getBitmapFromPath(StaticUtils.getRealPathFromURI(context, Uri.parse(imagePath)));
        originalBitmapHeight = highBitmap.getHeight();
        originalBitmapWidth = highBitmap.getWidth();


        lowBitmap = ImageUtil.getScaledBitmap(highBitmap, originalBitmapWidth, originalBitmapHeight);

        imagePathLow = ImageUtil.saveImageFromBitmap(context,lowBitmap, "", false);
        if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false)) {
            imagePathHigh = ImageUtil.saveImageFromBitmap(context,highBitmap, "", true);
        }
        if (!lowBitmap.isRecycled()) {
            lowBitmap.recycle();
        }

        if (!highBitmap.isRecycled()) {
            highBitmap.recycle();
        }
        return null;
    }

    @Override
    protected void onPostExecute(Void aVoid) {
        super.onPostExecute(aVoid);
        onSignatureGenerationComplete.signaturProcess(imagePathLow, imagePathHigh,tagName,imagePath);
    }
}
