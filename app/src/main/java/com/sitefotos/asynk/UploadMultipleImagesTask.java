package com.sitefotos.asynk;

import static com.sitefotos.Constants.IMAGEPATHHIGH;
import static com.sitefotos.Constants.IMAGEPATHLOW;

import android.content.Context;
import android.graphics.Bitmap;
import android.location.Location;
import android.net.Uri;
import android.os.AsyncTask;

import com.sitefotos.Constants;
import com.sitefotos.camera.PropertiesVo;
import com.sitefotos.gallery.AllImageModel;
import com.sitefotos.interfaces.OnPostResult;
import com.sitefotos.models.Cluster;
import com.sitefotos.models.FormData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblCluster;
import com.sitefotos.storage.tables.TblProperties;
import com.sitefotos.util.DBUtils;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.PolygonCalculation;
import com.sitefotos.util.StaticUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;


public class UploadMultipleImagesTask extends AsyncTask<String, Void, JSONArray> {

    private List<AllImageModel> lstImages;
    private Context context;
    private OnPostResult onPostResult;

    private List<PropertiesVo> lstProperties = null;
    private List<Cluster> lstClusters = null;
    private double currentLatitude;
    private double currentLongitude;
    private long distanceAccuracy;
    private List<String> lstIgnoreList;
    private FormData formData;
    private boolean isTMForm;
    private int tagId;
    private boolean isIssue;

    public UploadMultipleImagesTask(Context context, List<AllImageModel> lstImages, double currentLatitude,
                                    double currentLongitude, long distanceAccuracy, List<String> lstIgnoreList,
                                    FormData formData, boolean isTMForm, int tagId, boolean isIssue,
                                    OnPostResult onPostResult) {
        this.context = context;
        this.lstImages = lstImages;
        this.currentLatitude = currentLatitude;
        this.currentLongitude = currentLongitude;
        this.distanceAccuracy = distanceAccuracy;
        this.lstIgnoreList = lstIgnoreList;
        this.formData = formData;
        this.isTMForm = isTMForm;
        this.tagId = tagId;
        this.isIssue = isIssue;
        this.onPostResult = onPostResult;
    }

    @Override
    protected void onPreExecute() {
        TblProperties tblProperties = new TblProperties(context);
        TblCluster tblCluster = new TblCluster(context);

        lstProperties = tblProperties.getAllProperties();
        lstClusters = tblCluster.getAllClusterData();
    }

    @Override
    protected JSONArray doInBackground(String... params) {
        JSONArray imageArray = new JSONArray();
        try {
            for (int i = 0; i < lstImages.size(); i++) {
                String buildingId = "0";
                String imagePathHigh = "", imagePathLow = "";
                Location locationExif = null;
                String imagePath = null;

                Bitmap bitmapLow, bitmapHigh;

                bitmapHigh = ImageUtil.getBitmapFromPath(StaticUtils.getRealPathFromURI(context, Uri.parse(lstImages.get(i).getImagePath())));

                try {
                    imagePath = StaticUtils.getRealPathFromURI(context, Uri.parse(lstImages.get(i).getImagePath()));
                    locationExif = ImageUtil.getLocationFromImage(imagePath);
                    PolygonCalculation polygonCalculation = PolygonCalculation.getPolygonInstance();

                    polygonCalculation.getFilteredPropertyListFromPolygon(context, true, locationExif,
                            currentLatitude, currentLongitude, distanceAccuracy, lstIgnoreList, lstClusters, lstProperties);
                    buildingId = polygonCalculation.buildingId;
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                } finally {
                    // Convert bitmap to lower resolution
                    bitmapLow = ImageUtil.createScaledBitmap(bitmapHigh);

                    if (bitmapLow != null) {
                        imagePathLow = ImageUtil.saveImageFromBitmap(context,bitmapLow, imagePath, false);
                    }

                    if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_ORIGINAL_SIZE, false)) {
                        imagePathHigh = ImageUtil.saveImageFromBitmap(context,bitmapHigh, imagePath, true);
                    }
                    String exifDateWithFormat = null;
                    if (imagePath != null && !imagePath.isEmpty()) {
                        String exifDate = DateUtil.getDateFromExifMetadata(imagePath);
                        exifDateWithFormat = DateUtil.convertExpectedDateFormat(exifDate);
                    }

                    DBUtils.insertImageUploadData(context, imagePathLow, imagePathHigh, "", exifDateWithFormat, buildingId,
                            StaticUtils.getLocation(locationExif, currentLatitude, currentLongitude, true), 0.0, 0.0,
                            formData, isTMForm, tagId, isIssue, null,true,true);


                    if (formData != null) {
                        JSONObject imageObject = new JSONObject();
                        imageObject.put(IMAGEPATHLOW, imagePathLow);
                        imageObject.put(IMAGEPATHHIGH, imagePathHigh);
                        imageArray.put(imageObject);
                    }
                    ImageUtil.recycleBitmap(bitmapLow);
                    ImageUtil.recycleBitmap(bitmapHigh);
                }
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        } finally {
            return imageArray;
        }
    }

    @Override
    protected void onPostExecute(JSONArray jsonArray) {
        // Start upload of images if any
        onPostResult.PhotoSaveAndPrepareOnPost(jsonArray);
    }

}
