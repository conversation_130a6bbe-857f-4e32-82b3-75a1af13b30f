package com.sitefotos.asynk;

import static com.sitefotos.Constants.IMAGEPATHLOW;

import android.content.Context;
import android.net.Uri;
import android.os.AsyncTask;
import android.text.TextUtils;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Directory;
import com.drew.metadata.Metadata;
import com.drew.metadata.Tag;
import com.sitefotos.BuildConfig;
import com.sitefotos.Constants;
import com.sitefotos.interfaces.OnPostResult;
import com.sitefotos.models.FormData;
import com.sitefotos.models.UploadImageData;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.tables.TblForms;
import com.sitefotos.storage.tables.TblTMForms;
import com.sitefotos.storage.tables.TblUploadImage;
import com.sitefotos.util.DateUtil;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.StaticUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.Date;

public class AddSignatureInUploadQueue extends AsyncTask<String, Void, JSONArray> {

    private Context context;
    private FormData formData;
    private OnPostResult onPostResult;
    private boolean isTmForm;

    public AddSignatureInUploadQueue(Context context, FormData formData, boolean isTmForm, OnPostResult onPostResult) {
        this.context = context;
        this.formData = formData;
        this.isTmForm = isTmForm;
        this.onPostResult = onPostResult;
    }

    @Override
    protected void onPreExecute() {
    }

    @Override
    protected JSONArray doInBackground(String... params) {
        try {
            JSONArray imageArray = new JSONArray();
            JSONObject jsonImageData;
            FormData formData2;
            if (isTmForm) {
                TblTMForms tblTMForms = new TblTMForms(context);
                formData2 = tblTMForms.getFormDataByPKId(formData.getFormPKId());
            } else {
                TblForms tblForms = new TblForms(context);
                formData2 = tblForms.getFormDataByPKId(formData.getFormPKId());
            }
            jsonImageData = new JSONObject(formData2.getImageData());
            JSONArray jsonImageArray = jsonImageData.getJSONArray(Constants.DATA);

            for (int k = 0; k < jsonImageArray.length(); k++) {
                JSONObject iJsonObject = jsonImageArray.getJSONObject(k);
                if (iJsonObject.has(Constants.IS_SIGNATURE) && iJsonObject.getBoolean(Constants.IS_SIGNATURE)) {
                    String exifDateString = null;
                    Metadata exifMetadata = null;

                    String imagePath;
                    UploadImageData uploadImageData = new UploadImageData();

                    try {
                        imagePath = StaticUtils.getRealPathFromURI(context, Uri.parse(iJsonObject.getString(IMAGEPATHLOW)));
                        try {
                            exifMetadata = ImageMetadataReader.readMetadata(new File(imagePath));
                        } catch (VerifyError | ImageProcessingException | IOException ignored) {
                        }
                        exifDateString = "";

                        if (exifMetadata != null) {
                            for (Directory directory : exifMetadata.getDirectories()) {
                                for (Tag tag : directory.getTags()) {
                                    if (tag.getTagName().equalsIgnoreCase("Date/Time")) {
                                        // Date time of image from EXIF interface .
                                        exifDateString = tag.getDescription().trim();
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        FirebaseEventUtils.logException(e);
                    } finally {
                        uploadImageData.setImagePathLow(iJsonObject.getString(IMAGEPATHLOW));
                        uploadImageData.setBuilding(String.valueOf(formData2.getLastBuildingId()));
                        uploadImageData.setAccessCode(AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, ""));
                        uploadImageData.setEmail(AppPrefShared.getString(Constants.LOGGED_IN_USER_EMAIL_ADDRESS, ""));
                        uploadImageData.setValid(true);
                        uploadImageData.setUploadOriginalSize(false);

                        uploadImageData.setFormImage(false);
                        uploadImageData.setFormSign(true);
                        if (isTmForm) {
                            uploadImageData.setTmFormPkId(formData.getFormPKId());
                            uploadImageData.setWpId(formData.getSiteId());
                        } else {
                            uploadImageData.setFormPkId(formData.getFormPKId());
                            uploadImageData.setWpId(-2);
                        }
                        uploadImageData.setFormId(formData.getFormId());
                        uploadImageData.setUuid(StaticUtils.getUuid());
                        uploadImageData.setFormData(String.valueOf(formData.getFormPKId()).concat(",").concat(String.valueOf(formData.getFormId())));
                        uploadImageData.setRetryCount(0);
                        uploadImageData.setTagId(0);
                        uploadImageData.setTimeInUnix(System.currentTimeMillis() / 1000);
                        uploadImageData.setAppVersion(BuildConfig.VERSION_NAME);
                        if (!TextUtils.isEmpty(exifDateString))
                            uploadImageData.setDate(exifDateString);
                        else
                            uploadImageData.setDate(DateUtil.fullDateTimeT.format(new Date()));
                        // get json object fro uploadVO object
                        TblUploadImage tblUploadImage = new TblUploadImage(context);
                        boolean isDataInserted = tblUploadImage.insertData(uploadImageData);
                        if (!isDataInserted) {
                            FirebaseEventUtils.InsertImageDataExceptionEvent(context, uploadImageData.getUuid());
                        } else {
                            FirebaseEventUtils.InsertImageDataEvent(context, uploadImageData.getUuid());
                        }
                    }
                }
            }
            return imageArray;
        } catch (JSONException e) {
            FirebaseEventUtils.logException(e);
        }
        return null;
    }

    @Override
    protected void onPostExecute(JSONArray result) {
        // Start upload of images if any
        onPostResult.PhotoSaveAndPrepareOnPost(result);

    }

}
