package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

public class FormDetail implements Parcelable {

    private int formId;
    private  int formPKId;

    public int getFormId() {
        return formId;
    }

    public void setFormId(int formId) {
        this.formId = formId;
    }

    public int getFormPKId() {
        return formPKId;
    }

    public void setFormPKId(int formPKId) {
        this.formPKId = formPKId;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.formId);
        dest.writeInt(this.formPKId);
    }

    public FormDetail() {
    }

    protected FormDetail(Parcel in) {
        this.formId = in.readInt();
        this.formPKId = in.readInt();
    }

    public static final Parcelable.Creator<FormDetail> CREATOR = new Parcelable.Creator<FormDetail>() {
        @Override
        public FormDetail createFromParcel(Parcel source) {
            return new FormDetail(source);
        }

        @Override
        public FormDetail[] newArray(int size) {
            return new FormDetail[size];
        }
    };
}
