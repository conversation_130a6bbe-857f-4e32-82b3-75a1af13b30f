package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class DynamicDropDownItem implements Parcelable {

    private long pkId = 0;
    @SerializedName("Id")
    private int id = 0;
    @SerializedName("Value")
    private String value = "";
    @SerializedName("Type")
    private String type = "";
    private long createdDate;
    private long updatedDate;

    boolean isSelected;


    public DynamicDropDownItem() {


    }

    protected DynamicDropDownItem(Parcel in) {
        pkId = in.readLong();
        id = in.readInt();
        value = in.readString();
        type = in.readString();
        createdDate = in.readLong();
        updatedDate = in.readLong();
        isSelected = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(pkId);
        dest.writeInt(id);
        dest.writeString(value);
        dest.writeString(type);
        dest.writeLong(createdDate);
        dest.writeLong(updatedDate);
        dest.writeByte((byte) (isSelected ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<DynamicDropDownItem> CREATOR = new Creator<DynamicDropDownItem>() {
        @Override
        public DynamicDropDownItem createFromParcel(Parcel in) {
            return new DynamicDropDownItem(in);
        }

        @Override
        public DynamicDropDownItem[] newArray(int size) {
            return new DynamicDropDownItem[size];
        }
    };

    public long getPkId() {
        return pkId;
    }

    public void setPkId(long pkId) {
        this.pkId = pkId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }



    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

}
