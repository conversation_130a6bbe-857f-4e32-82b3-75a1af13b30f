package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * Created by dk on 21/8/17.
 */

public class Material implements Parcelable {

    @SerializedName("MaterialID")
    private int materialID;
    @SerializedName("MaterialName")
    private String materialName = "";
    @SerializedName("MaterialUnit")
    private String materialUnit = "";
    private double Usage;

    public int getMaterialID() {
        return materialID;
    }

    public void setMaterialID(int materialID) {
        this.materialID = materialID;
    }

    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    public String getMaterialUnit() {
        return materialUnit;
    }

    public void setMaterialUnit(String materialUnit) {
        this.materialUnit = materialUnit;
    }

    public double getUsage() {
        return Usage;
    }

    public void setUsage(double usage) {
        this.Usage = usage;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.materialID);
        dest.writeString(this.materialName);
        dest.writeString(this.materialUnit);
        dest.writeDouble(this.Usage);
    }

    public Material() {
    }

    protected Material(Parcel in) {
        this.materialID = in.readInt();
        this.materialName = in.readString();
        this.materialUnit = in.readString();
        this.Usage = in.readDouble();
    }

    public static final Creator<Material> CREATOR = new Creator<Material>() {
        @Override
        public Material createFromParcel(Parcel source) {
            return new Material(source);
        }

        @Override
        public Material[] newArray(int size) {
            return new Material[size];
        }
    };

    public boolean equals(Object obj) {
        if (obj == null || !Material.class.isAssignableFrom(obj.getClass()) || this.materialID != ((Material) obj).getMaterialID()) {
            return false;
        }
        return true;
    }
}



