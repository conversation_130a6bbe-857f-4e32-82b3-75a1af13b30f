package com.sitefotos.models;

/**
 *
 */

public class UploadImageData {

    private int uploadId;
    private long formId;
    private long formPkId;
    private long tmFormPkId;
    private long wpId;
    private int tagId;
    private long wpPkId; // Site ID
    private String uploadUrlLow;
    private String uploadUrlHigh;
    private String accessCode;
    private String lrImage;
    private String hrImage;
    private String date;
    private boolean isUploadOriginalSize;
    private String description;
    private String building;
    private String email;
    private String imagePathLow;
    private String imagePathHigh;
    private double latitude;
    private double longitude;
    private double extraLatitude;
    private double extraLongitude;
    private boolean isValid;
    private boolean isFormImage;
    private boolean isFormSign;
    private int retryCountImageLow;
    private long capturedTime;
    private String uuid;
    private double totalProgress;
    private boolean isImageUploadProcessStart;
    private boolean isLowImageUploadProcessStarted;
    private boolean isHighImageUploadProcessStarted;
    private long createdDate;
    private long updatedDate;
    private long uploadStartTime;
    private String tags = "";
    private boolean isHighImageUploaded;
    private boolean isLowImageUploaded;
    private double lowImageProgress;
    private double highImageProgress;
    private String appVersion;
    private int cameraImage;

    public UploadImageData() {

    }

    public int getUploadId() {
        return uploadId;
    }

    public void setUploadId(int uploadId) {
        this.uploadId = uploadId;
    }

    public long getFormId() {
        return formId;
    }

    public void setFormId(long formId) {
        this.formId = formId;
    }

    public long getFormPkId() {
        return formPkId;
    }

    public void setFormPkId(long formPkId) {
        this.formPkId = formPkId;
    }

    public String getUploadUrlLow() {
        return uploadUrlLow;
    }

    public void setUploadUrlLow(String uploadUrlLow) {
        this.uploadUrlLow = uploadUrlLow;
    }

    public String getUploadUrlHigh() {
        return uploadUrlHigh;
    }

    public void setUploadUrlHigh(String uploadUrlHigh) {
        this.uploadUrlHigh = uploadUrlHigh;
    }

    public String getAccessCode() {
        return accessCode;
    }

    public void setAccessCode(String accessCode) {
        this.accessCode = accessCode;
    }

    public String getLrImage() {
        return lrImage;
    }

    public void setLrImage(String lrImage) {
        this.lrImage = lrImage;
    }

    public String getHrImage() {
        return hrImage;
    }

    public void setHrImage(String hrImage) {
        this.hrImage = hrImage;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getBuilding() {
        return building;
    }

    public void setBuilding(String building) {
        this.building = building;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getImagePathLow() {
        return imagePathLow;
    }

    public void setImagePathLow(String imagePathLow) {
        this.imagePathLow = imagePathLow;
    }

    public String getImagePathHigh() {
        return imagePathHigh;
    }

    public void setImagePathHigh(String imagePathHigh) {
        this.imagePathHigh = imagePathHigh;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getExtraLatitude() {
        return extraLatitude;
    }

    public void setExtraLatitude(double extraLatitude) {
        this.extraLatitude = extraLatitude;
    }

    public double getExtraLongitude() {
        return extraLongitude;
    }

    public void setExtraLongitude(double extraLongitude) {
        this.extraLongitude = extraLongitude;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public void setUploadOriginalSize(boolean uploadOriginalSize) {
        isUploadOriginalSize = uploadOriginalSize;
    }

    public boolean isFormImage() {
        return isFormImage;
    }

    public void setFormImage(boolean formImage) {
        isFormImage = formImage;
    }

    public boolean isFormSign() {
        return isFormSign;
    }

    public void setFormSign(boolean formSign) {
        isFormSign = formSign;
    }

    public int getRetryCount() {
        return retryCountImageLow;
    }

    public void setRetryCount(int retryCountImageLow) {
        this.retryCountImageLow = retryCountImageLow;
    }

    public double getTotalProgress() {
        return totalProgress;
    }

    public void setTotalProgress(double totalProgress) {
        this.totalProgress = totalProgress;
    }

    public boolean isUploadOriginalSize() {
        return isUploadOriginalSize;
    }

    public String formData = "";

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }


    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public long getWpId() {
        return wpId;
    }

    public void setWpId(long wpId) {
        this.wpId = wpId;
    }

    public long getWpPkId() {
        return wpPkId;
    }

    public void setWpPkId(long wpPkId) {
        this.wpPkId = wpPkId;
    }

    public int getTagId() {
        return tagId;
    }

    public void setTagId(int tagId) {
        this.tagId = tagId;
    }

    public long getTmFormPkId() {
        return tmFormPkId;
    }

    public void setTmFormPkId(long tmFormPkId) {
        this.tmFormPkId = tmFormPkId;
    }

    public long getTimeInUnix() {
        return capturedTime;
    }

    public void setTimeInUnix(long capturedTime) {
        this.capturedTime = capturedTime;
    }

    public boolean isImageUploadProcessStart() {
        return isImageUploadProcessStart;
    }

    public void setImageUploadProcessStart(boolean imageUploadProcessStart) {
        isImageUploadProcessStart = imageUploadProcessStart;
    }

    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isHighImageUploaded() {
        return isHighImageUploaded;
    }

    public void setHighImageUploaded(boolean highImageUploaded) {
        isHighImageUploaded = highImageUploaded;
    }

    public boolean isLowImageUploaded() {
        return isLowImageUploaded;
    }

    public void setLowImageUploaded(boolean lowImageUploaded) {
        isLowImageUploaded = lowImageUploaded;
    }

    public double getLowImageProgress() {
        return lowImageProgress;
    }

    public void setLowImageProgress(double lowImageProgress) {
        this.lowImageProgress = lowImageProgress;
    }

    public double getHighImageProgress() {
        return highImageProgress;
    }

    public void setHighImageProgress(double highImageProgress) {
        this.highImageProgress = highImageProgress;
    }

    public boolean isLowImageUploadProcessStarted() {
        return isLowImageUploadProcessStarted;
    }

    public void setLowImageUploadProcessStarted(boolean lowImageUploadProcessStarted) {
        isLowImageUploadProcessStarted = lowImageUploadProcessStarted;
    }

    public boolean isHighImageUploadProcessStarted() {
        return isHighImageUploadProcessStarted;
    }

    public void setHighImageUploadProcessStarted(boolean highImageUploadProcessStarted) {
        isHighImageUploadProcessStarted = highImageUploadProcessStarted;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public int getCameraImage() {
        return cameraImage;
    }

    public void setCameraImage(int cameraImage) {
        this.cameraImage = cameraImage;
    }
}
