package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class ClientDetails implements Parcelable {

    @SerializedName("FirstName")
    private String firstName;
    @SerializedName("LastName")
    private String lastName;
    @SerializedName("Company")
    private String company;
    @SerializedName("Mobile")
    private String mobile;
    @SerializedName("Email")
    private String email;
    @SerializedName("Phone")
    private String phone;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.firstName);
        dest.writeString(this.lastName);
        dest.writeString(this.company);
        dest.writeString(this.mobile);
        dest.writeString(this.email);
        dest.writeString(this.phone);
    }

    public ClientDetails() {
    }

    protected ClientDetails(Parcel in) {
        this.firstName = in.readString();
        this.lastName = in.readString();
        this.company = in.readString();
        this.mobile = in.readString();
        this.email = in.readString();
        this.phone = in.readString();
    }

    public static final Parcelable.Creator<ClientDetails> CREATOR = new Parcelable.Creator<ClientDetails>() {
        @Override
        public ClientDetails createFromParcel(Parcel source) {
            return new ClientDetails(source);
        }

        @Override
        public ClientDetails[] newArray(int size) {
            return new ClientDetails[size];
        }
    };

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }
}
