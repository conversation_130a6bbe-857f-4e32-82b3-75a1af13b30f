package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

public class MapPinData implements Parcelable {

    private int id;
    private String pngURL;
    private String svgURL;
    private String pinLabel;
    private boolean isSelected;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getPngURL() {
        return pngURL;
    }

    public void setPngURL(String pngURL) {
        this.pngURL = pngURL;
    }

    public String getSvgURL() {
        return svgURL;
    }

    public void setSvgURL(String svgURL) {
        this.svgURL = svgURL;
    }

    public String getPinLabel() {
        return pinLabel;
    }

    public void setPinLabel(String pinLabel) {
        this.pinLabel = pinLabel;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public MapPinData(){

    }
    protected MapPinData(Parcel in) {
        id = in.readInt();
        pngURL = in.readString();
        svgURL = in.readString();
        pinLabel = in.readString();
        isSelected = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(pngURL);
        dest.writeString(svgURL);
        dest.writeString(pinLabel);
        dest.writeByte((byte) (isSelected ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<MapPinData> CREATOR = new Creator<MapPinData>() {
        @Override
        public MapPinData createFromParcel(Parcel in) {
            return new MapPinData(in);
        }

        @Override
        public MapPinData[] newArray(int size) {
            return new MapPinData[size];
        }
    };


}
