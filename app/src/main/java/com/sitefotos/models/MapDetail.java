package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

public class MapDetail implements Parcelable {

    @SerializedName("MapName")
    private String mapName;
    @SerializedName("MapURL")
    private String mapURL;

    public String getMapName() {
        return mapName;
    }

    public void setMapName(String mapName) {
        this.mapName = mapName;
    }

    public String getMapURL() {
        return mapURL;
    }

    public void setMapURL(String mapURL) {
        this.mapURL = mapURL;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.mapName);
        dest.writeString(this.mapURL);
    }

    public MapDetail() {
    }

    protected MapDetail(Parcel in) {
        this.mapName = in.readString();
        this.mapURL = in.readString();
    }

    public static final Parcelable.Creator<MapDetail> CREATOR = new Parcelable.Creator<MapDetail>() {
        @Override
        public MapDetail createFromParcel(Parcel source) {
            return new MapDetail(source);
        }

        @Override
        public MapDetail[] newArray(int size) {
            return new MapDetail[size];
        }
    };
}
