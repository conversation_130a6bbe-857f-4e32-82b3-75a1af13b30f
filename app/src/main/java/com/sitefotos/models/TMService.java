package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class TMService implements Parcelable {

    @SerializedName("ServiceID")
    private int serviceID;
    @SerializedName("ServiceName")
    private String serviceName;
    @SerializedName("ServiceCategory")
    private String serviceCategory;

    @SerializedName("ServiceType")
    private String serviceType;
    @SerializedName("ServiceOptions")
    private ServiceOptions serviceOptions;


    private boolean isCompleted;
    private String people = "";
    private String hours = "";
    private long initialStartTime;
    private long StartTime;
    private long StopTime;
    private long updatedStopTime;
    private long startChronometerTime;
    private boolean preSelectAttempted;
    private List<TimeLog> lstTimeLog = new ArrayList<>();

    protected TMService(Parcel in) {
        serviceID = in.readInt();
        serviceName = in.readString();
        serviceCategory = in.readString();
        serviceType = in.readString();
        serviceOptions = in.readParcelable(ServiceOptions.class.getClassLoader());
        isCompleted = in.readByte() != 0;
        people = in.readString();
        hours = in.readString();
        initialStartTime = in.readLong();
        StartTime = in.readLong();
        StopTime = in.readLong();
        updatedStopTime = in.readLong();
        startChronometerTime = in.readLong();
        preSelectAttempted = in.readByte() != 0;
        lstTimeLog = in.createTypedArrayList(TimeLog.CREATOR);
    }

    public TMService() {

    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(serviceID);
        dest.writeString(serviceName);
        dest.writeString(serviceCategory);
        dest.writeString(serviceType);
        dest.writeParcelable(serviceOptions, flags);
        dest.writeByte((byte) (isCompleted ? 1 : 0));
        dest.writeString(people);
        dest.writeString(hours);
        dest.writeLong(initialStartTime);
        dest.writeLong(StartTime);
        dest.writeLong(StopTime);
        dest.writeLong(updatedStopTime);
        dest.writeLong(startChronometerTime);
        dest.writeTypedList(lstTimeLog);
        dest.writeByte((byte) (preSelectAttempted ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<TMService> CREATOR = new Creator<TMService>() {
        @Override
        public TMService createFromParcel(Parcel in) {
            return new TMService(in);
        }

        @Override
        public TMService[] newArray(int size) {
            return new TMService[size];
        }
    };

    public String getHours() {
        return hours;
    }

    public String getPeople() {
        return people;
    }

    public void setHours(String hours) {
        this.hours = hours;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
    }

    public void setPeople(String people) {
        this.people = people;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public int getServiceID() {
        return serviceID;
    }

    public void setServiceID(int serviceID) {
        this.serviceID = serviceID;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceCategory() {
        return serviceCategory;
    }

    public void setServiceCategory(String serviceCategory) {
        this.serviceCategory = serviceCategory;
    }


    public ServiceOptions getServiceOptions() {
        return serviceOptions;
    }

    public void setServiceOptions(ServiceOptions serviceOptions) {
        this.serviceOptions = serviceOptions;
    }

    public long getStartTime() {
        return StartTime;
    }

    public void setStartTime(long startTime) {
        this.StartTime = startTime;
    }

    public long getStopTime() {
        return StopTime;
    }

    public void setStopTime(long stopTime) {
        this.StopTime = stopTime;
    }

    public long getStartChronometerTime() {
        return startChronometerTime;
    }

    public void setStartChronometerTime(long startChronometerTime) {
        this.startChronometerTime = startChronometerTime;
    }

    public long getInitialStartTime() {
        return initialStartTime;
    }

    public void setInitialStartTime(long initialStartTime) {
        this.initialStartTime = initialStartTime;
    }

    public long getUpdatedStopTime() {
        return updatedStopTime;
    }

    public void setUpdatedStopTime(long updatedStopTime) {
        this.updatedStopTime = updatedStopTime;
    }


    public String getServiceType() {
        return serviceType;
    }

    public void setServiceType(String serviceType) {
        this.serviceType = serviceType;
    }


    public List<TimeLog> getLstTimeLog() {
        return lstTimeLog;
    }

    public void setLstTimeLog(List<TimeLog> lstTimeLog) {
        this.lstTimeLog = lstTimeLog;
    }

    public boolean isPreSelectAttempted() {
        return preSelectAttempted;
    }

    public void setPreSelectAttempted(boolean preSelectAttempted) {
        this.preSelectAttempted = preSelectAttempted;
    }
}
