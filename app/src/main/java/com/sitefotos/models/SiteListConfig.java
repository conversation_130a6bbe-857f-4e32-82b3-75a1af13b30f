package com.sitefotos.models;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SiteListConfig  {

    @SerializedName("Ordered")
    private boolean ordered;

    @SerializedName("AllSites")
    private boolean allSites;

    @SerializedName("siteConfig")
    private List<SiteConfig> siteConfig = null;

    @SerializedName("siteForms")
    private List<FormData> siteForms = null;

    @SerializedName("siteFilterDistance")
    private float siteFilterDistance = -1.0f;

    public boolean getOrdered() {
        return ordered;
    }

    public void setOrdered(boolean ordered) {
        this.ordered = ordered;
    }

    public boolean getAllSites() {
        return allSites;
    }

    public void setAllSites(boolean allSites) {
        this.allSites = allSites;
    }


    public float getSiteFilterDistance() {
        return siteFilterDistance;
    }

    public void setSiteFilterDistance(float siteFilterDistance) {
        this.siteFilterDistance = siteFilterDistance;
    }

    public List<SiteConfig> getSiteConfig() {
        return siteConfig;
    }

    public void setSiteConfig(List<SiteConfig> siteConfig) {
        this.siteConfig = siteConfig;
    }

    public List<FormData> getSiteForms() {
        return siteForms;
    }

    public void setSiteForms(List<FormData> siteForms) {
        this.siteForms = siteForms;
    }
}
