package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class ServiceOptions implements Parcelable {

    @SerializedName("Multiple")
    private String multiple;
    @SerializedName("Options")
    private List<String> options = null;

    private List<String> optionsValue = null;

    public List<String> getOptionsValue() {
        return optionsValue;
    }

    public void setOptionsValue(List<String> optionsValue) {
        this.optionsValue = optionsValue;
    }

    public String getMultiple() {
        return multiple;
    }

    public void setMultiple(String multiple) {
        this.multiple = multiple;
    }

    public List<String> getOptions() {
        return options;
    }

    public void setOptions(List<String> options) {
        this.options = options;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.multiple);
        dest.writeStringList(this.options);
    }

    public ServiceOptions() {
    }

    protected ServiceOptions(Parcel in) {
        this.multiple = in.readString();
        this.options = in.createStringArrayList();
    }

    public static final Parcelable.Creator<ServiceOptions> CREATOR = new Parcelable.Creator<ServiceOptions>() {
        @Override
        public ServiceOptions createFromParcel(Parcel source) {
            return new ServiceOptions(source);
        }

        @Override
        public ServiceOptions[] newArray(int size) {
            return new ServiceOptions[size];
        }
    };
}
