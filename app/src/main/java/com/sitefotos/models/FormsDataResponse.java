package com.sitefotos.models;


import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class FormsDataResponse implements Parcelable{

    @SerializedName("Forms")
        private List<FormData> forms;
        private String userType;

    private FormsDataResponse(Parcel in) {
        forms = in.createTypedArrayList(FormData.CREATOR);
        userType = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(forms);
        dest.writeString(userType);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<FormsDataResponse> CREATOR = new Creator<FormsDataResponse>() {
        @Override
        public FormsDataResponse createFromParcel(Parcel in) {
            return new FormsDataResponse(in);
        }

        @Override
        public FormsDataResponse[] newArray(int size) {
            return new FormsDataResponse[size];
        }
    };

    public List<FormData> getForms() {
            return forms;
        }

        public void setForms(List<FormData> forms) {
            this.forms = forms;
        }

        public String getUserType() {
            return userType;
        }

        public void setUserType(String userType) {
            this.userType = userType;
        }

    }