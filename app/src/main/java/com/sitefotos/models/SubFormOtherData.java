package com.sitefotos.models;


import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class SubFormOtherData implements Parcelable {

    String crewName;
    int crewId;
    List<String> lstServiceName;

    public SubFormOtherData(){
    }

    protected SubFormOtherData(Parcel in) {
        crewName = in.readString();
        lstServiceName = in.createStringArrayList();
        crewId = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(crewName);
        dest.writeStringList(lstServiceName);
        dest.writeInt(crewId);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SubFormOtherData> CREATOR = new Creator<SubFormOtherData>() {
        @Override
        public SubFormOtherData createFromParcel(Parcel in) {
            return new SubFormOtherData(in);
        }

        @Override
        public SubFormOtherData[] newArray(int size) {
            return new SubFormOtherData[size];
        }
    };

    public String getCrewName() {
        return crewName;
    }

    public void setCrewName(String crewName) {
        this.crewName = crewName;
    }

    public List<String> getLstServiceName() {
        return lstServiceName;
    }

    public void setLstServiceName(List<String> lstServiceName) {
        this.lstServiceName = lstServiceName;
    }

    public int getCrewId() {
        return crewId;
    }

    public void setCrewId(int crewId) {
        this.crewId = crewId;
    }

}
