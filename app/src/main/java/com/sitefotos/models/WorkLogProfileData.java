package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.List;

public class WorkLogProfileData implements Parcelable {

    private int profileID;
    private boolean profileSubmitted;
    private String imageData = "";
    private int pkId;
    private int siteId;
    private String profileCategory;
    private List<Material> materials = null;
    private List<TMService> services = null;
    private String notes;
    private String userNote="";
    private List<Integer> formDetails = null;
    private List<MapDetail> mapDetails = null;
    private List<Employees> lstEmployees = null;
    private boolean photos;
    private boolean issues;
    private boolean notesSelected;
    private ClientDetails clientDetails;
    private String rawData;
    private String servicesType;
    private String modifiedData;
    private double latitude;
    private double longitude;
    private int photoCount;
    private int issuePhotoCount;
    private long created;
    private long updated;
    private boolean checkInOut;
    private boolean crewTracking;
    private long checkinTime;
    private long checkoutTime;
    private List<FormDetail> lstFormDetails = null;
    private boolean photosRequired;
    private boolean notesRequired;
    private boolean isCrewSubmitted;

    public int getProfileID() {
        return profileID;
    }

    public void setProfileID(int profileID) {
        this.profileID = profileID;
    }

    public String getProfileCategory() {
        return profileCategory;
    }

    public void setProfileCategory(String profileCategory) {
        this.profileCategory = profileCategory;
    }

    public List<Material> getMaterials() {
        return materials;
    }

    public void setMaterials(List<Material> materials) {
        this.materials = materials;
    }

    public List<TMService> getServices() {
        return services;
    }

    public void setServices(List<TMService> services) {
        this.services = services;
    }


    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<Integer> getFormDetails() {
        return formDetails;
    }

    public void setFormDetails(List<Integer> formDetails) {
        this.formDetails = formDetails;
    }

    public List<MapDetail> getMapDetails() {
        return mapDetails;
    }

    public void setMapDetails(List<MapDetail> mapDetails) {
        this.mapDetails = mapDetails;
    }

    public ClientDetails getClientDetails() {
        return clientDetails;
    }

    public void setClientDetails(ClientDetails clientDetails) {
        this.clientDetails = clientDetails;
    }

    public int getSiteId() {
        return siteId;
    }

    public void setSiteId(int siteId) {
        this.siteId = siteId;
    }

    public long getCreated() {
        return created;
    }

    public void setCreated(long created) {
        this.created = created;
    }

    public long getUpdated() {
        return updated;
    }

    public void setUpdated(long updated) {
        this.updated = updated;
    }


    public String getRawData() {
        return rawData;
    }

    public void setRawData(String rawData) {
        this.rawData = rawData;
    }

    public String getModifiedData() {
        return modifiedData;
    }

    public void setModifiedData(String modifiedData) {
        this.modifiedData = modifiedData;
    }

    public int getPkId() {
        return pkId;
    }

    public void setPkId(int pkId) {
        this.pkId = pkId;
    }

    public boolean isProfileSubmitted() {
        return profileSubmitted;
    }

    public void setProfileSubmitted(boolean profileSubmitted) {
        this.profileSubmitted = profileSubmitted;
    }

    public String getImageData() {
        return imageData;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }


    public boolean isCheckInOut() {
        return checkInOut;
    }

    public void setCheckInOut(boolean checkInOut) {
        this.checkInOut = checkInOut;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }

    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public int getPhotoCount() {
        return photoCount;
    }

    public void setPhotoCount(int photoCount) {
        this.photoCount = photoCount;
    }

    public int getIssuePhotoCount() {
        return issuePhotoCount;
    }

    public void setIssuePhotoCount(int issuePhotoCount) {
        this.issuePhotoCount = issuePhotoCount;
    }


    public boolean isCrewTracking() {
        return crewTracking;
    }

    public void setCrewTracking(boolean crewTracking) {
        this.crewTracking = crewTracking;
    }

    public List<Employees> getLstEmployees() {
        return lstEmployees;
    }

    public void setLstEmployees(List<Employees> lstEmployees) {
        this.lstEmployees = lstEmployees;
    }

    public long getCheckinTime() {
        return checkinTime;
    }

    public void setCheckinTime(long checkinTime) {
        this.checkinTime = checkinTime;
    }

    public long getCheckoutTime() {
        return checkoutTime;
    }

    public void setCheckoutTime(long checkoutTime) {
        this.checkoutTime = checkoutTime;
    }


    public boolean isPhotos() {
        return photos;
    }

    public void setPhotos(boolean photos) {
        this.photos = photos;
    }

    public boolean isIssues() {
        return issues;
    }

    public void setIssues(boolean issues) {
        this.issues = issues;
    }

    public String getServicesType() {
        return servicesType;
    }

    public void setServicesType(String servicesType) {
        this.servicesType = servicesType;
    }

    public boolean isNotesSelected() {
        return notesSelected;
    }

    public void setNotesSelected(boolean notesSelected) {
        this.notesSelected = notesSelected;
    }

    public String getUserNote() {
        return userNote;
    }

    public void setUserNote(String userNote) {
        this.userNote = userNote;
    }


    public List<FormDetail> getLstFormDetails() {
        return lstFormDetails;
    }

    public void setLstFormDetails(List<FormDetail> lstFormDetails) {
        this.lstFormDetails = lstFormDetails;
    }

    public boolean isPhotosRequired() {
        return photosRequired;
    }

    public void setPhotosRequired(boolean photosRequired) {
        this.photosRequired = photosRequired;
    }

    public boolean isNotesRequired() {
        return notesRequired;
    }

    public void setNotesRequired(boolean notesRequired) {
        this.notesRequired = notesRequired;
    }

    public boolean isCrewSubmitted() {
        return isCrewSubmitted;
    }

    public void setCrewSubmitted(boolean crewSubmitted) {
        isCrewSubmitted = crewSubmitted;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.profileID);
        dest.writeByte(this.profileSubmitted ? (byte) 1 : (byte) 0);
        dest.writeString(this.imageData);
        dest.writeInt(this.pkId);
        dest.writeInt(this.siteId);
        dest.writeString(this.profileCategory);
        dest.writeTypedList(this.materials);
        dest.writeTypedList(this.services);
        dest.writeString(this.notes);
        dest.writeString(this.userNote);
        dest.writeList(this.formDetails);
        dest.writeTypedList(this.mapDetails);
        dest.writeTypedList(this.lstEmployees);
        dest.writeByte(this.photos ? (byte) 1 : (byte) 0);
        dest.writeByte(this.issues ? (byte) 1 : (byte) 0);
        dest.writeByte(this.notesSelected ? (byte) 1 : (byte) 0);
        dest.writeParcelable(this.clientDetails, flags);
        dest.writeString(this.rawData);
        dest.writeString(this.servicesType);
        dest.writeString(this.modifiedData);
        dest.writeDouble(this.latitude);
        dest.writeDouble(this.longitude);
        dest.writeInt(this.photoCount);
        dest.writeInt(this.issuePhotoCount);
        dest.writeLong(this.created);
        dest.writeLong(this.updated);
        dest.writeByte(this.checkInOut ? (byte) 1 : (byte) 0);
        dest.writeByte(this.crewTracking ? (byte) 1 : (byte) 0);
        dest.writeLong(this.checkinTime);
        dest.writeLong(this.checkoutTime);
        dest.writeTypedList(this.lstFormDetails);
        dest.writeByte(this.photosRequired ? (byte) 1 : (byte) 0);
        dest.writeByte(this.notesRequired ? (byte) 1 : (byte) 0);
        dest.writeByte(this.isCrewSubmitted ? (byte) 1 : (byte) 0);
    }

    public WorkLogProfileData() {
    }

    protected WorkLogProfileData(Parcel in) {
        this.profileID = in.readInt();
        this.profileSubmitted = in.readByte() != 0;
        this.imageData = in.readString();
        this.pkId = in.readInt();
        this.siteId = in.readInt();
        this.profileCategory = in.readString();
        this.materials = in.createTypedArrayList(Material.CREATOR);
        this.services = in.createTypedArrayList(TMService.CREATOR);
        this.notes = in.readString();
        this.userNote = in.readString();
        this.formDetails = new ArrayList<Integer>();
        in.readList(this.formDetails, Integer.class.getClassLoader());
        this.mapDetails = in.createTypedArrayList(MapDetail.CREATOR);
        this.lstEmployees = in.createTypedArrayList(Employees.CREATOR);
        this.photos = in.readByte() != 0;
        this.issues = in.readByte() != 0;
        this.notesSelected = in.readByte() != 0;
        this.clientDetails = in.readParcelable(ClientDetails.class.getClassLoader());
        this.rawData = in.readString();
        this.servicesType = in.readString();
        this.modifiedData = in.readString();
        this.latitude = in.readDouble();
        this.longitude = in.readDouble();
        this.photoCount = in.readInt();
        this.issuePhotoCount = in.readInt();
        this.created = in.readLong();
        this.updated = in.readLong();
        this.checkInOut = in.readByte() != 0;
        this.crewTracking = in.readByte() != 0;
        this.checkinTime = in.readLong();
        this.checkoutTime = in.readLong();
        this.lstFormDetails = in.createTypedArrayList(FormDetail.CREATOR);
        this.photosRequired = in.readByte() != 0;
        this.notesRequired = in.readByte() != 0;
        this.isCrewSubmitted = in.readByte() != 0;
    }

    public static final Parcelable.Creator<WorkLogProfileData> CREATOR = new Parcelable.Creator<WorkLogProfileData>() {
        @Override
        public WorkLogProfileData createFromParcel(Parcel source) {
            return new WorkLogProfileData(source);
        }

        @Override
        public WorkLogProfileData[] newArray(int size) {
            return new WorkLogProfileData[size];
        }
    };
}
