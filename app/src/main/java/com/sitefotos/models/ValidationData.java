package com.sitefotos.models;

import android.view.View;


public class ValidationData {
    private String value;
    private String title;
    private boolean isValidate;
    private int pageNumber;
    private View view;
    private boolean isEmail;
    private boolean shouldNotMarkError;

    public View getView() {
        return view;
    }

    public void setView(View view) {
        this.view = view;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public String getTitle() {
        return title;
    }

    public String getValue() {
        return value;
    }

    public boolean isValidate() {
        return isValidate;
    }

    public void setValidate(boolean validate) {
        isValidate = validate;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setTitle(String title) {
        this.title = title;
    }



    public boolean isEmail() {
        return isEmail;
    }

    public void setEmail(boolean email) {
        isEmail = email;
    }

    public boolean isShouldNotMarkError() {
        return shouldNotMarkError;
    }

    public void setShouldNotMarkError(boolean shouldNotMarkError) {
        this.shouldNotMarkError = shouldNotMarkError;
    }
}
