package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;


public class Employees implements Parcelable {

    @SerializedName("EmployeeID")
    private int employeeID;

    @SerializedName("EmployeeFirstName")
    private String employeeFirstName;

    @SerializedName("EmployeeLastName")
    private String employeeLastName;

    @SerializedName("EmployeeSearchID")
    private String employeeSearchID;

    @SerializedName("IsCrew")
    private boolean isCrew;

    private boolean isSelected;
    private long createdDate;
    private long updatedDate;
   // private int pkId;

    public Employees(Parcel in) {
        employeeID = in.readInt();
        employeeFirstName = in.readString();
        employeeLastName = in.readString();
        employeeSearchID = in.readString();
        isCrew = in.readByte() != 0;
        isSelected = in.readByte() != 0;
        createdDate = in.readLong();
        updatedDate = in.readLong();
        //pkId = in.readInt();
    }

    public static final Creator<Employees> CREATOR = new Creator<Employees>() {
        @Override
        public Employees createFromParcel(Parcel in) {
            return new Employees(in);
        }

        @Override
        public Employees[] newArray(int size) {
            return new Employees[size];
        }
    };

    public Employees() {


    }

    public int getEmployeeID() {
        return employeeID;
    }

    public void setEmployeeID(int employeeID) {
        this.employeeID = employeeID;
    }

    public String getEmployeeFirstName() {
        return employeeFirstName;
    }

    public void setEmployeeFirstName(String employeeFirstName) {
        this.employeeFirstName = employeeFirstName;
    }

    public String getEmployeeLastName() {
        return employeeLastName;
    }

    public void setEmployeeLastName(String employeeLastName) {
        this.employeeLastName = employeeLastName;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }


    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getEmployeeSearchID() {
        return employeeSearchID;
    }

    public void setEmployeeSearchID(String employeeSearchID) {
        this.employeeSearchID = employeeSearchID;
    }

    public boolean isCrew() {
        return isCrew;
    }

    public void setCrew(boolean crew) {
        isCrew = crew;
    }


   /* public int getPkId() {
        return pkId;
    }

    public void setPkId(int pkId) {
        this.pkId = pkId;
    }*/

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(employeeID);
        dest.writeString(employeeFirstName);
        dest.writeString(employeeLastName);
        dest.writeString(employeeSearchID);
        dest.writeByte((byte) (isCrew ? 1 : 0));
        dest.writeByte((byte) (isSelected ? 1 : 0));
        dest.writeLong(createdDate);
        dest.writeLong(updatedDate);
       // dest.writeInt(pkId);
    }
}
