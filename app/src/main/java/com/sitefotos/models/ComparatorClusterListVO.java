package com.sitefotos.models;

import java.util.Comparator;

/**
 * Created by dk on 27/8/17.
 */

public class ComparatorClusterListVO implements Comparator<Cluster> {
    public int compare(Cluster first, Cluster second) {
        double firstValue = first.getSgcDistance();
        double secondValue = second.getSgcDistance();
        return firstValue < secondValue ? -1 : firstValue > secondValue ? 1 : 0;
    }
}
