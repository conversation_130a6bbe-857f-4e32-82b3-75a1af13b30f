package com.sitefotos.models;

import android.graphics.Bitmap;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;

public class DrawModel {
    private Paint paint;
    private Path path;
    private Bitmap bitmap;
    private Matrix matrix;

    public DrawModel(Path path, Paint paint) {
        this.path = path;
        this.paint = paint;
    }

    public DrawModel(Bitmap bitmap, Matrix matrix) {
        this.bitmap = bitmap;
        this.matrix = matrix;
    }

    public Path getPath() {
        return this.path;
    }

    public void setPath(Path path) {
        this.path = path;
    }

    public Paint getPaint() {
        return this.paint;
    }

    public void setPaint(Paint paint) {
        this.paint = paint;
    }


    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public Matrix getMatrix() {
        return matrix;
    }

    public void setMatrix(Matrix matrix) {
        this.matrix = matrix;
    }
}
