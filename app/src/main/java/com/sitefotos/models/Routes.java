package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;


public class Routes implements Parcelable {

    @SerializedName("routeID")
    private String routeId;

    @SerializedName("routeCategory")
    private String routeCategory;

    @SerializedName("routeName")
    private String routeName;

    @SerializedName("siteList")
    private List<Long> siteList;

    private long createdDate;
    private long updatedDate;
    @SerializedName("isOrdered")
    private boolean ordered = true;

    @SerializedName("filter")
    private boolean filter;


    private Routes(Parcel in) {
        routeId = in.readString();
        routeCategory = in.readString();
        routeName = in.readString();
        createdDate = in.readLong();
        updatedDate = in.readLong();
    }

    public Routes() {

    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(routeId);
        dest.writeString(routeCategory);
        dest.writeString(routeName);
        dest.writeLong(createdDate);
        dest.writeLong(updatedDate);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<Routes> CREATOR = new Creator<Routes>() {
        @Override
        public Routes createFromParcel(Parcel in) {
            return new Routes(in);
        }

        @Override
        public Routes[] newArray(int size) {
            return new Routes[size];
        }
    };

    public String getRouteCategory() {
        return routeCategory;
    }

    public void setRouteCategory(String routeCategory) {
        this.routeCategory = routeCategory;
    }

    public String getRouteName() {
        return routeName;
    }

    public void setRouteName(String routeName) {
        this.routeName = routeName;
    }

    public List<Long> getSiteList() {
        return siteList;
    }

    public void setSiteList(List<Long> siteList) {
        this.siteList = siteList;
    }

    public String getRouteId() {
        return routeId;
    }

    public void setRouteId(String routeId) {
        this.routeId = routeId;
    }

    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }

    public boolean isOrdered() {
        return ordered;
    }

    public void setOrdered(boolean ordered) {
        this.ordered = ordered;
    }

    public boolean isFilter() {
        return filter;
    }

    public void setFilter(boolean filter) {
        this.filter = filter;
    }
}