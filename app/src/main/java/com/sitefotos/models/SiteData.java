package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.sitefotos.camera.PropertiesVo;

import java.util.List;

public class SiteData implements Parcelable {

    private String siteName;
    private long siteId;
    private String propertyAddress;
    private double propertyLatitude;
    private double propertyLongitude;
    private double distance;
    private long createdDate;
    private long updatedDate;
    private String propertyData;
    private boolean ordered;
    private boolean allSites;
    private float siteFilterDistance;
    private List<Integer> FormID;
    private SiteConfig siteConfig;
    private int sitePKId;
    private String cityName;
    private String stateName;
    private String zipCode;
    private PropertiesVo propertiesVo;
    private boolean isAllFormCompleted;

    protected SiteData(Parcel in) {
        siteName = in.readString();
        siteId = in.readLong();
        propertyAddress = in.readString();
        propertyLatitude = in.readDouble();
        propertyLongitude = in.readDouble();
        distance = in.readDouble();
        createdDate = in.readLong();
        updatedDate = in.readLong();
        propertyData = in.readString();
        ordered = in.readByte() != 0;
        allSites = in.readByte() != 0;
        siteFilterDistance = in.readFloat();
        siteConfig = in.readParcelable(SiteConfig.class.getClassLoader());
        sitePKId = in.readInt();
        cityName = in.readString();
        stateName = in.readString();
        zipCode = in.readString();
        isAllFormCompleted = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(siteName);
        dest.writeLong(siteId);
        dest.writeString(propertyAddress);
        dest.writeDouble(propertyLatitude);
        dest.writeDouble(propertyLongitude);
        dest.writeDouble(distance);
        dest.writeLong(createdDate);
        dest.writeLong(updatedDate);
        dest.writeString(propertyData);
        dest.writeByte((byte) (ordered ? 1 : 0));
        dest.writeByte((byte) (allSites ? 1 : 0));
        dest.writeFloat(siteFilterDistance);
        dest.writeParcelable(siteConfig, flags);
        dest.writeInt(sitePKId);
        dest.writeString(cityName);
        dest.writeString(stateName);
        dest.writeString(zipCode);
        dest.writeByte((byte) (isAllFormCompleted ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SiteData> CREATOR = new Creator<SiteData>() {
        @Override
        public SiteData createFromParcel(Parcel in) {
            return new SiteData(in);
        }

        @Override
        public SiteData[] newArray(int size) {
            return new SiteData[size];
        }
    };

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public long getSiteId() {
        return siteId;
    }

    public void setSiteId(long siteId) {
        this.siteId = siteId;
    }

    public String getPropertyAddress() {
        return propertyAddress;
    }

    public void setPropertyAddress(String propertyAddress) {
        this.propertyAddress = propertyAddress;
    }

    public double getPropertyLatitude() {
        return propertyLatitude;
    }

    public void setPropertyLatitude(double propertyLatitude) {
        this.propertyLatitude = propertyLatitude;
    }

    public double getPropertyLongitude() {
        return propertyLongitude;
    }

    public void setPropertyLongitude(double propertyLongitude) {
        this.propertyLongitude = propertyLongitude;
    }

    public double getDistance() {
        return distance;
    }

    public void setDistance(double distance) {
        this.distance = distance;
    }


    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }

    public String getPropertyData() {
        return propertyData;
    }

    public void setPropertyData(String propertyData) {
        this.propertyData = propertyData;
    }

    public boolean isOrdered() {
        return ordered;
    }

    public void setOrdered(boolean ordered) {
        this.ordered = ordered;
    }

    public boolean isAllSites() {
        return allSites;
    }

    public void setAllSites(boolean allSites) {
        this.allSites = allSites;
    }


    public List<Integer> getFormID() {
        return FormID;
    }

    public void setFormID(List<Integer> formID) {
        this.FormID = formID;
    }


    public SiteData() {
    }

    public SiteConfig getSiteConfig() {
        return siteConfig;
    }

    public void setSiteConfig(SiteConfig siteConfig) {
        this.siteConfig = siteConfig;
    }

    public int getSitePKId() {
        return sitePKId;
    }

    public void setSitePKId(int sitePKId) {
        this.sitePKId = sitePKId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getStateName() {
        return stateName;
    }

    public void setStateName(String stateName) {
        this.stateName = stateName;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public boolean isAllFormCompleted() {
        return isAllFormCompleted;
    }

    public void setAllFormCompleted(boolean allFormCompleted) {
        isAllFormCompleted = allFormCompleted;
    }

    public PropertiesVo getPropertiesVo() {
        return propertiesVo;
    }

    public void setPropertiesVo(PropertiesVo propertiesVo) {
        this.propertiesVo = propertiesVo;
    }
}
