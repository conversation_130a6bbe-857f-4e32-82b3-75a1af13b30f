package com.sitefotos.models;


import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class FormData implements Parcelable, Cloneable {
    private long sf_id;
    private String sf_form_name;
    private String sf_form_data_app;
    private String modifiedFormData;
    private long sf_created;
    private long sf_updated;
    private long sf_submited;
    private int formPKId;
    private String imageData = "";
    private double latitude;
    private double longitude;
    private int lastBuildingId;
    private boolean isCheckInOut;
    private boolean isCheckInOutComplete;
    private boolean CheckInOutFormComplete;
    private long siteId;
    private int imageCount;
    private int signatureCount;
    private boolean formSubmitted;
    private long checkin_time;
    private long checkout_time;
    private List<Integer> crewIds;
    private boolean plotOnMap;
    private boolean preSelectServices;

    private String formSubmissionId;

    private boolean isSubForm;
    private boolean hasSubForm;
    private SubFormOtherData subFormOtherData;



    private boolean canceledForm;

    private boolean isFormUploaded;

    private int mainFormPkId;

    public FormData() {

    }

    protected FormData(Parcel in) {
        sf_id = in.readLong();
        sf_form_name = in.readString();
        sf_form_data_app = in.readString();
        modifiedFormData = in.readString();
        sf_created = in.readLong();
        sf_updated = in.readLong();
        sf_submited = in.readLong();
        formPKId = in.readInt();
        mainFormPkId = in.readInt();
        imageData = in.readString();
        latitude = in.readDouble();
        longitude = in.readDouble();
        lastBuildingId = in.readInt();
        isCheckInOut = in.readByte() != 0;
        isCheckInOutComplete = in.readByte() != 0;
        CheckInOutFormComplete = in.readByte() != 0;
        siteId = in.readLong();
        imageCount = in.readInt();
        signatureCount = in.readInt();
        formSubmitted = in.readByte() != 0;
        checkin_time = in.readLong();
        checkout_time = in.readLong();
        plotOnMap = in.readByte() != 0;
        preSelectServices = in.readByte() != 0;
        formSubmissionId = in.readString();
        isSubForm = in.readByte() != 0;
        hasSubForm = in.readByte() != 0;
        canceledForm = in.readByte() != 0;
        isFormUploaded = in.readByte() != 0;
        subFormOtherData = in.readParcelable(SubFormOtherData.class.getClassLoader());
    }

    public static final Creator<FormData> CREATOR = new Creator<FormData>() {
        @Override
        public FormData createFromParcel(Parcel in) {
            return new FormData(in);
        }

        @Override
        public FormData[] newArray(int size) {
            return new FormData[size];
        }
    };

    public long getFormId() {
        return sf_id;
    }

    public void setFormId(long sfId) {
        this.sf_id = sfId;
    }

    public String getFormName() {
        return sf_form_name;
    }

    public void setFormName(String sfFormName) {
        this.sf_form_name = sfFormName;
    }

    public String getFormData() {
        return sf_form_data_app;
    }

    public void setFormData(String sfFormDataApp) {
        this.sf_form_data_app = sfFormDataApp;
    }

    public long getFormCreated() {
        return sf_created;
    }

    public void setFormCreated(long sf_created) {
        this.sf_created = sf_created;
    }

    public long getFormUpdated() {
        return sf_updated;
    }

    public void setFormUpdated(long sf_updated) {
        this.sf_updated = sf_updated;
    }

    public String getModifiedFormData() {
        return modifiedFormData;
    }

    public void setModifiedFormData(String modifiedFormData) {
        this.modifiedFormData = modifiedFormData;
    }

    public long getSiteId() {
        return siteId;
    }

    public void setSiteId(long siteId) {
        this.siteId = siteId;
    }

    public int getFormPKId() {
        return formPKId;
    }

    public void setFormPKId(int formPKId) {
        this.formPKId = formPKId;
    }

    public String getImageData() {
        return imageData;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }

    public boolean isFormSubmitted() {
        return formSubmitted;
    }

    public void setFormSubmitted(boolean formSubmitted) {
        this.formSubmitted = formSubmitted;
    }


    public double getLongitude() {
        return longitude;
    }

    public void setLongitude(double longitude) {
        this.longitude = longitude;
    }

    public double getLatitude() {
        return latitude;
    }

    public void setLatitude(double latitude) {
        this.latitude = latitude;
    }


    public int getImageCount() {
        return imageCount;
    }

    public void setImageCount(int imageCount) {
        this.imageCount = imageCount;
    }

    public int getSignatureCount() {
        return signatureCount;
    }

    public void setSignatureCount(int signatureCount) {
        this.signatureCount = signatureCount;
    }


    public int getLastBuildingId() {
        return lastBuildingId;
    }

    public void setLastBuildingId(int lastBuildingId) {
        this.lastBuildingId = lastBuildingId;
    }


    public boolean getIsCheckInOut() {
        return isCheckInOut;
    }

    public void setIsCheckInOut(boolean isCheckInOut) {
        this.isCheckInOut = isCheckInOut;
    }

    public long getSf_submited() {
        return sf_submited;
    }

    public void setSf_submited(long sf_submited) {
        this.sf_submited = sf_submited;
    }

    public boolean isCheckInOutComplete() {
        return isCheckInOutComplete;
    }

    public void setCheckInOutComplete(boolean checkInOutComplete) {
        isCheckInOutComplete = checkInOutComplete;
    }

    public boolean isCheckInOutFormComplete() {
        return CheckInOutFormComplete;
    }

    public void setCheckInOutFormComplete(boolean checkInOutFormComplete) {
        CheckInOutFormComplete = checkInOutFormComplete;
    }

    public long getCheckin_time() {
        return checkin_time;
    }

    public void setCheckin_time(long checkin_time) {
        this.checkin_time = checkin_time;
    }

    public long getCheckout_time() {
        return checkout_time;
    }

    public void setCheckout_time(long checkout_time) {
        this.checkout_time = checkout_time;
    }


    public String getFormSubmissionId() {
        return formSubmissionId;
    }

    public void setFormSubmissionId(String formSubmissionId) {
        this.formSubmissionId = formSubmissionId;
    }

    public boolean hasSubForm() {
        return hasSubForm;
    }

    public void setHasSubForm(boolean hasSubForm) {
        this.hasSubForm = hasSubForm;
    }



    public List<Integer> getCrewIds() {
        return crewIds;
    }

    public void setCrewIds(List<Integer> crewIds) {
        this.crewIds = crewIds;
    }



    public boolean isPlotOnMap() {
        return plotOnMap;
    }

    public void setPlotOnMap(boolean plotOnMap) {
        this.plotOnMap = plotOnMap;
    }
    public boolean isPreSelectServices() {
        return preSelectServices;
    }

    public void setPreSelectServices(boolean preSelectServices) {
        this.preSelectServices = preSelectServices;
    }

    public boolean isCanceledForm() {
        return canceledForm;
    }

    public void setCanceledForm(boolean canceledForm) {
        this.canceledForm = canceledForm;
    }
    public void setFormUploaded(boolean formUploaded) {
        isFormUploaded = formUploaded;
    }

    public int getMainFormPkId() {
        return mainFormPkId;
    }

    public void setMainFormPkId(int mainFormPkId) {
        this.mainFormPkId = mainFormPkId;
    }


    public boolean isSubForm() {
        return isSubForm;
    }

    public void setSubForm(boolean subForm) {
        isSubForm = subForm;
    }

    public SubFormOtherData getSubFormOtherData() {
        return subFormOtherData;
    }

    public void setSubFormOtherData(SubFormOtherData subFormOtherData) {
        this.subFormOtherData = subFormOtherData;
    }

    public boolean isFormUploaded() {
        return isFormUploaded;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();

    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel parcel, int i) {
        parcel.writeLong(sf_id);
        parcel.writeString(sf_form_name);
        parcel.writeString(sf_form_data_app);
        parcel.writeString(modifiedFormData);
        parcel.writeLong(sf_created);
        parcel.writeLong(sf_updated);
        parcel.writeLong(sf_submited);
        parcel.writeInt(formPKId);
        parcel.writeString(imageData);
        parcel.writeDouble(latitude);
        parcel.writeDouble(longitude);
        parcel.writeInt(lastBuildingId);
        parcel.writeByte((byte) (isCheckInOut ? 1 : 0));
        parcel.writeByte((byte) (isCheckInOutComplete ? 1 : 0));
        parcel.writeByte((byte) (CheckInOutFormComplete ? 1 : 0));
        parcel.writeLong(siteId);
        parcel.writeInt(imageCount);
        parcel.writeInt(signatureCount);
        parcel.writeByte((byte) (formSubmitted ? 1 : 0));
        parcel.writeLong(checkin_time);
        parcel.writeLong(checkout_time);
        parcel.writeByte((byte) (plotOnMap ? 1 : 0));
        parcel.writeByte((byte) (preSelectServices ? 1 : 0));
        parcel.writeString(formSubmissionId);
        parcel.writeByte((byte) (isSubForm ? 1 : 0));
        parcel.writeByte((byte) (hasSubForm ? 1 : 0));
        parcel.writeByte((byte) (canceledForm ? 1 : 0));
        parcel.writeByte((byte) (isFormUploaded ? 1 : 0));
        parcel.writeParcelable(subFormOtherData, i);
        parcel.writeInt(mainFormPkId);
    }


}

