package com.sitefotos.models;


import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class FormListResponse implements Parcelable {

    private String userType;
    private List<FormData> forms;
    @SerializedName("Materials")
    private List<Material> materials = null;
    @SerializedName("Services")
    private List<TMService> services = null;

    @SerializedName("Crew")
    private List<Employees> lstCrew;

    protected FormListResponse(Parcel in) {
        userType = in.readString();
        forms = in.createTypedArrayList(FormData.CREATOR);
        materials = in.createTypedArrayList(Material.CREATOR);
        services = in.createTypedArrayList(TMService.CREATOR);
        lstCrew = in.createTypedArrayList(Employees.CREATOR);
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(userType);
        dest.writeTypedList(forms);
        dest.writeTypedList(materials);
        dest.writeTypedList(services);
        dest.writeTypedList(lstCrew);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<FormListResponse> CREATOR = new Creator<FormListResponse>() {
        @Override
        public FormListResponse createFromParcel(Parcel in) {
            return new FormListResponse(in);
        }

        @Override
        public FormListResponse[] newArray(int size) {
            return new FormListResponse[size];
        }
    };

    public List<FormData> getForms() {
        return forms;
    }

    public void setForms(List<FormData> forms) {
        this.forms = forms;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public List<Material> getMaterials() {
        return materials;
    }

    public void setMaterials(List<Material> materials) {
        this.materials = materials;
    }

    public List<TMService> getServices() {
        return services;
    }

    public void setServices(List<TMService> services) {
        this.services = services;
    }

    public FormListResponse() {
    }

    public List<Employees> getLstCrew() {
        return lstCrew;
    }

    public void setLstCrew(List<Employees> lstCrew) {
        this.lstCrew = lstCrew;
    }
}