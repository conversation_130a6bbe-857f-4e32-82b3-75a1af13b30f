package com.sitefotos.models;

import com.sitefotos.camera.PropertiesVo;

import java.util.List;

public class AppDataResponse {

    private String employeeID = "";
    private boolean routesActive;
    private boolean isTagsAllowed;
    private String companyName = "";
    private boolean extendedCrewSearch;
    private long distanceDelta = 100;
    private String userType = "";
    private int clockInOut;
    private int skipGeo;
    private int screenType;
    private int formLock;
    private int formsPermission;
    private int sitesPermission;
    private int breadcrumbs;
    private int androidVersionCode;
    private boolean forceUpdate;
    private List<TMService> services;
    private List<Material> materials;
    private List<Employees> crew;
    private List<Routes> routes;
    private boolean forceSites;
    private SiteListConfig siteListConfig;
    private List<FormData> forms;
    private List<Cluster> clusters;
    private String[] tags = null;
    private List<MapPinData> mapPinURLs;
    private String hiRes;
    private String rapidRH;
    private List<PropertiesVo> sites;
    private int isGPSRequire;
    private String stag;
    private String defaultMapPinURL;
    private List<Vehicles> vehicles;
    private List<Equipments> equipments;
    private List<DynamicDropDownItem> dynamicDropdownItems;
    private boolean isError;
    private boolean disableMainCamera;

    private String message = "";


    public String getEmployeeID() {
        return employeeID;
    }

    public void setEmployeeID(String employeeID) {
        this.employeeID = employeeID;
    }

    public boolean isRoutesActive() {
        return routesActive;
    }

    public void setRoutesActive(boolean routesActive) {
        this.routesActive = routesActive;
    }

    public boolean isTagsAllowed() {
        return isTagsAllowed;
    }

    public void setTagsAllowed(boolean tagsAllowed) {
        isTagsAllowed = tagsAllowed;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public boolean isExtendedCrewSearch() {
        return extendedCrewSearch;
    }

    public void setExtendedCrewSearch(boolean extendedCrewSearch) {
        this.extendedCrewSearch = extendedCrewSearch;
    }

    public long getDistanceDelta() {
        return distanceDelta;
    }

    public void setDistanceDelta(long distanceDelta) {
        this.distanceDelta = distanceDelta;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public int getClockInOut() {
        return clockInOut;
    }

    public void setClockInOut(int clockInOut) {
        this.clockInOut = clockInOut;
    }

    public int getSkipGeo() {
        return skipGeo;
    }

    public void setSkipGeo(int skipGeo) {
        this.skipGeo = skipGeo;
    }

    public int getScreenType() {
        return screenType;
    }

    public void setScreenType(int screenType) {
        this.screenType = screenType;
    }

    public int getFormLock() {
        return formLock;
    }

    public void setFormLock(int formLock) {
        this.formLock = formLock;
    }

    public int getFormsPermission() {
        return formsPermission;
    }

    public void setFormsPermission(int formsPermission) {
        this.formsPermission = formsPermission;
    }

    public int getSitesPermission() {
        return sitesPermission;
    }

    public void setSitesPermission(int sitesPermission) {
        this.sitesPermission = sitesPermission;
    }

    public int getBreadcrumbs() {
        return breadcrumbs;
    }

    public void setBreadcrumbs(int breadcrumbs) {
        this.breadcrumbs = breadcrumbs;
    }

    public int getAndroidVersionCode() {
        return androidVersionCode;
    }

    public void setAndroidVersionCode(int androidVersionCode) {
        this.androidVersionCode = androidVersionCode;
    }

    public boolean isForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    public List<TMService> getServices() {
        return services;
    }

    public void setServices(List<TMService> services) {
        this.services = services;
    }

    public List<Material> getMaterials() {
        return materials;
    }

    public void setMaterials(List<Material> materials) {
        this.materials = materials;
    }

    public List<Employees> getCrew() {
        return crew;
    }

    public void setCrew(List<Employees> crew) {
        this.crew = crew;
    }

    public List<Routes> getRoutes() {
        return routes;
    }

    public void setRoutes(List<Routes> routes) {
        this.routes = routes;
    }

    public SiteListConfig getSiteListConfig() {
        return siteListConfig;
    }

    public void setSiteListConfig(SiteListConfig siteListConfig) {
        this.siteListConfig = siteListConfig;
    }

    public List<FormData> getForms() {
        return forms;
    }

    public void setForms(List<FormData> forms) {
        this.forms = forms;
    }

    public List<Cluster> getClusters() {
        return clusters;
    }

    public void setClusters(List<Cluster> clusters) {
        this.clusters = clusters;
    }

    public String[] getTags() {
        return tags;
    }

    public void setTags(String[] tags) {
        this.tags = tags;
    }

    public String getHiRes() {
        return hiRes;
    }

    public void setHiRes(String hiRes) {
        this.hiRes = hiRes;
    }

    public String getRapidRH() {
        return rapidRH;
    }

    public void setRapidRH(String rapidRH) {
        this.rapidRH = rapidRH;
    }

    public List<PropertiesVo> getSites() {
        return sites;
    }

    public void setSites(List<PropertiesVo> sites) {
        this.sites = sites;
    }

    public int getIsGPSRequire() {
        return isGPSRequire;
    }

    public void setIsGPSRequire(int isGPSRequire) {
        this.isGPSRequire = isGPSRequire;
    }

    public String getStag() {
        return stag;
    }

    public void setStag(String stag) {
        this.stag = stag;
    }

    public boolean isForceSites() {
        return forceSites;
    }

    public void setForceSites(boolean forceSites) {
        this.forceSites = forceSites;
    }

    public List<MapPinData> getMapPinUrls() {
        return mapPinURLs;
    }

    public void setMapPinUrl(List<MapPinData> mapPinURLs) {
        this.mapPinURLs = mapPinURLs;
    }

    public String getDefaultMapPinURL() {
        return defaultMapPinURL;
    }

    public void setDefaultMapPinURL(String defaultMapPinURL) {
        this.defaultMapPinURL = defaultMapPinURL;
    }
    public List<Vehicles> getLstVehicle() {
        return vehicles;
    }

    public void setLstVehicle(List<Vehicles> lstVehicle) {
        this.vehicles = lstVehicle;
    }

    public List<Equipments> getLstEquipment() {
        return equipments;
    }

    public void setLstEquipment(List<Equipments> lstEquipment) {
        this.equipments = lstEquipment;
    }

    public List<DynamicDropDownItem> getDynamicDropdownItems() {
        return dynamicDropdownItems;
    }

    public void setDynamicDropdownItems(List<DynamicDropDownItem> dynamicDropdownItems) {
        this.dynamicDropdownItems = dynamicDropdownItems;
    }
    public boolean isError() {
        return isError;
    }

    public void setError(boolean error) {
        isError = error;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isDisableMainCamera() {
        return disableMainCamera;
    }

    public void setDisableMainCamera(boolean disableMainCamera) {
        this.disableMainCamera = disableMainCamera;
    }
}
