package com.sitefotos.models;

public class CheckInDetails {
    private boolean isCheckIn;
    private int formPkId;
    private long siteId;
    private long formId;
    private String siteName;
    private String formName;
    private boolean isValidate;

    public int getFormPkId() {
        return formPkId;
    }

    public boolean isValidate() {
        return isValidate;
    }

    public void setValidate(boolean validate) {
        isValidate = validate;
    }

    public String getFormName() {
        return formName;
    }

    public String getSiteName() {
        return siteName;
    }

    public boolean isCheckIn() {
        return isCheckIn;
    }

    public void setCheckIn(boolean checkIn) {
        isCheckIn = checkIn;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public void setFormPkId(int formPkId) {
        this.formPkId = formPkId;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public long getSiteId() {
        return siteId;
    }

    public void setSiteId(long siteId) {
        this.siteId = siteId;
    }

    public long getFormId() {
        return formId;
    }

    public void setFormId(long formId) {
        this.formId = formId;
    }

}
