package com.sitefotos.models;

public class Md5Keys {

    private String crewKey = "";
    private String materialKey= "";
    private String formKey= "";
    private String siteFormKey= "";
    private String propertyKey= "";
    private String serviceKey= "";
    private String siteKey= "";
    private String routesKey= "";
    private String siteListConfigKey= "";
    private String clusterKey= "";
    private String mapPinKey= "";

    private String vehicleKey = "";
    private String equipmentKey = "";


    private String dynamicDropDownItemsKey = "";

    public String getCrewKey() {
        return crewKey;
    }

    public void setCrewKey(String crewKey) {
        this.crewKey = crewKey;
    }

    public String getMaterialKey() {
        return materialKey;
    }

    public void setMaterialKey(String materialKey) {
        this.materialKey = materialKey;
    }

    public String getFormKey() {
        return formKey;
    }

    public void setFormKey(String formKey) {
        this.formKey = formKey;
    }

    public String getSiteFormKey() {
        return siteFormKey;
    }

    public void setSiteFormKey(String siteFormKey) {
        this.siteFormKey = siteFormKey;
    }

    public String getPropertyKey() {
        return propertyKey;
    }

    public void setPropertyKey(String propertyKey) {
        this.propertyKey = propertyKey;
    }

    public String getServiceKey() {
        return serviceKey;
    }

    public void setServiceKey(String serviceKey) {
        this.serviceKey = serviceKey;
    }

    public String getSiteKey() {
        return siteKey;
    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }

    public String getRoutesKey() {
        return routesKey;
    }

    public void setRoutesKey(String routesKey) {
        this.routesKey = routesKey;
    }

    public String getSiteListConfigKey() {
        return siteListConfigKey;
    }

    public void setSiteListConfigKey(String siteListConfigKey) {
        this.siteListConfigKey = siteListConfigKey;
    }


    public String getClusterKey() {
        return clusterKey;
    }

    public void setClusterKey(String clusterKey) {
        this.clusterKey = clusterKey;
    }

    public String getMapPinKey() {
        return mapPinKey;
    }

    public void setMapPinKey(String mapPinKey) {
        this.mapPinKey = mapPinKey;
    }


    public String getVehicleKey() {
        return vehicleKey;
    }

    public void setVehicleKey(String vehicleKey) {
        this.vehicleKey = vehicleKey;
    }

    public String getEquipmentKey() {
        return equipmentKey;
    }

    public void setEquipmentKey(String equipmentKey) {
        this.equipmentKey = equipmentKey;
    }


    public String getDynamicDropDownItemsKey() {
        return dynamicDropDownItemsKey;
    }

    public void setDynamicDropDownItemsKey(String dynamicDropDownItemsKey) {
        this.dynamicDropDownItemsKey = dynamicDropDownItemsKey;
    }

}
