package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

public class CheckInMap  implements Parcelable{

    private long siteId;
    private long routeId;
    private String siteName;
    private String formName;
    private long createdDate;
    private long updatedDate;
    private long formId;
    private List<Integer> crewIds;
    private List<Integer> breakCrewIds;
    private int pkId;
    private int formPkId;
    private boolean formValidate;
    private String formSubmissionId;

    public CheckInMap() {
    }

    protected CheckInMap(Parcel in) {
        siteId = in.readLong();
        routeId = in.readLong();
        siteName = in.readString();
        formName = in.readString();
        createdDate = in.readLong();
        updatedDate = in.readLong();
        formId = in.readLong();
        pkId = in.readInt();
        formPkId = in.readInt();
        formValidate = in.readByte() != 0;
        formSubmissionId = in.readString();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(siteId);
        dest.writeLong(routeId);
        dest.writeString(siteName);
        dest.writeString(formName);
        dest.writeLong(createdDate);
        dest.writeLong(updatedDate);
        dest.writeLong(formId);
        dest.writeInt(pkId);
        dest.writeInt(formPkId);
        dest.writeByte((byte) (formValidate ? 1 : 0));
        dest.writeString(formSubmissionId);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<CheckInMap> CREATOR = new Creator<CheckInMap>() {
        @Override
        public CheckInMap createFromParcel(Parcel in) {
            return new CheckInMap(in);
        }

        @Override
        public CheckInMap[] newArray(int size) {
            return new CheckInMap[size];
        }
    };

    public long getSiteId() {
        return siteId;
    }

    public void setSiteId(long siteId) {
        this.siteId = siteId;
    }

    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }

    public long getFormId() {
        return formId;
    }

    public void setFormId(long formId) {
        this.formId = formId;
    }

    public List<Integer> getCrewIds() {
        return crewIds;
    }

    public void setCrewIds(List<Integer> crewIds) {
        this.crewIds = crewIds;
    }

    public int getPkId() {
        return pkId;
    }

    public void setPkId(int pkId) {
        this.pkId = pkId;
    }

    public long getRouteId() {
        return routeId;
    }

    public void setRouteId(long routeId) {
        this.routeId = routeId;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getFormName() {
        return formName;
    }

    public void setFormName(String formName) {
        this.formName = formName;
    }

    public boolean isFormValidate() {
        return formValidate;
    }

    public void setFormValidate(boolean formValidate) {
        this.formValidate = formValidate;
    }

    public int getFormPkId() {
        return formPkId;
    }

    public void setFormPkId(int formPkId) {
        this.formPkId = formPkId;
    }

    public List<Integer> getBreakCrewIds() {
        return breakCrewIds;
    }

    public void setBreakCrewIds(List<Integer> breakCrewIds) {
        this.breakCrewIds = breakCrewIds;
    }

    public String getFormSubmissionId() {
        return formSubmissionId;
    }

    public void setFormSubmissionId(String formSubmissionId) {
        this.formSubmissionId = formSubmissionId;
    }

}
