package com.sitefotos.models;

import com.sitefotos.camera.PropertiesVo;

import java.util.Comparator;

/**
 * Created by dk on 27/8/17.
 */

public class ComparatorPropertyListVO implements Comparator<PropertiesVo> {
    public int compare(PropertiesVo first, PropertiesVo second) {
        double firstValue = first.getDistanceInMeters();
        double secondValue = second.getDistanceInMeters();
        return firstValue < secondValue ? -1 : firstValue > secondValue ? 1 : 0;
    }
}
