package com.sitefotos.models;

/**
 * Created on 17/3/18.
 */

public class UploadOtherData {

    private int id;
    private String uuid;
    private String dataType;
    private String BaseUrl;
    private String data;
    private String requestedData;
    private long createdAt;
    private long updatedAt;
    private String title;
    private boolean isImageUploaded;
    private int formPKId;
    private int tmFormPKId;
    private int issueTagId;
    private int wpPKId;
    private boolean isUploadProcessStart;
    private long processStartTime;
    private boolean isDataUploaded;
    private boolean isSubForm;
    private boolean hasSubForm;
    private String formSubmissionId;

    public UploadOtherData() {

    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public boolean isUploadProcessStart() {
        return isUploadProcessStart;
    }

    public long getProcessStartTime() {
        return processStartTime;
    }

    public void setProcessStartTime(long processStartTime) {
        this.processStartTime = processStartTime;
    }

    public void setUploadProcessStart(boolean uploadProcessStart) {
        isUploadProcessStart = uploadProcessStart;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public String getBaseUrl() {
        return BaseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        BaseUrl = baseUrl;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }


    public String getRequestedData() {
        return requestedData;
    }

    public void setRequestedData(String requestedData) {
        this.requestedData = requestedData;
    }

    public boolean isImageUploaded() {
        return isImageUploaded;
    }

    public void setImageUploaded(boolean imageUploaded) {
        isImageUploaded = imageUploaded;
    }

    public int getFormPKId() {
        return formPKId;
    }

    public void setFormPKId(int formPKId) {
        this.formPKId = formPKId;
    }

    public int getWpPKId() {
        return wpPKId;
    }

    public void setWpPKId(int wpPKId) {
        this.wpPKId = wpPKId;
    }

    public int getTmFormPKId() {
        return tmFormPKId;
    }

    public void setTmFormPKId(int tmFormPKId) {
        this.tmFormPKId = tmFormPKId;
    }

    public int getIssueTagId() {
        return issueTagId;
    }

    public void setIssueTagId(int issueTagId) {
        this.issueTagId = issueTagId;
    }

    public boolean isDataUploaded() {
        return isDataUploaded;
    }

    public void setDataUploaded(boolean dataUploaded) {
        isDataUploaded = dataUploaded;
    }


    public String getFormSubmissionId() {
        return formSubmissionId;
    }

    public void setFormSubmissionId(String formSubmissionId) {
        this.formSubmissionId = formSubmissionId;
    }

    public boolean isSubForm() {
        return isSubForm;
    }

    public void setSubForm(boolean subForm) {
        isSubForm = subForm;
    }

    public boolean hasSubForm() {
        return hasSubForm;
    }

    public void setHasSubForm(boolean hasSubForm) {
        this.hasSubForm = hasSubForm;
    }
}
