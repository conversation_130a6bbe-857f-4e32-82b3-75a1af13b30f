package com.sitefotos.models;

import android.os.Build;
import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class SiteConfig implements Parcelable {

    @SerializedName("SiteID")
    private int siteID;
    @SerializedName("FormID")
    private List<Integer> formId = null;
    @SerializedName("Notes")
    private String notes;
    @SerializedName("OperatingHours")
    private String operatingHours;
    @SerializedName("MapDetails")
    private List<MapDetail> mapDetails = null;

    @SerializedName("SiteRadius")
    private String siteRadius;

    @SerializedName("forceRadiusOnCheckout")
    private boolean forceRadiusOnCheckout;

    protected SiteConfig(Parcel in) {
        siteID = in.readInt();
        notes = in.readString();
        operatingHours = in.readString();
        mapDetails = in.createTypedArrayList(MapDetail.CREATOR);
        siteRadius = in.readString();
        forceRadiusOnCheckout = (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) ? in.readBoolean() : in.readInt() != 0;

    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(siteID);
        dest.writeString(notes);
        dest.writeString(operatingHours);
        dest.writeTypedList(mapDetails);
        dest.writeString(siteRadius);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            dest.writeBoolean(forceRadiusOnCheckout);
        } else {
            dest.writeByte((byte) (forceRadiusOnCheckout ? 1 : 0));
        }

    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<SiteConfig> CREATOR = new Creator<SiteConfig>() {
        @Override
        public SiteConfig createFromParcel(Parcel in) {
            return new SiteConfig(in);
        }

        @Override
        public SiteConfig[] newArray(int size) {
            return new SiteConfig[size];
        }
    };

    public int getSiteID() {
        return siteID;
    }

    public void setSiteID(int siteID) {
        this.siteID = siteID;
    }

    public List<Integer> getFormId() {
        return formId;
    }

    public void setFormId(List<Integer> formId) {
        this.formId = formId;
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<MapDetail> getMapDetails() {
        return mapDetails;
    }

    public void setMapDetails(List<MapDetail> mapDetails) {
        this.mapDetails = mapDetails;
    }

    public String getOperatingHours() {
        return operatingHours;
    }

    public void setOperatingHours(String operatingHours) {
        this.operatingHours = operatingHours;
    }


    public String getSiteRadius() {
        return siteRadius;
    }

    public void setSiteRadius(String siteRadius) {
        this.siteRadius = siteRadius;
    }

    public boolean isForceRadiusOnCheckout() {
        return forceRadiusOnCheckout;
    }

    public void setForceRadiusOnCheckout(boolean forceRadiusOnCheckout) {
        this.forceRadiusOnCheckout = forceRadiusOnCheckout;
    }
}
