package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

public class TimeLog implements Parcelable {

    private long startTime;
    private long stopTime;

    public TimeLog() {
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getStopTime() {
        return stopTime;
    }

    public void setStopTime(long stopTime) {
        this.stopTime = stopTime;
    }
    public TimeLog(Parcel in) {
        startTime = in.readLong();
        stopTime = in.readLong();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeLong(startTime);
        dest.writeLong(stopTime);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<TimeLog> CREATOR = new Creator<TimeLog>() {
        @Override
        public TimeLog createFromParcel(Parcel in) {
            return new TimeLog(in);
        }

        @Override
        public TimeLog[] newArray(int size) {
            return new TimeLog[size];
        }
    };
}
