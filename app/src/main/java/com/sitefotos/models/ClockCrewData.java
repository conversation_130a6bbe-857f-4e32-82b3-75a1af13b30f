package com.sitefotos.models;

public class ClockCrewData {
    private long totalSession;
    private long startSession;
    private long breakTime;
    private long createdDate;
    private long updatedDate;
    private String crewName;
    private int crewId;
    private String clockInType;
    private int pkId;
    private boolean isCrew;
    private boolean isSelected;
    private boolean isLoginUser;
    private boolean isTimerRunning;

    public long getTotalSession() {
        return totalSession;
    }

    public void setTotalSession(long totalSession) {
        this.totalSession = totalSession;
    }

    public long getStartSession() {
        return startSession;
    }

    public void setStartSession(long startSession) {
        this.startSession = startSession;
    }

    public long getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(long createdDate) {
        this.createdDate = createdDate;
    }

    public long getUpdatedDate() {
        return updatedDate;
    }

    public void setUpdatedDate(long updatedDate) {
        this.updatedDate = updatedDate;
    }

    public long getBreakTime() {
        return breakTime;
    }

    public void setBreakTime(long breakTime) {
        this.breakTime = breakTime;
    }

    public String getCrewName() {
        return crewName;
    }

    public void setCrewName(String crewName) {
        this.crewName = crewName;
    }

    public int getCrewId() {
        return crewId;
    }

    public void setCrewId(int crewId) {
        this.crewId = crewId;
    }

    public String getClockInType() {
        return clockInType;
    }

    public void setClockInType(String clockInType) {
        this.clockInType = clockInType;
    }

    public int getPkId() {
        return pkId;
    }

    public void setPkId(int pkId) {
        this.pkId = pkId;
    }

    public boolean isCrew() {
        return isCrew;
    }

    public void setCrew(boolean crew) {
        isCrew = crew;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public boolean isLoginUser() {
        return isLoginUser;
    }

    public void setLoginUser(boolean loginUser) {
        isLoginUser = loginUser;
    }

    public boolean isTimerRunning() {
        return isTimerRunning;
    }

    public void setTimerRunning(boolean timerRunning) {
        isTimerRunning = timerRunning;
    }
}
