package com.sitefotos.models;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

public class WorkLogProfile implements Parcelable {
    @SerializedName("ProfileID")
    private int profileID;

    @SerializedName("ProfileCategory")
    private String profileCategory;

    @SerializedName("CheckInOut")
    private boolean checkInOut;

    @SerializedName("CrewTracking")
    private boolean crewTracking;

    @SerializedName("Materials")
    private List<Integer> materials = null;

    @SerializedName("Services")
    private List<Integer> services = null;

    @SerializedName("Photos")
    private boolean photos;

    @SerializedName("Issues")
    private boolean issues;

    @SerializedName("Notes")
    private boolean notes;

    @SerializedName("ServicesType")
    private String servicesType;

    @SerializedName("PhotosRequired")
    private boolean photosRequired;

    @SerializedName("NotesRequired")
    private boolean notesRequired;


    public int getProfileID() {
        return profileID;
    }

    public void setProfileID(int profileID) {
        this.profileID = profileID;
    }

    public String getProfileCategory() {
        return profileCategory;
    }

    public void setProfileCategory(String profileCategory) {
        this.profileCategory = profileCategory;
    }

    public boolean isCheckInOut() {
        return checkInOut;
    }

    public void setCheckInOut(boolean checkInOut) {
        this.checkInOut = checkInOut;
    }

    public boolean isCrewTracking() {
        return crewTracking;
    }

    public void setCrewTracking(boolean crewTracking) {
        this.crewTracking = crewTracking;
    }

    public List<Integer> getMaterials() {
        return materials;
    }

    public void setMaterials(List<Integer> materials) {
        this.materials = materials;
    }

    public List<Integer> getServices() {
        return services;
    }

    public void setServices(List<Integer> services) {
        this.services = services;
    }

    public boolean isPhotos() {
        return photos;
    }

    public void setPhotos(boolean photos) {
        this.photos = photos;
    }

    public boolean isIssues() {
        return issues;
    }

    public void setIssues(boolean issues) {
        this.issues = issues;
    }

    public boolean isNotes() {
        return notes;
    }

    public void setNotes(boolean notes) {
        this.notes = notes;
    }

    public String getServicesType() {
        return servicesType;
    }

    public void setServicesType(String servicesType) {
        this.servicesType = servicesType;
    }

    public void setPhotosRequired(boolean photosRequired) {
        this.photosRequired = photosRequired;
    }

    public boolean isNotesRequired() {
        return notesRequired;
    }

    public void setNotesRequired(boolean notesRequired) {
        this.notesRequired = notesRequired;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(this.profileID);
        dest.writeString(this.profileCategory);
        dest.writeByte(this.checkInOut ? (byte) 1 : (byte) 0);
        dest.writeByte(this.crewTracking ? (byte) 1 : (byte) 0);
        dest.writeList(this.materials);
        dest.writeList(this.services);
        dest.writeByte(this.photos ? (byte) 1 : (byte) 0);
        dest.writeByte(this.issues ? (byte) 1 : (byte) 0);
        dest.writeByte(this.notes ? (byte) 1 : (byte) 0);
        dest.writeString(this.servicesType);
        dest.writeByte(this.photosRequired ? (byte) 1 : (byte) 0);
        dest.writeByte(this.notesRequired ? (byte) 1 : (byte) 0);
    }

    public WorkLogProfile() {
    }

    protected WorkLogProfile(Parcel in) {
        this.profileID = in.readInt();
        this.profileCategory = in.readString();
        this.checkInOut = in.readByte() != 0;
        this.crewTracking = in.readByte() != 0;
        this.materials = new ArrayList<>();
        in.readList(this.materials, Integer.class.getClassLoader());
        this.services = new ArrayList<>();
        in.readList(this.services, Integer.class.getClassLoader());
        this.photos = in.readByte() != 0;
        this.issues = in.readByte() != 0;
        this.notes = in.readByte() != 0;
        this.servicesType = in.readString();
        this.photosRequired = in.readByte() != 0;
        this.notesRequired = in.readByte() != 0;
    }

    public static final Parcelable.Creator<WorkLogProfile> CREATOR = new Parcelable.Creator<WorkLogProfile>() {
        @Override
        public WorkLogProfile createFromParcel(Parcel source) {
            return new WorkLogProfile(source);
        }

        @Override
        public WorkLogProfile[] newArray(int size) {
            return new WorkLogProfile[size];
        }
    };
}
