package com.sitefotos;

import static com.sitefotos.Constants.IMAGE_UPLOAD_SERVICE_BROADCAST;
import static com.sitefotos.Constants.LOGGED_IN_USER_COMPANY_ID;
import static com.sitefotos.Constants.LOGGED_IN_USER_EMAIL_ADDRESS;
import static com.sitefotos.Constants.PARAM_ACCESS_CODE;
import static com.sitefotos.Constants.PARAM_DT;
import static com.sitefotos.Constants.PARAM_EMAIL;
import static com.sitefotos.Constants.PARAM_LAT;
import static com.sitefotos.Constants.PARAM_LON;
import static com.sitefotos.Constants.PARAM_TYPE;
import static com.sitefotos.Constants.UPLOAD_BOTH_DATA_BROADCAST;
import static com.sitefotos.Constants.UPLOAD_OTHER_DATA_BROADCAST;

import android.app.ActivityManager;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.location.LocationManager;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Handler;
import android.os.StrictMode;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.google.android.libraries.places.api.Places;
import com.google.firebase.FirebaseApp;
import com.sitefotos.api.ApiInterface;
import com.sitefotos.api.RetrofitProvider;
import com.sitefotos.event.AppStatusChangedEvent;
import com.sitefotos.event.CustomEvent;
import com.sitefotos.event.UploadFileStatusEvent;
import com.sitefotos.interfaces.MainAppCallBackEvent;
import com.sitefotos.login.LoginActivity;
import com.sitefotos.main.MainActivity;
import com.sitefotos.models.SimpleResponse;
import com.sitefotos.receiver.GPSChangeListener;
import com.sitefotos.service.FileUploaderService;
import com.sitefotos.storage.AppPref;
import com.sitefotos.storage.AppPrefShared;
import com.sitefotos.storage.DBOpenHelper;
import com.sitefotos.util.FirebaseEventUtils;
import com.sitefotos.util.ImageUtil;
import com.sitefotos.util.LocaleHelper;
import com.sitefotos.util.NetworkUtil;
import com.sitefotos.util.NotificationUtils;
import com.sitefotos.util.StaticUtils;

import org.greenrobot.eventbus.EventBus;

import java.io.File;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import retrofit2.Call;

public class BaseApplication extends Application {
    private String topActivityName;

    /**
     * A singleton instance of the application class for easy access in other places
     */
    private static BaseApplication sInstance;

    public boolean isAppWentToBg = true;
    GPSChangeListener gpsChangeListener;

    public Handler handler = new Handler();

    public static Resources resources;
    //Thread to manage image data migration with form and other form data upload process
    public ExecutorService dataExecutorService = Executors.newSingleThreadExecutor();

    //Thread to manage image upload data in BG thread.
    public ExecutorService imageExecutorService = Executors.newSingleThreadExecutor();

    @Override
    public void onCreate() {
        super.onCreate();
        sInstance = this;
        solveGoogleMapIssue();
        setRules();
        FirebaseApp.initializeApp(this);
        AppPref.init(this);
        AppPrefShared.init(this);
        DBOpenHelper.getInstance(this);
        registerActivityLifeCycle();
        transferPrefData();
        initializePlaceAPI();
        StaticUtils.setWindowDimensions(this);
        StaticUtils.setWindowDimensionsForCamera(this);
        registerGPSListener();
        LocaleHelper.setLocale(getApplicationContext(), AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
        setLocale(AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en"));
        resources = getResources();
    }

    private void setRules() {
        StrictMode.VmPolicy.Builder builder = new StrictMode.VmPolicy.Builder();
        StrictMode.setVmPolicy(builder.build());
    }

    private void registerActivityLifeCycle() {
        ApplicationLifecycleHandler applicationLifecycleHandler = new ApplicationLifecycleHandler(new MainAppCallBackEvent() {
            @Override
            public void appInBackground() {
                //appWentInBackgroundBreadcrumb();
            }

            @Override
            public void appInForeground() {
                //appBoughtInForeGroundBreadcrumb();
            }
        });
        registerActivityLifecycleCallbacks(applicationLifecycleHandler);
        registerComponentCallbacks(applicationLifecycleHandler);
    }

    /**
     * Get all default share pref data into Sharable preferences.
     * This is added from version 2.2.9
     */
    private void transferPrefData() {
        Map<String, ?> map = AppPref.getAll();
        //Once existing data copied  to shared pref or for new users map is empty
        if (map.isEmpty())
            return;
        for (Map.Entry<String, ?> entry : map.entrySet()) {
            if (entry.getValue() instanceof String) {
                AppPrefShared.putValue(entry.getKey(), (String) entry.getValue());
            } else if (entry.getValue() instanceof Boolean) {
                AppPrefShared.putValue(entry.getKey(), (Boolean) entry.getValue());
            } else if (entry.getValue() instanceof Integer) {
                AppPrefShared.putValue(entry.getKey(), (Integer) entry.getValue());
            } else if (entry.getValue() instanceof Long) {
                AppPrefShared.putValue(entry.getKey(), (Long) entry.getValue());
            } else if (entry.getValue() instanceof Float) {
                AppPrefShared.putValue(entry.getKey(), (Float) entry.getValue());
            } else if (entry.getValue() instanceof Long) {
                AppPrefShared.putValue(entry.getKey(), (Long) entry.getValue());
            }

        }
        AppPref.clear();
    }

    private void initializePlaceAPI() {
        if (!Places.isInitialized()) {
            Places.initialize(this, BuildConfig.googleMapApiKey);
        }
    }


    public static synchronized BaseApplication getInstance() {
        return sInstance;
    }

    public void startImageUpload() {
        checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);
        Intent intent = new Intent(IMAGE_UPLOAD_SERVICE_BROADCAST);
        sendBroadcast(intent);
    }

    public void startBothUploads() {
        checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);
        Intent intent = new Intent(UPLOAD_BOTH_DATA_BROADCAST);
        sendBroadcast(intent);

    }

    public void startOtherUploads() {
        checkAndStartFileUploaderServiceIfRequired(FileUploaderService.class);
        Intent intent = new Intent(UPLOAD_OTHER_DATA_BROADCAST);
        sendBroadcast(intent);
    }


    public void startStopUploadingOnConnectivityChange() {
        if (isInternetAvailable()) {
            // Start image upload process when network is connected.
            startBothUploads();

        } else {
            // Stop image uploading process when network is disconnected.
            sendBroadCastForPendingImage(0);

        }

    }

    public void sendBroadCastForPendingImage(int uploadId) {
        CustomEvent event = new CustomEvent();
        event.intValue = uploadId;
        event.action = Constants.INTERNAL_PENDING_IMAGE_UPLOAD;
        EventBus.getDefault().post(new UploadFileStatusEvent(event));
    }


    /**
     * Check Network Availability
     *
     * @param context Context Object Of Activity
     * @return true/false
     */
    public boolean isOnline(Context context) {

        ConnectivityManager cm = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        NetworkInfo netInfo;
        if (cm != null) {
            netInfo = cm.getActiveNetworkInfo();
            return netInfo != null && netInfo.isConnected();
        }
        return false;

    }


    public boolean isInternetAvailable() {
        if (AppPrefShared.getBoolean(Constants.LOGGED_IN_USER_UPLOAD_USING_CELLULAR_DATA, true)) {
            return isOnline(getApplicationContext());
        } else {
            return isOnline(getApplicationContext())
                    && (!NetworkUtil.isMobileDataEnabled(getApplicationContext()) || NetworkUtil.isWiFiDataEnabled(getApplicationContext()));
        }
    }

    public void checkAndStartFileUploaderServiceIfRequired(Class<?> serviceClass) {
        try {
            //if service is not running then this function start new notification service instance.
            if (isServiceRunning(serviceClass))
                return;
            Intent notificationService = new Intent(this, serviceClass);
            startService(notificationService);
        }  catch (Exception ignored) {
            // No need to capture this exception as system now do not allow to upload when app goes in BG.
            //Also Some provider instant stop service and some do not. So no need to set any other condition to manage this flow.
        }
    }

    private boolean isServiceRunning(Class<?> serviceClass) {
        ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager != null) {
            //RunningServiceInfo class returns running all services in background with extra data.
            for (ActivityManager.RunningServiceInfo service : activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (serviceClass.getName().equals(service.service.getClassName())) {
                    //Our service is running, so no need to restart it.
                    return true;
                }
            }
        }
        return false;
    }

    public void stopServiceIfNotRequired(Class<?> serviceClass) {
        try {

            boolean isServiceRunning = isServiceRunning(serviceClass);
            if (isServiceRunning) {
                Intent serviceIntent = new Intent(this, serviceClass);
                stopService(serviceIntent);
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }

    }

    private void solveGoogleMapIssue() {
        try {
            SharedPreferences googleBug = getSharedPreferences("google_bug_154855417", Context.MODE_PRIVATE);
            if (!googleBug.contains("fixed")) {
                File corruptedZoomTables = new File(getFilesDir(), "ZoomTables.data");
                corruptedZoomTables.delete();
                googleBug.edit().putBoolean("fixed", true).apply();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    private void setLocale(String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);
        Configuration config = getBaseContext().getResources().getConfiguration();
        config.locale = locale;
        getBaseContext().getResources().updateConfiguration(config,
                getBaseContext().getResources().getDisplayMetrics());

    }

    public void setTopActivityName(String activityName) {
        this.topActivityName = activityName;
    }

    public String getTopActivityName() {
        return topActivityName;
    }

    protected void registerGPSListener() {
        try {
            if (gpsChangeListener == null) {
                gpsChangeListener = new GPSChangeListener();
            }
            IntentFilter filter = new IntentFilter(LocationManager.PROVIDERS_CHANGED_ACTION);
            filter.addAction(Intent.ACTION_PROVIDER_CHANGED);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                //We need to set RECEIVER_EXPORTED as we upload our data on another process
                registerReceiver(gpsChangeListener, filter, Context.RECEIVER_EXPORTED);
            } else {
                registerReceiver(gpsChangeListener, filter);
            }
        } catch (IllegalArgumentException e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void unRegisterGPSListener(GPSChangeListener gpsChangeListener) {
        if (gpsChangeListener != null) {
            unregisterReceiver(gpsChangeListener);
        }
    }

    private void appWentInBackgroundBreadcrumb() {
        if (TextUtils.isEmpty(AppPrefShared.getString(LOGGED_IN_USER_COMPANY_ID, ""))) {
            return;
        }
        EventBus.getDefault().post(new AppStatusChangedEvent(false));
        sendInstantBreadcrumbs(16);
    }

    private void appBoughtInForeGroundBreadcrumb() {
        if (TextUtils.isEmpty(AppPrefShared.getString(LOGGED_IN_USER_COMPANY_ID, ""))) {
            return;
        }
        EventBus.getDefault().post(new AppStatusChangedEvent(true));
        sendInstantBreadcrumbs(15);
    }

    public void sendInstantBreadcrumbs(int type) {
        ApiInterface apiService = RetrofitProvider.createService(ApiInterface.class);
        HashMap<String, Object> params = new HashMap<>();
        params.put(PARAM_LAT, MainActivity.currentLatitude);
        params.put(PARAM_LON, MainActivity.currentLongitude);
        params.put(PARAM_ACCESS_CODE, AppPrefShared.getString(LOGGED_IN_USER_COMPANY_ID, " "));
        params.put(PARAM_DT, System.currentTimeMillis() / 1000);
        params.put(PARAM_TYPE, type);
        params.put(PARAM_EMAIL, AppPrefShared.getString(LOGGED_IN_USER_EMAIL_ADDRESS, ""));
        params.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
        params.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
        StaticUtils.addCommonData(params);
        Call<SimpleResponse> call = apiService.requestToPostBreadCrumbsData(params);
        call.enqueue(new retrofit2.Callback<SimpleResponse>() {
            @Override
            public void onResponse(@NonNull Call<SimpleResponse> call, @NonNull retrofit2.Response<SimpleResponse> response) {
                // CustomLogKt.errorLog("ResponseCode","Code:::"+response.code());
            }

            @Override
            public void onFailure(@NonNull Call<SimpleResponse> call, @NonNull Throwable throwable) {
                try {
                    //CustomLogKt.errorLog("onFailure","error:::"+throwable.getMessage());
                } catch (Exception e) {
                    FirebaseEventUtils.logException(e);
                }
            }
        });
    }

    public void prepareDataForBreadCrumb(int type, double currentLatitude, double currentLongitude) {
        try {
            HashMap<String, Object> data = new HashMap<>();
            data.put(PARAM_LAT, currentLatitude);
            data.put(PARAM_LON, currentLongitude);
            data.put(PARAM_TYPE, type);
            String title = StaticUtils.getBreadcrumbTypeTitle(this, type);
            data.put(Constants.PARAM_TITLE, getString(R.string.breadcrumb_single, title));
            data.put(PARAM_DT, System.currentTimeMillis() / 1000);
            data.put(Constants.PARAM_APP_UDID, StaticUtils.checkAndGetDeviceId());
            data.put(Constants.PARAM_ACCESS_CODE, AppPrefShared.getString(Constants.LOGGED_IN_USER_COMPANY_ID, " "));
            data.put(Constants.PARAM_APP_VERSION, BuildConfig.VERSION_NAME);
            StaticUtils.addCommonData(data);
            data.put(Constants.PARAM_SUBMITTED_TIME, System.currentTimeMillis() / 1000);
            data.put(Constants.PARAM_UUID, StaticUtils.getUuid());
            StaticUtils.prepareBreadCrumbDataToAddInUploadQueue(this, data);
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void logoutUser(boolean shouldClearData) {
        if (shouldClearData) {
            NotificationUtils.removeLocalNotificationFromAlarmManagerIfSet(this);
            clearSharedPreferencesLogOut();
        }
        DBOpenHelper.getInstance(this).truncateDatabase();
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);

    }

    public void clearSharedPreferencesLogOut() {
        String languageCode = AppPrefShared.getString(Constants.USER_CURRENT_LANGUAGE, "en");
        AppPrefShared.clear();
        AppPrefShared.putValue(Constants.USER_CURRENT_LANGUAGE, languageCode);
        BaseApplication.getInstance().stopServiceIfNotRequired(FileUploaderService.class);
        ImageUtil.deleteAllFilesFromFolder(this);
    }

    /**
     * Re initialize data executor service if it is terminated or shutdown
     */
    public void reInitiateDataExecutorServiceIfShutDown(){
        try {
            if (dataExecutorService.isShutdown() || dataExecutorService.isTerminated()){
                dataExecutorService  = Executors.newSingleThreadExecutor();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }


    /**
     * Re initialize image executor service if it is terminated or shutdown
     */
    public void reInitiateImageExecutorServiceIfShutDown(){
        try {
            if (imageExecutorService.isShutdown() || imageExecutorService.isTerminated()){
                imageExecutorService  = Executors.newSingleThreadExecutor();
            }
        } catch (Exception e) {
            FirebaseEventUtils.logException(e);
        }
    }

    public void shutDownExecutorService() {
        dataExecutorService.shutdown();
        imageExecutorService.shutdown();
        try {
            if (!dataExecutorService.awaitTermination(120, TimeUnit.SECONDS)) {
                dataExecutorService.shutdownNow();
            }

            if (!imageExecutorService.awaitTermination(120, TimeUnit.SECONDS)) {
                imageExecutorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            FirebaseEventUtils.logException(e);
            dataExecutorService.shutdownNow();
            imageExecutorService.shutdownNow();
        }
    }

}
