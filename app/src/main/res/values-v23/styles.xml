<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppBaseTheme" parent="Base.Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="android:navigationBarColor">@color/dashboard_background</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:datePickerStyle">@style/AppThemeDatePickerStyle</item>
        <item name="android:timePickerStyle">@style/AppThemeDatePickerStyle</item>
        <item name="android:datePickerDialogTheme">@style/AppThemeDatePickerStyle</item>
        <item name="android:timePickerDialogTheme">@style/AppThemeDatePickerStyle</item>
        <!--<item name="android:alertDialogTheme">@style/AppThemeDatePickerStyle</item>-->
        <item name="android:textColorLink">@color/colorAccent</item>
        <item name="actionBarStyle">@style/CustomActionBar</item>
        <item name="actionBarTheme">@style/CustomActionBarTheme</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o_mr1">true</item>
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <item name="android:homeAsUpIndicator">@drawable/ic_arrow_back</item>
        <item name="android:actionMenuTextColor">@color/white</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="AppBaseThemeNoActionBarFullScreen" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="android:navigationBarColor">@color/dashboard_background</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:datePickerStyle">@style/AppThemeDatePickerStyle</item>
        <item name="android:timePickerStyle">@style/AppThemeDatePickerStyle</item>
        <item name="android:datePickerDialogTheme">@style/AppThemeDatePickerStyle</item>
        <item name="android:timePickerDialogTheme">@style/AppThemeDatePickerStyle</item>
        <!--<item name="android:alertDialogTheme">@style/AppThemeDatePickerStyle</item>-->
        <item name="android:textColorLink">@color/colorAccent</item>
        <item name="android:windowLightNavigationBar" tools:targetApi="o_mr1">true</item>
        <!--<item name="actionBarStyle">@style/CustomActionBar</item>-->
        <!--<item name="actionBarTheme">@style/CustomActionBarTheme</item>-->
        <item name="android:windowLightStatusBar">false</item>

        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="AppBaseThemeNoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>

        <!-- <item name="windowNoTitle">true</item>
         <item name="windowActionBar">false</item>
         <item name="android:windowFullscreen">true</item>
         <item name="android:windowContentOverlay">@null</item>-->
    </style>

    <!-- Application theme. -->
    <style name="AppTheme" parent="AppBaseTheme">

        <!-- All customizations that are NOT specific to a particular API-level can go here. -->
        <!--         <item name="android:windowNoTitle">true</item> -->
        <!--         <item name="windowActionBar">true</item> -->
        <!--         <item name="android:windowFullscreen">false</item> -->
        <!--         <item name="android:windowContentOverlay">@null</item> -->
        <!--         <item name="android:actionBarWidgetTheme">@style/MyActionBarWidgetTheme</item> -->
    </style>

    <style name="MyActionBarWidgetTheme" parent="Theme.AppCompat.Light">
        <item name="android:textColorHint">@color/white_selector</item>

    </style>

    <style name="DrawerArrowStyle" parent="Widget.AppCompat.DrawerArrowToggle">
        <item name="spinBars">true</item>
        <item name="color">@android:color/white</item>
    </style>

    <style name="DialogTheme" parent="Theme.AppCompat.Light">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:headerBackground">@color/colorAccent</item>
        <item name="android:divider">@color/colorAccent</item>
        <item name="android:title">@string/select_date_time</item>
        <item name="android:textColor">@color/colorAccent</item>
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
    </style>

    <style name="MaterialDialogTheme" parent="Theme.MaterialComponents.Light.Dialog.Alert">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:headerBackground">@color/colorAccent</item>
        <item name="android:divider">@color/colorAccent</item>
        <item name="android:title">@string/select_date_time</item>
        <item name="android:textColor">@color/colorAccent</item>
        <item name="buttonBarNegativeButtonStyle">@style/MaterialNegativeButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/MaterialPositiveButtonStyle</item>
        <!--<item name="android:layout_width">wrap_content</item>-->
        <!--<item name="android:layout_height">wrap_content</item>-->
    </style>

    <style name="MaterialNegativeButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="MaterialPositiveButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>


    <style name="AppThemeDatePickerStyle" parent="Base.Theme.AppCompat.Light.Dialog">
        <item name="android:headerBackground">@color/colorAccent</item>
        <!--<item name="android:windowIsFloating">true</item>-->
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <style name="MyDatePickerStyle" parent="@android:style/Widget.Material.Light.DatePicker">
        <item name="android:headerBackground">@color/colorAccent</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:listDivider">@color/colorAccent</item>
    </style>

    <style name="CustomActionBar" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="titleTextStyle">@style/CustomActionBarTextStyle</item>
        <!--<item name="android:background">@color/white_selector</item>-->
    </style>

    <style name="CustomActionBarTextStyle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="CustomActionBarTheme" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <!-- THIS is where you can color the arrow! -->
        <item name="colorControlNormal">@color/colorPrimary</item>
    </style>

    <style name="CustomActionBarMap" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="titleTextStyle">@style/CustomActionBarTextStyleMap</item>
        <!--<item name="android:background">@color/white_selector</item>-->
    </style>

    <style name="CustomActionBarTextStyleMap" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="CustomActionBarThemeMap" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <!-- THIS is where you can color the arrow! -->
        <item name="colorControlNormal">@color/colorPrimary</item>
        <item name="colorPrimary">@color/grey_actionbar_bg</item>
        <item name="colorPrimaryDark">@color/grey_actionbar_bg</item>
        <item name="android:windowLightStatusBar">true</item>
    </style>

    <style name="AppBaseThemeMap" parent="Base.Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/grey_actionbar_bg</item>
        <item name="colorPrimaryDark">@color/grey_actionbar_bg</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:datePickerStyle">@style/AppThemeDatePickerStyle</item>
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:textColorLink">@color/colorAccent</item>
        <item name="actionBarStyle">@style/CustomActionBarMap</item>
        <item name="actionBarTheme">@style/CustomActionBarThemeMap</item>

    </style>

    <style name="AppCompatAlertDialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:textColorPrimary">@android:color/black</item>
        <item name="android:textSize">16sp</item>
        <!--<item name="android:titleTextAppearance">@style/AppCompatAlertDialogTitleTextAppearance</item>-->
        <item name="windowNoTitle">false</item>
        <!--<item name="android:background">#5fa3d0</item>-->
    </style>

    <style name="AppCompatAlertDialogTitleTextAppearance">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <!--<item name="android:textColor">?textColorPrimary</item>-->
    </style>

</resources>
