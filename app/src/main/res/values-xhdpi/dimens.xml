<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="microPadding">2dp</dimen>
    <dimen name="padding_tiny">4dp</dimen>
    <dimen name="padding_small_tiny">6dp</dimen>
    <dimen name="padding_small">9dp</dimen>
    <dimen name="padding_medium_small">11dp</dimen>
    <dimen name="padding_medium">14dp</dimen>
    <dimen name="padding_medium_large">17dp</dimen>
    <dimen name="padding_large">19dp</dimen>
    <dimen name="padding_xlarge">23dp</dimen>
    <dimen name="padding_xxlarge">28dp</dimen>
    <dimen name="padding_xxxlarge">33dp</dimen>
    <dimen name="padding_xxxxlarge">38dp</dimen>
    <dimen name="padding_largest">43dp</dimen>
    <dimen name="padding_xxxlargest">90dp</dimen>

    <dimen name="text_size_tiny">6sp</dimen>
    <dimen name="text_size_small">8sp</dimen>
    <dimen name="text_size_medium">9sp</dimen>
    <dimen name="text_size_medium_large">11sp</dimen>
    <dimen name="text_size_large">14sp</dimen>
    <dimen name="text_size_xlarge">16sp</dimen>
    <dimen name="text_size_xxlarge">18sp</dimen>
    <dimen name="text_size_xxxlarge">19sp</dimen>
    <dimen name="text_size_largest">21sp</dimen>
    <dimen name="text_size_xlargest">23sp</dimen>
    <dimen name="text_size_xxlargest">24sp</dimen>
    <dimen name="text_view_size">30dp</dimen>
    <dimen name="spinner_minimum_height">35dp</dimen>
    <dimen name="minimum_width">30dp</dimen>
    <dimen name="image_height_width">80dp</dimen>
    <dimen name="minimum_hight_comment">80dp</dimen>

    <integer name="camera_max_width">1080</integer>
    <integer name="camera_max_height">1500</integer>
</resources>
