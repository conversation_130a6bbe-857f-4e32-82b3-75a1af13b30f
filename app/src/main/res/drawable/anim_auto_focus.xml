<?xml version="1.0" encoding="utf-8"?>
<animation-list
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:oneshot="true">
    <item android:drawable="@android:color/transparent" android:duration="10"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_1" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_2" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_3" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_4" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_5" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_6" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_7" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_8" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_9" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_10" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_11" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_12" android:duration="30"/>
    <item android:drawable="@drawable/ic_auto_focus_shape_13" android:duration="30"/>
    <item android:drawable="@android:color/transparent" android:duration="10"/>
</animation-list>