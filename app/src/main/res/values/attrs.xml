<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="ProgressWheel">
        <attr name="progressIndeterminate" format="boolean" />
        <attr name="barColor" format="color" />
        <attr name="rimColor" format="color" />
        <attr name="rimWidth" format="dimension" />
        <attr name="spinSpeed" format="float" />
        <attr name="barSpinCycleTime" format="integer" />
        <attr name="circleRadius" format="dimension" />
        <attr name="fillRadius" format="boolean" />
        <attr name="barWidth" format="dimension" />
        <attr name="linearProgress" format="boolean" />
    </declare-styleable>

    // RecyclerView resources
    <declare-styleable name="CustomRecyclerView">
        <attr name="gird_span" format="integer" />
        <attr name="list_type" />
        <attr name="list_orientation" />
    </declare-styleable>

    <attr name="list_type">
        <enum name="list" value="0" />
        <enum name="grid" value="1" />
        <enum name="staggered" value="2" />
    </attr>

    <attr name="list_orientation">
        <enum name="vertical" value="0" />
        <enum name="horizontal" value="1" />
    </attr>

    <declare-styleable name="VerticalSlideColorPicker">
        <attr name="borderColor" format="color" />
        <attr name="borderWidth" format="dimension" />
        <attr name="colors" format="reference" />
    </declare-styleable>

    <declare-styleable name="ShaderImageView">
        <attr name="siSquare" format="boolean" />
        <attr name="siBorderColor" format="color" />
        <attr name="siBorderWidth" format="dimension" />
        <attr name="siBorderAlpha" format="float" />
        <attr name="siForeground" format="integer|reference" />
        <!-- Rounded Image View -->
        <attr name="siRadius" format="dimension" />
        <!--BubbleImageView-->
        <attr name="siArrowPosition"/>
        <attr name="siTriangleHeight" format="dimension"/>
        <!--PorterImageView-->
        <attr name="siShape" format="integer|reference" />
        <!--ShaderImageView-->
        <attr name="siBorderType"/>
        <attr name="siStrokeCap"/>
        <attr name="siStrokeJoin"/>
        <attr name="siStrokeMiter" format="dimension" />
    </declare-styleable>

    <attr name="siArrowPosition">
        <enum name="left" value="0" />
        <enum name="right" value="1" />
    </attr>

    <attr name="siBorderType">
        <enum name="stroke" value="0" />
        <enum name="fill" value="1" />
    </attr>

    <attr name="siStrokeCap">
        <enum name="butt" value="0" />
        <enum name="round" value="1" />
        <enum name="square" value="2" />
    </attr>

    <attr name="siStrokeJoin">
        <enum name="bevel" value="0" />
        <enum name="miter" value="1" />
        <enum name="round" value="2" />
    </attr>
</resources>