<resources>

    <!-- Application theme. -->
    <style name="AppTheme" parent="AppBaseTheme">

    </style>

    <!-- Base application theme. -->
    <style name="AppBaseTheme" parent="Base.Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:datePickerStyle">@style/AppThemeDatePickerStyle</item>
        <!--<item name="android:alertDialogTheme">@style/AppThemeDatePickerStyle</item>-->
        <item name="android:textColorLink">@color/colorAccent</item>
        <item name="actionBarStyle">@style/CustomActionBar</item>
        <item name="actionBarTheme">@style/CustomActionBarTheme</item>
        <item name="alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:homeAsUpIndicator">@drawable/ic_arrow_back</item>
        <item name="android:actionMenuTextColor">@color/white</item>

    </style>


    <style name="BottomSheetDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:navigationBarColor">@color/setting_activity_background</item>
        <item name="android:windowIsFloating">false</item>
    </style>


    <style name="AppBaseThemeNoActionBarFullScreen" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:datePickerStyle">@style/AppThemeDatePickerStyle</item>
        <!--<item name="android:alertDialogTheme">@style/AppThemeDatePickerStyle</item>-->
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:textColorLink">@color/colorAccent</item>
        <item name="actionBarStyle">@style/CustomActionBar</item>
        <item name="actionBarTheme">@style/CustomActionBarTheme</item>

        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="AppBaseThemeNoActionBar" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>

        <!-- <item name="windowNoTitle">true</item>
         <item name="windowActionBar">false</item>
         <item name="android:windowFullscreen">true</item>
         <item name="android:windowContentOverlay">@null</item>-->
    </style>

    <style name="MyActionBarWidgetTheme" parent="Theme.AppCompat.Light">
        <item name="android:textColorHint">@color/white_selector</item>
    </style>

    <style name="DrawerArrowStyle" parent="Widget.AppCompat.DrawerArrowToggle">
        <item name="spinBars">true</item>
        <item name="color">@android:color/white</item>
    </style>


    <style name="AlertDialogTheme" parent="ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:headerBackground">@color/colorAccent</item>
        <item name="android:divider">@color/colorAccent</item>
        <item name="android:textColor">@color/colorAccent</item>
        <item name="buttonBarNegativeButtonStyle">@style/NegativeButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">@style/PositiveButtonStyle</item>
    </style>

    <style name="NegativeButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <style name="PositiveButtonStyle" parent="Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:textColor">@color/colorAccent</item>
    </style>


    <style name="DialogTheme" parent="Theme.AppCompat.Light">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:headerBackground">@color/colorAccent</item>
        <item name="android:divider">@color/colorAccent</item>
        <item name="android:title">@string/select_date_time</item>
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <style name="BottomDialogTheme" parent="Theme.Design.Light.BottomSheetDialog">
        <item name="android:windowTranslucentStatus">true</item>
    </style>


    <style name="tvToolbarSubTitle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:padding">@dimen/padding_small</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:visibility">gone</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>


    <style name="tvToolbarTitle">
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:focusable">false</item>
        <item name="android:clickable">false</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>

    <style name="tvListMapToggle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:paddingTop">@dimen/padding_tiny</item>
        <item name="android:paddingBottom">@dimen/padding_tiny</item>
        <item name="android:paddingStart">@dimen/padding_xlarge</item>
        <item name="android:paddingEnd">@dimen/padding_xlarge</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:ellipsize">end</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/white</item>
    </style>


    <style name="ivToolbarIcon">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:contentDescription">@string/app_name</item>
        <item name="android:padding">@dimen/padding_medium_small</item>
        <item name="android:visibility">gone</item>
        <item name="android:layout_centerVertical">true</item>
    </style>

    <style name="btnToolbarBack">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_alignParentStart">false</item>
        <item name="android:layout_centerVertical">false</item>
        <item name="android:background">?attr/selectableItemBackgroundBorderless</item>
        <item name="android:contentDescription">@string/app_name</item>
        <item name="android:gravity">center</item>
        <item name="android:adjustViewBounds">true</item>
        <item name="android:padding">@dimen/padding_medium</item>
        <item name="android:src">@drawable/ic_arrow_back</item>
        <item name="android:visibility">visible</item>
    </style>


    <style name="AppThemeDatePickerStyle" parent="Base.Theme.AppCompat.Light.Dialog">
        <item name="android:headerBackground">@color/colorAccent</item>
        <!--<item name="android:windowIsFloating">true</item>-->
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <!--<style name="progressLinear">-->
    <!--<item name="android:progress"></item>-->
    <!--</style>-->

    <style name="MyProgressBar" parent="Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressDrawable">@drawable/custom_progressbar</item>
    </style>

    <style name="CustomActionBar" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="titleTextStyle">@style/CustomActionBarTextStyle</item>
    </style>

    <style name="CustomActionBarTextStyle" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="CustomActionBarTheme" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <!-- THIS is where you can color the arrow! -->
        <item name="colorControlNormal">@color/colorPrimary</item>
    </style>

    <style name="CustomActionBarMap" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="titleTextStyle">@style/CustomActionBarTextStyleMap</item>
    </style>

    <style name="CustomActionBarTextStyleMap" parent="@style/TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="CustomActionBarThemeMap" parent="@style/ThemeOverlay.AppCompat.ActionBar">
        <!-- THIS is where you can color the arrow! -->
        <item name="colorControlNormal">@color/colorPrimary</item>
        <item name="colorPrimary">@color/grey_actionbar_bg</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
    </style>

    <style name="AppBaseThemeMap" parent="Base.Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/white_selector</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlNormal">@color/colorAccent</item>
        <item name="colorControlActivated">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorAccent</item>
        <item name="colorSwitchThumbNormal">@color/colorAccent</item>
        <item name="drawerArrowStyle">@style/DrawerArrowStyle</item>
        <item name="android:datePickerStyle">@style/AppThemeDatePickerStyle</item>
        <item name="android:alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:textColorLink">@color/colorAccent</item>
        <item name="actionBarStyle">@style/CustomActionBarMap</item>
        <item name="actionBarTheme">@style/CustomActionBarThemeMap</item>
    </style>

    <style name="OrangeSwitchStyle">
        <!-- Active thumb color & Active track color(30% transparency) -->
        <item name="colorControlActivated">@color/orange_transparent_30</item>
        <!-- Inactive thumb color -->
        <item name="colorSwitchThumbNormal">@color/white_transparent_30</item>
        <!-- Inactive track color(30% transparency) -->
        <item name="android:colorForeground">@color/white_transparent_30</item>
    </style>

    <style name="AppCompatAlertDialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:textColorPrimary">@android:color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="windowNoTitle">false</item>
        <!--<item name="android:titleTextAppearance">@style/AppCompatAlertDialogTitleTextAppearance</item>-->
        <!--<item name="android:background">#5fa3d0</item>-->
    </style>

    <style name="AppCompatAlertDialogTitleTextAppearance">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">normal</item>
        <!--<item name="android:textColor">?textColorPrimary</item>-->
    </style>


    <style name="materialItemTextStyle">
        <item name="android:padding">@dimen/padding_small</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/black_font</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>

    <style name="servicesListTitleStyle">
        <item name="android:padding">@dimen/padding_medium</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/white_selector</item>
        <item name="android:textStyle">bold</item>

    </style>


    <style name="DialogTitleText">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_marginBottom">5dp</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/colorPrimary</item>
    </style>

    <style name="DialogMessageStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_marginBottom">8dp</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/black</item>
    </style>

    <!--  Style for  permission title and purpose of permission message-->
    <style name="permission_dialog_ButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginRight">20dp</item>
        <item name="android:layout_marginEnd">20dp</item>
        <item name="android:padding">8dp</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textAllCaps">true</item>

    </style>

    <style name="LeftViewStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/black_font</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:minHeight">@dimen/spinner_minimum_height</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
        <item name="android:layout_marginEnd">@dimen/padding_tiny</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_weight">50</item>
    </style>

    <style name="RightViewStyle">
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:background">@drawable/drawable_lines_sign</item>
        <item name="android:minHeight">@dimen/spinner_minimum_height</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:padding">@dimen/padding_tiny</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_weight">50</item>
    </style>


    <style name="layoutMainStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/normal_distance</item>
        <item name="android:layout_marginBottom">@dimen/microPadding</item>
        <item name="android:background">@color/setting_activity_background</item>
        <item name="android:padding">@dimen/padding_tiny</item>
    </style>

    <style name="recyclerview_empty_button" parent="@style/Base.Widget.AppCompat.Button.Borderless">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gray</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_margin">@dimen/padding_small</item>
    </style>

    <style name="recyclerview_empty_text">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/gray</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:layout_margin">@dimen/padding_small</item>
    </style>

    <style name="loginEditText">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginStart">8dp</item>
        <item name="android:layout_marginEnd">8dp</item>
        <item name="android:background">@drawable/shape_edt_text_background</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:padding">8dp</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textColorHint">@color/gray</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="baseUrlEdtStyle">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">@dimen/base_url_button_size</item>
        <item name="android:background">@drawable/shape_edt_text_background</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:layout_weight">1</item>
        <item name="android:focusable">true</item>
        <item name="android:padding">8dp</item>
        <item name="android:focusableInTouchMode">true</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textColorHint">@color/gray</item>
        <item name="android:hint">@string/website_sitefotos</item>
        <item name="android:textSize">16sp</item>
        <item name="minHeight">48dp</item>
    </style>

    <style name="baseUrlButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/drawable_background_tab_selected</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/white</item>
    </style>


    <style name="loginButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">40dp</item>
        <item name="android:background">@drawable/shape_button_background</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColorHint">@color/colorPrimary</item>
        <item name="android:textSize">20sp</item>
    </style>

    <style name="btnOrangeBorder">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/padding_xlarge</item>
        <item name="android:background">@drawable/segment_background</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:textSize">@dimen/text_size_xxxlarge</item>

    </style>

    <style name="btnOrangeBorderRectangle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">@dimen/padding_xlarge</item>
        <item name="android:background">@drawable/bg_fill_orange_rectangle</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:textSize">@dimen/text_size_xxxlarge</item>
    </style>


    <style name="btnOrangeBG">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/drawable_background_tab_selected</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:paddingBottom">@dimen/padding_small_tiny</item>
        <item name="android:paddingTop">@dimen/padding_small_tiny</item>
        <item name="android:paddingStart">@dimen/padding_medium_small</item>
        <item name="android:paddingEnd">@dimen/padding_medium_small</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
        <item name="android:foreground">?attr/selectableItemBackground</item>
        <item name="android:layout_marginEnd">@dimen/padding_tiny</item>
        <item name="android:layout_marginBottom">@dimen/padding_small</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:minWidth">0dp</item>

    </style>


    <style name="btnBGOrangeRadiusBorder">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@drawable/shape_button_background</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:padding">@dimen/padding_small</item>
        <item name="android:textSize">16sp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:minWidth">0dp</item>
    </style>

    <style name="StyleCameraTopView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingBottom">@dimen/padding_medium_large</item>
        <item name="android:paddingTop">@dimen/padding_medium_large</item>

    </style>

    <style name="CameraButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@null</item>
        <item name="android:contentDescription">@string/app_name</item>
        <item name="android:padding">@dimen/padding_medium_small</item>
        <item name="android:gravity">center</item>

    </style>

    <style name="DrawButtonStyle" parent="CameraButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@null</item>
        <item name="android:contentDescription">@string/app_name</item>
        <item name="android:padding">@dimen/padding_medium_small</item>
        <item name="android:gravity">center</item>

    </style>

    <style name="searchEdtStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">@dimen/padding_small</item>
        <item name="android:background">@drawable/search_background</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:singleLine">true</item>
        <item name="maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingTop">@dimen/padding_tiny</item>
        <item name="android:imeOptions">actionDone</item>
        <item name="android:paddingBottom">@dimen/padding_tiny</item>
        <item name="android:paddingStart">@dimen/padding_small</item>
        <item name="android:paddingEnd">@dimen/padding_small</item>
    </style>

    <style name="searchEdtStyleGrayBG">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">@dimen/padding_small</item>
        <item name="android:background">@drawable/search_background_gray_bg</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:singleLine">true</item>
        <item name="maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingTop">@dimen/padding_tiny</item>
        <item name="android:imeOptions">actionDone</item>
        <item name="android:paddingBottom">@dimen/padding_tiny</item>
        <item name="android:paddingStart">@dimen/padding_small</item>
        <item name="android:paddingEnd">@dimen/padding_small</item>
    </style>

    <style name="crewSearchEdtStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">@dimen/microPadding</item>
        <item name="android:background">@drawable/search_background</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="searchViewStyle" parent="Widget.AppCompat.Light.SearchView">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">@dimen/padding_small</item>
        <item name="android:background">@drawable/search_background</item>
        <item name="android:textColorHint">@color/gray</item>
        <item name="searchIcon">@drawable/ic_search</item>
        <item name="android:singleLine">true</item>
    </style>


    <style name="buttonTopBarStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:background">@null</item>
        <item name="android:contentDescription">@string/app_name</item>
        <item name="android:padding">@dimen/padding_medium_small</item>
        <item name="android:gravity">center</item>
        <item name="layout_gravity">start</item>
        <item name="singleLine">true</item>
        <item name="fontFamily">@font/roboto_regular</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>

    </style>

    <style name="btnClockStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:singleLine">true</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_marginTop">@dimen/padding_small_tiny</item>
        <item name="android:layout_marginBottom">@dimen/padding_small_tiny</item>
        <item name="elevation">5dp</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:paddingTop">@dimen/padding_small_tiny</item>
        <item name="android:paddingBottom">@dimen/padding_small_tiny</item>
        <item name="android:paddingStart">@dimen/padding_xxlarge</item>
        <item name="android:paddingEnd">@dimen/padding_xxlarge</item>
    </style>

    <style name="siteInfoTitleStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_marginTop">2dp</item>
        <item name="android:layout_marginBottom">2dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:background">@drawable/segment_square_unselected</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:gravity">center</item>
        <item name="android:paddingStart">@dimen/padding_medium_large</item>
        <item name="android:paddingTop">@dimen/padding_tiny</item>
        <item name="android:paddingEnd">@dimen/padding_medium_large</item>
        <item name="android:paddingBottom">@dimen/padding_tiny</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">@dimen/text_size_large</item>

    </style>

    <style name="galleryTextStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>

    <style name="ivPermissionStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">@dimen/padding_small</item>
        <item name="srcCompat">@drawable/ic_lock_camera</item>
    </style>

    <style name="tvPermissionStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">@dimen/padding_small</item>
        <item name="android:layout_marginStart">@dimen/padding_large</item>
        <item name="android:layout_marginEnd">@dimen/padding_large</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
    </style>

    <style name="btnPermissionStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:gravity">center</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:minWidth">100dp</item>
        <item name="android:layout_marginTop">@dimen/padding_small</item>
        <item name="android:padding">@dimen/padding_small_tiny</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:elevation">@dimen/padding_small_tiny</item>
        <item name="android:background">@drawable/background_button_selected_orange</item>
        <item name="android:textSize">@dimen/text_size_xlarge</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="txtSettingTitleStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_marginStart">@dimen/padding_small</item>
        <item name="android:layout_marginTop">@dimen/padding_large</item>
        <item name="android:textColor">@color/gray_2</item>
        <item name="textAllCaps">true</item>
        <item name="fontFamily">@font/roboto_medium</item>
        <item name="android:textSize">@dimen/text_size_medium_large</item>
    </style>

    <style name="txtSettingItemTitleStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:minHeight">40dp</item>
        <item name="android:textColor">@color/colorPrimary</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="fontFamily">@font/roboto_medium</item>
    </style>

    <style name="txtSettingItemTitleStyle.camera">
        <item name="android:layout_width">0dp</item>
        <item name="android:paddingEnd">@dimen/padding_small</item>
        <item name="android:paddingTop">@dimen/padding_medium</item>
        <item name="android:paddingBottom">@dimen/padding_medium</item>
        <item name="android:layout_weight">1</item>
    </style>

    <style name="txtSettingItemTitleStyle.general">
        <item name="android:paddingEnd">@dimen/padding_small</item>
        <item name="android:paddingTop">@dimen/padding_small</item>
        <item name="android:paddingBottom">@dimen/padding_small</item>
        <item name="android:background">@color/white</item>

        <item name="android:gravity">center_vertical</item>

    </style>

    <style name="llSettingStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:background">@color/white</item>
        <item name="android:orientation">horizontal</item>
    </style>

    <style name="switchSettingStyle">
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:theme">@style/OrangeSwitchStyle</item>
        <item name="android:layout_marginEnd">@dimen/padding_tiny</item>
    </style>


    <style name="searchBgStyle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">@dimen/padding_small</item>
        <item name="android:background">@drawable/search_background</item>

    </style>
    <style name="searchEdit">
        <item name="android:minHeight">40dp</item>
        <item name="android:singleLine">true</item>
        <item name="maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingTop">@dimen/padding_tiny</item>
        <item name="android:imeOptions">actionDone</item>
        <item name="android:paddingBottom">@dimen/padding_tiny</item>
        <item name="android:paddingStart">@dimen/padding_small</item>
        <item name="android:paddingEnd">@dimen/padding_small</item>
    </style>

    <style name="grayBGRondCornerStyle">
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:background">@drawable/drawable_round_corner_gray</item>
        <item name="android:minHeight">@dimen/spinner_minimum_height</item>
        <item name="android:gravity">center_vertical</item>
        <item name="android:padding">@dimen/padding_small_tiny</item>
        <item name="android:textSize">@dimen/text_size_large</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_width">wrap_content</item>
    </style>
</resources>
