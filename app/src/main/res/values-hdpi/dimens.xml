<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="microPadding">2dp</dimen>
    <dimen name="padding_tiny">3dp</dimen>
    <dimen name="padding_small_tiny">5dp</dimen>
    <dimen name="padding_small">8dp</dimen>
    <dimen name="padding_medium_small">10dp</dimen>
    <dimen name="padding_medium">12dp</dimen>
    <dimen name="padding_medium_large">15dp</dimen>
    <dimen name="padding_large">18dp</dimen>
    <dimen name="padding_xlarge">20dp</dimen>
    <dimen name="padding_xxlarge">25dp</dimen>
    <dimen name="padding_xxxlarge">30dp</dimen>
    <dimen name="padding_xxxxlarge">35dp</dimen>
    <dimen name="padding_largest">40dp</dimen>
    <dimen name="padding_xxxlargest">80dp</dimen>

    <dimen name="text_size_tiny">5sp</dimen>
    <dimen name="text_size_small">7sp</dimen>
    <dimen name="text_size_medium">9sp</dimen>
    <dimen name="text_size_medium_large">11sp</dimen>
    <dimen name="text_size_large">13sp</dimen>
    <dimen name="text_size_xlarge">14sp</dimen>
    <dimen name="text_size_xxlarge">19sp</dimen>
    <dimen name="text_size_xxxlarge">20sp</dimen>
    <dimen name="text_size_largest">21sp</dimen>
    <dimen name="text_size_xlargest">22sp</dimen>
    <dimen name="text_size_xxlargest">24sp</dimen>

    <integer name="camera_max_width">1080</integer>
    <integer name="camera_max_height">1500</integer>

</resources>
