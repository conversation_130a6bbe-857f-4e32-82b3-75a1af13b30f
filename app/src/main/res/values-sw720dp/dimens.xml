<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="microPadding">7dp</dimen>
    <dimen name="padding_tiny">8dp</dimen>
    <dimen name="padding_small_tiny">10dp</dimen>
    <dimen name="padding_small">12dp</dimen>
    <dimen name="padding_medium_small">14dp</dimen>
    <dimen name="padding_medium">17dp</dimen>
    <dimen name="padding_medium_large">20dp</dimen>
    <dimen name="padding_large">22dp</dimen>
    <dimen name="padding_xlarge">27dp</dimen>
    <dimen name="padding_xxlarge">32dp</dimen>
    <dimen name="padding_xxxlarge">37dp</dimen>
    <dimen name="padding_xxxxlarge">42dp</dimen>
    <dimen name="padding_largest">47dp</dimen>
    <dimen name="padding_xxxlargest">105dp</dimen>
    <dimen name="elevation_tiny">7dp</dimen>

    <dimen name="text_size_tiny">10sp</dimen>
    <dimen name="text_size_small">12sp</dimen>
    <dimen name="text_size_medium">13sp</dimen>
    <dimen name="text_size_medium_large">15sp</dimen>
    <dimen name="text_size_large">18sp</dimen>
    <dimen name="text_size_xlarge">20sp</dimen>
    <dimen name="text_size_xxlarge">22sp</dimen>
    <dimen name="text_size_xxxlarge">23sp</dimen>
    <dimen name="text_size_largest">25sp</dimen>
    <dimen name="text_size_xlargest">27sp</dimen>
    <dimen name="text_size_xxlargest">28sp</dimen>
    <integer name="camera_max_width">2200</integer>
    <integer name="camera_max_height">3200</integer>

</resources>
