<resources>
    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="microPadding">4dp</dimen>
    <dimen name="padding_tiny">5dp</dimen>
    <dimen name="padding_small_tiny">8dp</dimen>
    <dimen name="padding_small">10dp</dimen>
    <dimen name="padding_medium_small">12dp</dimen>
    <dimen name="padding_medium">15dp</dimen>
    <dimen name="padding_medium_large">18dp</dimen>
    <dimen name="padding_large">20dp</dimen>
    <dimen name="padding_xlarge">25dp</dimen>
    <dimen name="padding_xxlarge">30dp</dimen>
    <dimen name="padding_xxxlarge">35dp</dimen>
    <dimen name="padding_xxxxlarge">40dp</dimen>
    <dimen name="padding_largest">45dp</dimen>
    <dimen name="padding_xxxlargest">100dp</dimen>
    <dimen name="elevation_tiny">5dp</dimen>

    <dimen name="text_size_tiny">7sp</dimen>
    <dimen name="text_size_small">9sp</dimen>
    <dimen name="text_size_medium">10sp</dimen>
    <dimen name="text_size_medium_large">12sp</dimen>
    <dimen name="text_size_large">15sp</dimen>
    <dimen name="text_size_xlarge">17sp</dimen>
    <dimen name="text_size_xxlarge">19sp</dimen>
    <dimen name="text_size_xxxlarge">20sp</dimen>
    <dimen name="text_size_largest">22sp</dimen>
    <dimen name="text_size_xlargest">24sp</dimen>
    <dimen name="text_size_xxlargest">27sp</dimen>
    <integer name="camera_max_width">2400</integer>
    <integer name="camera_max_height">3400</integer>

</resources>
