<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llMultiSelect"
    style="@style/layoutMainStyle"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvMultiSelectTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/black_font"
        android:text="@string/select_crew"
        android:textSize="@dimen/text_size_xlarge" />

    <TextView
        android:id="@+id/tvMultiSelected"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/microPadding"
        android:background="@drawable/drawable_lines_sign"
        android:gravity="center_vertical"
        android:hint="@string/tap_to_select_crew"
        android:minHeight="@dimen/spinner_minimum_height"
        android:paddingEnd="@dimen/padding_tiny"
        android:paddingStart="@dimen/padding_small"
        android:paddingTop="@dimen/padding_tiny"
        android:paddingBottom="@dimen/padding_tiny"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_large" />

</LinearLayout>
