<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingBottom="@dimen/padding_tiny"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@font/roboto_medium"
            android:paddingStart="@dimen/padding_medium"
            android:paddingTop="@dimen/microPadding"
            android:paddingEnd="@dimen/padding_medium"
            android:text="@string/txt_english"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />


        <TextView
            android:id="@+id/tvLang"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@font/roboto_medium"
            android:paddingStart="@dimen/padding_medium"
            android:paddingEnd="@dimen/padding_medium"
            android:paddingBottom="@dimen/microPadding"
            android:text="@string/txt_english_title"
            android:textColor="@color/gray"
            android:textSize="@dimen/text_size_large" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/cbLang"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end|center_vertical"
        android:layout_marginEnd="@dimen/padding_small"
        android:layout_weight="1"
        android:background="@null"
        android:gravity="center_vertical"
        android:paddingStart="@dimen/padding_medium_large"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium_large"
        android:paddingBottom="@dimen/padding_small" />

</LinearLayout>