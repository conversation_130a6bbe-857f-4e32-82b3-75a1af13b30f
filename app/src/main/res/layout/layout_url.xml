<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/layoutMainStyle">

        <TextView
            android:id="@+id/tvUrlTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/microPadding"
            android:gravity="center"
            android:singleLine="true"
            android:ellipsize="end"
            android:paddingEnd="@dimen/padding_tiny"
            android:paddingStart="@dimen/padding_tiny"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_large" />

</LinearLayout>
