<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent"
    android:layout_margin="@dimen/microPadding"
    android:gravity="center">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/adjust_img_pdng"
        app:cardCornerRadius="@dimen/microPadding"
        android:padding="1dp"
        android:background="@color/white"
        app:cardElevation="@dimen/padding_medium_small"
        app:cardMaxElevation="@dimen/padding_medium_small">

        <RelativeLayout
            android:layout_width="match_parent"

            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivGalleryPlaceholder"
                android:layout_width="match_parent"
                android:layout_height="@dimen/galleryItemHeight"
                android:contentDescription="@string/app_name"
                android:scaleType="centerCrop" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivSelected"
                android:layout_width="match_parent"
                android:layout_height="@dimen/galleryItemHeight"
                android:contentDescription="@string/app_name"
                android:scaleType="centerCrop" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivSelectionCount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_gravity="end"
                android:layout_margin="@dimen/adjust_img_pdng"
                android:padding="@dimen/padding_tiny"
                android:background="@drawable/drawable_circle_orange_border"
                android:src="@drawable/ic_tick"
                android:tint="@color/colorAccent"
                android:gravity="center"
                android:textSize="@dimen/text_size_medium"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvVideoDuration"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:layout_marginBottom="@dimen/padding_tiny"
                android:background="@drawable/drawable_timebg"
                android:paddingStart="@dimen/microPadding"
                android:paddingEnd="@dimen/microPadding"
                android:text="00:00"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:visibility="visible" />
        </RelativeLayout>
    </androidx.cardview.widget.CardView>

</RelativeLayout>