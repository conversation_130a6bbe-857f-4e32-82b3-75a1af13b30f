<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="0dp"
    android:layout_marginVertical="0dp"
    android:focusableInTouchMode="true"
    tools:targetApi="o">

    <RelativeLayout
        android:id="@+id/rlMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/padding_tiny">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginHorizontal="0dp"
            android:layout_marginVertical="0dp"
            android:layout_marginEnd="@dimen/padding_small"
            android:layout_toStartOf="@+id/ivDelete"
            android:orientation="horizontal"
            android:weightSum="100"
            tools:targetApi="o">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="55"
                android:background="@drawable/shape_white_bg_grey_border_corner">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvStar"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:padding="@dimen/microPadding"
                    android:text="*"
                    android:textColor="@color/black" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvMaterialName"
                    style="@style/materialItemTextStyle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/ivClear"
                    android:layout_toEndOf="@+id/tvStar"
                    android:padding="@dimen/padding_tiny"
                    android:textSize="@dimen/text_size_xlarge" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivClear"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:padding="@dimen/padding_tiny"
                    android:tint="@color/grey_label_font"
                    android:visibility="gone"
                    app:srcCompat="@drawable/icn_close" />
            </RelativeLayout>

            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:layout_marginStart="@dimen/padding_tiny"
                android:background="@color/grey_actionbar_bg" />

            <TextView
                android:id="@+id/tvUnit"
                style="@style/materialItemTextStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="25"
                android:padding="@dimen/microPadding"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="@dimen/padding_tiny"
                android:singleLine="true"
                android:textSize="@dimen/text_size_xlarge" />


            <View
                android:layout_width="1dp"
                android:layout_height="match_parent"
                android:background="@color/grey_actionbar_bg" />

            <com.sitefotos.util.views.CustomEditText
                android:id="@+id/edtQty"
                style="@style/materialItemTextStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/padding_tiny"
                android:layout_weight="20"
                android:background="@drawable/shape_white_bg_grey_border_corner"
                android:gravity="center"
                android:hint="@string/value"
                android:inputType="numberDecimal"
                android:padding="@dimen/microPadding"
                android:textSize="@dimen/text_size_xlarge" />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivDelete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/icn_close" />
    </RelativeLayout>

    <include
        layout="@layout/layout_saparator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/rlMain" />
</RelativeLayout>