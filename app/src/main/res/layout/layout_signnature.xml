<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llSign"
    style="@style/layoutMainStyle"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvSignatureTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/microPadding"
        android:fontFamily="@font/roboto_regular"
        android:paddingStart="@dimen/microPadding"
        android:paddingEnd="@dimen/microPadding"
        android:text="@string/sign_here"
        android:textColor="@color/black_font"
        android:textSize="@dimen/text_size_xlarge" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/microPadding"
        android:layout_marginTop="@dimen/microPadding"
        android:background="@drawable/drawable_lines_sign">
        <ImageView
            android:id="@+id/ivSign"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_height"
            android:scaleType="centerCrop" />
        <TextView
            android:id="@+id/tvClear"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/padding_tiny"
            android:padding="@dimen/padding_small"
            android:text="@string/clear"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_large"
            android:textStyle="bold"
            android:visibility="gone" />

    </RelativeLayout>
</LinearLayout>
