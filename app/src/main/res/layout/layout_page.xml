<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/svScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="vertical"
        android:scrollIndicators="end"
        android:animateLayoutChanges="true"
        android:layout_marginBottom="@dimen/padding_tiny"
        tools:targetApi="m">

        <LinearLayout
            android:id="@+id/llMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical" />
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
