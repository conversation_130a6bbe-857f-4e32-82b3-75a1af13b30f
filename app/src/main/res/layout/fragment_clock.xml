<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">

    <include
        android:id="@+id/tlClock"
        layout="@layout/layout_toolbar_clock" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_small">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edtSearch"
            style="@style/searchEdtStyle"
            android:hint="@string/search_crew" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/srlRefresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/llBottom">
                <com.sitefotos.util.views.CustomRecyclerView
                    android:id="@+id/rvClockCrew"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_above="@+id/llBottom"
                    android:layout_alignParentStart="true"
                    app:list_type="list" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <LinearLayout
            android:id="@+id/llBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:background="@color/setting_activity_background"
            android:orientation="vertical">

            <include
                android:id="@+id/dividerGray"
                layout="@layout/layout_divider_horizontal_grey" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:gravity="center"

                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnStart"
                    style="@style/btnClockStyle"
                    android:layout_marginStart="@dimen/padding_xxlarge"
                    android:layout_marginEnd="@dimen/padding_xxlarge"
                    android:background="@drawable/background_button_selected_green"
                    android:text="@string/start"
                    android:textColor="@color/white" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnStop"
                    style="@style/btnClockStyle"
                    android:layout_marginStart="@dimen/padding_xxlarge"
                    android:layout_marginEnd="@dimen/padding_xxlarge"
                    android:background="@drawable/background_button_selected_red"
                    android:text="@string/stop"
                    android:textColor="@color/white" />
            </LinearLayout>
        </LinearLayout>


        <include
            android:id="@+id/emptyView"
            layout="@layout/layout_recycler_emptyview" />
    </RelativeLayout>


</LinearLayout>
