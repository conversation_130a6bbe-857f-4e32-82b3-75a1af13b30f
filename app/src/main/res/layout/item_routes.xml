<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/padding_tiny">

    <LinearLayout
        android:id="@+id/llTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/ivArrow"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_tiny">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:clickable="false"
            android:padding="@dimen/padding_tiny"
            android:visibility="invisible"
            android:src="@drawable/ic_image_tick" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_medium"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge"
            tools:text="@string/routes_name" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCategory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/llTop"
        android:layout_marginTop="@dimen/padding_small_tiny"
        android:ellipsize="end"
        android:paddingStart="@dimen/padding_medium"
        android:paddingEnd="0dp"
        android:layout_toStartOf="@+id/ivArrow"
        android:fontFamily="@font/roboto_regular"
        android:maxLines="2"
        android:textColor="@color/gray"
        android:textSize="@dimen/text_size_xlarge"
        tools:text="@string/route_category" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerInParent="true"
        android:layout_gravity="end|center_vertical"
        android:paddingStart="@dimen/padding_medium"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium"
        android:paddingBottom="@dimen/padding_small"
        android:visibility="visible"
        android:src="@drawable/ic_forward" />
</RelativeLayout>