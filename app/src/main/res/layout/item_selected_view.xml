<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llMain"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="1dp">

    <androidx.cardview.widget.CardView
        android:layout_width="@dimen/galleryItemHeight"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/microPadding"
        android:layout_marginTop="@dimen/micro"
        android:layout_marginEnd="@dimen/micro"
        android:layout_marginBottom="@dimen/micro"
        app:cardBackgroundColor="@color/transparent_primary_50"
        app:cardCornerRadius="@dimen/padding_small_tiny">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivGalleryPlaceholder"
                android:layout_width="@dimen/galleryItemHeight"
                android:layout_height="@dimen/galleryItemHeight"
                android:contentDescription="@string/app_name"
                android:scaleType="centerCrop" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivDelete"
                android:layout_width="@dimen/padding_large"
                android:layout_height="@dimen/padding_large"
                android:layout_alignParentEnd="true"
                android:layout_gravity="end"
                android:layout_margin="@dimen/micro"
                android:contentDescription="@string/app_name"
                app:srcCompat="@drawable/ic_cancel" />
        </RelativeLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>