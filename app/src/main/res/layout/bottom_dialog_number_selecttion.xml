<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llAlertDialog"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/setting_activity_background"
        android:gravity="end">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnCancel"
            style="@style/CameraButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="@dimen/padding_small"
            android:text="@string/txt_cancel"
            android:textAllCaps="false"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />


        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnCheckin"
            style="@style/CameraButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="0dp"
            android:padding="@dimen/padding_small"
            android:text="@string/done"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_xlarge" />
    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvNumber"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/microPadding"
        android:paddingBottom="@dimen/padding_small"
        tools:listitem="@layout/item_single_number_selection" />

</LinearLayout>
