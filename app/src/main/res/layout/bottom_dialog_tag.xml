<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rlTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/grey_actionbar_bg"
            android:orientation="horizontal">


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto_regular"
                android:layout_marginStart="@dimen/padding_small"
                android:gravity="start"
                android:padding="@dimen/padding_small"
                android:text="@string/tags"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTagDone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:fontFamily="@font/roboto_regular"
                android:padding="@dimen/padding_small"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="@dimen/padding_small"
                android:text="@string/done"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/text_size_xlarge" />

        </RelativeLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvTag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_small"
            android:layout_marginTop="@dimen/microPadding"
            android:layout_marginEnd="@dimen/padding_small"
            android:background="@color/white"
            tools:listitem="@layout/item_tag" />


    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
