<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <CheckBox
            android:id="@+id/cbItem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_margin="@dimen/padding_tiny"
            android:textSize="@dimen/text_size_xlarge" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCheckboxItem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/cbItem"
            android:padding="@dimen/padding_small"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />
    </RelativeLayout>


</LinearLayout>