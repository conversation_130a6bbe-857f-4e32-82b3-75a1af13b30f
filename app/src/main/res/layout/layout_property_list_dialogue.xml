<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="8dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center"
        android:orientation="vertical"
        android:weightSum="1">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="4dp"
            android:text="@string/app_name"
            android:textColor="@android:color/darker_gray"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="4dp"
            android:text="@string/whichProperty"
            android:textColor="@android:color/darker_gray"
            android:textSize="14sp" />

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="4dp"
            android:src="@android:color/darker_gray" />

        <ListView
            android:id="@+id/listCustomDialoguePropertyList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:orientation="vertical">

            <Button
                android:id="@+id/btnCustomDialogueAddNewProperty"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/shape_property_dialogue_btn_background"
                android:text="@string/newProperty"
                android:textAllCaps="false"
                android:textColor="@color/colorPrimary" />

            <Button
                android:id="@+id/btnCustomDialogueNoneOfAbove"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:background="@drawable/shape_property_dialogue_btn_background"
                android:text="@string/noneOfAbove"
                android:textAllCaps="false"
                android:textColor="@color/colorPrimary"
                android:visibility="visible" />

        </LinearLayout>
    </LinearLayout>

</RelativeLayout>