<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rlMapLocatorView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_below="@+id/ivAutoFocusAnim"
    android:orientation="vertical"
    android:visibility="visible">

    <com.google.android.gms.maps.MapView
        android:id="@+id/mapView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="visible" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clToolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="false"
        android:background="@color/black_background_primary_90"
        android:clickable="false"
        android:focusable="false"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/icn_back"
            android:drawablePadding="@dimen/padding_tiny"
            android:gravity="center_vertical"
            android:padding="@dimen/padding_medium_small"
            android:text="@string/back"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_xxlarge"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvNext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableEnd="@drawable/icn_next"
            android:drawablePadding="@dimen/padding_tiny"
            android:gravity="center_vertical"
            android:padding="@dimen/padding_medium_small"
            android:text="@string/next"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_xxlarge"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <RelativeLayout
        android:id="@+id/rlPin"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/clToolbar"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginTop="@dimen/padding_small_tiny"
        android:layout_marginEnd="@dimen/padding_medium"
        android:background="@drawable/shape_transparent_bg_90_rounded_corner"
        android:orientation="vertical"
        android:padding="@dimen/padding_tiny"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPin"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:src="@drawable/ic_marker_blue"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvChoosePin"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/ivPin"
            android:gravity="center_vertical"
            android:text="@string/choose_pin"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_xxlarge"
            android:visibility="visible" />
    </RelativeLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivDummyViewOnMap"
        android:layout_width="90dp"
        android:layout_height="90dp"
        android:layout_below="@+id/clToolbar"
        android:background="@color/colorPrimaryDarkTransparent"
        android:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFakeView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@+id/clToolbar"
        android:visibility="gone" />

</RelativeLayout>
