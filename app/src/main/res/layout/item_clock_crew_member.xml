<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/padding_medium_small"
    android:layout_marginEnd="@dimen/padding_medium_small"
    android:background="@drawable/drawable_white_filled_corner_radius">

    <LinearLayout
        android:id="@+id/llTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/normal_distance"
        android:orientation="horizontal"
        android:padding="@dimen/padding_small_tiny">

        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/cbCrew"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:buttonTint="@color/app_blue"
            android:layout_marginEnd="@dimen/padding_medium_small"
            android:checked="false" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:layout_marginEnd="@dimen/padding_small_tiny"
            android:layout_weight="1"
            android:gravity="start"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCrewMemberName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:padding="@dimen/normal_distance"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge" />

            <Chronometer
                android:id="@+id/cmTime"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_regular"
                android:padding="@dimen/normal_distance"
                android:textColor="@color/grey_label_font"
                android:textSize="@dimen/text_size_large" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="end|center"
            android:layout_marginTop="@dimen/padding_tiny"
            android:layout_marginEnd="@dimen/padding_small"
            android:layout_marginBottom="@dimen/padding_tiny" />


    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_below="@+id/llTop"
        android:layout_marginStart="@dimen/padding_small"
        android:layout_marginEnd="@dimen/padding_small"
        android:background="@color/grey_actionbar_bg" />
</RelativeLayout>
