<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/padding_small"
            android:text="@string/app_name"
            android:textColor="@android:color/darker_gray"
            android:textSize="@dimen/text_size_xlarge"
            android:textStyle="bold" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="4dp"
            android:text="@string/share_via"
            android:textColor="@android:color/darker_gray"
            android:textSize="@dimen/text_size_xlarge" />

        <include layout="@layout/layout_divider_horizontal" />


        <Button
            android:id="@+id/btnSMS"
            style="@style/shareOptionStyle"
            android:text="@string/sms" />

        <include layout="@layout/layout_divider_horizontal" />

        <Button
            android:id="@+id/btnEmail"
            style="@style/shareOptionStyle"
            android:text="@string/email" />

        <include layout="@layout/layout_divider_horizontal" />

        <Button
            android:id="@+id/btnCancel"
            style="@style/shareOptionStyle"
            android:paddingBottom="@dimen/padding_medium_small"
            android:text="@string/txt_cancel" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
