<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingEnd="@dimen/padding_tiny"
    android:paddingStart="@dimen/padding_tiny">

        <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFormName"
        android:padding="@dimen/padding_small"
        android:layout_width="match_parent"
        android:textSize="@dimen/text_size_large"
        android:layout_toStartOf="@id/ivArrow"
        android:layout_height="wrap_content" />

    <ImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_gravity="end|center_vertical"
        android:layout_centerInParent="true"
        android:paddingBottom="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium"
        android:paddingStart="@dimen/padding_medium"
        android:paddingTop="@dimen/padding_small"
        android:src="@drawable/ic_forward" />
</RelativeLayout>
