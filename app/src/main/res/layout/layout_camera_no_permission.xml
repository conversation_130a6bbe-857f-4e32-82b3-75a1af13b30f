<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llCameraPermissionView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white_selector"
    android:gravity="center"
    android:orientation="vertical"
    android:visibility="gone">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCameraPermissionMessage"
        style="@style/ivPermissionStyle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCameraPermissionMessage"
        style="@style/tvPermissionStyle"
        android:text="@string/sitefotos_is_not_allowed_to_access_camera" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnPermission"
        style="@style/btnPermissionStyle"
        android:text="@string/settings" />
</LinearLayout>



