<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/rlMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_alignParentTop="true"
            android:orientation="vertical"
            android:paddingBottom="@dimen/microPadding">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:background="@color/colorPrimary"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="@dimen/padding_small">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_gravity="center"
                    android:background="@null"
                    android:fontFamily="@font/roboto_regular"
                    android:gravity="start|center_vertical"
                    android:maxWidth="140dp"
                    android:minWidth="0dp"
                    android:paddingStart="0dp"
                    android:paddingEnd="@dimen/padding_small"
                    android:text="@string/txt_cancel"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_xlarge" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/padding_small"
                    android:ellipsize="end"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center"
                    android:padding="@dimen/padding_small"
                    android:text="@string/txt_crew_selector"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_xlarge" />


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnCheckin"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_gravity="center"
                    android:background="@null"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="end|center_vertical"
                    android:maxWidth="140dp"
                    android:minWidth="0dp"
                    android:paddingStart="@dimen/padding_small"
                    android:paddingEnd="0dp"
                    android:text="@string/check_in"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_xlarge" />
            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/colorPrimary"
                android:orientation="horizontal"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="@dimen/padding_small">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/edtSearch"
                    style="@style/searchEdtStyle"
                    android:hint="@string/search_crew" />
            </LinearLayout>


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCrew"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/microPadding"
                android:visibility="visible" />

        </LinearLayout>

    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
