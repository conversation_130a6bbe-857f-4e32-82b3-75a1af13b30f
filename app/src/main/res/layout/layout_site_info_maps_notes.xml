<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llSiteInfo"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/setting_activity_background"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingTop="@dimen/padding_tiny"
    android:paddingBottom="@dimen/normal_distance">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvInfo"
            style="@style/siteInfoTitleStyle"
            android:layout_marginStart="2dp"
            android:text="@string/notes"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvOperatingHours"
            style="@style/siteInfoTitleStyle"
            android:text="@string/operating_hours"
            android:visibility="gone" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvMap"
            style="@style/siteInfoTitleStyle"
            android:text="@string/site_maps"
            android:visibility="gone" />
    </LinearLayout>

    <include
        layout="@layout/layout_saparator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/normal_distance"
        android:layout_marginBottom="@dimen/normal_distance" />

</LinearLayout>

