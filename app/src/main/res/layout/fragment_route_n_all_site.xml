<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/coOrdSite"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/setting_activity_background">

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/shape_border_edt_text"
        android:focusableInTouchMode="true"
        android:animateLayoutChanges="true"
        android:orientation="vertical">

        <include
            android:id="@+id/tlWorkProfile"
            layout="@layout/layout_toolbar_workprofile" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:orientation="horizontal"
            android:paddingStart="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_small">

        </LinearLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/vpFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>