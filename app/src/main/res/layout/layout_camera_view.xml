<?xml version="1.0" encoding="utf-8"?>
<com.otaliastudios.cameraview.CameraView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/camera"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:keepScreenOn="true"
    app:cameraAudio="off"
    app:cameraAutoFocusMarker="@string/cameraview_default_autofocus_marker"
    app:cameraEngine="camera1"
    app:cameraExperimental="true"
    app:cameraGestureLongTap="none"
    app:cameraGesturePinch="zoom"
    app:cameraGestureTap="autoFocus"
    app:cameraGrid="off"
    app:cameraWhiteBalance="auto"
    app:cameraHdr="off"
    app:cameraMode="picture"
    app:cameraPictureFormat="jpeg"
    app:cameraPictureSizeBiggest="false"
    app:cameraPictureSizeMaxHeight="@integer/camera_max_height"
    app:cameraPictureSizeMaxWidth="@integer/camera_max_width"
    app:cameraFrameProcessingMaxHeight="@integer/camera_max_height"
    app:cameraFrameProcessingMaxWidth="@integer/camera_max_width"
    app:cameraPlaySounds="true"
    app:cameraAutoFocusResetDelay="2000"
    app:cameraPreview="glSurface"
    app:cameraRequestPermissions="false"
    app:cameraUseDeviceOrientation="false" />
