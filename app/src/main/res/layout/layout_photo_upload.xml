<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llUploadPhoto"
    style="@style/layoutMainStyle"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tvUploadPhoto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_toStartOf="@+id/ivAddPhoto"
            android:fontFamily="@font/roboto_regular"
            android:gravity="center_vertical"
            android:padding="@dimen/padding_tiny"
            android:text="@string/upload_photo"
            android:textColor="@color/black_font"
            android:textSize="@dimen/text_size_xlarge" />

        <ImageView
            android:id="@+id/ivAddPhoto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_alignParentEnd="true"
            android:contentDescription="@string/app_name"
            android:padding="@dimen/padding_tiny"
            android:src="@drawable/ic_add_photo" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/llPhotos"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rlImages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>
</LinearLayout>
