<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">

    <include
        android:id="@+id/tlOther"
        layout="@layout/layout_toolbar_other" />

    <ScrollView
        android:id="@+id/cvScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/llRootLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtSettingActivityUser"
                style="@style/txtSettingTitleStyle"
                android:text="@string/user" />

            <LinearLayout
                android:id="@+id/linearSettingActivityEmailAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="1dp"
                android:background="@color/white_selector"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvMailAddress"
                    style="@style/txtSettingItemTitleStyle.camera"
                    android:layout_width="match_parent" />

                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <TextView
                    android:id="@+id/tvUserName"
                    style="@style/txtSettingItemTitleStyle.camera"
                    android:layout_width="match_parent" />

            </LinearLayout>

            <TextView
                android:id="@+id/txtSettingActivityCamera"
                style="@style/txtSettingTitleStyle"
                android:text="@string/camera" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="1dp"
                android:layout_marginTop="8dp"
                android:background="@color/white_selector"
                android:orientation="vertical">


                <LinearLayout
                    android:id="@+id/llTargetLocator"
                    style="@style/llSettingStyle">

                    <TextView
                        android:id="@+id/tvTargetLocator"
                        style="@style/txtSettingItemTitleStyle.camera"
                        android:text="@string/target_locator" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/swTargetLocator"
                        style="@style/switchSettingStyle" />

                </LinearLayout>

                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <LinearLayout
                    android:id="@+id/linearSettingActivitySaveToDevice"
                    style="@style/llSettingStyle">

                    <TextView
                        android:id="@+id/txtSettingActivitySaveToDevice"
                        style="@style/txtSettingItemTitleStyle.camera"
                        android:text="@string/saveToDevice" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/swSaveToDevice"
                        style="@style/switchSettingStyle" />

                </LinearLayout>


                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <LinearLayout
                    android:id="@+id/linearSettingActivityUsingCellularData"
                    style="@style/llSettingStyle">

                    <TextView
                        android:id="@+id/txtSettingActivityUsingCellularData"
                        style="@style/txtSettingItemTitleStyle.camera"
                        android:text="@string/usingCellularData" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/swUsingCellularData"
                        style="@style/switchSettingStyle" />
                </LinearLayout>

                <include layout="@layout/layout_setting_divider_horizontal_grey" />


                <LinearLayout
                    android:id="@+id/linearSettingActivityUploadOriginalSize"
                    style="@style/llSettingStyle">

                    <TextView
                        android:id="@+id/txtSettingActivityUploadOriginalSize"
                        style="@style/txtSettingItemTitleStyle.camera"
                        android:text="@string/upload_Original_Size" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/swUploadOriginalSize"
                        style="@style/switchSettingStyle" />

                </LinearLayout>


            </LinearLayout>


            <TextView
                android:id="@+id/txtSettingActivityGeneral"
                style="@style/txtSettingTitleStyle"
                android:text="@string/general" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="1dp"
                android:layout_marginTop="8dp"
                android:background="@color/white_selector"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/ivLanguage"
                    style="@style/txtSettingItemTitleStyle.general"
                    android:text="@string/change_lang"
                    app:drawableEndCompat="@drawable/ic_arrow_forward" />

                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <TextView
                    android:id="@+id/ivMeasurementUnit"
                    style="@style/txtSettingItemTitleStyle.general"
                    android:text="@string/unit_of_measurement"
                    app:drawableEndCompat="@drawable/ic_arrow_forward" />

                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <TextView
                    android:id="@+id/txtTermsOfService"
                    style="@style/txtSettingItemTitleStyle.general"
                    android:text="@string/terms_Of_Service"
                    app:drawableEndCompat="@drawable/ic_arrow_forward" />

                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <TextView
                    android:id="@+id/txtPrivacyPolicy"
                    style="@style/txtSettingItemTitleStyle.general"
                    android:text="@string/privacy_policy"
                    app:drawableEndCompat="@drawable/ic_arrow_forward" />

                <include layout="@layout/layout_setting_divider_horizontal_grey" />

                <TextView
                    android:id="@+id/txtLogOut"
                    style="@style/txtSettingItemTitleStyle.general"
                    android:text="@string/log_Out" />

            </LinearLayout>

            <TextView
                android:id="@+id/txtAppVersion"
                style="@style/txtSettingTitleStyle"
                android:layout_marginTop="@dimen/padding_xlarge"
                android:layout_marginBottom="@dimen/padding_xlarge"
                android:text="@string/app_version"
                android:textAllCaps="false" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>