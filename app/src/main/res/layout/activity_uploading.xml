<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/tlMain"
        layout="@layout/layout_toolbar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="50"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/padding_tiny"
            android:layout_marginStart="@dimen/padding_tiny"
            android:layout_marginTop="@dimen/padding_tiny"
            android:fontFamily="@font/roboto_medium"
            android:paddingEnd="@dimen/padding_tiny"
            android:paddingStart="@dimen/padding_tiny"
            android:text="@string/image_uploads"
            android:textColor="@color/colorPrimary"

            android:textSize="@dimen/text_size_xxlarge" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tvUploadImageError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:padding="16dp"
                android:textColor="@color/colorPrimary"
                android:textSize="20sp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvUploadImage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </RelativeLayout>
    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_weight="50"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/padding_tiny"
            android:layout_marginStart="@dimen/padding_tiny"
            android:layout_marginTop="@dimen/padding_tiny"
            android:fontFamily="@font/roboto_medium"
            android:paddingEnd="@dimen/padding_tiny"
            android:paddingStart="@dimen/padding_tiny"
            android:text="@string/data_uploads"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_xxlarge" />


        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <TextView
                android:id="@+id/tvUploadOtherDataErrorMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:gravity="center"
                android:padding="16dp"
                android:textColor="@color/colorPrimary"
                android:textSize="20sp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvUploadOtherData"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

        </RelativeLayout>
    </LinearLayout>


</LinearLayout>