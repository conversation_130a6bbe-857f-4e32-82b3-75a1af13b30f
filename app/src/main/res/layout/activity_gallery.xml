<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/clMain"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"
            android:orientation="vertical">

            <RelativeLayout
                android:id="@+id/rlToolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@color/colorPrimary">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivBack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/padding_medium"
                    android:padding="@dimen/padding_small"
                    app:srcCompat="@drawable/ic_arrow_back" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/tvToolbarTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"

                    android:text="@string/gallery" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCount"
                    style="@style/galleryTextStyle"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/padding_medium"
                    android:layout_marginEnd="@dimen/padding_medium"
                    android:layout_toStartOf="@+id/ivDone"
                    android:fontFamily="@font/roboto_bold"
                    android:padding="@dimen/padding_small"
                    android:textColor="@color/white" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivDone"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="@dimen/padding_medium"
                    android:fontFamily="@font/roboto_bold"
                    android:padding="@dimen/padding_small"
                    android:text="@string/done"
                    android:textColor="@color/white"
                    app:srcCompat="@drawable/ic_tick_gallery_gray" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.sitefotos.util.views.CustomRecyclerView
                    android:id="@+id/rvFolderView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_medium"
                    android:background="@color/transparent"
                    android:paddingStart="@dimen/padding_small_tiny"
                    android:paddingEnd="@dimen/padding_small_tiny"
                    android:paddingBottom="@dimen/microPadding"
                    app:list_orientation="horizontal"
                    app:list_type="list" />
            </LinearLayout>

            <com.sitefotos.util.views.CustomRecyclerView
                android:id="@+id/rvImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:paddingStart="@dimen/padding_small_tiny"
                android:paddingTop="@dimen/padding_small"
                android:paddingEnd="@dimen/padding_small_tiny"
                android:paddingBottom="@dimen/padding_medium"
                app:gird_span="4"
                app:list_type="grid" />
        </LinearLayout>
        <ProgressBar
            android:id="@+id/pbProgress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:visibility="gone" />

    </RelativeLayout>
</RelativeLayout>
