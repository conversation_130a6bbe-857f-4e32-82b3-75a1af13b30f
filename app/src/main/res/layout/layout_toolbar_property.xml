<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tlGeneral"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/colorAccent"
    app:contentInsetLeft="0dp"
    app:contentInsetStart="0dp"
    app:layout_collapseMode="pin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="1">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnBack"
            style="@style/btnToolbarBack"
            android:tint="@color/white" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:padding="@dimen/padding_small_tiny"
            android:layout_gravity="center"
            android:textSize="@dimen/text_size_xlarge"
            android:fontFamily="@font/roboto_regular"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:layout_marginStart="@dimen/padding_medium"
            android:background="@drawable/shape_button_background"
            android:layout_marginEnd="@dimen/padding_medium_large"
            android:layout_weight="1" />


    </LinearLayout>

</androidx.appcompat.widget.Toolbar>


