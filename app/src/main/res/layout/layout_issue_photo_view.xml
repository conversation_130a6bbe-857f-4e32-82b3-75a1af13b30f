<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llUploadPhoto"
    style="@style/layoutMainStyle"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="0dp"
        android:minWidth="0dp"
        android:minHeight="0dp"
        android:orientation="horizontal"
        android:padding="0dp"
        tools:targetApi="o">


        <com.sitefotos.util.views.CustomEditText
            android:id="@+id/edtText"
            style="@style/RightViewStyle"
            android:layout_toStartOf="@+id/ivAddPhoto"
            android:hint="@string/issue_detail"
            android:imeOptions="actionDone"
            android:inputType="text"
            android:importantForAutofill="no"
            android:padding="@dimen/microPadding" />

        <ImageView
            android:id="@+id/ivAddPhoto"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:contentDescription="@string/app_name"
            android:padding="@dimen/microPadding"
            app:srcCompat="@drawable/ic_add_photo" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/llPhotos"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rlImages"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/llButtonView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRemove"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/padding_medium"
            android:padding="@dimen/padding_small"
            android:text="@string/remove"
            android:textColor="@color/colorAccent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSubmit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            android:text="@string/submit"
            android:textColor="@color/colorAccent" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/grey_actionbar_bg" />
</LinearLayout>
