<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rlCameraPreview"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="visible">

    <RelativeLayout
        android:id="@+id/rlPreviewTopView"
        style="@style/StyleCameraTopView"
        android:animateLayoutChanges="false"
        android:padding="0dp"
        android:visibility="visible">

        <RelativeLayout
            android:id="@+id/rlCameraTop"
            style="@style/StyleCameraTopView"
            android:animateLayoutChanges="false"
            android:padding="0dp"
            android:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnDelete"
                style="@style/CameraButtonStyle"
                android:layout_alignParentStart="true"
                android:src="@drawable/ic_delete"
                android:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPropertyName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/padding_tiny"
                android:layout_toStartOf="@+id/imgBtnText"
                android:layout_toEndOf="@+id/imgBtnDelete"
                android:gravity="center"
                android:minHeight="@dimen/spinner_minimum_height"
                android:paddingLeft="@dimen/padding_medium"
                android:paddingEnd="30dp"
                android:paddingRight="@dimen/padding_medium"
                android:singleLine="true"
                android:textColor="@color/colorPrimary"
                android:textSize="21sp" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgBtnRefresh"
                style="@style/CameraButtonStyle"
                android:layout_toStartOf="@+id/imgBtnText"
                android:minWidth="30dp"
                android:minHeight="30dp"
                android:paddingStart="@dimen/microPadding"
                android:paddingEnd="@dimen/microPadding"
                android:src="@drawable/ic_refresh"
                android:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnText"
                style="@style/CameraButtonStyle"
                android:layout_toStartOf="@+id/imgBtnEdit"
                android:paddingStart="@dimen/padding_small_tiny"
                android:paddingEnd="@dimen/padding_small_tiny"
                android:src="@drawable/ic_text"
                android:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnEdit"
                style="@style/CameraButtonStyle"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_pencil"
                android:visibility="visible" />
        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rlTagTextView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@+id/rlCameraTop"
            android:layout_alignParentEnd="true"
            android:visibility="visible">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnTextTag"
                style="@style/CameraButtonStyle"
                android:paddingStart="@dimen/padding_small_tiny"
                android:paddingEnd="@dimen/padding_small_tiny"
                android:src="@drawable/ic_text"
                android:visibility="visible" />

            <RelativeLayout
                android:id="@+id/rlTags"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/imgBtnTextTag"
                android:visibility="visible">


                <androidx.appcompat.widget.AppCompatImageButton
                    android:id="@+id/imgBtnTags"
                    style="@style/CameraButtonStyle"
                    android:layout_width="wrap_content"
                    android:paddingStart="@dimen/padding_small_tiny"
                    android:paddingTop="@dimen/microPadding"
                    android:paddingEnd="@dimen/padding_small_tiny"
                    android:visibility="visible"
                    app:srcCompat="@drawable/ic_tag" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTagCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/microPadding"
                    android:layout_marginEnd="@dimen/padding_xxlarge"
                    android:background="@drawable/drawable_circle_selected"
                    android:gravity="center"
                    android:textColor="@color/white"
                    android:visibility="visible" />
            </RelativeLayout>
        </RelativeLayout>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/llTextCaptionView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/rlEditCapturedImageDone"
        android:layout_marginBottom="@dimen/padding_small"
        android:background="@color/transparent_30"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/padding_small"
        android:visibility="visible">


        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edtTextCaption"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="@string/enter_caption"
            android:padding="@dimen/padding_small"
            android:textColorHint="@color/grey_label_font"
            android:textColor="@color/white_selector"
            android:textSize="@dimen/text_size_xlarge"
            android:visibility="visible"
            tools:ignore="SpeakableTextPresentCheck,TouchTargetSizeCheck" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCaptionDone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center"
            android:padding="@dimen/padding_small"
            android:text="@string/done"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_large" />

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/rlEditCapturedImageDone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:gravity="bottom">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnPreviewDone"
            style="@style/CameraButtonStyle"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/padding_medium"
            android:layout_marginBottom="@dimen/padding_medium"
            android:src="@drawable/ic_image_done"
            android:visibility="visible" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnWeather"
            style="@style/CameraButtonStyle"
            android:layout_alignBottom="@+id/imgBtnPreviewDone"
            android:layout_alignParentStart="true"
            android:src="@drawable/selector_weather_icon"
            android:visibility="visible" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnShare"
            style="@style/CameraButtonStyle"
            android:layout_alignBottom="@+id/imgBtnPreviewDone"
            android:layout_alignParentEnd="true"
            android:src="@drawable/selector_share_icon_orange"
            android:visibility="visible" />
    </RelativeLayout>
</RelativeLayout>

