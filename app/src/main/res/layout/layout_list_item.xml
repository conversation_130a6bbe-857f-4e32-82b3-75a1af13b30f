<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txtListItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:background="@drawable/shape_property_dialogue_btn_background"
        android:gravity="center"
        android:padding="@dimen/padding_small_tiny"
        android:textAllCaps="false"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_size_xlarge"
        android:textStyle="bold" />

    <include layout="@layout/layout_divider_horizontal_grey"/>

</LinearLayout>