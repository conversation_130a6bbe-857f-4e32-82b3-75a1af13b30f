<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_notes"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:paddingTop="@dimen/padding_large">

    <ImageView
        android:id="@+id/ivClear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_medium"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/padding_tiny"
        android:src="@drawable/ic_delete" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivUndo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_marginEnd="@dimen/padding_medium"
        android:layout_toStartOf="@+id/verticalColorPicker"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/padding_tiny"
        android:src="@drawable/ic_undo_white"
        android:tint="@color/colorPrimary"
        android:visibility="gone" />

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/ivClear"
        android:layout_centerInParent="true"
        app:cardElevation="0dp">


        <com.sitefotos.util.views.DrawingView
            android:id="@+id/drawing"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white" />
    </androidx.cardview.widget.CardView>


    <ImageView
        android:id="@+id/ivImageDone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/padding_medium"
        android:layout_marginStart="@dimen/padding_medium"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/padding_tiny"
        android:src="@drawable/ic_image_save" />

    <com.sitefotos.util.views.VerticalColorPicker
        android:id="@+id/verticalColorPicker"
        android:layout_width="35dp"
        android:layout_height="250dp"
        android:layout_below="@+id/ivImageDone"
        android:layout_marginTop="@dimen/padding_small_tiny"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/padding_medium" />

</RelativeLayout>


