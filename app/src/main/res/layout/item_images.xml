<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llMain"
    android:layout_width="@dimen/image_height_width"
    android:layout_height="@dimen/image_height_width"
    android:layout_margin="@dimen/padding_tiny"
    app:cardCornerRadius="@dimen/padding_large">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMainImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivDelete"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/padding_small"
            android:layout_marginTop="@dimen/padding_small"
            android:src="@drawable/ic_delete" />
    </RelativeLayout>
</androidx.cardview.widget.CardView>
