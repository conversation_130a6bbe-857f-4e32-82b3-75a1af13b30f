<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llAlertDialog"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:padding="@dimen/padding_medium"
        android:text="@string/app_name"
        android:textColor="@color/black_font"
        android:textSize="@dimen/text_size_xxxlarge"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/btnClear"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_medium"
        android:layout_marginEnd="@dimen/padding_medium"
        android:padding="@dimen/padding_small"
        android:background="?attr/selectableItemBackground"
        android:text="@string/clear"
        android:textSize="@dimen/text_size_xlarge" />

    <TextView
        android:id="@+id/btnDelete"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_large"
        android:layout_marginStart="@dimen/padding_medium"
        android:padding="@dimen/padding_small"
        android:layout_marginEnd="@dimen/padding_medium"
        android:text="@string/delete"
        android:background="?attr/selectableItemBackground"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_size_xlarge" />
</LinearLayout>
