<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:wheel="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coOrdSite"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/setting_activity_background">

    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="true"
        android:background="@color/shape_border_edt_text"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <include
            android:id="@+id/tlWorkProfile"
            layout="@layout/layout_toolbar_workprofile" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRouteName"
            style="@style/tvToolbarTitle"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:gravity="start"
            android:padding="@dimen/padding_small"
            android:paddingStart="@dimen/padding_xlarge"
            android:paddingEnd="@dimen/padding_medium"
            android:text="@string/sites"
            android:textSize="@dimen/text_size_largest"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:orientation="horizontal"
            android:paddingStart="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_small">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edtSearch"
                style="@style/searchEdtStyle"
                android:hint="@string/search_sites" />
        </LinearLayout>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/vpFragment"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <RelativeLayout
                android:id="@+id/llProgress"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <com.pnikosis.materialishprogress.ProgressWheel
                    android:id="@+id/pwLoader"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_centerInParent="true"
                    android:visibility="gone"
                    wheel:matProg_barColor="@color/colorPrimary"
                    wheel:matProg_progressIndeterminate="true" />

            </RelativeLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>