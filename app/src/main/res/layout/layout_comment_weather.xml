<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/layoutMainStyle"
    android:layout_marginStart="@dimen/microPadding"
    android:layout_marginEnd="@dimen/microPadding"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tvCommentTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/roboto_regular"
            android:textColor="@color/black_font"
            android:textSize="@dimen/text_size_xlarge" />

        <ImageView
            android:id="@+id/ivWeather"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="@dimen/padding_tiny"
            android:layout_marginEnd="@dimen/padding_small"
            android:layout_marginBottom="@dimen/padding_tiny"
            android:src="@drawable/selector_weather_icon" />
    </RelativeLayout>

    <com.sitefotos.util.views.CustomEditText
        android:id="@+id/edtComment"
        style="@style/LeftViewStyle"
        android:layout_marginTop="@dimen/microPadding"
        android:background="@drawable/drawable_lines_sign"
        android:gravity="top"
        android:lines="5"
        android:maxLines="5"
        android:minHeight="@dimen/minimum_hight_comment"
        android:overScrollMode="always"
        android:padding="@dimen/padding_tiny"
        android:scrollbarStyle="insideInset"
        android:scrollbars="vertical"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_size_xlarge" />

</LinearLayout>
