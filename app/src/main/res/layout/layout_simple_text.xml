<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llNote"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:focusableInTouchMode="true"
    android:nestedScrollingEnabled="true"
    >

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvText"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:padding="@dimen/padding_tiny"
        android:autoLink="phone|web|email|map"
        android:linksClickable="true"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_size_xlarge" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/grey_actionbar_bg" />

</LinearLayout>


