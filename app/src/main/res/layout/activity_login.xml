<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llMain"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusableInTouchMode="true">


    <ImageView
        android:id="@+id/imgHeader"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@+id/llLoginCenter"
        android:layout_centerInParent="true"
        android:layout_marginBottom="120dp"
        android:contentDescription="@string/app_name"
        android:scaleType="centerInside"
        android:src="@drawable/ic_splash" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLanguage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/padding_small"
        android:layout_marginEnd="@dimen/padding_small"
        android:gravity="center_vertical"
        android:padding="8dp"
        app:srcCompat="@drawable/ic_language" />

    <LinearLayout
        android:id="@+id/llLoginCenter"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical"
        android:paddingLeft="30dp"
        android:paddingRight="30dp">

        <com.sitefotos.util.views.CustomEditText
            android:id="@+id/edtEmailAddress"
            style="@style/loginEditText"
            android:layout_width="match_parent"
            android:autofillHints="emailAddress"
            android:hint="@string/email_address"
            android:inputType="textEmailAddress"
            android:minHeight="48dp" />

        <com.sitefotos.util.views.CustomEditText
            android:id="@+id/edtCompanyId"
            style="@style/loginEditText"
            android:layout_width="match_parent"
            android:layout_marginTop="20dp"
            android:hint="@string/company_id"
            android:imeActionId="6"
            android:minHeight="48dp" />

        <LinearLayout
            android:id="@+id/llBaseUrl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="20dp"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:visibility="gone"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edtChangeUrl"
                style="@style/baseUrlEdtStyle" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnSave"
                style="@style/baseUrlButtonStyle"
                android:layout_width="@dimen/base_url_button_width"
                android:layout_height="@dimen/padding_xxxlarge"
                android:layout_marginStart="8dp"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="@dimen/padding_small"
                android:padding="@dimen/elevation_tiny"
                android:text="@string/save"
                android:textSize="16sp" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/llLoginCenter"
        android:orientation="vertical">

        <Button
            android:id="@+id/btnLogin"
            style="@style/loginButton"
            android:layout_marginLeft="50dp"
            android:layout_marginRight="50dp"
            android:text="@string/setup_site_photoes" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:gravity="center_horizontal"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTermsOfService"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:padding="4dp"
                android:text="@string/terms_of_service"
                android:textColor="@color/colorPrimary"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/tvPrivacyPolicy"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:padding="4dp"
                android:text="@string/privacy_policy"
                android:textColor="@color/colorPrimary"
                android:textSize="12sp" />
        </LinearLayout>


    </LinearLayout>

</RelativeLayout>
