<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rlSegment"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/microPadding"
    android:layout_marginEnd="@dimen/microPadding"
    android:layout_marginTop="@dimen/padding_small">

    <LinearLayout
        android:id="@+id/llSegmentView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_tiny"
        android:layout_marginEnd="@dimen/padding_tiny"
        android:layout_marginBottom="@dimen/padding_tiny"
        android:background="@drawable/segment_background"
        android:orientation="horizontal">

    </LinearLayout>

</RelativeLayout>