<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llAlertDialog"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:cardCornerRadius="@dimen/padding_small"
        app:cardUseCompatPadding="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/padding_tiny">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@mipmap/ic_launcher" />

            <TextView
                android:id="@+id/tvAppName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/padding_tiny"
                android:text="@string/app_name"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge" />

            <TextView
                android:id="@+id/tvText"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/padding_tiny"
                android:text="@string/are_you_sure_you_want_to_delete_this_view"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_large" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:layout_marginTop="@dimen/padding_large"
                android:gravity="end|center_vertical">

                <TextView
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/padding_small"
                    android:text="@string/txt_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/text_size_large" />

                <View
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:layout_marginBottom="@dimen/padding_small"
                    android:layout_marginTop="@dimen/padding_small"
                    android:background="@color/black"
                    android:padding="@dimen/padding_tiny" />

                <TextView
                    android:id="@+id/tvDelete"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/padding_medium"
                    android:text="@string/delete"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_size_large"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>
