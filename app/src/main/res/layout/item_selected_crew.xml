<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/padding_small"
    android:layout_marginEnd="@dimen/padding_small"

    android:background="@drawable/drawable_white_filled_corner_radius"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/microPadding"
        android:paddingBottom="@dimen/microPadding"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivCrewSelection"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:padding="@dimen/padding_small_tiny"
            app:srcCompat="@drawable/ic_checkbox_selected_blue" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvExtendedCrew"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/padding_medium_small"
            android:fontFamily="@font/roboto_medium"
            android:layoutDirection="rtl"
            android:paddingTop="@dimen/padding_small_tiny"
            android:paddingBottom="@dimen/padding_small_tiny"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />
    </LinearLayout>

    <include layout="@layout/layout_divider_item_grey" />
</LinearLayout>
