<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_tiny"
        android:layout_marginEnd="@dimen/padding_tiny"
        android:layout_marginTop="@dimen/padding_tiny"

        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvStartService"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/background_button_selected_orange"
            android:fontFamily="@font/roboto_medium"
            android:paddingStart="@dimen/padding_medium"
            android:paddingTop="@dimen/padding_small_tiny"
            android:paddingEnd="@dimen/padding_medium"
            android:paddingBottom="@dimen/padding_small_tiny"
            android:text="@string/start"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_xlarge" />


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvServiceName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:padding="@dimen/padding_small_tiny"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />

        <Chronometer
            android:id="@+id/cmTotalTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/microPadding"
            android:paddingTop="@dimen/padding_small_tiny"
            android:paddingEnd="@dimen/padding_small_tiny"
            android:paddingBottom="@dimen/padding_small_tiny"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llCurrentTime"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="visible">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/padding_small_tiny"
            android:paddingTop="@dimen/microPadding"
            android:paddingEnd="@dimen/microPadding"
            android:paddingBottom="@dimen/microPadding"
            android:text="@string/currentTime"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />

        <Chronometer
            android:id="@+id/cmCurrentTime"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/microPadding"
            android:paddingTop="@dimen/microPadding"
            android:paddingEnd="@dimen/padding_small_tiny"
            android:paddingBottom="@dimen/microPadding"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />

    </LinearLayout>

</LinearLayout>