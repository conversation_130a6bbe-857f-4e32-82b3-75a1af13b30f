<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:wheel="http://schemas.android.com/apk/res-auto"
    android:id="@+id/coOrdSite"
    android:layout_width="match_parent"
    android:background="@color/setting_activity_background"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/flFragment"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <RelativeLayout
        android:id="@+id/llProgress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.pnikosis.materialishprogress.ProgressWheel
            android:id="@+id/progressBar"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_centerInParent="true"
            android:visibility="gone"
            wheel:matProg_barColor="@color/colorPrimary"
            wheel:matProg_progressIndeterminate="true" />

    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>