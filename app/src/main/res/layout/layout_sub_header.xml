<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/layoutMainStyle"
    android:background="@color/colorPrimary"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvSubHeader"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="@dimen/microPadding"
        android:layout_marginTop="@dimen/microPadding"
        android:fontFamily="@font/roboto_regular"
        android:paddingEnd="@dimen/padding_tiny"
        android:paddingStart="@dimen/padding_tiny"
        android:textColor="@color/white"
        android:textSize="@dimen/text_size_xlarge" />

</LinearLayout>
