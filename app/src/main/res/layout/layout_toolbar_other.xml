<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tlGeneral"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/colorAccent"
    app:contentInsetLeft="0dp"
    app:contentInsetStart="0dp"
    app:layout_collapseMode="pin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="1">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnBack"
            style="@style/btnToolbarBack"
            android:tint="@color/white" />


        <LinearLayout
            android:id="@+id/llTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/padding_tiny"
            android:layout_weight="1"
            android:gravity="center"
            android:layout_gravity="center"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMainTitle"
                style="@style/tvToolbarTitle"
                android:layout_gravity="center"
                android:gravity="center"
                android:textSize="@dimen/text_size_medium_large"
                android:visibility="gone"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                style="@style/tvToolbarTitle"
                android:layout_gravity="center"
                android:gravity="center"
                android:text="@string/app_name"/>
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSubmit"
            style="@style/tvToolbarSubTitle"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="@string/submit" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivThirdRight"
            style="@style/ivToolbarIcon"
            android:layout_weight="1"
            android:src="@drawable/ic_settings_camera_activity" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivPlotMap"
            style="@style/ivToolbarIcon"
            android:visibility="gone"
            android:layout_weight="1"
            android:src="@drawable/icn_map_pins" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivFormDetail"
            style="@style/ivToolbarIcon"
            android:visibility="gone"
            android:layout_weight="1"
            app:tint="@color/white"
            android:src="@drawable/ic_form" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivSecondRight"
            android:visibility="gone"
            style="@style/ivToolbarIcon"
            android:layout_height="match_parent"
            app:srcCompat="@drawable/ic_upload_white"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivRight"
            style="@style/ivToolbarIcon"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/padding_small"
            android:layout_weight="1"
            android:src="@drawable/ic_settings_camera_activity"
            android:tint="@color/white"
            android:visibility="gone" />
    </LinearLayout>

</androidx.appcompat.widget.Toolbar>


