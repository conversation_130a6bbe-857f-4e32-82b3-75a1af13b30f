<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:wheel="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/setting_activity_background"
    tools:context=".site.detail.SiteFormFragment">

    <include
        android:id="@+id/tlOther"
        layout="@layout/layout_toolbar_other" />


    <com.pnikosis.materialishprogress.ProgressWheel
        android:id="@+id/progressBar"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_gravity="center"
        android:visibility="gone"
        wheel:matProg_barColor="@color/colorPrimary"
        wheel:matProg_progressIndeterminate="true" />

    <com.sitefotos.util.views.CustomRecyclerView
        android:id="@+id/rvSiteForms"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tlOther"
        android:background="@color/white"
        android:paddingStart="@dimen/padding_medium"
        app:list_type="list" />

    <include
        android:id="@+id/emptyView"
        layout="@layout/layout_recycler_emptyview" />

</RelativeLayout>