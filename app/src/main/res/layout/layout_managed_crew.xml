<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llManagedCrew"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/llAddSubForm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_regular"
                android:gravity="center_vertical"
                android:padding="@dimen/padding_small_tiny"
                android:paddingStart="@dimen/microPadding"
                android:paddingEnd="@dimen/microPadding"
                android:textColor="@color/black_font"
                android:textSize="@dimen/text_size_xlarge" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivAddSubForm"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/microPadding"
                android:padding="@dimen/padding_tiny"
                android:src="@drawable/ic_add_new_property" />

        </LinearLayout>

        <include
            android:id="@+id/llSeparator"
            layout="@layout/layout_saparator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_small"
            android:layout_marginBottom="@dimen/padding_tiny" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvSubForm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:layout_marginStart="@dimen/padding_tiny"
            android:layout_marginEnd="@dimen/padding_tiny"
            android:orientation="vertical"/>

    </LinearLayout>


</LinearLayout>
