<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/layout_toolbar"
        layout="@layout/layout_toolbar_other" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/MyProgressBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="8dip"
        android:minHeight="5dip"
        android:visibility="visible"/>
    <FrameLayout
        android:id="@+id/fullscreen_custom_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimaryDark"
        android:visibility="gone" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/error_console"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

        <FrameLayout
            android:id="@+id/main_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>

</LinearLayout>