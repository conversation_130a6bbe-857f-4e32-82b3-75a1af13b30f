<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="0dp"
    android:layout_marginVertical="0dp"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:padding="@dimen/padding_tiny"
    tools:targetApi="o">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="0dp"
        android:layout_marginVertical="0dp"
        android:orientation="horizontal"
        android:weightSum="100"
        tools:targetApi="o">

        <TextView
            android:id="@+id/tvMaterialName"
            style="@style/materialItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="52"
            android:textSize="@dimen/text_size_xlarge"
            android:padding="@dimen/padding_tiny"
            android:text="" />

        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/padding_tiny"
            android:background="@color/grey_actionbar_bg" />

        <TextView
            android:id="@+id/tvUnit"
            style="@style/materialItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="24"
            android:padding="@dimen/padding_tiny"
            android:textSize="@dimen/text_size_xlarge"
            android:singleLine="true"
            android:paddingStart="@dimen/padding_small"/>


        <View
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/grey_actionbar_bg" />

        <com.sitefotos.util.views.CustomEditText
            android:id="@+id/edtQty"
            style="@style/materialItemTextStyle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="24"
            android:background="@drawable/shape_white_bg_grey_border_corner"
            android:gravity="center"
            android:layout_marginStart="@dimen/padding_tiny"
            android:hint="@string/value"
            android:textSize="@dimen/text_size_xlarge"
            android:inputType="numberDecimal"
            android:padding="@dimen/padding_tiny" />
    </LinearLayout>

    <TextView
        android:id="@+id/tvMaterialTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/microPadding"
        android:fontFamily="@font/roboto_medium"
        android:textColor="@color/black_font"
        android:textSize="@dimen/text_size_xlarge"
        android:visibility="gone" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="@dimen/microPadding"
        android:layout_marginTop="@dimen/microPadding"
        android:background="@color/grey_actionbar_bg"
        android:visibility="visible" />
</LinearLayout>