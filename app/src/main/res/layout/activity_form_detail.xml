<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llMain"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">

    <include
        android:id="@+id/tlOther"
        layout="@layout/layout_toolbar_other" />

    <RelativeLayout
        android:id="@+id/rlFormDetails"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible">

        <com.sitefotos.util.views.CustomLinearLayout
            android:id="@+id/llPagerMain"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@+id/viewSeparator"
            android:layout_alignParentTop="true"
            android:orientation="vertical">

            <ViewFlipper
                android:id="@+id/vfPages"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/setting_activity_background">

            </ViewFlipper>
        </com.sitefotos.util.views.CustomLinearLayout>

        <View
            android:id="@+id/viewSeparator"
            android:layout_width="match_parent"
            android:layout_height="1px"
            android:layout_above="@+id/rlBottom"
            android:background="@color/grey_label_font" />

        <RelativeLayout
            android:id="@+id/rlBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/dashboard_background"
            android:visibility="gone">


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnPrevious"
                style="@style/btnOrangeBG"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/padding_small"
                android:minWidth="80dp"
                android:text="@string/previous" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPageNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:fontFamily="@font/roboto_light"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_medium_large" />


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnCheckIn"
                style="@style/btnOrangeBG"
                android:layout_centerInParent="true"
                android:minWidth="80dp"
                android:text="@string/check_in"
                android:visibility="gone" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnNext"
                style="@style/btnOrangeBG"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/padding_small"
                android:minWidth="80dp"
                android:text="@string/next" />

        </RelativeLayout>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rlMap"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:visibility="gone">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/plotOnMap"
            android:name="com.google.android.gms.maps.SupportMapFragment"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/rlMapBottom"
            android:layout_gravity="center" />

        <RelativeLayout
            android:id="@+id/rlMapBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/dashboard_background"
            android:layout_alignParentBottom="true"
            android:minWidth="80dp"
            android:visibility="visible">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnMapCheckIn"
                style="@style/btnOrangeBG"
                android:layout_centerInParent="true"
                android:text="@string/check_in"
                android:minWidth="80dp"
                android:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivCamera"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/padding_largest"
                android:layout_alignParentEnd="true"
                android:padding="@dimen/padding_tiny"
                android:layout_marginEnd="@dimen/padding_small"
                android:src="@drawable/icn_form_camera"
                android:visibility="visible" />
        </RelativeLayout>
    </RelativeLayout>
</LinearLayout>
