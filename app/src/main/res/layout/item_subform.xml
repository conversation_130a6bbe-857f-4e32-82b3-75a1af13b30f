<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/transparent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCrewName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_regular"
        android:paddingStart="@dimen/padding_large"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_large"
        android:paddingBottom="@dimen/padding_small"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_large"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/ivArrow"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvServiceName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_regular"
        android:paddingStart="@dimen/padding_large"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_large"
        android:paddingBottom="@dimen/padding_small"
        android:singleLine="true"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_large"
        app:layout_constraintEnd_toStartOf="@+id/cmTime"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvCrewName" />


    <Chronometer
        android:id="@+id/cmTime"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fontFamily="@font/roboto_regular"
        android:paddingStart="@dimen/padding_large"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_large"
        android:paddingBottom="@dimen/padding_small"
        android:singleLine="true"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_large"
        app:layout_constraintBottom_toBottomOf="@+id/tvServiceName"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintEnd_toStartOf="@+id/ivArrow"
        app:layout_constraintStart_toEndOf="@+id/tvServiceName"
        app:layout_constraintTop_toTopOf="@+id/tvServiceName" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:paddingStart="@dimen/padding_medium"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium"
        android:paddingBottom="@dimen/padding_small"
        android:src="@drawable/ic_forward"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvCrewName"
        app:layout_constraintBottom_toBottomOf="@+id/tvServiceName"/>

</androidx.constraintlayout.widget.ConstraintLayout>