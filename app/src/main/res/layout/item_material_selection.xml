<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/drawable_white_filled_corner_radius"
    android:layout_marginTop="1dp"
    android:layout_marginStart="@dimen/padding_small"
    android:layout_marginEnd="@dimen/padding_small"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatCheckBox
        android:clickable="false"
        android:layout_marginStart="@dimen/padding_small"
        android:id="@+id/cbMaterial"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:buttonTint="@color/app_blue"
        android:layout_marginEnd="@dimen/padding_medium_small" />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_gravity="center"
        android:id="@+id/tvSelectedItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/padding_medium_small"
        android:paddingBottom="@dimen/padding_medium_small"
        android:fontFamily="@font/roboto_medium"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_xlarge" />
</LinearLayout>
