<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tlGeneral"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/colorAccent"
    app:contentInsetLeft="0dp"
    app:contentInsetStart="0dp"
    app:layout_collapseMode="pin">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            style="@style/tvToolbarTitle"
            android:layout_centerInParent="true"
            android:text="@string/time_clock" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivUpload"
                style="@style/ivToolbarIcon"
                android:layout_gravity="center_vertical"
                android:visibility="visible"
                app:srcCompat="@drawable/ic_upload_white" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnAddCrew"
                style="@style/buttonTopBarStyle"
                android:layout_height="match_parent"
                android:layout_gravity="center_vertical"
                android:ellipsize="end"
                android:maxWidth="130dp"
                android:minWidth="80dp"
                android:padding="@dimen/padding_tiny"
                android:paddingEnd="@dimen/padding_medium_small"
                android:singleLine="true"
                android:text="@string/add_crew"
                android:visibility="visible" />
        </LinearLayout>
    </RelativeLayout>

</androidx.appcompat.widget.Toolbar>


