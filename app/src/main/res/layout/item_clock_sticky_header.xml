<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"

    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/setting_activity_background"
    android:clickable="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:padding="@dimen/padding_tiny"
    android:paddingStart="@dimen/padding_small_tiny"
    android:paddingEnd="@dimen/padding_tiny"
    android:paddingBottom="@dimen/padding_tiny">

    <RelativeLayout
        android:id="@+id/llRootLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/ivHeaderCrewSelect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/padding_medium_large"
            android:layout_marginEnd="@dimen/padding_medium_small"
            android:src="@drawable/ic_checkbox_unselected" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHeaderTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/btnBreakResume"
            android:layout_toEndOf="@+id/ivHeaderCrewSelect"
            android:fontFamily="@font/roboto_medium"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_xxlarge" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/btnBreakResume"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/padding_small"
            android:background="@drawable/transparent_background"
            android:fontFamily="@font/roboto_regular"
            android:paddingStart="@dimen/padding_large"
            android:paddingTop="@dimen/padding_small_tiny"
            android:paddingEnd="@dimen/padding_large"
            android:paddingBottom="@dimen/padding_small_tiny"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_large" />

    </RelativeLayout>
</RelativeLayout>