<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tlGeneral"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/colorAccent"
    app:contentInsetLeft="0dp"
    app:contentInsetStart="0dp"
    app:layout_collapseMode="pin">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:weightSum="1">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnBack"
            style="@style/btnToolbarBack"
            android:tint="@color/white" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            style="@style/tvToolbarTitle"
            android:text="@string/app_name"
            android:layout_marginEnd="@dimen/padding_tiny"
            android:layout_weight="1" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSubmit"
            style="@style/tvToolbarSubTitle"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:visibility="visible"
            android:text="@string/save" />

    </LinearLayout>

</androidx.appcompat.widget.Toolbar>


