<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llAlertDialog"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="vertical">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:padding="@dimen/padding_medium"
        android:text="@string/app_name"
        android:textColor="@color/black_font"
        android:textSize="@dimen/text_size_xxxlarge"
        android:textStyle="bold" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_small"
        android:gravity="center"
        android:padding="@dimen/padding_small"
        android:textSize="@dimen/text_size_large" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/padding_small"
        android:background="@color/grey_actionbar_bg"
        android:paddingBottom="@dimen/padding_tiny" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvCancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="center"
            android:padding="@dimen/padding_small"
            android:singleLine="true"
            android:text="@string/txt_cancel"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/text_size_xlarge" />


        <View
            android:id="@+id/viewCancelDivider"
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/grey_actionbar_bg"
            android:paddingTop="@dimen/padding_small"
            android:paddingBottom="@dimen/padding_tiny" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvClear"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/roboto_medium"
            android:gravity="center"
            android:padding="@dimen/padding_small"
            android:text="@string/yes"
            android:textColor="@color/colorAccent"
            android:textSize="@dimen/text_size_xlarge" />

    </LinearLayout>

</LinearLayout>
