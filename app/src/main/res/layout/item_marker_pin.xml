<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/padding_tiny">

    <androidx.appcompat.widget.AppCompatRadioButton
        android:id="@+id/btnMarker"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:buttonTint="@color/app_blue"
        android:fontFamily="@font/roboto_medium"
        android:padding="@dimen/padding_small"
        android:textSize="@dimen/text_size_large" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivMarker"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:scaleType="centerInside"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium_large"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true" />
</RelativeLayout>
