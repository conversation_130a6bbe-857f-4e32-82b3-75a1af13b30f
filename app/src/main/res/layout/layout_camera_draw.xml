<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/rlDrawView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:visibility="gone">

    <RelativeLayout
        android:id="@+id/rlDrawingTopView"
        style="@style/StyleCameraTopView"
        android:layout_alignParentTop="true"
        android:animateLayoutChanges="false"
        android:padding="@dimen/padding_small"
        android:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnDrawingDone"
            style="@style/DrawButtonStyle"
            android:layout_alignParentStart="true"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@drawable/ic_done"
            android:visibility="visible" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnDrawingUndo"
            style="@style/DrawButtonStyle"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/imgBtnEditDrawingToggle"
            android:padding="0dp"
            android:layout_marginStart="@dimen/padding_small"
            android:layout_marginTop="@dimen/padding_small"
            android:layout_marginBottom="@dimen/padding_small"
            android:layout_marginEnd="@dimen/padding_medium_large"
            android:src="@drawable/ic_undo_white"
            android:visibility="visible" />


        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnEditDrawingToggle"
            style="@style/DrawButtonStyle"
            android:layout_alignParentEnd="true"
            android:src="@drawable/ic_pencil_white"
            android:padding="0dp"
            android:layout_margin="@dimen/padding_small"
            android:visibility="visible" />
    </RelativeLayout>

    <com.sitefotos.util.views.VerticalColorPicker
        android:id="@+id/verticalColorPicker"
        android:layout_width="35dp"
        android:layout_height="250dp"
        android:layout_below="@+id/rlDrawingTopView"
        android:layout_alignParentEnd="true"
        android:layout_marginTop="@dimen/padding_medium"
        android:layout_marginEnd="@dimen/padding_medium" />

</RelativeLayout>

