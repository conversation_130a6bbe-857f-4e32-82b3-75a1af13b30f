<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="@dimen/padding_xxxlargest"
    android:layout_height="@dimen/padding_xxxlargest"
    android:background="@drawable/border_bubble_view">

    <ImageView
        android:id="@+id/ivMarkerDetails"
        android:layout_width="@dimen/padding_xxxlargest"
        android:layout_height="@dimen/padding_xxxlargest"
        android:src="@mipmap/ic_launcher"
        android:paddingTop="@dimen/padding_small"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_small"
        android:paddingBottom="@dimen/padding_medium"
        android:scaleType="fitXY"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>