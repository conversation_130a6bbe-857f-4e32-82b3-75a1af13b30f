<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/layoutMainStyle"
    android:orientation="vertical">


    <TextView
        android:id="@+id/tvGeoTitle"
        style="@style/LeftViewStyle"
        android:text="@string/location" />

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/microPadding">

        <TextView
            android:id="@+id/tvPropertyName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:background="@drawable/shape_transparent_bg_rounded_corner"
            android:ellipsize="end"
            android:gravity="center"
            android:paddingEnd="@dimen/padding_xxxlarge"
            android:paddingStart="@dimen/padding_small"
            android:singleLine="true"
            android:minHeight="@dimen/spinner_minimum_height"
            android:textColor="@color/white"
            android:textSize="19sp" />

        <ImageView
            android:id="@+id/ivRefresh"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:background="@null"
            android:contentDescription="@string/app_name"
            android:minHeight="@dimen/spinner_minimum_height"
            android:minWidth="@dimen/spinner_minimum_height"
            android:gravity="center"
            android:src="@drawable/ic_refresh"
            android:visibility="visible" />

    </RelativeLayout>

</LinearLayout>
