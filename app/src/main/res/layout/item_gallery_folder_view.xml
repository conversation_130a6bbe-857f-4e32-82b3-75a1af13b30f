<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llMain"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/margin_folderview"
    android:orientation="vertical"
    android:padding="1dp">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_tiny">

        <androidx.cardview.widget.CardView
            android:id="@+id/cvIcon"
            android:layout_width="@dimen/directory_view"
            android:layout_height="@dimen/directory_view"
            app:cardBackgroundColor="@color/black"
            app:cardCornerRadius="@dimen/directory_view_radius">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivGalleryPlaceholder"
                android:layout_width="@dimen/directory_view"
                android:layout_height="@dimen/directory_view"
                android:contentDescription="@string/app_name"
                android:scaleType="centerCrop" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivSelection"
                android:layout_width="@dimen/directory_view"
                android:layout_height="@dimen/directory_view"
                android:src="@drawable/drawable_folder_unselected" />
        </androidx.cardview.widget.CardView>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvImageName"
            android:layout_width="@dimen/directory_view"
            android:layout_height="wrap_content"
            android:layout_below="@+id/cvIcon"
            android:ellipsize="end"
            android:fontFamily="@font/roboto_regular"
            android:gravity="start"
            android:paddingStart="2dp"
            android:paddingEnd="2dp"
            android:singleLine="true"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_medium" />

    </RelativeLayout>

</LinearLayout>