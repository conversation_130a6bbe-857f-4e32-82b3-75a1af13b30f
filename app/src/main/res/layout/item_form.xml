<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFormCompleteStatus"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:clickable="false"
        android:visibility="gone"
        android:layout_gravity="center_vertical"
        android:padding="@dimen/padding_tiny"
        android:src="@drawable/ic_image_tick" />


    <TextView
        android:id="@+id/tvFormName"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:fontFamily="@font/roboto_medium"
        android:paddingStart="0dp"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium"
        android:paddingBottom="@dimen/padding_small"
        android:maxLines="3"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textSize="@dimen/text_size_large" />

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end|center_vertical"
        android:paddingStart="@dimen/padding_medium"
        android:paddingTop="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_medium"
        android:paddingBottom="@dimen/padding_small"
        android:src="@drawable/ic_forward" />

</LinearLayout>