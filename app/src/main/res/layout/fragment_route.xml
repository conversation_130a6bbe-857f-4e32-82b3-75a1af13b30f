<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_small">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edtSearch"
            style="@style/searchEdtStyle"
            android:hint="@string/search_routes" />
    </LinearLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/srlRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/llSearch">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/llWorkProgressView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone"
                android:layout_marginBottom="40dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="5dp"
                    android:fontFamily="@font/roboto_medium"
                    android:singleLine="true"
                    android:text="Work In Progress"
                    android:textColor="@color/gray_2"
                    android:textSize="@dimen/text_size_large" />

                <com.sitefotos.util.views.CustomRecyclerView
                    android:id="@+id/rvWorkingSites"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/padding_small"
                    app:list_type="list" />

            </LinearLayout>

            <com.sitefotos.util.views.CustomRecyclerView
                android:id="@+id/rvRoute"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_small"
                app:list_type="list" />

        </LinearLayout>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <include
        android:id="@+id/emptyView"
        layout="@layout/layout_recycler_emptyview" />
</RelativeLayout>
