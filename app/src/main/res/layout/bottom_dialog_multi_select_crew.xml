<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <RelativeLayout
        android:id="@+id/rlMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/llTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            android:background="@color/colorPrimary"
            android:orientation="vertical"

            android:paddingBottom="@dimen/padding_small">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingStart="@dimen/padding_small"
                android:paddingEnd="@dimen/padding_small">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@null"
                    android:fontFamily="@font/roboto_regular"
                    android:minWidth="0dp"
                    android:paddingStart="0dp"
                    android:paddingEnd="@dimen/padding_small"
                    android:text="@string/txt_cancel"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_xlarge" />


                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/padding_small"
                    android:fontFamily="@font/roboto_medium"
                    android:gravity="center_vertical"
                    android:padding="@dimen/padding_small"
                    android:text="@string/choose_crew_members"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_xlarge" />
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/edtSearch"
                style="@style/searchEdtStyle"
                android:hint="@string/search_crew" />

        </LinearLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvCrewMain"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/llTop"
            android:layout_marginStart="@dimen/padding_small"
            android:layout_marginTop="@dimen/microPadding"
            android:layout_marginEnd="@dimen/padding_small"
            android:background="@color/white"
            tools:listitem="@layout/item_employee" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_above="@+id/llBottom"
            android:background="@color/grey_actionbar_bg" />

        <RelativeLayout
            android:id="@+id/llBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:background="@color/setting_activity_background"
            android:gravity="center_vertical"
            android:paddingStart="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_small">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2"
                android:layout_toStartOf="@+id/btnSubmit">

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnSelectAll"
                    style="@style/CameraButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingStart="@dimen/microPadding"
                    android:paddingEnd="@dimen/microPadding"
                    android:layout_gravity="start"
                    android:singleLine="true"
                    android:fontFamily="@font/roboto_regular"
                    android:text="@string/select_all"
                    android:textAllCaps="false"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_size_xlarge" />


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btnDeSelectAll"
                    style="@style/CameraButtonStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginStart="@dimen/padding_small"
                    android:fontFamily="@font/roboto_regular"
                    android:text="@string/deselect_all"
                    android:textAllCaps="false"
                    android:paddingStart="@dimen/microPadding"
                    android:paddingEnd="@dimen/microPadding"
                    android:singleLine="true"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_size_xlarge" />


            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnSubmit"
                style="@style/CameraButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_medium"
                android:gravity="end"
                android:layout_alignParentEnd="true"
                android:paddingStart="@dimen/microPadding"
                android:paddingEnd="@dimen/microPadding"
                android:singleLine="true"
                android:text="@string/submit"
                android:textAllCaps="false"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/text_size_xlarge" />

        </RelativeLayout>
    </RelativeLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
