<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.Toolbar xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tlGeneral"
    android:layout_width="match_parent"
    android:layout_height="?attr/actionBarSize"
    android:background="@color/colorAccent"
    app:contentInsetLeft="0dp"
    app:contentInsetStart="0dp"
    app:layout_collapseMode="pin">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/imgBtnBack"
            style="@style/btnToolbarBack"
            android:tint="@color/white"
            android:visibility="gone"/>

        <LinearLayout
        android:id="@+id/llListOrMap"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:gravity="center">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvListView"
                style="@style/tvListMapToggle"
                android:minWidth="80dp"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMapView"
                style="@style/tvListMapToggle"
                android:minWidth="80dp"/>

        </LinearLayout>


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivUpload"
            style="@style/ivToolbarIcon"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="@dimen/padding_small"
            app:srcCompat="@drawable/ic_upload_white"
            android:visibility="gone" />

    </RelativeLayout>

</androidx.appcompat.widget.Toolbar>
