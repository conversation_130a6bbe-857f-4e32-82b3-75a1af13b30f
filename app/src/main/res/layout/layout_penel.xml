<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvPanelTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center_vertical"
                android:padding="@dimen/padding_small_tiny"
                android:paddingStart="@dimen/microPadding"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_xlarge"
                android:textStyle="bold" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/microPadding"
                android:padding="@dimen/padding_tiny"
                android:src="@drawable/ic_cross_border"
                android:tint="@color/white"
                android:visibility="gone" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivNewGroup"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/microPadding"
                android:padding="@dimen/padding_tiny"
                android:src="@drawable/icn_plus_border"
                android:tint="@color/white" />


        </LinearLayout>

    </LinearLayout>

    <include
        android:id="@+id/llSeparator"
        layout="@layout/layout_saparator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/padding_tiny"
        android:layout_marginTop="@dimen/padding_small" />
</LinearLayout>
