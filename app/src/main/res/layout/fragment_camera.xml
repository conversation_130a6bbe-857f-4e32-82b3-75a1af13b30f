<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rlMain"
    android:animateLayoutChanges="true"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <RelativeLayout
        android:id="@+id/rlCameraMainView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible">

        <include
            android:id="@+id/cameraView"
            layout="@layout/layout_camera_view" />

        <include
            android:id="@+id/cameraPermission"
            layout="@layout/layout_camera_no_permission" />

        <RelativeLayout
            android:id="@+id/rlCameraTopView"
            style="@style/StyleCameraTopView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:padding="@dimen/normal_distance">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnFlipCamera"
                style="@style/CameraButtonStyle"
                android:src="@drawable/ic_flip_camera" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnFlash"
                style="@style/CameraButtonStyle"
                android:layout_toEndOf="@+id/imgBtnFlipCamera"
                android:src="@drawable/selector_button_flash"
                android:visibility="gone" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/imgBtnImageUpload"
                style="@style/CameraButtonStyle"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:visibility="visible"
                app:srcCompat="@drawable/ic_upload_orange" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnFormCancel"
                style="@style/CameraButtonStyle"
                android:layout_alignParentEnd="true"
                android:src="@drawable/ic_close"
                android:tint="@color/colorPrimary"
                android:visibility="gone" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/rlCameraBottom"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:gravity="bottom"
            android:paddingTop="@dimen/padding_medium_large">

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnCaptureImage"
                style="@style/CameraButtonStyle"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/padding_medium"
                android:layout_marginBottom="@dimen/padding_medium"
                android:src="@drawable/ic_capture_image_circle_before"
                android:visibility="visible" />

            <androidx.appcompat.widget.AppCompatImageButton
                android:id="@+id/imgBtnGallary"
                style="@style/CameraButtonStyle"
                android:layout_alignBottom="@+id/imgBtnCaptureImage"
                android:layout_alignParentStart="true"
                android:src="@drawable/ic_button_gallary" />

        </RelativeLayout>
    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/rlMainPreview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:animateLayoutChanges="false"
        android:visibility="visible">

        <ImageView
            android:id="@+id/ivCapturedImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:visibility="visible" />

        <FrameLayout
            android:id="@+id/flDrawingView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible" />

    </RelativeLayout>

    <include
        android:id="@+id/cameraPreview"
        layout="@layout/layout_camera_preview" />

    <include
        android:id="@+id/cameraDraw"
        layout="@layout/layout_camera_draw" />


    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivAutoFocusAnim"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:adjustViewBounds="true"
        android:visibility="visible" />

    <include
        android:id="@+id/cameraMapView"
        layout="@layout/dialog_map_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />


    <RelativeLayout
        android:id="@+id/rlProgress"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ProgressBar
            android:id="@+id/pbPolygon"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_centerHorizontal="true" />
    </RelativeLayout>

</RelativeLayout>