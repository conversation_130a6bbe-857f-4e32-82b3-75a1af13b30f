<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/content_notes"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingTop="@dimen/padding_xxlarge"
    app:layout_behavior="@string/appbar_scrolling_view_behavior">

    <ImageView
        android:id="@+id/ivClear"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_marginStart="@dimen/padding_medium"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/padding_tiny"
        android:src="@drawable/ic_delete" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivUndo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/padding_medium"
        android:contentDescription="@string/app_name"
        android:padding="@dimen/padding_tiny"
        android:src="@drawable/ic_undo_white"
        android:tint="@color/colorPrimary"
        android:visibility="gone" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cvSign"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_margin="@dimen/padding_medium"
        app:cardCornerRadius="@dimen/padding_tiny"
        app:cardElevation="0dp">

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/shape_black_border"
            android:padding="@dimen/padding_tiny">

            <com.sitefotos.util.views.DrawingView
                android:id="@+id/drawing"
                android:layout_width="match_parent"
                android:layout_height="@dimen/size_height"
                android:background="@color/white"/>
        </RelativeLayout>
    </androidx.cardview.widget.CardView>


    <androidx.appcompat.widget.AppCompatImageButton
        android:id="@+id/imgBtnDone"
        style="@style/CameraButtonStyle"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/padding_medium"
        android:layout_marginBottom="@dimen/padding_medium"
        android:src="@drawable/ic_image_done"
        android:layout_alignParentBottom="true"
        android:visibility="visible" />
</RelativeLayout>
