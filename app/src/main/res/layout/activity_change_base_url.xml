<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ChangeBaseUrlActivity.ChangeBaseUrlActivity">

    <include layout="@layout/layout_toolbar_other" />

   <LinearLayout
       android:gravity="center"
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       android:orientation="vertical">

    <com.sitefotos.util.views.CustomEditText
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:id="@+id/edtChangeUrl"
        style="@style/loginEditText"
        android:padding="14dp"
        android:layout_width="match_parent"
        android:hint="@string/changeUrl" />


    <Button
        android:layout_marginBottom="10dp"
        android:id="@+id/btnSave"
        style="@style/loginButton"
        android:layout_marginLeft="50dp"
        android:layout_marginRight="50dp"
        android:text="@string/save" />
   </LinearLayout>
</LinearLayout>