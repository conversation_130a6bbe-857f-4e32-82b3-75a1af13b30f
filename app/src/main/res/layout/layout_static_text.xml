<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    style="@style/layoutMainStyle"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tvStaticTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:textColor="@color/black_font"
        android:textSize="@dimen/text_size_large" />

    <TextView
        android:id="@+id/tvStaticValue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/microPadding"
        android:textColor="@color/colorPrimary"
        android:layout_gravity="center"
        android:paddingStart="@dimen/padding_tiny"
        android:paddingEnd="@dimen/padding_tiny"
        android:textSize="@dimen/text_size_large" />

</LinearLayout>
