<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/setting_activity_background"
    android:clickable="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:padding="@dimen/padding_tiny"
    android:paddingStart="@dimen/padding_small_tiny"
    android:paddingEnd="@dimen/padding_tiny"
    android:paddingBottom="@dimen/padding_tiny">

    <RelativeLayout
        android:id="@+id/rlMainLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvHeaderTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/ivArrow"
            android:fontFamily="@font/roboto_medium"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_xxlarge" />

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginEnd="@dimen/padding_medium_small"
            android:src="@drawable/icn_arrow_forward_orrange" />

    </RelativeLayout>
</RelativeLayout>