<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/llMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/padding_medium_small"
        android:background="@color/white"
        android:orientation="vertical">

        <RelativeLayout
            android:id="@+id/rlTop"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@color/grey_actionbar_bg"
            android:gravity="center"
            android:orientation="horizontal">


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:fontFamily="@font/roboto_regular"
                android:gravity="center"
                android:padding="@dimen/padding_small"
                android:text="@string/choose_form_field"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge" />

        </RelativeLayout>


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvPhotoComponent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:paddingStart="@dimen/padding_small"
            android:paddingTop="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_small"
            tools:listitem="@layout/item_image_component" />


        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnSelectionCancel"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_tiny"
            android:background="@color/transparent"
            android:fontFamily="@font/roboto_medium"
            android:text="@string/txt_cancel"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_xxlarge"
            android:visibility="visible" />

    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
