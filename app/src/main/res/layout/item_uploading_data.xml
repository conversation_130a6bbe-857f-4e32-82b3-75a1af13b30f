<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:wheel="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cvUploadingActivity"
    android:layout_width="wrap_content"
    android:layout_height="180dp"
    android:layout_margin="@dimen/padding_small"
    app:cardCornerRadius="@dimen/padding_tiny"
    app:cardPreventCornerOverlap="false">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/ivImage"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:scaleType="centerCrop"
            android:src="@drawable/img_placeholder" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:orientation="vertical">

            <com.sitefotos.util.ProgressWheel
                android:id="@+id/pwUploading"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_marginStart="@dimen/padding_small"
                wheel:barColor="@color/colorPrimary"
                wheel:progressIndeterminate="true" />

            <ProgressBar
                android:id="@+id/ProgressBar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:indeterminate="false"
                android:maxHeight="2dp"
                android:minHeight="2dp"
                android:padding="@dimen/padding_small_tiny"
                android:progressDrawable="@drawable/layer_list_progress_bar" />
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>

