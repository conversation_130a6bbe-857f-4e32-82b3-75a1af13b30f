<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"

    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include layout="@layout/layout_toolbar_new_property" />

    <ScrollView
        android:id="@+id/scrviewNewPropertyActivity"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TextView
                android:id="@+id/txtNewPropertyActivityAddress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:padding="8dp"
                android:text="@string/address"
                android:textColor="@color/gray_2"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:src="@color/addNewPropertyDivider" />

            <EditText
                android:id="@+id/edtNewPropertyActivityEnterPropertyName"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:hint="@string/enter_property_name"
                android:padding="8dp"
                android:layout_marginBottom="50dp"
                android:textColor="@color/colorPrimary"
                android:textColorHint="@color/textColorForStreetAddress"
                android:textSize="14sp" />

            <EditText
                android:id="@+id/edtNewPropertyActivityStreetAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/streetAddress"
                android:padding="8dp"
                android:textColor="@color/colorPrimary"
                android:textColorHint="@color/textColorForStreetAddress"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:src="@color/addNewPropertyDivider" />

            <EditText
                android:id="@+id/edtNewPropertyActivityCity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/enterYourCity"
                android:padding="8dp"
                android:textColor="@color/colorPrimary"
                android:textColorHint="@color/textColorForStreetAddress"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:src="@color/addNewPropertyDivider" />

            <EditText
                android:id="@+id/edtNewPropertyActivityState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/enterYourState"
                android:padding="8dp"
                android:text=""
                android:textColor="@color/colorPrimary"
                android:textColorHint="@color/textColorForStreetAddress"
                android:textSize="14sp" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:src="@color/addNewPropertyDivider" />

            <EditText
                android:id="@+id/edtNewPropertyActivityZipCode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@null"
                android:hint="@string/enterYourZipCode"
                android:inputType="number"
                android:padding="8dp"
                android:textColor="@color/colorPrimary"
                android:textColorHint="@color/textColorForStreetAddress"
                android:textSize="14sp" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>