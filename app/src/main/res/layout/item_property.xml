<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/llProgressTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        tools:ignore="RtlSymmetry">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="25dp"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="5dp"
            android:fontFamily="@font/roboto_medium"
            android:singleLine="true"
            android:text="Work In Progress"
            android:textColor="@color/gray_2"
            android:textSize="@dimen/text_size_large" />

    </LinearLayout>

    <com.google.android.material.divider.MaterialDivider
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#CCCCCC" />

    <RelativeLayout
        android:id="@+id/rlMain"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="@dimen/padding_tiny">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivFormCompleteStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:clickable="false"
            android:padding="@dimen/padding_tiny"
            android:src="@drawable/ic_image_tick" />

        <LinearLayout
            android:id="@+id/llLeft"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/llRight"
            android:layout_toEndOf="@+id/ivFormCompleteStatus"
            android:orientation="vertical"
            android:paddingStart="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_tiny">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/roboto_medium"
                android:singleLine="true"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_large"
                tools:text="Name" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_tiny"
                android:ellipsize="end"
                android:fontFamily="@font/roboto_regular"
                android:singleLine="true"
                android:textColor="@color/gray"
                android:textSize="@dimen/text_size_large"
                tools:text="Address" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAddress2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_tiny"
                android:ellipsize="end"
                android:fontFamily="@font/roboto_regular"
                android:maxLines="2"
                android:textColor="@color/gray"
                android:textSize="@dimen/text_size_large"
                tools:text="Address" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/llRight"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@+id/ivArrow"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivIcon"
                android:layout_width="wrap_content"
                android:layout_height="25dp"
                android:layout_gravity="end"
                android:layout_marginStart="@dimen/padding_small"
                android:layout_marginTop="@dimen/padding_tiny"
                android:layout_marginEnd="@dimen/padding_small"
                android:maxWidth="18dp"
                android:maxHeight="18dp"
                android:minWidth="15dp"
                android:minHeight="15dp"
                android:visibility="visible"
                app:srcCompat="@drawable/ic_marker_blue" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMiles"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginStart="@dimen/padding_small"
                android:layout_marginEnd="@dimen/padding_small"
                android:fontFamily="@font/roboto_medium"
                android:textColor="@color/gray"
                tools:text="300.0mi" />
        </LinearLayout>

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerInParent="true"
            android:layout_gravity="end|center_vertical"
            android:paddingStart="@dimen/padding_medium"
            android:paddingTop="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_medium"
            android:paddingBottom="@dimen/padding_small"
            android:src="@drawable/ic_forward"
            android:visibility="gone" />

    </RelativeLayout>

    <com.google.android.material.divider.MaterialDivider
        android:id="@+id/bottomDivider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginBottom="@dimen/padding_largest"
        android:background="#CCCCCC"
        android:visibility="gone" />

</LinearLayout>