<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/padding_small"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/rlItem"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <CheckBox
            android:id="@+id/cbItem"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="@dimen/padding_tiny"
            android:layout_marginTop="@dimen/padding_tiny"
            android:layout_marginEnd="@dimen/padding_tiny"
            android:layout_marginBottom="@dimen/padding_tiny" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@+id/cbItem"
            android:padding="@dimen/padding_tiny"
            android:textColor="@color/black"
            android:textSize="@dimen/text_size_xlarge" />
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/llPeopleHours"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/padding_medium_large"
        android:layout_marginEnd="@dimen/padding_small"
        android:layout_marginBottom="@dimen/padding_tiny"
        android:orientation="horizontal"
        android:visibility="visible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="50"
            android:focusableInTouchMode="true"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="60"
                android:gravity="center"
                android:text="@string/txtpeople"
                android:textColor="@color/black" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPeople"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="40"
                android:background="@drawable/shape_white_bg_grey_border_corner"
                android:gravity="center_vertical"
                android:hint="@string/txtpeople"
                android:inputType="number"
                android:padding="@dimen/microPadding"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge" />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/padding_small"
            android:layout_weight="50"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_weight="60"
                android:gravity="center"
                android:inputType="number"
                android:text="@string/hour"
                android:textColor="@color/black" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvHour"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="40"
                android:background="@drawable/shape_white_bg_grey_border_corner"
                android:gravity="center_vertical"
                android:hint="@string/hour"
                android:inputType="numberDecimal"
                android:padding="@dimen/microPadding"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_xlarge" />
        </LinearLayout>

    </LinearLayout>

    <include
        layout="@layout/layout_saparator"
        android:id="@+id/llSeparator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/padding_small"
        android:layout_marginBottom="@dimen/padding_tiny" />
</LinearLayout>