<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:wheel="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/setting_activity_background"
    android:orientation="vertical"
    tools:context=".form.FormListingFragment">

    <include
        android:id="@+id/tlOther"
        layout="@layout/layout_toolbar_other" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_small">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edtSearch"
            style="@style/searchEdtStyle"
            android:hint="@string/search_forms" />
    </LinearLayout>

    <com.pnikosis.materialishprogress.ProgressWheel
        android:id="@+id/progressBar"
        android:layout_width="70dp"
        android:layout_height="70dp"
        android:layout_gravity="center"
        android:visibility="gone"
        wheel:matProg_barColor="@color/colorPrimary"
        wheel:matProg_progressIndeterminate="true" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/srlRefresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.core.widget.NestedScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">
                    <com.sitefotos.util.views.CustomRecyclerView
                        android:id="@+id/rvForm"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@color/white"
                        android:paddingStart="@dimen/padding_medium"
                        android:paddingEnd="@dimen/microPadding"
                        app:list_type="list" />
                </LinearLayout>
            </androidx.core.widget.NestedScrollView>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <include
            android:id="@+id/llEmptyView"
            layout="@layout/layout_recycler_emptyview" />
    </RelativeLayout>


</LinearLayout>