<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/llDynamicDropDown"
    style="@style/layoutMainStyle"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:fontFamily="@font/roboto_regular"
            android:textColor="@color/black_font"
            android:textSize="@dimen/text_size_xlarge" />

        <TextView
            android:id="@+id/tvSelected"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/drawable_lines_sign"
            android:gravity="center_vertical"
            android:hint="@string/tap_to_select_item"
            android:layout_marginTop="@dimen/padding_tiny"
            android:minHeight="@dimen/spinner_minimum_height"
            android:paddingBottom="@dimen/padding_tiny"
            android:paddingEnd="@dimen/padding_tiny"
            android:paddingStart="@dimen/padding_small"
            android:paddingTop="@dimen/padding_tiny"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_size_xlarge" />
    </LinearLayout>

</LinearLayout>
