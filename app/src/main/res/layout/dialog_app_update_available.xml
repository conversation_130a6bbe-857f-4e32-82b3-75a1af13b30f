<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llAlertDialog"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:cardCornerRadius="@dimen/padding_small"
        app:cardUseCompatPadding="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/padding_tiny">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:src="@mipmap/ic_launcher" />

            <TextView
                android:id="@+id/tvAppName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/padding_tiny"
                android:text="@string/app_name"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_large"/>

            <TextView
                android:id="@+id/tvUpdateDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/padding_tiny"
                android:text="@string/msg_update_available"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_large" />

            <TextView
                android:id="@+id/tvAppVersion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="@dimen/padding_tiny"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="@dimen/text_size_medium_large" />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:layout_marginTop="@dimen/padding_large"
                android:gravity="end|center_vertical">

                <TextView
                    android:id="@+id/tvLater"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/padding_medium"
                    android:text="@string/later"
                    android:textColor="@color/black"
                    android:textSize="@dimen/text_size_large" />



                <TextView
                    android:id="@+id/tvUpdateNow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/padding_medium"
                    android:text="@string/update_now"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_size_large"
                    android:textStyle="bold" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>
