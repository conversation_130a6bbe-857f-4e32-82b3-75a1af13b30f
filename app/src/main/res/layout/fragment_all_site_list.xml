<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/setting_activity_background"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/llSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="horizontal"
        android:paddingStart="@dimen/padding_small"
        android:paddingEnd="@dimen/padding_small">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edtSearch"
            style="@style/searchEdtStyle"
            android:hint="@string/search_sites" />
    </LinearLayout>
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/srlRefresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/llSearch"
        android:layout_marginTop="@dimen/padding_tiny">

        <com.sitefotos.util.views.CustomRecyclerView
            android:id="@+id/rvSites"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/padding_small"
            app:list_type="list" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <include
        android:id="@+id/emptyView"
        layout="@layout/layout_recycler_emptyview" />
</RelativeLayout>
