<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llAlertDialog"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:cardCornerRadius="@dimen/padding_small"
        app:cardUseCompatPadding="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/white"
            android:orientation="vertical"
            android:padding="@dimen/padding_tiny">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvCrewMain"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/microPadding"
                android:layout_weight="1"
                tools:listitem="@layout/item_employee"
                android:background="@color/white" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/padding_tiny"
                android:gravity="center">

                <TextView
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/padding_large"
                    android:text="@string/txt_cancel"
                    android:textColor="@color/black"
                    android:textSize="@dimen/text_size_xlarge" />

                <View
                    android:id="@+id/divider"
                    android:layout_width="1dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    android:layout_marginTop="@dimen/padding_small"
                    android:layout_marginBottom="@dimen/padding_small"
                    android:background="@color/black"
                    android:padding="@dimen/padding_tiny" />

                <TextView
                    android:id="@+id/btnSubmit"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:padding="@dimen/padding_large"
                    android:text="@string/ok"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/text_size_xlarge"
                    android:textStyle="bold" />
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>
</RelativeLayout>
