<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:wheel="http://schemas.android.com/apk/res-auto"
    android:id="@+id/llMain"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/tvUploadDataTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_toStartOf="@+id/pwUploadDataProgress"
        android:paddingBottom="@dimen/padding_tiny"
        android:paddingEnd="@dimen/padding_medium"
        android:paddingStart="@dimen/padding_medium"
        android:paddingTop="@dimen/padding_tiny"
        android:textColor="@color/colorPrimary"
        android:textSize="@dimen/text_size_large" />

    <com.sitefotos.util.ProgressWheel
        android:id="@+id/pwUploadDataProgress"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="@dimen/padding_tiny"
        android:layout_marginStart="@dimen/padding_medium"
        wheel:barColor="@color/colorPrimary"
        wheel:progressIndeterminate="true" />


</RelativeLayout>