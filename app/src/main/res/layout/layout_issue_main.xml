<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">


    <LinearLayout
        android:id="@+id/llIssue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvIssueTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:padding="@dimen/padding_small_tiny"
                android:text="@string/new_issue"
                android:textColor="@color/black_font"
                android:textSize="@dimen/text_size_xlarge" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivNewIssue"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/microPadding"
                android:padding="@dimen/padding_tiny"
                android:src="@drawable/ic_add_new_property" />


        </LinearLayout>

        <include
            layout="@layout/layout_saparator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/microPadding"
            android:layout_marginBottom="@dimen/microPadding" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/llParent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

    </LinearLayout>


</LinearLayout>
