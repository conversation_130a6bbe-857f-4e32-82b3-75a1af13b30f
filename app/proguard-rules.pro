# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Volumes/DATA/Mac-Android/SDK_Mac/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

-keepattributes Signature
-keepattributes Annotation


## Android X  rules ###############
-dontwarn com.google.android.material.**
-dontnote com.google.android.material.**
-keep class com.google.android.material.** { *; }

-dontwarn androidx.**
-keep class androidx.** { *; }
-keep interface androidx.** { *; }


############ Android support lib rules ###############
-dontwarn android.support.v4.**
-keep class android.support.v4.** { *; }

-dontwarn android.support.v7.**
-keep class android.support.v7.** { *; }

###### Android and google #####################
-dontwarn com.google.**
-keep class com.google.** { *; }
-keep interface com.google.**{*;}
-keep class com.google.android.gms.maps.** { *; }
-keep interface com.google.android.gms.maps.** { *; }
-keep class android.support.**{*;}
-dontwarn android.support.**
-keep class android.**{*;}
-dontwarn android.**


########### OKHTTP ##################
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
-dontwarn okio.**


################ ButterKnief################
# Retain generated class which implement Unbinder.
-keep public class * implements butterknife.Unbinder { public <init>(**, android.view.View); }

# Prevent obfuscation of types which use ButterKnife annotations since the simple name
# is used to reflectively look up the generated ViewBinding.
-keep class butterknife.*
-keepclasseswithmembernames class * { @butterknife.* <methods>; }
-keepclasseswithmembernames class * { @butterknife.* <fields>; }
-keep class **$$ViewBinder { *; }
-keep class **_ViewBinding { *; }
-dontwarn butterknife.internal.**

#################### Glide Rules ##################
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep class * extends com.bumptech.glide.module.AppGlideModule {
 <init>(...);
}
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.bumptech.glide.load.data.ParcelFileDescriptorRewinder$InternalRewinder {
  *** rewind();
}

####### Retrofit Rules ##################

# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
# Retain declared checked exceptions for use by a Proxy instance.
-keepattributes Exceptions

-keepclassmembers,allowshrinking,allowobfuscation interface * {
    @retrofit2.http.* <methods>;
}

# Okio which org.jsoupretrofit 2 usses as a internal hood.
-dontwarn okio.**
-dontwarn retrofit2.converter.**
-keep class retrofit2.converter.**{*;}

-keep class javax.annotation.**{*;}
-dontwarn javax.annotation.**
-keep class com.squareup.**{*;}
-dontwarn com.squareup.**
-keep class org.jsoup.**{*;}
-dontwarn org.jsoup.**

############ Timber ##################
-dontwarn org.jetbrains.annotations.**

############### Metadata extractor #################
-keep class com.drew.** {*;}
-keep interface com.drew.** {*;}
-keep enum com.drew.** {*;}



### Dont forget to add this line it proguard. this is used to generate .java classes. ###
-dontwarn com.squareup.javapoet.**


########### App rules ####################

-keep class com.sitefotos.models.**{*;}
-dontwarn com.sitefotos.models.**


-keep class com.sitefotos.service.**{*;}
-dontwarn com.sitefotos.service.**

-keep class com.google.android.gms.measurement.AppMeasurement { *; }
-keep class com.google.android.gms.measurement.AppMeasurement$OnEventListener { *; }


######### Crashlytics  ####################
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends java.lang.Exception
-keep class com.crashlytics.** { *; }
-dontwarn com.crashlytics.**

############## EventBus ######################
-keepattributes *Annotation*
-keepclassmembers class * {
    @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# And if you use AsyncExecutor:
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
    <init>(java.lang.Throwable);
}

