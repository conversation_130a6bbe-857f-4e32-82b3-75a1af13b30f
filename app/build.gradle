apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'

android {
    compileSdkVersion 35
    namespace 'com.sitefotos'
    lintOptions {
        checkReleaseBuilds true
    }
    packagingOptions {
        exclude("META-INF/*.kotlin_module")
    }
    flavorDimensions "app"
    defaultConfig {
        applicationId "com.sitefotos"
        minSdkVersion 23
        targetSdkVersion 35
        versionCode 93
        versionName "2.4.10"
        ndk.abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86', 'x86_64'

        // Enabling multidex support.
        multiDexEnabled true

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"

    }
    android.buildFeatures.buildConfig true
    android.defaultConfig.vectorDrawables.useSupportLibrary = true
    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
                applicationIdSuffix ".dev"
        }
    }
    /*  afterEvaluate {
          tasks.matching {
              it.name.startsWith('dex')
          }.each { dx ->
              if (dx.additionalParameters == null) {
                  dx.additionalParameters = ['--multi-dex']
              } else {
                  dx.additionalParameters += '--multi-dex'
              }
          }
      }*/
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = '17'
    }

    productFlavors {

        signed {
            dimension "app"
            buildConfigField "String", "BASE_URL", '"https://www.sitefotos.com/"'
            buildConfigField "boolean", "shouldUseFirebase", "true"
            manifestPlaceholders = [enableCrashReporting: "true",googleMapApiKey:"AIzaSyCYemI2LeB6GC05cF_wDDmRpkztTRrpoMw"]
            buildConfigField "String", "googleMapApiKey", '"AIzaSyCYemI2LeB6GC05cF_wDDmRpkztTRrpoMw"'
        }
        development {
            dimension "app"
            buildConfigField "String", "BASE_URL", '"https://ak-formbuilder.sitefotos.com/"'
            buildConfigField "boolean", "shouldUseFirebase", "true"
            manifestPlaceholders = [enableCrashReporting: "true",googleMapApiKey:"AIzaSyCYemI2LeB6GC05cF_wDDmRpkztTRrpoMw"]
            buildConfigField "String", "googleMapApiKey", '"AIzaSyCYemI2LeB6GC05cF_wDDmRpkztTRrpoMw"'
            /*AIzaSyBGs7KgXmdpeuEkKFVlW_7K4F1C0Jc30nk*/
        }
    }


    //Use bellow code only when you need to run build in virtual device.
    // TODO Comment this in Release build
    /*splits {
        abi {
            enable true
            reset()
            include 'x86'
            universalApk true
        }
    }*/

    testOptions {
        unitTests.returnDefaultValues = true
        unitTests.includeAndroidResources = true
    }
    buildFeatures {
        viewBinding true
    }

    // https://www.geeksforgeeks.org/how-to-change-the-default-generated-apk-name-in-android-studio/.
    // add the code from below line.
   /* applicationVariants.all {
            // this method is use to rename your release apk only
        variant ->
            variant.outputs.each {
                output ->
                    project.ext { appName = 'Sitefotos' }
                    def formattedDate = new Date().format('yyyy-MM-dd-HH-mm')
                    def newName = output.outputFile.name
                    newName = newName.replace("app-", "$project.ext.appName-")
                    newName = newName.replace("-release", "_$defaultConfig.versionName-" + formattedDate)
                    newName = newName.replace("-debug", "_$defaultConfig.versionName-" + formattedDate)
                    output.outputFileName = newName
            }
    }*/

}
repositories {
    google()
    mavenCentral()
    maven { url "https://jitpack.io" }
}

dependencies {
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation project(':customWeb')
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.gridlayout:gridlayout:1.1.0'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.preference:preference-ktx:1.2.1'
    implementation 'androidx.annotation:annotation:1.9.1'
    implementation 'androidx.exifinterface:exifinterface:1.4.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'com.google.code.gson:gson:2.13.1'

    implementation 'com.google.android.gms:play-services-maps:19.2.0'
    implementation 'com.google.maps.android:android-maps-utils:3.13.0'
    implementation 'com.google.android.gms:play-services-location:21.3.0'
    implementation 'com.google.android.libraries.places:places:4.2.0'
    implementation 'cn.bingerz.android:fastlocation:1.2.2'
    // Used for background service upload - https://github.com/gotev/android-upload-service

    implementation platform('com.google.firebase:firebase-bom:33.13.0')
    implementation 'com.google.firebase:firebase-crashlytics'
    implementation 'com.google.firebase:firebase-analytics'
    implementation 'com.google.firebase:firebase-messaging'

    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    // retrofit
    implementation 'com.squareup.retrofit2:retrofit:2.11.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.11.0'
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.squareup.retrofit2:converter-scalars:2.11.0'
    implementation 'com.pnikosis:materialish-progress:1.7'
    implementation 'com.drewnoakes:metadata-extractor:2.19.0'

    implementation 'com.otaliastudios:cameraview:2.7.2'

    implementation 'org.greenrobot:eventbus:3.3.1'
    implementation 'com.frybits.harmony:harmony:1.2.6'
    //bubble imageview
    implementation 'com.github.AgnaldoNP:BubbleImageView:1.5'
    implementation 'org.brotli:dec:0.1.2'
}