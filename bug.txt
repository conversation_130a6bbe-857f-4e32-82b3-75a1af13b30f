# Crashlytics - Stack trace
# Application: com.sitefotos
# Platform: android
# Version: 2.4.10 (93)
# Issue: 683558460b6bb9b650d9a1bc4c854927
# Session: 689CEA5503A6000173D0E24D8B5CC779_DNE_0_v2
# Date: Wed Aug 13 2025 23:41:54 GMT+0400 (Gulf Standard Time)

Fatal Exception: java.lang.IllegalStateException: Fragment already added
       at androidx.fragment.app.Fragment.setInitialSavedState(Fragment.java:828)
       at androidx.viewpager2.adapter.FragmentStateAdapter.ensureFragment(FragmentStateAdapter.java:267)
       at androidx.viewpager2.adapter.FragmentStateAdapter.onBindViewHolder(FragmentStateAdapter.java:185)
       at androidx.viewpager2.adapter.FragmentStateAdapter.onBindViewHolder(FragmentStateAdapter.java:74)
       at androidx.recyclerview.widget.RecyclerView$Adapter.onBindViewHolder(RecyclerView.java:7846)
       at androidx.recyclerview.widget.RecyclerView$Adapter.bindViewHolder(RecyclerView.java:7953)
       at androidx.recyclerview.widget.RecyclerView$Recycler.tryBindViewHolderByDeadline(RecyclerView.java:6742)
       at androidx.recyclerview.widget.RecyclerView$Recycler.tryGetViewHolderForPositionByDeadline(RecyclerView.java:7013)
       at androidx.recyclerview.widget.RecyclerView$Recycler.getViewForPosition(RecyclerView.java:6853)
       at androidx.recyclerview.widget.RecyclerView$Recycler.getViewForPosition(RecyclerView.java:6849)
       at androidx.recyclerview.widget.LinearLayoutManager$LayoutState.next(LinearLayoutManager.java:2422)
       at androidx.recyclerview.widget.LinearLayoutManager.layoutChunk(LinearLayoutManager.java:1722)
       at androidx.recyclerview.widget.LinearLayoutManager.fill(LinearLayoutManager.java:1682)
       at androidx.recyclerview.widget.LinearLayoutManager.onLayoutChildren(LinearLayoutManager.java:747)
       at androidx.recyclerview.widget.RecyclerView.dispatchLayoutStep2(RecyclerView.java:4737)
       at androidx.recyclerview.widget.RecyclerView.dispatchLayout(RecyclerView.java:4459)
       at androidx.recyclerview.widget.RecyclerView.onLayout(RecyclerView.java:5011)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at androidx.viewpager2.widget.ViewPager2.onLayout(ViewPager2.java:535)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.layoutChild(CoordinatorLayout.java:1255)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.onLayoutChild(CoordinatorLayout.java:941)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.onLayout(CoordinatorLayout.java:961)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1812)
       at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1656)
       at android.widget.LinearLayout.onLayout(LinearLayout.java:1565)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.layoutChild(CoordinatorLayout.java:1255)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.onLayoutChild(CoordinatorLayout.java:941)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.onLayout(CoordinatorLayout.java:961)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.FrameLayout.layoutChildren(FrameLayout.java:323)
       at android.widget.FrameLayout.onLayout(FrameLayout.java:261)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.layoutChild(CoordinatorLayout.java:1255)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.onLayoutChild(CoordinatorLayout.java:941)
       at androidx.coordinatorlayout.widget.CoordinatorLayout.onLayout(CoordinatorLayout.java:961)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.FrameLayout.layoutChildren(FrameLayout.java:323)
       at android.widget.FrameLayout.onLayout(FrameLayout.java:261)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1812)
       at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1656)
       at android.widget.LinearLayout.onLayout(LinearLayout.java:1565)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.FrameLayout.layoutChildren(FrameLayout.java:323)
       at android.widget.FrameLayout.onLayout(FrameLayout.java:261)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at androidx.appcompat.widget.ActionBarOverlayLayout.onLayout(ActionBarOverlayLayout.java:553)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.FrameLayout.layoutChildren(FrameLayout.java:323)
       at android.widget.FrameLayout.onLayout(FrameLayout.java:261)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.LinearLayout.setChildFrame(LinearLayout.java:1812)
       at android.widget.LinearLayout.layoutVertical(LinearLayout.java:1656)
       at android.widget.LinearLayout.onLayout(LinearLayout.java:1565)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.widget.FrameLayout.layoutChildren(FrameLayout.java:323)
       at android.widget.FrameLayout.onLayout(FrameLayout.java:261)
       at com.android.internal.policy.DecorView.onLayout(DecorView.java:1085)
       at android.view.View.layout(View.java:22417)
       at android.view.ViewGroup.layout(ViewGroup.java:6594)
       at android.view.ViewRootImpl.performLayout(ViewRootImpl.java:3453)
       at android.view.ViewRootImpl.performTraversals(ViewRootImpl.java:2907)
       at android.view.ViewRootImpl.doTraversal(ViewRootImpl.java:1955)
       at android.view.ViewRootImpl$TraversalRunnable.run(ViewRootImpl.java:8633)
       at android.view.Choreographer$CallbackRecord.run(Choreographer.java:988)
       at android.view.Choreographer.doCallbacks(Choreographer.java:765)
       at android.view.Choreographer.doFrame(Choreographer.java:700)
       at android.view.Choreographer$FrameDisplayEventReceiver.run(Choreographer.java:967)
       at android.os.Handler.handleCallback(Handler.java:873)
       at android.os.Handler.dispatchMessage(Handler.java:99)
       at android.os.Looper.loop(Looper.java:214)
       at android.app.ActivityThread.main(ActivityThread.java:7156)
       at java.lang.reflect.Method.invoke(Method.java)
       at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:494)
       at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:975)

AsyncTask #4:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

glide-disk-cache-thread-0:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.bumptech.glide.load.engine.executor.GlideExecutor$DefaultThreadFactory$1.run(GlideExecutor.java:424)
       at java.lang.Thread.run(Thread.java:764)
       at com.bumptech.glide.load.engine.executor.GlideExecutor$DefaultPriorityThreadFactory$1.run(GlideExecutor.java:383)

process reaper:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Firebase-Messaging-Init:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1120)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms:play-services-basement@@18.5.0:2)
       at java.lang.Thread.run(Thread.java:764)

pool-39-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

GmsDynamite:
       at java.lang.Object.wait(Object.java)
       at com.google.android.gms.dynamite.zza.run(com.google.android.gms:play-services-basement@@18.5.0:2)

FinalizerDaemon:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:188)
       at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:209)
       at java.lang.Daemons$FinalizerDaemon.runInternal(Daemons.java:232)
       at java.lang.Daemons$Daemon.run(Daemons.java:103)
       at java.lang.Thread.run(Thread.java:764)

pool-11-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-lses-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1120)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

DefaultDispatcher-worker-2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:353)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.park(CoroutineScheduler.kt:855)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.tryPark(CoroutineScheduler.kt:803)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:751)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)

OkHttp TaskRunner:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-Labeling:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at m140.eff.call(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:254)
       at m140.hkw.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:3)
       at m140.hjy.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:19)
       at m140.hkx.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:459)
       at java.util.concurrent.FutureTask.run(FutureTask.java:266)
       at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

glide-source-thread-0:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.bumptech.glide.load.engine.executor.GlideExecutor$DefaultThreadFactory$1.run(GlideExecutor.java:424)
       at java.lang.Thread.run(Thread.java:764)
       at com.bumptech.glide.load.engine.executor.GlideExecutor$DefaultPriorityThreadFactory$1.run(GlideExecutor.java:383)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

pool-36-thread-2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Okio Watchdog:
       at java.lang.Object.wait(Object.java)
       at com.android.okhttp.okio.AsyncTimeout.awaitTimeout(AsyncTimeout.java:311)
       at com.android.okhttp.okio.AsyncTimeout.access$000(AsyncTimeout.java:40)
       at com.android.okhttp.okio.AsyncTimeout$Watchdog.run(AsyncTimeout.java:286)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

DefaultDispatcher-worker-3:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:353)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.park(CoroutineScheduler.kt:855)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.tryPark(CoroutineScheduler.kt:803)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:751)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

pool-43-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

magnifier pixel copy result handler:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

DefaultDispatcher-worker-4:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:353)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.park(CoroutineScheduler.kt:855)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.tryPark(CoroutineScheduler.kt:803)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:751)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)

GL-Map:
       at java.lang.Object.wait(Object.java)
       at m140.fgl.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:961)

Thread-15:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at m140.ahg.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at m140.ahg.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:6)

androidmapsapi-bg-2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

GL-Map:
       at java.lang.Object.wait(Object.java)
       at m140.fgl.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:961)

androidmapsapi-bg-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

OkHttp ConnectionPool:
       at java.lang.Object.wait(Object.java)
       at com.android.okhttp.ConnectionPool$1.run(ConnectionPool.java:101)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

OkHttp TaskRunner:
       at java.lang.Object.wait(Object.java)
       at okhttp3.internal.concurrent.TaskRunner$RealBackend.coordinatorWait(TaskRunner.kt:294)
       at okhttp3.internal.concurrent.TaskRunner.awaitTaskToRun(TaskRunner.kt:218)
       at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:59)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

OkHttp TaskRunner:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-scpm-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1132)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

pool-31-thread-3:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

AsyncTask #3:
       at libcore.reflect.GenericSignatureParser.parseOptTypeArguments(GenericSignatureParser.java:340)
       at libcore.reflect.GenericSignatureParser.parseClassTypeSignature(GenericSignatureParser.java:317)
       at libcore.reflect.GenericSignatureParser.parseFieldTypeSignature(GenericSignatureParser.java:289)
       at libcore.reflect.GenericSignatureParser.parseForField(GenericSignatureParser.java:218)
       at java.lang.reflect.Field.getGenericType(Field.java:177)
       at com.google.gson.internal.bind.ReflectiveTypeAdapterFactory.getBoundFields(ReflectiveTypeAdapterFactory.java:394)
       at com.google.gson.internal.bind.ReflectiveTypeAdapterFactory.create(ReflectiveTypeAdapterFactory.java:165)
       at com.google.gson.Gson.getAdapter(Gson.java:628)
       at com.google.gson.Gson.fromJson(Gson.java:1358)
       at com.google.gson.Gson.fromJson(Gson.java:1260)
       at com.google.gson.Gson.fromJson(Gson.java:1170)
       at com.google.gson.Gson.fromJson(Gson.java:1107)
       at com.sitefotos.storage.tables.TblSites.getAllDataFromDb(TblSites.java:353)
       at com.sitefotos.storage.tables.TblSites.getAllData(TblSites.java:317)
       at com.sitefotos.site.screens.SitesAndMapListFragment.getSortedList(SitesAndMapListFragment.java:289)
       at com.sitefotos.site.screens.SitesAndMapListFragment.-$$Nest$mgetSortedList()
       at com.sitefotos.site.screens.SitesAndMapListFragment$GetDataFromDB.doInBackground(SitesAndMapListFragment.java:530)
       at com.sitefotos.site.screens.SitesAndMapListFragment$GetDataFromDB.doInBackground(SitesAndMapListFragment.java:515)
       at android.os.AsyncTask$2.call(AsyncTask.java:333)
       at java.util.concurrent.FutureTask.run(FutureTask.java:266)
       at android.os.AsyncTask$SerialExecutor$1.run(AsyncTask.java:245)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Firebase-Messaging-Topics-Io:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1120)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.android.gms.common.util.concurrent.zza.run(com.google.android.gms:play-services-basement@@18.5.0:2)
       at java.lang.Thread.run(Thread.java:764)

GoogleApiHandler:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

Thread-17:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at m140.ahg.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at m140.ahg.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:6)

pool-39-thread-2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Firebase Background Thread #0:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
       at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass)
       at java.lang.Thread.run(Thread.java:764)

pool-31-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Harmony-sitefotos_preferences:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

FirebaseSessions_HandlerThread:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

Firebase Blocking Thread #0:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
       at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass)
       at java.lang.Thread.run(Thread.java:764)

CameraViewEngine:
       at android.hardware.Camera.native_release(Camera.java)
       at android.hardware.Camera.release(Camera.java:663)
       at com.otaliastudios.cameraview.engine.Camera1Engine.onStopEngine(Camera1Engine.java:354)
       at com.otaliastudios.cameraview.engine.CameraEngine$7.call(CameraEngine.java:414)
       at com.otaliastudios.cameraview.engine.CameraEngine$7.call(CameraEngine.java:411)
       at com.otaliastudios.cameraview.engine.orchestrator.CameraStateOrchestrator$2.call(CameraStateOrchestrator.java:69)
       at com.otaliastudios.cameraview.engine.orchestrator.CameraStateOrchestrator$2.call(CameraStateOrchestrator.java:60)
       at com.otaliastudios.cameraview.engine.orchestrator.CameraOrchestrator$3.run(CameraOrchestrator.java:152)
       at com.otaliastudios.cameraview.internal.WorkerHandler.run(WorkerHandler.java:137)
       at com.otaliastudios.cameraview.engine.orchestrator.CameraOrchestrator.execute(CameraOrchestrator.java:147)
       at com.otaliastudios.cameraview.engine.orchestrator.CameraOrchestrator.access$100(CameraOrchestrator.java:34)
       at com.otaliastudios.cameraview.engine.orchestrator.CameraOrchestrator$2.run(CameraOrchestrator.java:137)
       at android.os.Handler.handleCallback(Handler.java:873)
       at android.os.Handler.dispatchMessage(Handler.java:99)
       at android.os.Looper.loop(Looper.java:214)
       at android.os.HandlerThread.run(HandlerThread.java:65)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-Background_23:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

Firebase Background Thread #3:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
       at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass)
       at java.lang.Thread.run(Thread.java:764)

FileObserver:
       at android.os.FileObserver$ObserverThread.observe(FileObserver.java)
       at android.os.FileObserver$ObserverThread.run(FileObserver.java:86)

GL-Map:
       at java.lang.Object.wait(Object.java)
       at m140.fgl.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:961)

DefaultDispatcher-worker-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:353)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.park(CoroutineScheduler.kt:855)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.tryPark(CoroutineScheduler.kt:803)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:751)
       at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:704)

androidmapsapi-appenvironment-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1132)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Thread-14:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at m140.ahg.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at m140.ahg.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:6)

OkHttp Dispatcher:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

pool-42-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

pool-42-thread-2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Okio Watchdog:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2189)
       at okio.AsyncTimeout$Companion.awaitTimeout$okio(AsyncTimeout.kt:308)
       at okio.AsyncTimeout$Watchdog.run(AsyncTimeout.kt:186)

ConnectivityThread:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

androidmapsapi-Labeling:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at m140.eff.call(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:254)
       at m140.hkw.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:3)
       at m140.hjy.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:19)
       at m140.hkx.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:459)
       at java.util.concurrent.FutureTask.run(FutureTask.java:266)
       at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

GLThread 15836:
       at java.lang.Object.wait(Object.java)
       at android.opengl.GLSurfaceView$GLThread.guardedRun(GLSurfaceView.java:1495)
       at android.opengl.GLSurfaceView$GLThread.run(GLSurfaceView.java:1270)

OkHttp Dispatcher:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

AsyncTask #2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-Scheduler:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1132)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-ula-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

glide-active-resources:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:188)
       at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:209)
       at com.bumptech.glide.load.engine.ActiveResources.cleanReferenceQueue(ActiveResources.java:132)
       at com.bumptech.glide.load.engine.ActiveResources$2.run(ActiveResources.java:61)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.bumptech.glide.load.engine.ActiveResources$1$1.run(ActiveResources.java:43)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-ula-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

FinalizerWatchdogDaemon:
       at java.lang.Thread.sleep(Thread.java)
       at java.lang.Thread.sleep(Thread.java:373)
       at java.lang.Thread.sleep(Thread.java:314)
       at java.lang.Daemons$FinalizerWatchdogDaemon.sleepFor(Daemons.java:342)
       at java.lang.Daemons$FinalizerWatchdogDaemon.waitForFinalization(Daemons.java:364)
       at java.lang.Daemons$FinalizerWatchdogDaemon.runInternal(Daemons.java:281)
       at java.lang.Daemons$Daemon.run(Daemons.java:103)
       at java.lang.Thread.run(Thread.java:764)

GLThread 15887:
       at java.lang.Object.wait(Object.java)
       at android.opengl.GLSurfaceView$GLThread.guardedRun(GLSurfaceView.java:1495)
       at android.opengl.GLSurfaceView$GLThread.run(GLSurfaceView.java:1270)

androidmapsapi-Background_24:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

pool-40-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

pool-13-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

pool-31-thread-4:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

ProcessStablePhenotypeFlag:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1120)
       at java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:849)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-Labeling:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at m140.eff.call(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:254)
       at m140.hkw.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:3)
       at m140.hjy.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:19)
       at m140.hkx.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:459)
       at java.util.concurrent.FutureTask.run(FutureTask.java:266)
       at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:301)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-ula-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Firebase Background Thread #2:
       at dalvik.system.VMStack.getThreadStackTrace(VMStack.java)
       at java.lang.Thread.getStackTrace(Thread.java:1538)
       at java.lang.Thread.getAllStackTraces(Thread.java:1588)
       at com.google.firebase.crashlytics.internal.common.CrashlyticsReportDataCapture.populateThreadsList(CrashlyticsReportDataCapture.java:343)
       at com.google.firebase.crashlytics.internal.common.CrashlyticsReportDataCapture.populateExecutionData(CrashlyticsReportDataCapture.java:314)
       at com.google.firebase.crashlytics.internal.common.CrashlyticsReportDataCapture.populateEventApplicationData(CrashlyticsReportDataCapture.java:261)
       at com.google.firebase.crashlytics.internal.common.CrashlyticsReportDataCapture.captureEventData(CrashlyticsReportDataCapture.java:112)
       at com.google.firebase.crashlytics.internal.common.SessionReportingCoordinator.persistEvent(SessionReportingCoordinator.java:337)
       at com.google.firebase.crashlytics.internal.common.SessionReportingCoordinator.persistFatalEvent(SessionReportingCoordinator.java:130)
       at com.google.firebase.crashlytics.internal.common.CrashlyticsController$2.call(CrashlyticsController.java:212)
       at com.google.firebase.crashlytics.internal.common.CrashlyticsController$2.call(CrashlyticsController.java:198)
       at com.google.firebase.crashlytics.internal.concurrency.CrashlyticsWorker.lambda$submitTask$2(CrashlyticsWorker.java:118)
       at com.google.firebase.crashlytics.internal.concurrency.CrashlyticsWorker$$ExternalSyntheticLambda3.then(D8$$SyntheticClass)
       at com.google.android.gms.tasks.zze.run(com.google.android.gms:play-services-tasks@@18.1.0:1)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
       at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass)
       at java.lang.Thread.run(Thread.java:764)

GoogleApiHandler:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

glide-active-resources:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:188)
       at java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:209)
       at m140.ame.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:7)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.amc.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:8)
       at java.lang.Thread.run(Thread.java:764)

pool-36-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-Background_22:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at m140.bpr.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:40)
       at java.lang.Thread.run(Thread.java:764)

FallbackCameraThread:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

Thread-13:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at m140.agz.b(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:3)
       at m140.agz.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:11)

ReferenceQueueDaemon:
       at java.lang.Object.wait(Object.java)
       at java.lang.Daemons$ReferenceQueueDaemon.runInternal(Daemons.java:178)
       at java.lang.Daemons$Daemon.run(Daemons.java:103)
       at java.lang.Thread.run(Thread.java:764)

OkHttp www.sitefotos.com:
       at com.android.org.conscrypt.NativeCrypto.SSL_read(NativeCrypto.java)
       at com.android.org.conscrypt.NativeSsl.read(NativeSsl.java:407)
       at com.android.org.conscrypt.ConscryptFileDescriptorSocket$SSLInputStream.read(ConscryptFileDescriptorSocket.java:579)
       at okio.InputStreamSource.read(JvmOkio.kt:93)
       at okio.AsyncTimeout$source$1.read(AsyncTimeout.kt:128)
       at okio.RealBufferedSource.request(RealBufferedSource.kt:209)
       at okio.RealBufferedSource.require(RealBufferedSource.kt:202)
       at okhttp3.internal.http2.Http2Reader.nextFrame(Http2Reader.kt:89)
       at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:618)
       at okhttp3.internal.http2.Http2Connection$ReaderRunnable.invoke(Http2Connection.kt:609)
       at okhttp3.internal.concurrent.TaskQueue$execute$1.runOnce(TaskQueue.kt:98)
       at okhttp3.internal.concurrent.TaskRunner.runTask(TaskRunner.kt:116)
       at okhttp3.internal.concurrent.TaskRunner.access$runTask(TaskRunner.kt:42)
       at okhttp3.internal.concurrent.TaskRunner$runnable$1.run(TaskRunner.kt:65)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1167)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

Thread-16:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.PriorityBlockingQueue.take(PriorityBlockingQueue.java:548)
       at m140.ahg.a(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:5)
       at m140.ahg.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:6)

Firebase Background Thread #1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
       at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass)
       at java.lang.Thread.run(Thread.java:764)

queued-work-looper:
       at android.os.MessageQueue.nativePollOnce(MessageQueue.java)
       at android.os.MessageQueue.next(MessageQueue.java:326)
       at android.os.Looper.loop(Looper.java:181)
       at android.os.HandlerThread.run(HandlerThread.java:65)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

Measurement Worker:
       at java.lang.Object.wait(Object.java)
       at java.lang.Object.wait(Object.java:422)
       at m7.qm.run(:com.google.android.gms.dynamite_measurementdynamite@253031009@25.30.31 (100300-0):67)

ScionFrontendApi:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

AsyncTask #1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:2101)
       at java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:467)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

JavaCronetEngine:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at org.chromium.net.impl.JavaCronetEngine$1$1.run(:com.google.android.gms.policy_maps_core_dynamite@252725201@252725200021.777614008.777614008:16)
       at java.lang.Thread.run(Thread.java:764)

androidmapsapi-lpbg-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

pool-15-thread-1:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.park(LockSupport.java:190)
       at java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:2059)
       at java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:442)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1092)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)

pool-31-thread-2:
       at java.lang.Object.wait(Object.java)
       at java.lang.Thread.parkFor$(Thread.java:2137)
       at sun.misc.Unsafe.park(Unsafe.java:358)
       at java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:230)
       at java.util.concurrent.SynchronousQueue$TransferStack.awaitFulfill(SynchronousQueue.java:461)
       at java.util.concurrent.SynchronousQueue$TransferStack.transfer(SynchronousQueue.java:362)
       at java.util.concurrent.SynchronousQueue.poll(SynchronousQueue.java:937)
       at java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1091)
       at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1152)
       at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:641)
       at java.lang.Thread.run(Thread.java:764)