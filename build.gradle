// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        agp_version = '8.6.0'
    }
    ext.kotlin_version = '2.1.0'
    repositories {
        google()
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath "com.android.tools.build:gradle:$agp_version"
        classpath 'com.google.gms:google-services:4.4.2'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        jcenter()
        maven { url 'https://maven.google.com' }
        maven { url 'https://jitpack.io' }

    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
